﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class MergeInputBill : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MergeInputBill",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MergeInvoiceNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AgentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AgentName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MergeTime = table.Column<DateTime>(type: "date", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    PurchaseDutyNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SaleDutyNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NotaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MergeInputBill", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MergeInputBillDebt",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MergeInputBillId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DebtId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DebtCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DebtAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MergeInputBillDebt", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MergeInputBillDebt_MergeInputBill_MergeInputBillId",
                        column: x => x.MergeInputBillId,
                        principalTable: "MergeInputBill",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MergeInputBillDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MergeInputBillId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductNameId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NoTaxCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NoTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MergeInputBillDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MergeInputBillDetail_MergeInputBill_MergeInputBillId",
                        column: x => x.MergeInputBillId,
                        principalTable: "MergeInputBill",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MergeInputBillRelation",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MergeInputBillId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InputBillId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MergeInputBillRelation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MergeInputBillRelation_InputBill_InputBillId",
                        column: x => x.InputBillId,
                        principalTable: "InputBill",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MergeInputBillRelation_MergeInputBill_MergeInputBillId",
                        column: x => x.MergeInputBillId,
                        principalTable: "MergeInputBill",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MergeInputBillSubmitDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MergeInputBillId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ProductNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductNoId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    BussinessItemCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BussinessDate = table.Column<DateTime>(type: "date", nullable: true),
                    Quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ReceivedNumber = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    NoTaxCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    BusinessType = table.Column<int>(type: "int", nullable: true),
                    ProducerOrderNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ContractNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MergeInputBillSubmitDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MergeInputBillSubmitDetail_MergeInputBill_MergeInputBillId",
                        column: x => x.MergeInputBillId,
                        principalTable: "MergeInputBill",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MergeInputBillDebt_MergeInputBillId",
                table: "MergeInputBillDebt",
                column: "MergeInputBillId");

            migrationBuilder.CreateIndex(
                name: "IX_MergeInputBillDetail_MergeInputBillId",
                table: "MergeInputBillDetail",
                column: "MergeInputBillId");

            migrationBuilder.CreateIndex(
                name: "IX_MergeInputBillRelation_InputBillId",
                table: "MergeInputBillRelation",
                column: "InputBillId");

            migrationBuilder.CreateIndex(
                name: "IX_MergeInputBillRelation_MergeInputBillId",
                table: "MergeInputBillRelation",
                column: "MergeInputBillId");

            migrationBuilder.CreateIndex(
                name: "IX_MergeInputBillSubmitDetail_MergeInputBillId",
                table: "MergeInputBillSubmitDetail",
                column: "MergeInputBillId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MergeInputBillDebt");

            migrationBuilder.DropTable(
                name: "MergeInputBillDetail");

            migrationBuilder.DropTable(
                name: "MergeInputBillRelation");

            migrationBuilder.DropTable(
                name: "MergeInputBillSubmitDetail");

            migrationBuilder.DropTable(
                name: "MergeInputBill");
        }
    }
}
