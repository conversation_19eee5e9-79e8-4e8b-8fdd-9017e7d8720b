﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addLossRecognitionItemPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LossRecognitionItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "单号"),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "单据日期"),
                    BusinessDeptFullPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptFullName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreditAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreditType = table.Column<int>(type: "int", nullable: false),
                    AttachFileIds = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "附件Ids"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: true),
                    OARequestId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LossRecognitionItem", x => x.Id);
                },
                comment: "损失确认申请单");

            migrationBuilder.CreateTable(
                name: "LossRecognitionDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    LossRecognitionItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Classify = table.Column<int>(type: "int", nullable: false),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "明细单据日期（应收日期、应付日期）"),
                    BillCode = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "明细单据号（应收日期、应付日期）"),
                    Value = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "明细金额（应收金额、应付金额）"),
                    AbatmentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "已冲销金额"),
                    LeftAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "余额"),
                    BadAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "确认坏账金额"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LossRecognitionDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LossRecognitionDetail_LossRecognitionItem_LossRecognitionItemId",
                        column: x => x.LossRecognitionItemId,
                        principalTable: "LossRecognitionItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "损失确认明细表");

            migrationBuilder.CreateIndex(
                name: "IX_LossRecognitionDetail_LossRecognitionItemId",
                table: "LossRecognitionDetail",
                column: "LossRecognitionItemId");

            migrationBuilder.CreateIndex(
                name: "IX_LossRecognitionItem_BillCode",
                table: "LossRecognitionItem",
                column: "BillCode",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LossRecognitionDetail");

            migrationBuilder.DropTable(
                name: "LossRecognitionItem");
        }
    }
}
