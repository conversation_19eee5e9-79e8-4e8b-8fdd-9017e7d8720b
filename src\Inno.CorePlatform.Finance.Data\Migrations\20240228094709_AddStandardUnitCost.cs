﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddStandardUnitCost : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "StandardUnitCost",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "StandardUnitCost",
                table: "ReconciliationIncomeDetail",
                type: "decimal(18,10)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StandardUnitCost",
                table: "ReconciliationStockDetail");

            migrationBuilder.DropColumn(
                name: "StandardUnitCost",
                table: "ReconciliationIncomeDetail");
        }
    }
}
