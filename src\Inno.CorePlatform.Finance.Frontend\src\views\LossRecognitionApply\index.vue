<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>确认损失申请</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation
        :crud="crud"
        :permission="functionUris"
        :hiddenColumns="[]"
        hidden-opts-left
      >
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <inno-split-pane :min-percent="20" split="horizontal" style="padding: 0 15px 15px">
      <template #paneL="{ full, onFull }">
        <inno-query-operation v-model:query-list="queryList" :crud="crud" />
        <inno-crud-operation
          style="padding: 0px 0px 6px 0px"
          :crud="crud"
          rightAdjust
          :permission="functionUris"
          :hiddenColumns="[]"
          border
          hidden-opts-right
        >
          <template #opts-left>
            <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="changeTabClick">
              <el-tab-pane :label="`待提交(${tabModel.waitSubmitCount})`" name="0"></el-tab-pane>
              <el-tab-pane :label="`待审核(${tabModel.waitAuditCount})`" name="1"></el-tab-pane>
              <el-tab-pane :label="`已完成(${tabModel.complateCount})`" name="99"></el-tab-pane>
              <el-tab-pane :label="`已拒绝(${tabModel.refuseCount})`" name="66"></el-tab-pane>
              <el-tab-pane :label="`全部(${tabModel.allCount})`" name="-1"></el-tab-pane>
              <el-tab-pane :label="`我的审批(${tabModel.myCount})`" name="5000"></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <inno-button-tooltip type="primary"
              icon="CirclePlus"
              @click.stop="jumpCreate">
              新增
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              @click.stop="submitApply(crud.rowData.id)">
              提交
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              icon="Edit"
              @click.stop="jumpEdit(crud.rowData.id)">
              编辑
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              @click.stop="deleteApply(crud.rowData.id)">
              删除
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.oaRequestId"
              type="primary"
              icon="Document"
              :loading="btnLoading"
              @click="auditProcessClick(crud.rowData)">
              查看审批过程
            </inno-button-tooltip>
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <el-table
          ref="tableItem"
          v-inno-loading="crud.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crud.data"
          stripe
          @sort-change="crud.sortChange"
          @selection-change="crud.selectionChangeHandler"
          @row-click=" (e) => { getDetailData(e);}"
        >
          <el-table-column fixed="left" width="55">
            <template #default="scope">
              <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
            </template>
          </el-table-column>
          <!-- <el-table-column type="index" fixed="left" width="50" /> -->
          <el-table-column fixed="left" label="申请单号" property="billCode" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link @click.stop="jumpDetail(scope.row.id, scope.row.type)">
                {{ scope.row.billCode }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="应收类型" property="creditType" show-overflow-tooltip>
            <template #default="scope">
              {{ formatCreditType(scope.row.creditType) }}
            </template>
          </el-table-column>
          <el-table-column label="核算部门" property="businessDeptFullName" min-width="150" show-overflow-tooltip />
          <el-table-column label="单据日期" width="100" property="billDate" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="公司" width="150" property="companyName" show-overflow-tooltip></el-table-column>
          <el-table-column sortable class-name="isSum" prop="creditAmount" label="应收合计金额" min-width="110">
            <template #default="scope">
              <inno-numeral :value="scope.row.creditAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column label="状态" property="statusStr" show-overflow-tooltip />
          <el-table-column label="备注" property="remark" show-overflow-tooltip />
          <el-table-column
            label="创建时间"
            min-width="150"
            property="createdTime"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
            </template>
            <template #default="scope">{{ scope.row.createdByName }}</template>
          </el-table-column>
          <el-table-column label="附件" fixed="right" property="attachFileIds"  min-width="100">
            <template #default="scope">
                <el-button type="primary" v-if="scope.row.attachFileIds!==undefined && scope.row.attachFileIds !=='' && scope.row.attachFileIds !== null" size="small" @click.stop="showAttachFile(scope.row.attachFileIds, scope.row.id)">
                    查看文件
                </el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="app-page-footer background">
          已选择 {{ crud.selections.length }} 条
          <div class="flex-1" />

          <inno-crud-pagination :crud="crud" />
        </div>
      </template>
      <template #paneR="{ full, onFull }">
        <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
          <template #opts-left>
            <el-tabs v-model="setDetailTab" class="demo-tabs" @tab-change="tabDetailActiveClick">
              <el-tab-pane :label="`申请明细`" name="1"></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <!-- 申请明细 -->
        <div class="detail-box">
    <div class="detail-item" :style="{ 
      width: crud.rowData.creditType === 6 ? '100%' : '49%' 
    }">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px' }"
        class="zx-box-card"
        style="padding-bottom: 0px; margin-bottom: 10px"
      >
        <template #header>
          <div class="card-header" style="width: 100%">
            <span style="line-height: 42px; height: 42px">应收明细</span>
          </div>
        </template>
        <div>
          <el-table
            ref="refTable"
            :data="creditList"
            max-height="230"
            border
            stripe
            style="margin-top: 4px"
            show-summary
            :summary-method="getSummaries"
          >
            <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip />
            <el-table-column label="应收单号" min-width="120" property="billCode" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
                <template #default="scope">
                  {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="项目名称" width="120" property="projectName" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                </template>
              </el-table-column>
              <!-- <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip></el-table-column> -->
              <el-table-column label="终端医院" width="120" property="hospitalName" show-overflow-tooltip></el-table-column>
              <el-table-column class-name="isSum" label="应收单金额" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            <p>共 {{ creditList.length }} 条</p>
            <div class="flex-1" />
          </div>
        </div>
      </el-card>
    </div>
    <div class="detail-item">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px' }"
        class="zx-box-card"
        style="padding-bottom: 0px; margin-bottom: 10px"
      >
        
        <template #header>
          <div class="card-header" style="width: 100%">
            <span style="line-height: 42px; height: 42px">应收对应应付</span>
          </div>
        </template>
        <div>
          <el-table
            ref="refTable"
            :data="debtByCredilist"
            max-height="230"
            border
            stripe
            style="margin-top: 4px"
            show-summary
            :summary-method="getSummaries"
          >
            <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip />
            <el-table-column label="应付单号" width="120" property="billCode" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
                <template #default="scope">
                  {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="项目名称" width="120" property="projectName" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip></el-table-column>
              <!-- <el-table-column label="终端医院" width="120" property="hospitalName" show-overflow-tooltip></el-table-column> -->
              <el-table-column class-name="isSum" label="应付单金额" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column prop="badAmount" label="确认坏账金额" min-width="120">
              <template #default="scope">
                <inno-numeral :value="scope.row.badAmount" format="0,0.00" />
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            <p>共 {{ debtByCredilist.length }} 条</p>
            <div class="flex-1" />
          </div>
        </div>
      </el-card>
    </div>
  </div>
        <!-- <el-table
          v-if="setDetailTab === '1'"
          ref="tableDetail"
          v-inno-loading="crudDetail.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crudDetail.data"
          stripe
          show-summary
          :summary-method="getSummaries"
          @sort-change="crudDetail.sortChange"
          @selection-change="crudDetail.selectionChangeHandler"
          @row-click="crudDetail.rowClick"
        >
          <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip>
            <template #default="scope">
              {{ (crudDetail.page.page - 1) * crudDetail.page.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="应收单号" width="200" property="billCode" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="项目名称" width="200" property="projectName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip></el-table-column>
          <el-table-column label="终端医院" width="200" property="hospitalName" show-overflow-tooltip></el-table-column>
          <el-table-column class-name="isSum" label="应收单金额" property="value" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.value" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="单据日期" property="billDate" width="90" show-overflow-tooltip sortable>
          <template #default="scope">
            {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD')  }}
          </template>
        </el-table-column>
        </el-table> -->
      </template>
    </inno-split-pane>
    <el-dialog v-model="setFormVisible" title="审批" width="500">
      <el-form :model="setForm" :rules="setRules" ref="setRef">
        <el-form-item label="审批结果" prop="isApproved">
          <el-radio-group v-model="setForm.isApproved">
            <el-radio value="1">审批通过</el-radio>
            <el-radio   value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input v-model="setForm.auditRemark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveCancel">取消</el-button>
          <el-button type="primary" @click="approveConfirm(setRef)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
     <!-- 查看附件 -->
     <el-dialog v-model="comfile_show"
      title="查看附件"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      modal-class="position-fixed"
      draggable
    >
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ format(scope.row.length) }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="openViewFile(scope.row.id)">
              查看
            </span>

            <!-- <span style="cursor: pointer" @click="deleteFile_letter(scope.row.id)">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, Ref, onBeforeMount, onMounted, onActivated, computed, reactive, provide, nextTick } from 'vue';
import { ElTable, TableColumnCtx, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { useColumnStrategies } from '@inno/inno-mc-vue3/lib/utils/hooks';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { pageRedirect } from '@/utils/utils';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import _, { constant } from 'lodash';
import { formatDate, normalizeDate } from '@vueuse/shared';
import { Loading } from 'element-plus/es/components/loading/src/service';
import { Decimal } from 'decimal.js';
import approveProcess from '@/component/ApproveProcess.vue';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';

interface ListItem {
  value: string;
  label: string;
}
//服务商审批意见
interface AuditOpinionItem {
  auditOpinion: string;
  createTime: string;
  userName: string;
}

const CreditTypeList = [
  {
    id: 4,
    name: '销售应收'
  },
  {
    id: 6,
    name: '经销销售应收'
  },
  {
    id: 2,
    name: '初始应收'
  },
  {
    id: 14,
    name: '损失确认应收'
  }
]

const { username } = useUserStore();

const setDetailTab = ref('1');
//获取路由
const router = useRouter();
//数据策略权限
const functionUris = {
  audit: 'metadata://sia/aier-prereturn-apply',
};

//选项卡对象
const tabModel = ref({
  waitSubmitCount:0,
  waitAuditCount:0,
  refuseCount:0,
  allCount:0,
  complateCount:0,
  myCount:0,
});
//上传附件
const comfile_show = ref(false);
const showfiles = ref([]);
const approveProcessRef = ref();
//合计统计数据
const detailFooterStatistics = reactive<{
  totalQty: number;
  totalStoreInQty: number;
  totalReceiveQty: number;
  totalTariffAmount: number;
  totalImportAddAmount: number;
  totalUnitCost: number;
  totalSaleAmount: number;
  totalOriginalAmount: number;
  totalEstimatedAmount: number;
  totalInQty:number;
}>({
  totalQty: 0,
  totalStoreInQty: 0,
  totalReceiveQty: 0,
  totalTariffAmount: 0,
  totalImportAddAmount: 0,
  totalUnitCost: 0,
  totalSaleAmount: 0,
  totalOriginalAmount: 0,
  totalEstimatedAmount: 0,
  totalInQty: 0
});
const selectTemplateModel = reactive<{
  type: string;
}>({
  type: '00000009'
});
const dlgProjectVisable = ref(false);
const projectFormModel = {
  projectName: '',
  projectId: ''
};
//退货明细打印是否显示
const showPrintDetail = ref(false);
//选择退货明细打印模板类型
const dialogFormVisible = ref(false);
const setFormVisible = ref(false);
const setBtnLoading = ref(false);
const btnLoading = ref(false);
const downloadBtnLoading = ref(false);
const timeRef = ref();
const setRef = ref();
const producerList = ref([]);
const agentList = ref([]);
const serviceList = ref([]);
const projectList = ref([]);
const debtByCredilist = ref([]);
const creditList = ref([]);
const timeForm = reactive({
  startTime:''
})
const expandRowKeys = ref()
const setForm = reactive({
  auditRemark:undefined,
  isApproved:undefined,
})
const rules = reactive<FormRules<any>>({
  startTime: [
    {
      required: true,
      message: '请选择同步时间',
      trigger: 'change',
    },
  ]
})
// 表单验证
const setRules = reactive<FormRules>({
  isApproved: [
    {
      required: true,
      message: '请选择审批结果',
      trigger: 'change',
    },
  ],
})
const matchProjectList = ref<ListItem[]>([]);
const selectProjectId = ref('');
const showGroupCreateTypeModal = ref(false);
const groupCreateType = ref(1);
const appleType = ref('');
const strategies = ref({
  hidden: []
});
const auditOpinionShow = ref(false);
const auditOpinionList = ref<AuditOpinionItem[]>([]);


interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'amount') {
      const values = data.map((item) => item.amount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'badAmount') {
      const values = data.map((item) => item.badAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  else if (column.property === 'leftAmount') {
      const values = data.map((item) => item.leftAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'value') {
      const values = data.map((item) => item.value);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'abatmentAmount') {
      const values = data.map((item) => item.abatmentAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalAmount') {
      const values = data.map((item) => (item.originUnitPrice * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalOriginalAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } else if (column.property === 'estimatedAmount') {
      const values = data.map((item) => (item.unitCost * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalEstimatedAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } 
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
    if (column.property === 'quantity') {
      const total = values.reduce((prev, curr) => (prev  + curr), 0);
      sums[index] = total;
      return;
    }
    if (column.property === 'arrivedQty') {
      sums[index] = detailFooterStatistics.totalReceiveQty;
      return;
    }
    if (column.property === 'storeInQty') {
      sums[index] = detailFooterStatistics.totalStoreInQty;
      return;
    }
    if (column.property === 'originUnitPrice') {
      sums[index] = detailFooterStatistics.totalOriginalAmount;
      return;
    }
    if (column.property === 'storeInDetails.quantity') {
      sums[index] = detailFooterStatistics.totalInQty;
      return;
    }
    
    // if (
    //   column.property === 'quantity' ||
    //   column.property === 'arrivedQty' ||
    //   column.property === 'storeInQty'
    // ) {
    //   sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //   return;
    // }
  });
  return sums;
};


const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const tableInApplyDetail = ref<InstanceType<typeof ElTable>>();

const crud = CRUD(
  {
    title: '应用',
    url: `/api/LossRecognitionQuery/GetList`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    query: { status: '0' },
    tablekey: 'tablekey',
    userNames: ['createdBy', 'updatedBy'],
    optShow: {
      add: false,
      edit: false,
      del: false,
      download: false,
      reset: true
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
        showProjectModel();
      },
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          // appleType.value = crud.data[0].applyTypeName;
          crud.singleSelection(crud.data[0]);
          crudDetail.query.lossRecognitionItemId = crud.rowData.id;
          crudDetail.toQuery(); 
        }else {
          creditList.value = [];
          debtByCredilist.value = [];
          crudDetail.data = [];
        }
        getTabCount();
        // getMyAuditTabCount();
      }
    },
    pageConfig: {
      // 分页参数按项目可以单独配置
      pageIndex: 'pageIndex',
      pageSize: 'pageSize'
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
const crudDetail = CRUD(
  {
    title: '应用',
    url: `/api/LossRecognitionQuery/GetListDetail`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    tablekey: 'tablekeyCopy', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    page: {
      isPage: false
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
        if (!crud.rowData.id) {
          crudDetail.data = [];
          creditList.value = [];
          debtByCredilist.value = [];
        }
        if (crudDetail.data.length && crudDetail.data.length > 0) {
          creditList.value.splice(0);
          debtByCredilist.value.splice(0);
          crudDetail.data.map((el:any)=> {
            if(el.classify===1){
              creditList.value.push(el);
            }else{
              debtByCredilist.value.push(el);
            }
          })
          console.log(creditList.value,'=========1111111111');
          console.log(debtByCredilist.value,'=========22222222222222');
        }
        
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const crudInApplyDetail = CRUD(
  {
    title: '应用',
    url: `${window.gatewayUrl}v1.0/ic-backend/api/SpdStoreInForB/page`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    sort: ['createdTime,desc'],
    method: 'get',
    query: {},
    tablekey: 'tablekeyInApply', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
        // detailFooterStatistics.totalInQty = data.footer.totalQty;
      }
    }
  },
  {
    table: tableInApplyDetail
  }
);

const getTabCount = () => {
  request({
    url: `/api/LossRecognitionQuery/GetTabCount`,
    method: 'post',
    data: crud.query
  })
    .then((res) => {
      // tabModel.value.tabAwaitSubAudit = res.data.data.awaitSubAuditCount;
      tabModel.value.waitSubmitCount = res.data.data.waitSubmitCount;
      tabModel.value.waitAuditCount = res.data.data.waitAuditCount;
      tabModel.value.allCount = res.data.data.allCount;
      tabModel.value.complateCount = res.data.data.complateCount;
      tabModel.value.myCount = res.data.data.myCount;
      tabModel.value.refuseCount = res.data.data.refuseCount;
      console.log(res.data.data,'======================1111111111111111111');
    })
    .catch((error) => {
      console.log(error);
    });
};



const changeTabClick = () => {
  crudDetail.data = [];
  crudInApplyDetail.data = [];
  crud.toQuery();
};

const getDetailData = (e:any) => {
  console.log('点击行事件赋值并调用详细信息=========', crud.rowData.id);
  crudDetail.query.lossRecognitionItemId = e.id;
  crudDetail.toQuery();
  crud.singleSelection(e);
};

function showProjectModel() {
  dlgProjectVisable.value = true;
}

const props = defineProps({
  __refresh: Boolean
});

onBeforeMount(() => {
  crud.toQuery();
});

onMounted(() => {
  // 表头拖拽必须在
  tableDrag(tableItem);
  tableDrag(tableDetail);
  // tableDrag(tableInApplyDetail);
  const strategiesConfig = {
    functionUri: 'metadata://sia/storein-apply/routes/index-search',
    url:
      window.gatewayUrl +
      `v1.0/sia-backend/api/purchaseapply/getStrategy?functionUri=metadata://sia/storein-apply/routes/index-search`,
    method: 'get'
  };
  strategies.value = useColumnStrategies(strategiesConfig);
  // test();
});

onActivated(() => {
  crud.toQuery();
});
//高级检索
const queryList = computed(() => [
  {
    key: 'billCode',
    label: '申请单号',
    show: true
  },
  // {
  //   key: 'orderNo',
  //   label: '订单号',
  //   show: true
  // },
  // {
  //   key: 'salesOrderNo',
  //   label: '销售订单号',
  //   show: true
  // },
  // {
  //   key: 'originalOrderNo',
  //   label: '原始订单号',
  //   show: true
  // },
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: false
  },
  {
    key: 'customerId',
    label: '客户',
    show: true,
    type: 'remoteSelect',
    method: 'post',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    placeholder: '客户名称搜索'
  },
  {
    key: 'creditType',
    label: '应收类型',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    multiple: false,
    dataList: CreditTypeList,
    show: true
  },
  // {
  //   key: 'invoiceTitle',
  //   label: '发票抬头',
  //   show: false
  // },
  // {
  //   key: 'taxpayerNo',
  //   label: '纳税人识别号',
  //   show: false
  // },
  // {
  //   key: 'invoicePerson',
  //   label: '开票人',
  //   method: 'post',
  //   multiple: false,
  //   type: 'remoteSelect',
  //   url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
  //   placeholder: '用户名称搜索',
  //   valueK: 'name',
  //   labelK: 'displayName',
  //   props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
  //   slots: {
  //     option: ({ item }) => (
  //       <>
  //         <span>{item.displayName}</span>
  //         <span style="float:right">{item.name}</span>
  //       </>
  //     )
  //   }
  // },
  // {
  //   key: 'applyDateStart',
  //   endDate: 'applyDateEnd',
  //   label: '申请日期',
  //   type: 'daterange',
  //   defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
  //   props: { format: 'YYYY-MM-DD' }
  // },
  // {
  //   key: 'invoiceDateStart',
  //   endDate: 'invoiceDateEnd',
  //   label: '开票日期',
  //   type: 'daterange',
  //   defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
  //   props: { format: 'YYYY-MM-DD' }
  // },  
  
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

//切换
const tabDetailActiveClick = async (tab: any) => {
  if (tab === '1' && crud.rowData.id) {
    crudDetail.query.lossRecognitionItemId = crud.rowData.id;
    crudDetail.toQuery();
    crud.singleSelection(crud.rowData);
  } else if(tab === '2' && crud.rowData.code) {
    crudInApplyDetail.query.code = crud.rowData.code;
    crudInApplyDetail.toQuery();
    crud.singleSelection(crud.rowData);
  }
};

const approveConfirm = async(formEl:any) =>{
  if (!formEl) return
    let checkResult = await formEl.validate((valid:any, fields:any) => {return valid})
     if (!checkResult) {
        return false;
    }
  let postData = {
    id: crud.rowData.id,
    isApproved: setForm.isApproved === '1'? true : setForm.isApproved === '2' ? false :null,
    auditRemark: setForm.auditRemark
  }
  request({
    url: `/api/AierPreReturnApply/Audit`,
    method: 'POST',
    data: postData
  }).then(res=>{
    if(res.data.code ===200){
      ElMessage.success({ showClose: true, message: '审批成功！' });
      crud.toQuery();
      setFormVisible.value = false;
      approveCancel();
    }else{
      ElMessage.error({ showClose: true, message: res.data.message });
    }
  }).catch((error)=>{
    ElMessage.error({ showClose: true, message: error });
  })
  
 
}
  const submitApply =(id) => {
    request({
      url: `/api/LossRecognition/Submit?id=` + id,
      method: 'PUT',
    }).then(res => {
      if (res.data.code === 200) {
        ElMessage.success({ showClose: true, message: '提交成功！' });
        crud.toQuery();
      } else {
        ElMessage.error({ showClose: true, message: res.data.message });
      }
    }).catch((error) => {
      ElMessage.error({ showClose: true, message: error });
    })
  }
  const deleteApply = (id) => {
    ElMessageBox.confirm('此操作将删除当前单据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: `/api/LossRecognition/Del?id=` + id,
        method: 'DELETE',
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success({ showClose: true, message: '删除成功！' });
          crud.toQuery();
        } else {
          ElMessage.error({ showClose: true, message: res.data.message });
        }
      }).catch((error) => {
        ElMessage.error({ showClose: true, message: error });
      })
    });
  };

const approveCancel = () =>{
  setFormVisible.value = false;
  setForm.isApproved = undefined;
  setForm.auditRemark = undefined;
}
const openApprove = () =>{
  if(!crud.rowData.id || crud.rowData.id === ''){
    ElMessage.warning({ showClose: true, message: '请选择需要审批的数据！' });
    return false;
  }
  setFormVisible.value = true;
}
const jumpCreate = () =>{
  router.push({
    path: '/lossRecognitionApply/create',
    query: {
      __page_id: `${new Date().getTime()}`,
    },
    params: {
      __reload: true
    }
  });
}
const jumpEdit = (id:any) =>{
  router.push({
    path: '/lossRecognitionApply/edit',
    query: {
      id: id,
      __page_id: `${new Date().getTime()}`,
    },
    params: {
      __reload: true
    }
  });
}
//审批过程
const auditProcessClick = (row) => {
  //审批过程
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
const openViewFile = (ids:any) =>{
    let arr = ids.split(",");
    FileViewer.show(arr,  0,  {});
    // FileViewer.show([ids], 0, {});
}
const jumpDetail = (id: string, type: number) => {
  let routeName = '/lossRecognitionApply/detail';
  router.push({
    path: routeName,
    query: {
      id: id,
      __page_id: `${new Date().getTime()}`
    },
    params: {
      __reload: true
    }
  });
};
 //附件
 const showAttachFile = (fileIds:any,id:any) => {
  request({
    url: `/api/LossRecognition/GetAttachFile`,
    method: 'POST',
    data: {
      lossRecognitionItemId: id,
      fileIds:fileIds
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_show.value = true;
        showfiles.value = res.data.data;
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
    
  };
  //文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
};

const formatCreditType = (creditType) => {
  return CreditTypeList.filter((item) => {
    return item.id === creditType;
  })[0].name;
}
</script>
<style scoped lang="scss">
:deep(.el-tabs__item) {
  padding: 0 12px;
}
:deep(.right-adjust) {
  display: block;
  max-width: 42%;
  display: flex;
  flex-wrap: wrap;
  height: auto;
  justify-content: flex-end;
  margin-left: 20px;
  align-items: center;
}
:deep(.crud-opts-border) {
  justify-content: space-between;
}
/* :deep(.el-button) {
  margin-top: 8px;
} */
:deep(.crud-opts) {
  justify-content: space-between;
}
.detail-box{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-height: 40%;
  .detail-item{
    width: 49%;
  }
}
</style>
