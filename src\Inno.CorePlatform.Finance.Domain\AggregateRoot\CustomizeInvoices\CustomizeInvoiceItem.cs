﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    /// <summary>
    /// 运营制作开票单
    /// </summary>
    public class CustomizeInvoiceItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        ///  编号
        /// </summary> 
        public string Code { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        [MaxLength(200)]
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary> 
        [MaxLength(200)]
        public string? NameCode { get; set; }
        /// <summary>
        ///  付款单位Id
        /// </summary>
        [MaxLength(200)]
        public string CustomerId { get; set; }

        /// <summary>
        ///  付款单位名称
        /// </summary>
        [MaxLength(200)]
        public string CustomerName { get; set; }

        /// <summary>
        /// 发票总金额
        /// </summary> 
        [Column(TypeName = "decimal(18,4)")]
        public decimal InvoiceTotalAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum InvoiceType { get; set; }

        /// <summary>
        /// 是否推送金碟
        /// </summary>
        public bool IsPush { get; set; }

        /// <summary>
        /// 是否已开票
        /// </summary>
        public bool IsInvoiced { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 审批备注
        /// </summary>  
        public string? ApproveRemark { get; set; }
        /// <summary>
        /// 附件Ids
        /// </summary> 
        public string? AttachFileIds { get; set; }
        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// 关系Code
        /// </summary>
        [MaxLength(200)]
        public string? RelationCode { get; set; }

        /// <summary>
        /// 关系类型
        /// </summary>
        public int? RelationType { get; set; }

        /// <summary>
        /// 变更状态
        /// </summary>
        public int? ChangedStatus { get; set; }


        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 蓝字红冲金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BlueRedInvoiceAmount { get; set; }

        /// <summary>
        /// 申请方 2=销方申请，1=购方申请-未抵扣，0=购方申请-已抵扣
        /// </summary>
        public int? RedOffsetOpter { get; set; }

        /// <summary>
        /// 冲红原因 销货退回=1 开票有误=2 服务中止=3 销售折让=4
        /// </summary>
        public int? RedOffsetReason { get; set; }

        /// <summary>
        /// 开票单分类Id
        /// </summary>  
        public Guid? CustomizeInvoiceClassifyId { get; set; }
        /// <summary>
        /// 是否有红字确认单，0=否，1=是
        /// </summary>
        public int? IsNoRedConfirm { get; set; }
    }
}
