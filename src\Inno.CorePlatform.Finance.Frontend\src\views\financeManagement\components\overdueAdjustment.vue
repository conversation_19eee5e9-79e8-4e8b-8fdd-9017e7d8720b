<template>
    <el-dialog v-model="modalShow" title="预计付款日期调整申请" width="920px" draggable @close="Cancel">
        <el-form :model="form" ref="deliveryTimeRef" :inline="true" :rules="rules">
        <el-form-item :span="12" label="采购单号:" :label-width="125">
            <template #default="scope">
              <p style="width:280px;margin: 0;padding: 0;">{{ form.purchaseCode }}</p>
            </template>
        </el-form-item>
        <el-form-item :span="12" label="应付单号:" :label-width="125">
            <template #default="scope">
              <p style="width:280px;margin: 0;padding: 0;">{{ form.payableCode }}</p>
            </template>
        </el-form-item>
        <el-form-item :span="12" label="供应商:" :label-width="125">
            <template #default="scope">
              <p style="width:280px;margin: 0;padding: 0;">{{ form.agentName }}</p>
            </template>
        </el-form-item>
        <el-form-item :span="12" label="付款金额:" :label-width="125">
            <template #default="scope">
              <p style="width:280px;margin: 0;padding: 0;">{{ form.value }}</p>
            </template>
        </el-form-item>
        <el-form-item :span="12" label="预计付款日期:" :label-width="125">
            <template #default="scope">
              <p style="width:280px;margin: 0;padding: 0;">{{ dateFormat(form.probablyPayTime, 'YYYY-MM-DD') }}</p>
            </template>
        </el-form-item>
        <el-form-item label="修改预计付款日期:" :label-width="125" prop="accountingPeriodDateEdit">
            <el-date-picker
                v-model="form.accountingPeriodDateEdit"
                type="date"
                placeholder="修改预计付款日期"
                clearable
                style="width:280px"
            />
        </el-form-item>
        <el-form-item label="备注:" :label-width="125">
            <el-input v-model="form.remark" style="width:720px" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" autocomplete="off" />
        </el-form-item>
        <el-form-item label="附件:" :label-width="125">
            <inno-file-uploader
            v-model="form.attachFileIds"
            style="width: 720px"
            :appId="'purchase-backend'"
            :bizType="'purchase'"
            :fileMode="'large'"
            list-type="text"
            :drag="true"
            multiple
            >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
                拖动文件到这 或者
                <em>点击上传</em>
            </div>
            <template #tip>
                <div class="el-upload__tip"></div>
            </template>
            <template #fileList="{ list, remove, preview, download }">
                <el-table :data="list" border style="width: 100%">
                <el-table-column prop="name" label="文件名称" show-overflow-tooltip />
                <el-table-column prop="length" label="文件大小" show-overflow-tooltip />
                <el-table-column prop="ext" label="文件类型" show-overflow-tooltip />
                <el-table-column prop="uploadedByName" label="操作人" show-overflow-tooltip />
                <el-table-column label="操作时间" show-overflow-tooltip>
                    <template #default="{ row }">
                    {{ dateFormat(row.uploadedTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="200">
                    <template #default="{ row }">
                    <el-button type="primary" icon="View" @click="preview(row)">预览</el-button>
                    <el-button type="primary" icon="Download" @click="download(row)">
                        下载
                    </el-button>
                    <el-button type="primary" icon="Delete" @click="remove(row)">
                        删除
                    </el-button>
                    </template>
                </el-table-column>
                </el-table>
            </template>
            </inno-file-uploader>
        </el-form-item>
        </el-form>
        <template #footer>
        <div class="dialog-footer">
            <el-button @click="Cancel">取消</el-button>
            <el-button type="primary" :loading="btnLoading" @click="sumbitData(deliveryTimeRef)">
            提交
            </el-button>
        </div>
        </template>
    </el-dialog>
</template>
<script setup lang="tsx">
import { ref, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ElTable, ElForm, ElMessageBox, ElMessage,ElLoading } from 'element-plus';
import request from '@/utils/request';
const modalShow = ref(false);
const activeName = ref('PaymentPeriod');
const costTableData = reactive<Array<any>>([]);
const paymentPlanTableData = reactive<Array<any>>([]);
// 事件定义
const emit = defineEmits(['onLoadDetails']);
    
const isPurchase = ref('');
const btnLoading = ref(false);
const deliveryTimeRef = ref();
// 打开变更记录弹窗
const openOverdueDialog = (data:any) => {
    form.purchaseCode = data.purchaseCode;
    form.payableCode = data.debt.billCode;
    form.agentName = data.debt.agentName;
    form.value = data.value;
    form.probablyPayTime = data.probablyPayTime;
    form.id = data.id;
    modalShow.value = true;
};
const form = reactive({
  purchaseCode:'', //采购单号
  payableCode:'', //应付单号
  agentName:'', //供应商名称
  value:'', //付款金额
  probablyPayTime:'', //预计付款日期
  accountingPeriodDateEdit:'', //修订账期起始日期
  remark:'', //备注
  attachFileIds:'', //附件id
  id:'',
})

const rules: FormRules = {
    accountingPeriodDateEdit: [{ required: true, message: '请选择修订账期起始日期', trigger: 'change' }],
};
const sumbitData = async(formEl:any) =>{
    if (!formEl) return
    let checkResult = await formEl.validate((valid, fields) => {return valid})
     if (!checkResult) {
        ElMessage.warning({ showClose: true, message: '请按要求填写表单数据。' });
        return;
    }
    btnLoading.value = true;
    let postData = {
        id:form.id,
        accountingPeriodDateEdit: form.accountingPeriodDateEdit,
        remark: form.remark,
        attachFileIds: [form.attachFileIds]
    }
    request({
        url: `/api/DebtQuery/SaveAccountPeriodEdit`,
        method: 'POST',
        data: postData
    }).then(res=>{
        if(res && res.data.code === 200){
            ElMessage({
                showClose: true,
                message: '预计付款日期调整申请成功！',
                type: 'success',
            });
            form.accountingPeriodDateEdit = '',
            form.attachFileIds = '',
            form.remark = '',
            modalShow.value = false
            btnLoading.value = false;
            emit('onLoadDetails');
        }else{
            ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
            });
            btnLoading.value = false;
        }
    }).catch((err) => {
        btnLoading.value = false;
    })
}
const Cancel = () =>{
    form.accountingPeriodDateEdit = '',
    form.attachFileIds = '',
    form.remark = '',
    modalShow.value = false
}
// 暴漏方法
defineExpose({
    openOverdueDialog,
    isPurchase
});
</script>
<style scoped>
.btn-box {
    /* display: block; */
    padding: 20px 20px;
    display: flex;
}

.btn-item {
    display: block;
    margin: 0 auto;
    /* margin-right: 30px; */
}
</style>
