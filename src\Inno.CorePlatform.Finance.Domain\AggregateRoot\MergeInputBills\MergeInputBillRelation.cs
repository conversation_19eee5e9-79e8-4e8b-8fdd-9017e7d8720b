﻿﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.MergeInputBills
{
    /// <summary>
    /// 合并进项发票与原始进项发票关系
    /// </summary>
    public class MergeInputBillRelation : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        public virtual MergeInputBill MergeInputBill { get; set; }

        /// <summary>
        /// 原始进项发票ID
        /// </summary>
        public Guid InputBillId { get; set; }

        /// <summary>
        /// 原始进项发票
        /// </summary>
        public virtual InputBill InputBill { get; set; }
    }
}
