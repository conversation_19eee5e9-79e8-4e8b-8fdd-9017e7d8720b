﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 销项发票表
    /// </summary>
    [Table("OutputInvoice")]
    public class OutputInvoicePo : BasePo
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        [MaxLength(200)]

        public string Type { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [MaxLength(500)]
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [MaxLength(200)]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>

        [Column(TypeName = "decimal(18,10)")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        [Column(TypeName = "decimal(30,13)")]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Column(TypeName = "decimal(30,13)")]
        public decimal? UnitPriceOfNoTax { get; set; }

        /// <summary>
        /// 税率
        /// </summary> 

        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 开票申请单号
        /// </summary>
        [MaxLength(200)]
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditNo { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AmountOfNoTax { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxAmount { get; set; }
         
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTateCodeId { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        public int? RowNo { get; set; }
    }
}
