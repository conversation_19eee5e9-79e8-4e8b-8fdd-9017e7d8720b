<template>
  <el-card
    shadow="never"
    :body-style="{ padding: '10px' }"
    class="zx-box-card"
    style="padding-bottom: 0px; margin-bottom: 10px"
  >
    <template #header>
      <div class="card-header" style="width: 100%">
        <span>基本信息</span>
      </div>
    </template>
    <div>
      <el-form
        ref="refForm"
        :model="model.bindModel.basic"
        :rules="rules"
        label-position="right"
        label-width="100px"
        :inline="false"
      >
        <el-row :gutter="20">
          
          <el-col :span="12" :offset="0">
            <el-form-item label="申请单号">
              <el-input
                v-model="model.bindModel.basic.billCode"
                :disabled="true"
                placeholder="保存后自动生成"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="单据日期">
              <el-date-picker
                v-model="model.bindModel.basic.billDate"
                type="date"
                :disabled="true"
                placeholder="单据日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="应收类型" prop="creditType">
                <el-select
                v-model="model.bindModel.basic.creditType"
                :disabled="model.controlModel.isAllow"
                placeholder=""
                fit-input-width
                clearable
                filterable
              >
                <el-option
                  v-for="item in CreditTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="核算部门" prop="businessDeptId" :rules="rules.businessDeptId">
              <inno-department-select
                v-model="model.bindModel.basic.businessDeptId"
                v-model:path="model.bindModel.basic.businessDeptFullPath"
                v-model:fullName="model.bindModel.basic.businessDeptFullName"
                v-model:shortName="model.bindModel.basic.businessDeptShortName"
                v-model:deptShortName="model.bindModel.basic.businessDeptShortName"
                functionUri="metadata://pm/project-apply/routes/projectApply-index-search"
                :disabled="model.controlModel.isAllow"
                isShort
                @change="businessDeptsChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="公司" prop="company">
              <el-select
                v-model="model.bindModel.basic.company"
                value-key="id"
                placeholder=""
                :disabled="model.controlModel.isAllow"
                clearable
                filterable
                @change="changeSelectCompany"
              >
                <el-option
                  v-for="item in model.dataSource.basic.companys"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户" prop="customer">
              <inno-remote-select
                v-model="model.bindModel.basic.customer"
                :disabled="model.controlModel.isAllow"
                :is-guid="2"
                isObject
                placeholder="请选择客户"
                :queryData="{ functionUri: 'metadata://fam' }"
                :url="`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- :disabled="model.controlModel.detailIsAllow" -->
        <el-row :gutter="20">
          <el-col :span="24" :offset="0">
            <el-form-item label="备注">
              <el-input
                v-model="model.bindModel.basic.remark"
                type="textarea"
                :rows="3"
                placeholder=""
                :maxlength="-1"
                :show-word-limit="false"
                :autosize="{ minRows: 2, maxRows: 4 }"
                :disabled="model.controlModel.detailIsAllow"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" :offset="0">
            <el-form-item label="附件">
              <inno-file-uploader
                v-model="model.bindModel.basic.attachFileIds"
                :appId="model.bindModel.basic.appId"
                :bizType="model.bindModel.basic.bizType"
                :fileMode="'large'"
                list-type="text"
                style="width: 100%;"
                :drag="!model.bindModel.basic.disabled"
                :disabled="model.bindModel.basic.disabled"
                multiple
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  拖动文件到这 或者
                  <em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip"></div>
                </template>
                <template #fileList="{ list, remove, preview, download }">
                  <el-table :data="list" border style="width: 100%">
                    <el-table-column prop="name" label="文件名称" />
                    <el-table-column prop="length" label="文件大小">
                      <template #default="scope">
                        {{ scope.row.length + '字节' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="ext" label="文件类型">
                    </el-table-column>
                    <el-table-column prop="uploadedByName" label="操作人" />
                    <el-table-column prop="uploadedTime" label="操作时间">
                      <template #default="scope">
                        {{ dateFormat(scope.row.uploadedTime) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作">
                      <template #default="{ row }">
                        <el-button
                          v-if="!model.bindModel.basic.disabled"
                          type="primary"
                          @click="remove(row)"
                        >
                          删除
                        </el-button>
                        <el-button type="primary" @click="preview(row)">预览</el-button>
                        <el-button
                          type="primary"
                          @click="download(row)"
                        >
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </inno-file-uploader>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </el-card>
</template>
<script setup lang="tsx">
import { inject, onMounted, reactive, ref } from 'vue';
import { FormRules, FormInstance } from 'element-plus';
import {
  LossRecognitionApplyVModel,
  IProjectListItem,
  ICompany,
  CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY
} from '../models/LossRecognitionApplyVModel';
import { CreditTypeEnum } from '@/api/metaInfo';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { queryCheckedByDept } from '@/api/bdsData';
import { getCompanySysMonth } from '../apis/api';
const model = inject<LossRecognitionApplyVModel>(CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY) as LossRecognitionApplyVModel;
// 定义表单
const refForm = ref<FormInstance>();
model.controlModel.basicForm = refForm;
// const GetProjectURL = '/api/bff/GetProjectList?type=2&firstBusinessName=寄售';
const GetProjectURL = '/api/bff/SearchRunProjects';

const CreditTypeList = [
  {
    id: 4,
    name: '销售应收'
  },
  {
    id: 6,
    name: '经销销售应收'
  },
  {
    id: 2,
    name: '初始应收'
  }
]
// 表单验证
const rules = reactive<FormRules>({
  company: [
    {
      required: true,
      message: '请选择公司',
      trigger: 'change'
    }
  ],
  businessDeptId: [
    {
      required: true,
      message: '请选择核算部门',
      trigger: 'change'
    }
  ],
  creditType: [
    {
      required: true,
      message: '请选择应收类型',
      trigger: 'change'
    }
  ],
  customer: [
    {
      required: true,
      message: '请选择客户',
      trigger: 'change'
    }
  ]
});

type Props = {
  disabled: boolean;
  isView: boolean;
};
const props = withDefaults<Props, any>(defineProps<Props>(), {
  disabled: () => {
    return false;
  },
  isView: () => {
    return false;
  }
});

const changeSelectCompany = async (selectItem: ICompany) => {
  
};

const businessDeptsChange = async(node, notclear) => {
  if (!notclear) {
    model.bindModel.basic.company = { id: '', name: '', extraInfo: { nameCode: '' } };
  }
  if (node !== undefined) {
    //  formData.id = node.data.id;
   await queryCheckedByDept(node).then((res) => {
      model.dataSource.basic.companys = res.data.data;
    });
  }
};
// 如果路由参数有传projectId，则根据id设置默认选择项目
const setDefaultProject = () => {
  // if (model.propsModel.projectId) {
  //   if (model.dataSource.basic.projects) {
  //     let item = model.dataSource.basic.projects.find((el) => {
  //       return el.id === model.propsModel.projectId;
  //     });
  //     if (item) {
  //       changeProject(item);
  //     }
  //   }
  // }
};
onMounted(() => {
  // 初始化时加载数据
  setDefaultProject();
});
const allow = ref(false);
const allowEdit = (flag: boolean) => {
  allow.value = flag;
};
defineExpose({
  allowEdit
});
</script>
