﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddInvoiceAmountNoTax : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "InvoiceAmountNoTax",
                table: "InvoiceCredit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TaxAmount",
                table: "InvoiceCredit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "InvoiceAmountNoTax",
                table: "Invoice",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TaxAmount",
                table: "Invoice",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvoiceAmountNoTax",
                table: "InvoiceCredit");

            migrationBuilder.DropColumn(
                name: "TaxAmount",
                table: "InvoiceCredit");

            migrationBuilder.DropColumn(
                name: "InvoiceAmountNoTax",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "TaxAmount",
                table: "Invoice");
        }
    }
}
