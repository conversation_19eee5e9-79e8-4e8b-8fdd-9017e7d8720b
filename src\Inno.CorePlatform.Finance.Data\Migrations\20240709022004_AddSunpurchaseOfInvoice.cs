﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddSunpurchaseOfInvoice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Classify",
                table: "TempStoreTokKngdeeLog",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PreRequestBody",
                table: "TempStoreTokKngdeeLog",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "SunPurchaseStatus",
                table: "Invoice",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Classify",
                table: "TempStoreTokKngdeeLog");

            migrationBuilder.DropColumn(
                name: "PreRequestBody",
                table: "TempStoreTokKngdeeLog");

            migrationBuilder.DropColumn(
                name: "SunPurchaseStatus",
                table: "Invoice");
        }
    }
}
