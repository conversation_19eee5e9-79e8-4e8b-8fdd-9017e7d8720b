using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices
{
    /// <summary>
    /// 自动返利计提服务
    /// </summary>
    public class AutoRebateProvisionService : IAutoRebateProvisionService
    {
        private readonly ILogger<AutoRebateProvisionService> _logger;
        private readonly IApplyBFFService _applyBFFService;
        private readonly IRebateProvisionQueryService _rebateProvisionQueryService;
        private readonly IRebateProvisionItemRepository _rebateProvisionItemRepository;

        public AutoRebateProvisionService(
            ILogger<AutoRebateProvisionService> logger,
            IApplyBFFService applyBFFService,
            IRebateProvisionQueryService rebateProvisionQueryService,
            IRebateProvisionItemRepository rebateProvisionItemRepository)
        {
            _logger = logger;
            _applyBFFService = applyBFFService;
            _rebateProvisionQueryService = rebateProvisionQueryService;
            _rebateProvisionItemRepository = rebateProvisionItemRepository;
        }

        /// <summary>
        /// 处理创建返利计提事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, List<Guid> ProcessedCompanyIds)> ProcessCreateRebateProvisionEventAsync(CreateRebateProvisionEventDto eventDto)
        {
            try
            {
                _logger.LogInformation("开始处理创建返利计提事件 - 系统月度: {SysMonth}, 计提类型: {ProvisionType}", 
                    eventDto.SysMonth, eventDto.ProvisionType);

                // 创建公司返利计提记录并获取创建的记录信息
                var (success, message, createdCount) = await CreateCompanyRebateProvisionRecordsAsync(eventDto.SysMonth, eventDto.ProvisionType);
                if (!success)
                {
                    return (false, message, new List<Guid>());
                }

                _logger.LogInformation("创建返利计提事件处理完成 - 创建记录数: {CreatedCount}", createdCount);

                // 注意：这里返回空的公司ID列表，因为我们创建的是返利计提单而不是按公司分组
                // 如果需要返回具体的公司ID，需要修改CreateCompanyRebateProvisionRecordsAsync方法的返回值
                return (true, $"成功创建 {createdCount} 个返利计提记录", new List<Guid>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理创建返利计提事件失败 - 系统月度: {SysMonth}", eventDto.SysMonth);
                return (false, $"处理失败: {ex.Message}", new List<Guid>());
            }
        }

        /// <summary>
        /// 创建公司返利计提记录（公共方法）
        /// </summary>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="provisionType">计提类型</param>
        /// <returns>成功标志、消息、创建的返利计提记录数量</returns>
        public async Task<(bool Success, string Message, int CreatedCount)> CreateCompanyRebateProvisionRecordsAsync(string sysMonth, ProvisionTypeEnum provisionType)
        {
            try
            {
                _logger.LogInformation("开始创建公司返利计提记录 - 系统月度: {SysMonth}, 计提类型: {ProvisionType}",
                    sysMonth, provisionType);

                // 获取符合条件的公司列表（使用公共方法）
                var eligibleCompanies = await GetQualifiedCompaniesAsync(sysMonth, true);

                if (eligibleCompanies.Count == 0)
                {
                    var message = $"未找到符合条件的公司，系统月度: {sysMonth}";
                    _logger.LogWarning("未找到符合条件的公司 - 系统月度: {SysMonth}", sysMonth);
                    return (false, message, 0);
                }

                _logger.LogInformation("找到 {Count} 个符合条件的公司", eligibleCompanies.Count);

                // 为每个公司创建返利计提记录
                int totalCreatedCount = 0;
                var failedCompanies = new List<string>();

                foreach (var company in eligibleCompanies)
                {
                    try
                    {
                        // 检查是否已存在该公司该月度的返利计提记录
                        var existingRecords = await CheckExistingRebateProvisionAsync(Guid.Parse(company.Id), sysMonth, provisionType);
                        if (existingRecords)
                        {
                            _logger.LogInformation("公司 {CompanyName} 已存在该月度的返利计提记录，跳过创建", company.Name);
                            continue;
                        }

                        // 创建返利计提记录
                        var createInput = new RebateProvisionItemCreateInput
                        {
                            CompanyId = Guid.Parse(company.Id),
                            CompanyName = company.Name,
                            SysMonth = sysMonth,
                            ProvisionType = provisionType,
                            CurrentUser = "System", // 系统自动创建
                            UserName = "System",
                            // 与 StartInventory 保持一致，使用空值或默认值
                            BusinessDeptId = "", // 与 StartInventory 保持一致
                            BusinessDeptFullName = "",
                            BusinessDeptFullPath = "",
                            BusinessDeptShortName = ""
                        };

                        var createdCount = await _rebateProvisionQueryService.Create(createInput);
                        if (createdCount > 0)
                        {
                            totalCreatedCount += createdCount;
                            _logger.LogInformation("成功为公司 {CompanyName} 创建返利计提记录", company.Name);
                        }
                        else
                        {
                            failedCompanies.Add(company.Name);
                            _logger.LogWarning("为公司 {CompanyName} 创建返利计提记录失败", company.Name);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCompanies.Add(company.Name);
                        _logger.LogError(ex, "为公司 {CompanyName} 创建返利计提记录时发生异常", company.Name);
                    }
                }

                var resultMessage = $"成功创建 {totalCreatedCount} 个返利计提记录";
                if (failedCompanies.Any())
                {
                    resultMessage += $"，失败 {failedCompanies.Count} 个公司: {string.Join(", ", failedCompanies)}";
                }

                _logger.LogInformation("创建公司返利计提记录完成 - {ResultMessage}", resultMessage);
                return (true, resultMessage, totalCreatedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建公司返利计提记录失败 - 系统月度: {SysMonth}", sysMonth);
                return (false, $"创建失败: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// 获取符合条件的公司列表（公共方法）
        /// </summary>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="requireActiveStatus">是否要求公司状态为激活状态（Status == 1）</param>
        /// <returns>符合条件的公司列表</returns>
        private async Task<List<CompanyInfoOutput>> GetQualifiedCompaniesAsync(string sysMonth, bool requireActiveStatus = true)
        {
            try
            {
                _logger.LogInformation("获取符合条件的公司列表 - 系统月度: {SysMonth}, 要求激活状态: {RequireActiveStatus}",
                    sysMonth, requireActiveStatus);

                // 获取所有公司信息
                var companyList = await _applyBFFService.GetCompanyInfosAsync(new BaseEnableInput());

                // 应用过滤条件
                var eligibleCompanies = companyList.Where(c => c.SysMonth == sysMonth);

                if (requireActiveStatus)
                {
                    eligibleCompanies = eligibleCompanies.Where(c => c.Status == 1);
                }

                var result = eligibleCompanies.ToList();

                _logger.LogInformation("找到 {Count} 个符合条件的公司 - 系统月度: {SysMonth}, 要求激活状态: {RequireActiveStatus}",
                    result.Count, sysMonth, requireActiveStatus);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取符合条件的公司列表时发生异常 - 系统月度: {SysMonth}", sysMonth);
                return new List<CompanyInfoOutput>();
            }
        }

        /// <summary>
        /// 检查是否已存在返利计提记录（直接调用仓储方法，不加权限控制）
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="provisionType">计提类型</param>
        /// <returns>是否存在</returns>
        private async Task<bool> CheckExistingRebateProvisionAsync(Guid companyId, string sysMonth, ProvisionTypeEnum provisionType)
        {
            try
            {
                _logger.LogInformation("开始检查返利计提记录是否存在 - 公司ID: {CompanyId}, 系统月度: {SysMonth}, 计提类型: {ProvisionType}",
                    companyId, sysMonth, provisionType);

                // 解析系统月度，构建查询时间范围
                var sysDate = DateTime.Parse(sysMonth + "-01");
                var startDate = sysDate;
                var endDate = sysDate.AddMonths(1).AddDays(-1);

                // 直接从仓储查询返利计提记录，避免权限策略
                var existingRecords = await _rebateProvisionItemRepository.GetByCompanyIdAndDateRangeAsync(companyId, startDate, endDate);

                // 检查是否存在相同计提类型的记录
                var hasExisting = existingRecords.Any(x => x.ProvisionType == provisionType);

                _logger.LogInformation("返利计提记录检查完成 - 公司ID: {CompanyId}, 系统月度: {SysMonth}, 计提类型: {ProvisionType}, 是否存在: {HasExisting}",
                    companyId, sysMonth, provisionType, hasExisting);

                return hasExisting;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查返利计提记录是否存在时发生异常 - 公司ID: {CompanyId}, 系统月度: {SysMonth}, 计提类型: {ProvisionType}",
                    companyId, sysMonth, provisionType);
                return false; // 发生异常时返回false，允许创建
            }
        }
    }
}
