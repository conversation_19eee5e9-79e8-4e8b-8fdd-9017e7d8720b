import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, provide, reactive, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { GetDetailsInfo, GetDebtByCreditIds, create, submit, getItemById, GetProductDetails, DeleteAdvancePaymentDebtDetails, GetDebtDetails, ReSetDate} from '../apis/api';
import { id, th } from 'element-plus/es/locale';
import { queryCheckedByDept, GetProjectInfoByCompanyId } from '@/api/bdsData';
export const CONST_ADVANCECAPITALAPPLY_INJECTIONKEY = Symbol();
const router = useRouter();
export interface IProjectListItem {
  code: string;
  id: string;
  name: string;
  type: string;
  subType: string;
  ruleConfigList: Array<{
    company: Array<{ id: string; name: string; nameCode: string }>;
    producer: <PERSON>rra<PERSON><{ id: string; name: string }>;
    service: Array<{ id: string; name: string }>;
    agent: Array<{ id: string; name: string }>;
    firstBusinessAttr: { level: number; name: string };
  }>;
  authorizeList: Array<{
    id: string;
    name: string;
  }>;
  // 项目的事业部门
  businessDept?: IBusinessDept;
}
export interface IIdNameListItem {
  id: string;
  name: string;
}
// 公司
export interface ICompany {
  id: string;
  name: string;
  extraInfo: any;
}
// 核算部门
export interface IBusinessDept {
  id: string;
  name: string;
  deptName: string;
  deptShortName?: string;
  deptId: string;
  orignValue?: Array<string>;
  paths?: Array<string>;
}
// 仓库
export interface IStoreHouse {
  id: string;
  name: string;
  address: string;
  storeHouseType: string;
  dockingSystem: string;
  displayHouseTag: string;
}
//
export class AdvanceCapitalApplyVModel {
  constructor() {
    this.dataSource = {
      basic: {
        projects: [],
        companys: [],
        businessDepts: [],
        agents: [],
        services: [],
        producers: [],
        productTypes: [
          { id: '1', name: '定率' },
          { id: '3', name: '定价' }
        ],
        storeHouses: []
      },
      selectCreditDetails: []
    };
    this.bindModel = {
      basic: {
        productType: { id: '1', name: '定率' },
        appId: 'fam',
        bizType: 'finance',
        disabled: false
      },
      detailModal: {
        productName: '',
        productNo: '',
        selectProducts: [],
        likeSpecification:'',
        likeModel:'',
      },
      creditList: [],
      debtByCredilist:[],
      paymentList: [],
      detaisList: [],
      selectionDetails: [],
      selectionDebtByCreditDetails: [],
      detailImportModel: {
        showDialog: false,
        importStepActive: 1,
        templateFileUrl:
          'https://static.innostic.com/template/StoreInApply/consignsale_storeinapply_detail_import_tempate.xlsx',
        uploadTempFileId: '',
        importDetailInput: {
          TempFileId: ''
        }
      },
      importResult: {
        total: 0,
        successNumber: 0,
        failNumber: 0,
        failReportFileId: ''
      }
    };
    this.controlModel = {
      isAllow: false,
      isCreate: false,
      detailIsAllow: false,
      disabled: false,
      showAppendModal: false,
      showDebtByCreditModal: false,
      appendDetailTableLoading: false,
      originDetails: [],
      saveBtnLoading: false,
      submitBtnLoading: false,
      basicForm: {},
      productPagination: {
        pageIndex: 1,
        pageSize: 20,
        total: 0
      },
      debtByCreditPagination: {
        pageIndex: 1,
        pageSize: 20,
        total: 0
      },
      isView: false
    };
    this.propsModel = {};
  }
  // 页面数据源
  dataSource: {
    // 基础信息
    basic: {
      // 项目集合
      projects: Array<IProjectListItem>;
      // 公司集合
      companys: Array<ICompany>;
      // 核算部门（事业部）集合
      businessDepts?: any;
      // 供应商集合
      agents: Array<{
        id: string;
        name: string;
        containFMRecords?: boolean;
        validInfo?: Array<string>;
      }>;
      // 业务单元集合
      services: Array<IIdNameListItem>;
      // 厂家集合
      producers: Array<IIdNameListItem>;
      // 产品类型
      productTypes: Array<IIdNameListItem>;
      // 仓库列表
      storeHouses: Array<IStoreHouse>;
      customers: Array<any>;
    };
    // 选货列表
    selectCreditDetails?: Array<any>;
    selectDebtByCreditDetails?: Array<any>;
  };
  // 绑定模型
  bindModel: {
    basic: {
      id?: string;
      name?: string;
      // 申请单号
      billCode?: string;
      // 单据日期
      billDate?: string;
      startTime?: string;
      endTime?: string;
      // 应收类型
      creditType?: any;
      day?: any;
      monthRate?: any;
      confirmPaymentDateMode?: any;
      paySupplierGoodsDate?: any;
      paySupplierGoodsDay?: any;
      appId: string;
      bizType: string;
      disabled: boolean;
      attachFileIds?: string;
      customer?: any;
      // 所选公司
      company?: ICompany;
      service?: any;
      // 所选事业部门
      businessDept?: IBusinessDept;
      businessDeptId: string;
      businessDeptFullName: string;
      businessDeptFullPath: string;
      businessDeptShortName: string;
      // 备注
      remark?: string;
      project:any;
    };
    // 明细
    creditList: Array<any>;
    debtByCredilist: Array<any>;
    paymentList: Array<any>;
    detaisList: Array<any>;
    // 添加订货弹窗
    detailModal: {
      productName: string;
      productNo: string;
      productNameId?: string;
      likeSpecification?:string;
      likeModel?:string;
      likeRegistrationNo?:string;
      selectProducts: Array<any>;
      selectDebtByCredit: Array<any>;
      billCode?: string;
      projectId?: string;
      customerId?: string;
      serviceId?: string;
      purchaseCode?: any;
      agent?: any;
      debtBillNo?: any;
    };
    selectionDetails: Array<any>;
    selectionDebtByCreditDetails: Array<any>;
    detailImportModel: {
      showDialog: boolean;
      importStepActive: number;
      templateFileUrl: string;
      uploadTempFileId: string;
      importDetailInput: object;
    };
    importResult: {
      total: number;
      successNumber: number;
      failNumber: number;
      failReportFileId: string;
    };
  };
  // 控制模型
  controlModel: {
    isAllow: boolean;
    isCreate: boolean;
    detailIsAllow: boolean;
    disabled: boolean;
    showAppendModal: boolean;
    appendDetailTableLoading: boolean;
    appendDebtByCreditLoading: boolean;
    showDebtByCreditModal: boolean;
    originDetails: Array<any>;
    saveBtnLoading: boolean;
    submitBtnLoading: boolean;
    basicForm: any;
    productPagination: {
      pageIndex: number;
      pageSize: number;
      total: number;
    };
    debtByCreditPagination: {
      pageIndex: number;
      pageSize: number;
      total: number;
    };
    pageType: string;
    isView: boolean;
  };
  // 参数模型
  propsModel: {
    id?: string;
    projectId?: string;
  };
  private getFirstCreateData(): any {
    console.log(this.bindModel.basic.company,'==============2222222222')
    return {
      id: this.bindModel.basic.id || null,
      name: this.bindModel.basic.name,
      billCode:this.bindModel.basic.billCode,
      billDate: this.bindModel.basic.billDate,
      attachFileIds: this.bindModel.basic.attachFileIds,
      businessDeptId: this.bindModel.basic.businessDeptId,
      businessDeptFullName: this.bindModel.basic.businessDeptFullName,
      businessDeptFullPath: this.bindModel.basic.businessDeptFullPath,
      businessDeptShortName: this.bindModel.basic.businessDeptShortName,
      companyId: this.bindModel.basic.company?.id,
      companyName: this.bindModel.basic.company?.name,
      nameCode: this.bindModel.basic.company?.extraInfo.nameCode,
      projectCode: this.bindModel.basic.project?.code,
      projectId: this.bindModel.basic.project?.id,
      projectName: this.bindModel.basic.project?.name,
      customerId: this.bindModel.basic.customer?.id,
      customerName: this.bindModel.basic.customer?.name,
      serviceId: this.bindModel.basic.service?.businessUnitID,
      serviceName: this.bindModel.basic.service?.businessUnitName,
      startTime: this.bindModel.basic.startTime,
      endTime: this.bindModel.basic.endTime,
      day: this.bindModel.basic.day,
      monthRate: this.bindModel.basic.monthRate,
      confirmPaymentDateMode: this.bindModel.basic.confirmPaymentDateMode,
      paySupplierGoodsDay: this.bindModel.basic.paySupplierGoodsDay,
      paySupplierGoodsDate: this.bindModel.basic.paySupplierGoodsDate,
      remark: this.bindModel.basic.remark,
      advancePaymentDebtDetails: this.bindModel.paymentList
    };
  };
 async openAppendModal() {
  let validateResult = true;
  await this.controlModel.basicForm.validate((valid, fields) => {
    if (!valid) {
      validateResult = false;
      const scrollToFieldName = Object.keys(fields)[0];
      this.controlModel.basicForm.scrollToField(scrollToFieldName); // 滚动到验证错误的第一个字段
    }
  });
  if (!validateResult) {
    return;
  }
  this.controlModel.originDetails = [];
    this.controlModel.showAppendModal = true;
    this.queryProduct();
  };
  queryProduct() {
    this.controlModel.appendDetailTableLoading = true;
    let postData = {
      companyId:this.bindModel.basic.company?.id, // 公司id
      serviceId:this.bindModel.basic.service?.businessUnitID, // 业务单元id
      customerId:this.bindModel.basic.customer?.id, // 客户id
      debtBillNo:this.bindModel.detailModal.debtBillNo, // 应付单号
      purchaseCode:this.bindModel.detailModal.purchaseCode, // 采购单号
      agentId:this.bindModel.detailModal.agent?.id, // 供应商id
      returnDays:this.bindModel.basic.day,// 回款天数
      monthRate:this.bindModel.basic.monthRate,// 月利率
      confirmPaymentDateMode:this.bindModel.basic.confirmPaymentDateMode,// 确认支付日期方式
      paySupplierGoodsDay:this.bindModel.basic.paySupplierGoodsDay, // 付供应商货款天数
      paySupplierGoodsDate:this.bindModel.basic.paySupplierGoodsDate, // 付供应商货款日期
      advancePaymentItemId: this.bindModel.basic.id, // 单头id
      startTime:this.bindModel.basic.startTime, //取数开始时间
      endTime:this.bindModel.basic.endTime, //取数结束时间
      projectId:this.bindModel.detailModal.project?.id,
      businessDeptId:this.bindModel.basic.businessDeptId
      // creditType:this.bindModel.basic.creditType,
      // page: this.controlModel.productPagination.pageIndex,
      // limit: this.controlModel.productPagination.pageSize,
    };
    GetDetailsInfo(
      postData
    ).then((res: any) => {
      this.dataSource.selectCreditDetails?.splice(0);
      if (res && res.data.code === 200) {
        this.controlModel.productPagination.total = res.data.data.total;
        res.data.data.advancePaymentDebtDetails.forEach((el: any) => {
          el.classify = 1;
          this.dataSource.selectCreditDetails?.push({ ...el });
          // this.controlModel.originDetails.push({ ...el});
        });
      }
      this.controlModel.appendDetailTableLoading = false;
    });
  };
  async openDebtByCreditModal() {
    if(this.bindModel.paymentList.length < 1){
      ElMessage.warning({ showClose: true, message: '请选择应收！' });
      return;
    }
    
    this.controlModel.showDebtByCreditModal = true;
    this.queryDebtByCreditList();
  };
  queryDebtByCreditList() {
    this.controlModel.appendDebtByCreditLoading = true;
    console.log(this.bindModel.paymentList,'====================222222222223333333333333333333')
    let postData = {
      creditIds:this.bindModel.paymentList.map((el)=>{
        return el.creditId
      }),
      page: this.controlModel.debtByCreditPagination.pageIndex,
      limit: this.controlModel.debtByCreditPagination.pageSize,
    };
    GetDebtByCreditIds(
      postData
    ).then((res: any) => {
      this.dataSource.selectDebtByCreditDetails?.splice(0);
      if (res && res.data.code === 200) {
        this.controlModel.debtByCreditPagination.total = res.data.data.total;
        res.data.data.list.forEach((el: any) => {
          el.classify = 2;
          this.dataSource.selectDebtByCreditDetails?.push({ ...el });
          // this.controlModel.originDetails.push({ ...el});
        });
      }
      this.controlModel.appendDebtByCreditLoading = false;
    });
  };
    // 将选择的货品添加到明细列表
  async appendToDetails() {
      if (this.bindModel.detailModal.selectProducts.length > 0) {
        this.bindModel.detailModal.selectProducts.forEach((el) => {
          const item = this.bindModel.paymentList.find((r) => {
            return el.debtBillNo === r.debtBillNo && el.debtId === r.debtId;
          });
          if (item) {
            return false;
          } else {
            this.bindModel.paymentList.push(el);
          }
        });
        const createData = this.getFirstCreateData();
        if(createData.advancePaymentDebtDetails.length < 1){
          ElMessage.warning('请添加应收明细或者应付明细！');
          this.controlModel.saveBtnLoading = false;
          this.controlModel.submitBtnLoading = false;
          return false;
        }
        const res = await create(createData);
        if (res && res.data.code === 200) {
          this.bindModel.basic.id = res.data.data;
          let postData = {
            advancePaymentItemId: this.bindModel.basic.id
          };
          const detailRes = await getItemById(postData);
          if (detailRes && detailRes.data.code === 200) {
            this.bindModel.basic.billCode = detailRes.data.billCode;
            this.bindModel.basic.billDate = detailRes.data.billDate;
          }
          this.getDetailsList();
          this.controlModel.isAllow = true;
          ElMessage.success('添加成功！');
        }else{
          this.bindModel.paymentList = [];
          ElMessage.error(res.data.message);
        }
        
      } else {
        ElMessage.warning('请选择要添加的付款计划明细。');
      }
    }
    async getDetailsList(){
      let postData = {
        advancePaymentItemId: this.bindModel.basic.id
      };
      let res  = await GetProductDetails(postData);
      if(res && res.data.code === 200){
        this.bindModel.detaisList = res.data.data.list;
      }
    }
    async getPaymentDetailsList(){
      let postData = {
        advancePaymentItemId: this.bindModel.basic.id
      };
      let res  = await GetDebtDetails(postData);
      if(res && res.data.code === 200){
        this.bindModel.paymentList = res.data.data.list;
      }
    }
    
    async deleteAdvancePaymenDetail(data:any){
      let res = await DeleteAdvancePaymentDebtDetails(data);
      if(res  && res.data.code === 200){
        this.getPaymentDetailsList();
        this.getDetailsList();
        ElMessage.success('删除成功！');
      }
    }
    appendDebtByCredit() {
      if (this.bindModel.detailModal.selectDebtByCredit.length > 0) {
        this.bindModel.detailModal.selectDebtByCredit.forEach((el) => {
          const item = this.bindModel.detaisList.find((r) => {
            return el.id === r.id;
          });
          if (item) {
            return false;
          } else {
            el.badAmount = el.leftAmount
            this.bindModel.detaisList.push(el);
          }
        });
        this.controlModel.isAllow = true;
        // this.controlModel.detailIsAllow = true;
        ElMessage.success('添加成功！');
      } else {
        ElMessage.warning('请选择要添加的应收。');
      }
    }
  async save() {
    this.controlModel.saveBtnLoading = true;
    this.controlModel.submitBtnLoading = true;
    let result: boolean = false;
    const createData = this.getFirstCreateData();
    if(createData.advancePaymentDebtDetails.length < 1){
      ElMessage.warning('请添加应收明细或者应付明细！');
      this.controlModel.saveBtnLoading = false;
      this.controlModel.submitBtnLoading = false;
      return false;
    }
    const res = await create(createData);
    if (res && res.data.code === 200) {
      this.bindModel.basic.id = res.data.data;
      if (this.bindModel.basic.id) {
        let postData = {
          advancePaymentItemId: this.bindModel.basic.id
        };
        const detailRes = await getItemById(postData);
        if (detailRes && detailRes.data.code === 200) {
          this.bindModel.basic.billCode = detailRes.data.billCode;
          this.bindModel.basic.billDate = detailRes.data.billDate;
        }else{
          this.controlModel.saveBtnLoading = false;
          this.controlModel.submitBtnLoading = false;
        }
      }
      this.controlModel.saveBtnLoading = false;
      this.controlModel.submitBtnLoading = false;
      result = true;
      ElMessage.success('保存成功！');
    } else {
      result = false;
      this.controlModel.submitBtnLoading = false;
      this.controlModel.saveBtnLoading = false;
      ElMessage.error(res.data.message);
    }
    return result;
  }
  async saveDetails(){
    const createData = this.getFirstCreateData();
    const res = await create(createData);
    if(res && res.data.code === 200){
      this.bindModel.basic.id = res.data.data;
      let postData = {
          advancePaymentItemId: this.bindModel.basic.id
        };
        const detailRes = await getItemById(postData);
        if (detailRes && detailRes.data.code === 200) {
          const detail: any = detailRes.data.data;
          this.bindModel.paymentList = detail.advancePaymentDebtDetails;
          this.bindModel.detaisList = detail.advancePaymentProductDetails;
        }
    }else {
      ElMessage.error(res.data.message);
    }
  }
  async AllocateArrivalGoods (){
    const createData = this.getFirstCreateData();
    const res = await create(createData);
    if (res && res.data.code === 200) {
      this.bindModel.basic.id = res.data.data;
      if (this.bindModel.basic.id) {
        // let postData = {
        //   advancePaymentItemId: this.bindModel.basic.id
        // };
        // const detailRes = await getItemById(postData);
        // if (detailRes && detailRes.data.code === 200) {
        //   this.bindModel.basic.billCode = detailRes.data.billCode;
        //   this.bindModel.basic.billDate = detailRes.data.billDate;
        // }
        this.getPaymentDetailsList();
        this.getDetailsList();
      }
      ElMessage.success('分摊成功！');
    } else {
      ElMessage.error(res.data.message);
    }
  }
  async setPaymentDate(){
    const createData = this.getFirstCreateData();
    let res = await ReSetDate(createData);
    if(res && res.data.code === 200){
      this.getPaymentDetailsList();
      this.getDetailsList();
      ElMessage.success('设置成功！');
    }else{
      ElMessage.error(res.data.message);
    }
  }
  async submit() {
    this.controlModel.saveBtnLoading = true;
    this.controlModel.submitBtnLoading = true;
    let result: boolean = false;
    const createData = this.getFirstCreateData();
    if(createData.advancePaymentDebtDetails.length < 1){
      ElMessage.warning('请添加付款计划明细！');
      this.controlModel.saveBtnLoading = false;
      this.controlModel.submitBtnLoading = false;
      return false;
    }
    const res = await create(createData);
    if (res && res.data.code === 200) {
      this.bindModel.basic.id = res.data.data;
      let postData = {
        id: res.data.data
      };
    let resp = await submit(postData);
      if (resp && resp.data.code === 200) {
        result = true;
        this.controlModel.submitBtnLoading = false;
        this.controlModel.saveBtnLoading = false;
        ElMessage.success('提交成功！');
      }else {
        result = false;
        this.controlModel.submitBtnLoading = false;
        this.controlModel.saveBtnLoading = false;
        ElMessage.error(resp.data.message);
      }
    }else {
      result = false;
      this.controlModel.submitBtnLoading = false;
      this.controlModel.saveBtnLoading = false;
      ElMessage.error(res.data.message);
    }
    return result;
  }
   // 删除应收
   removeCredit() {
    if (this.bindModel.selectionDetails.length > 0) {
      ElMessageBox.confirm('确认删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        this.bindModel.selectionDetails.forEach((val, index) => {
          this.bindModel.paymentList?.forEach((v, i) => {
            if (val.id === v.id) {
              this.bindModel.paymentList.splice(i, 1);
            }
          });
          this.bindModel.detaisList.forEach((item,index) => {
            if (val.billCode === item.creditBillCode) {
              this.bindModel.detaisList.splice(index, 1);
            }
          });
        });
        if (this.bindModel.paymentList.length <= 0) {
          this.controlModel.isAllow = false;
            // this.controlModel.detailIsAllow = false;
        }
      });
    } else {
      ElMessage.warning('请先选择要移除的数据。');
    }
  }
  // 删除应付
  removeDebtByCredit() {
    if (this.bindModel.selectionDebtByCreditDetails.length > 0) {
      ElMessageBox.confirm('确认删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        this.bindModel.selectionDebtByCreditDetails.forEach((val, index) => {
          this.bindModel.detaisList?.forEach((v, i) => {
            if (val.id === v.id) {
              this.bindModel.detaisList.splice(i, 1);
            }
          });
        });
        if (this.bindModel.detaisList.length <= 0) {
          this.controlModel.isAllow = false;
            // this.controlModel.detailIsAllow = false;
        }
      });
    } else {
      ElMessage.warning('请先选择要移除的数据。');
    }
  };
  // 加载详情数据
  async loadDetail() {
    if (this.propsModel.id) {
      let postData = {
        advancePaymentItemId: this.propsModel.id
      };
      const res = await getItemById(postData);
      console.log('loadDetail-res', res);
      if (res.status === 200) {
        const detail: any = res.data.data;
        // 主键id
        this.bindModel.basic.id = this.propsModel.id;
        this.bindModel.basic.billCode = detail.billCode;
        this.bindModel.basic.billDate = detail.billDate;
        this.bindModel.basic.name = detail.name;
        this.bindModel.basic.startTime = detail.startTime;
        this.bindModel.basic.endTime = detail.endTime;
        this.bindModel.basic.day = detail.day;
        this.bindModel.basic.attachFileIds = detail.attachFileIds;
        this.bindModel.basic.monthRate = detail.monthRate;
        this.bindModel.basic.confirmPaymentDateMode = detail.confirmPaymentDateMode;
        this.bindModel.basic.paySupplierGoodsDay = detail.paySupplierGoodsDay;
        this.bindModel.basic.paySupplierGoodsDate = detail.paySupplierGoodsDate;
        this.bindModel.basic.businessDeptId = detail.businessDeptId;
        this.bindModel.basic.businessDeptFullPath = detail.businessDeptFullPath;
        this.bindModel.basic.businessDeptShortName = detail.businessDeptShortName;
        if(detail.businessDeptId){
          await queryCheckedByDept(detail.businessDeptId).then((res) => {
            this.dataSource.basic.companys = res.data.data;
          });
        }
        this.bindModel.basic.company = {
          id:detail.companyId.toUpperCase(),
          name:detail.companyName,
          extraInfo:{
            nameCode:detail.nameCode
          }
        };
        if(detail.companyId){
          await GetProjectInfoByCompanyId(detail.companyId).then((res:any) => {
            this.dataSource.basic.projects = res.data.data;
          });
        }
        this.bindModel.basic.project = {
          id:detail.projectId,
          name:detail.projectName,
          code:detail.projectCode
        }
        this.bindModel.basic.customer = {
          id:detail.customerId,
          name:detail.customerName,
        };
        this.bindModel.basic.service = {
          businessUnitID:detail.serviceId,
          businessUnitName:detail.serviceName,
        };
        this.bindModel.basic.remark = detail.remark;
        this.bindModel.paymentList = detail.advancePaymentDebtDetails;
        this.bindModel.detaisList = detail.advancePaymentProductDetails;
      } else {
        ElMessage.error({ showClose: true, message: res.data.message });
      }
    }
  }
}
