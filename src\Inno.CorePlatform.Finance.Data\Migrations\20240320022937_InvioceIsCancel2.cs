﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class InvioceIsCancel2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "IsCancel",
                table: "Invoice",
                type: "bit",
                nullable: true,
                comment: "是否取消",
                oldClrType: typeof(bool),
                oldType: "bit",
                oldComment: "是否取消");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "IsCancel",
                table: "Invoice",
                type: "bit",
                nullable: false,
                defaultValue: false,
                comment: "是否取消",
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true,
                oldComment: "是否取消");
        }
    }
}
