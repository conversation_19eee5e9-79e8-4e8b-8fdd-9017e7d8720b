﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.ReconciliationItem
{
    public class ReconciliationLetterItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    { 
        /// <summary>
      /// 单号
      /// </summary> 
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 欠款金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ArrearsAmount { get; set; }

        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime Deadline { get; set; }

        /// <summary>
        /// 对账涵模板
        /// </summary>
        public ReconciliationLetterEnum ReconciliationLetterTemplate { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary> 
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 询证函确认附件Ids
        /// </summary> 
        public string? ConfirmAttachFileIds { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }

        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
    }
}
