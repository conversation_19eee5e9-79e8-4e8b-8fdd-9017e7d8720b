﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddPreCustomizeInvoiceItemPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PreCustomizeInvoiceItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProjectCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "项目单号"),
                    ProjectName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "项目名称"),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "项目Id"),
                    BusinessDeptFullPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptFullName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PreCustomizeInvoiceItem", x => x.Id);
                },
                comment: "预开票表");

            migrationBuilder.CreateTable(
                name: "PreCustomizeInvoiceDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PreCustomizeInvoiceItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PackUnit = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Specification = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Value = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TaxTypeNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RelateCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OrderNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ProductNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Tag = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PreCustomizeInvoiceDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PreCustomizeInvoiceDetail_PreCustomizeInvoiceItem_PreCustomizeInvoiceItemId",
                        column: x => x.PreCustomizeInvoiceItemId,
                        principalTable: "PreCustomizeInvoiceItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "预开票明细");

            migrationBuilder.CreateIndex(
                name: "IX_PreCustomizeInvoiceDetail_PreCustomizeInvoiceItemId",
                table: "PreCustomizeInvoiceDetail",
                column: "PreCustomizeInvoiceItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PreCustomizeInvoiceDetail");

            migrationBuilder.DropTable(
                name: "PreCustomizeInvoiceItem");
        }
    }
}
