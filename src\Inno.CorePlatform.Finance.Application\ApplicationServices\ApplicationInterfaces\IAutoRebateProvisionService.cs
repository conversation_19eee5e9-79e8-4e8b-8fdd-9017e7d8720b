using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces
{
    /// <summary>
    /// 自动返利计提服务接口
    /// </summary>
    public interface IAutoRebateProvisionService
    {
        /// <summary>
        /// 处理创建返利计提事件
        /// </summary>
        /// <param name="eventDto">创建返利计提事件DTO</param>
        /// <returns>成功标志、消息、处理的公司ID列表</returns>
        Task<(bool Success, string Message, List<Guid> ProcessedCompanyIds)> ProcessCreateRebateProvisionEventAsync(CreateRebateProvisionEventDto eventDto);

        /// <summary>
        /// 创建公司返利计提记录（公共方法）
        /// </summary>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="provisionType">计提类型</param>
        /// <returns>成功标志、消息、创建的返利计提记录数量</returns>
        Task<(bool Success, string Message, int CreatedCount)> CreateCompanyRebateProvisionRecordsAsync(string sysMonth, ProvisionTypeEnum provisionType);
    }
}
