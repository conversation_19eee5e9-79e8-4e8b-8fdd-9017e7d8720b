﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class _20250414updateCloumnLength : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Remark",
                table: "LossRecognitionItem",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "OARequestId",
                table: "LossRecognitionItem",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "NameCode",
                table: "LossRecognitionItem",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "LossRecognitionItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "LossRecognitionItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptId",
                table: "LossRecognitionItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptFullPath",
                table: "LossRecognitionItem",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptFullName",
                table: "LossRecognitionItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "LossRecognitionItem",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                comment: "单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldComment: "单号");

            migrationBuilder.AlterColumn<string>(
                name: "AttachFileIds",
                table: "LossRecognitionItem",
                type: "nvarchar(400)",
                maxLength: 400,
                nullable: true,
                comment: "附件Ids",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "附件Ids");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "LossRecognitionDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalName",
                table: "LossRecognitionDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                comment: "终端医院",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "终端医院");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "LossRecognitionDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "终端医院Id");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "LossRecognitionDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                comment: "明细单据号（应收/应付单号）",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldComment: "明细单据号（应收日期、应付日期）");

            migrationBuilder.AlterColumn<string>(
                name: "AgentName",
                table: "LossRecognitionDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Remark",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "OARequestId",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "NameCode",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptId",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptFullPath",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BusinessDeptFullName",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "LossRecognitionItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                comment: "单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldComment: "单号");

            migrationBuilder.AlterColumn<string>(
                name: "AttachFileIds",
                table: "LossRecognitionItem",
                type: "nvarchar(max)",
                nullable: true,
                comment: "附件Ids",
                oldClrType: typeof(string),
                oldType: "nvarchar(400)",
                oldMaxLength: 400,
                oldNullable: true,
                oldComment: "附件Ids");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "终端医院");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true,
                oldComment: "终端医院Id");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: false,
                comment: "明细单据号（应收日期、应付日期）",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldComment: "明细单据号（应收/应付单号）");

            migrationBuilder.AlterColumn<string>(
                name: "AgentName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);
        }
    }
}
