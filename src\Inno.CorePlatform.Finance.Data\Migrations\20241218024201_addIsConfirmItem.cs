﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addIsConfirmItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>onfirm",
                table: "CreditRecordDetail");

            migrationBuilder.AddColumn<int>(
                name: "IsConfirm",
                table: "CreditRecordItem",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsConfirm",
                table: "CreditRecordItem");

            migrationBuilder.AddColumn<int>(
                name: "IsConfirm",
                table: "CreditRecordDetail",
                type: "int",
                nullable: true);
        }
    }
}
