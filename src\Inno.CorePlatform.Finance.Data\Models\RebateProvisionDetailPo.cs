﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 返利计提明细表
    /// </summary>
    [Table("RebateProvisionDetail")]
    public class RebateProvisionDetailPo : BasePo
    {
        /// <summary>
        /// 返利计提
        /// </summary>
        [Comment("返利计提")]
        public Guid? RebateProvisionItemId { get; set; }

        [ForeignKey("RebateProvisionItemId")]
        public virtual RebateProvisionItemPo? RebateProvisionItem { get; set; }
        /// <summary>
        /// 确认函日期
        /// </summary>
        [Column(TypeName = "Date")]
        [Comment("确认函日期")]
        public DateTime? ConfirmationDate { get; set; }

        /// <summary>
        /// 金额
        /// </summary>

        [Column(TypeName = "decimal(18,2)")]
        [Comment("金额")]
        public decimal? ConfirmTaxAmount { get; set; }

        /// <summary>
        /// 实际收到日期
        /// </summary>
        [Column(TypeName = "Date")]
        [Comment("实际收到日期")]
        public DateTime? ActualarrivalDate { get; set; }

        /// <summary>
        /// 政策期限
        /// </summary>
        [Comment("政策期限")]
        public string? Policydeadline { get; set; }

        /// <summary>
        /// 返利类型 A:平移返利, B:指标返利, C:补偿返利	
        /// </summary>
        [Comment("返利类型 A:平移返利, B:指标返利, C:补偿返利\t")]
        public RebateTypeOfKdEnum? RebateType { get; set; }

        /// <summary>
        /// 发票日期/优惠卷日期
        /// </summary> 
        [Column(TypeName = "Date")]
        [Comment("发票日期/优惠卷日期")]
        public DateTime? CouponDate { get; set; }

        /// <summary>
        /// 返利金额
        /// </summary>
        [Comment("返利金额")]

        [Column(TypeName = "decimal(18,2)")]
        public decimal? RebateAmount { get; set; }

        /// <summary>
        /// 返利不含税金额
        /// </summary>
        [Comment("返利不含税金额")]

        [Column(TypeName = "decimal(18,2)")]
        public decimal? RebateTaxAmount { get; set; }

        /// <summary>
        /// 厂家红票发票号
        /// </summary>
        [Comment("厂家红票发票号")]
        public string? Redinvoice { get; set; }

        /// <summary>
        /// 返利期间(摘要)
        /// </summary>
        [Comment("返利期间(摘要)")]
        public string? PeriodSummary { get; set; }

        /// <summary>
        /// 下家对应金额
        /// </summary>
        [Comment("下家对应金额")]

        [Column(TypeName = "decimal(18,2)")]
        public decimal? NextAmount { get; set; }

        /// <summary>
        /// 下家不含税金额
        /// </summary>
        [Comment("下家不含税金额")]

        [Column(TypeName = "decimal(18,2)")]
        public decimal? NextTaxAmount { get; set; }

        /// <summary>
        /// 下家返利方式 A:发票, B:优惠劵
        /// </summary>
        [Comment("下家返利方式 A:发票, B:优惠劵")]
        public NextRebateMethodEnum? NextRebateMethod { get; set; }

        /// <summary>
        /// 下家返利发票号/优惠券(针对已结算的)
        /// </summary>
        [Comment("下家返利发票号/优惠券(针对已结算的)")]
        public string? NextInvoiceOrCoupon { get; set; }
         
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }


        [Comment("项目id")]
        public Guid ProjectId { get; set; }

        [Comment("项目名称")]
        public string ProjectName { get; set; } = "";

        [Comment("项目单号")]
        public string ProjectCode { get; set; } = "";

        /// <summary>
        /// 客户Id
        /// </summary>
        [Comment("客户Id")]
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [Comment("客户")]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        [Comment("供应商Id")]
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary> 
        [Comment("供应商")]
        public string? AgentName { get; set; }
    }
}
