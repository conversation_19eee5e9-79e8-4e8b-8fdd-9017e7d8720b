﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 提前付款垫资应收明细
    /// </summary>    
    [Table("AdvancePaymentCreditDetail")]
    public class AdvancePaymentCreditDetailPo : BasePo
    {

        /// <summary>
        /// 提前付款垫资Id
        /// </summary>
        public Guid AdvancePaymentItemId { get; set; }

        /// <summary>
        /// 提前付款垫资
        /// </summary>
        [ForeignKey("AdvancePaymentItemId")]
        public virtual AdvancePaymentItemPo AdvancePaymentItem { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        [MaxLength(200)]
        public string CreditBillNo { get; set; }
        /// <summary>
        /// 发票号
        /// </summary> 
        [Comment("发票号")]
        [MaxLength(200)]
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 回款天数 
        /// </summary>
        public int? ReturnDays { get; set; }

        /// <summary>
        /// 预计回款天数
        /// </summary>
        public int? EstimateReturnDays { get; set; }
        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? EstimateReturnDate { get; set; }
        /// <summary>
        /// 实际支付上游日期
        /// </summary>
        public DateTime? ActualPaymentDate { get; set; }

        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? AdvancePaymentDays { get; set; }

        /// <summary>
        /// 月利率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthRate { get; set; }

        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? FinanceDiscount { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 垫资应收金额 
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AdvanceCreditAmount { get; set; }

        /// <summary>
        /// 含税垫资毛利率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AdvanceCreditTaxAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
