using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces
{
    /// <summary>
    /// 自动盘点服务接口
    /// </summary>
    public interface IAutoInventoryService
    {
        /// <summary>
        /// 处理开启盘点事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, List<Guid> ProcessedCompanyIds)> ProcessStartInventoryEventAsync(StartInventoryEventDto eventDto);

        /// <summary>
        /// 处理盘点记录生成事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        Task<(bool Success, string Message)> ProcessInventoryGenerateEventAsync(InventoryGenerateEventDto eventDto);

        /// <summary>
        /// 处理单个公司的盘点生成
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<(bool Success, string Message)> ProcessCompanyInventoryGenerationAsync(Guid companyId, string sysMonth);

        /// <summary>
        /// 处理盘点完成检查事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, List<Guid> CompletedCompanyIds)> ProcessCompleteInventoryCheckEventAsync(CompleteInventoryCheckEventDto eventDto);

        /// <summary>
        /// 检查盘点完成情况
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<(bool IsCompleted, string Message, List<string> MissingFields)> CheckInventoryCompletionAsync(Guid companyId, string sysMonth);

        /// <summary>
        /// 创建盘点记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, List<Guid> CreatedRecordIds)> CreateInventoryRecordsAsync(CreateInventoryRecordRequestDto request);

        /// <summary>
        /// 批量处理盘点记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, int ProcessedCount)> BatchProcessInventoryRecordsAsync(BatchProcessInventoryRecordRequestDto request);

        /// <summary>
        /// 获取盘点进度
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<InventoryProgressResponseDto> GetInventoryProgressAsync(Guid companyId, string sysMonth);



        /// <summary>
        /// 处理财务内部盘点（直接调用模式）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <param name="companyCode"></param>
        /// <param name="companyName"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, Dictionary<string, string> GeneratedCodes)> ProcessFinanceInternalInventoryAsync(
            Guid companyId, string sysMonth, string companyCode, string companyName);

        /// <summary>
        /// 处理外部能力中心盘点（事件驱动模式）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <param name="companyCode"></param>
        /// <param name="companyName"></param>
        /// <returns></returns>
        Task<(bool Success, string Message, List<InventoryActionType> TriggeredActions)> ProcessExternalInventoryAsync(
            Guid companyId, string sysMonth, string companyCode, string companyName);
    }
}
