<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;

    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>发票清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL>
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button
                slot="reference"
                v-if="
                  crud.rowData.Type === '纸质专用发票' &&
                  crud.rowData.companyName === '贵州致思蒲达企业管理有限公司'
                "
                :loading="refreshPushSPDLoading"
                type="danger"
                @click="refreshPushSPD"
              >重推SPD商务平台</el-button>
              <el-button
                v-if="
                  crud.rowData.preCustomizeInvoiceCode !== '' &&
                  crud.rowData.preCustomizeInvoiceCode !== null
                "
                slot="reference"
                type="success"
                @click="preInvoiceCreditAssociation"
              >预开票发票关联应收</el-button>
              <el-button slot="reference" :loading="exportLoading" type @click="exportattach">批量打包下载发票</el-button>
              <!-- v-auth="functionUris.export" -->
              <el-button
                slot="reference"
                :loading="downLoading"
                type="warning"
                @click="
                  downloadAsync(
                    '/api/InvoiceQuery/ExportInvoicesByCoordinate',
                    '发票清单',
                    crud.query
                  )
                "
                v-if="hasPermission(functionUris.export)"
              >导出数据</el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`全部`" name="0" lazy />
                  <el-tab-pane :label="`已作废`" name="1" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            :data="crud.data"
            stripe
            fit1
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crudSelectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column label="发票号" property="invoiceNo" fixed="left" show-overflow-tooltip sortable min-width="100">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票代码" property="invoiceCode" sortable min-width="110" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票类型" property="type" min-width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.type }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="invoiceTime" label="开票日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.invoiceTime === null
                ? ''
                : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
               <el-table-column class-name="isSum" label="价格类型" property="priceSourceStr" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.priceSourceStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="开票金额" property="invoiceAmount" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="不含税金额" property="invoiceAmountNoTax" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.invoiceAmountNoTax" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="已冲销金额" property="alreadyWriteOffAmount" width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.alreadyWriteOffAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="剩余冲销金额" property="residueWriteOffAmount" width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.residueWriteOffAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="终端医院" property="hospitalName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.hospitalName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目编码" property="projectCode" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="开票单号" property="code" min-width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="预开票单号" property="preCustomizeInvoiceCode" min-width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.preCustomizeInvoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" width="300" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="供应商" property="agentName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="厂家" property="producerName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="businessDeptFullName" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="statusStr" min-width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.statusStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="备注" property="remark" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红冲状态" property="changedStatusStr" width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.changedStatus !== 1 &&
                    scope.row.changedStatus !== 2
                      ? 'success'
                      : 'error'
                  "
                  disable-transitions
                >{{ scope.row.changedStatusStr }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="红/蓝发票号" property="blueInvoiceNo" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.blueInvoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.createdTime === null
                ? ''
                : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link
                  v-if="scope.row.code"
                  style="font-size: 12px"
                  type="primary"
                  @click="
                    downloadFileBefore(scope.row.invoiceNo,scope.row.invoiceCode)
                  "
                >查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <inno-split-pane :default-percent="50" split="vertical">
            <template #paneL="{ full, onFull }">
              <!-- 应收明细 -->
              <inno-crud-operation :crud="crud2" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
                <template #default>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
                  </el-button>
                  <!-- v-auth="functionUris.changeRelationship" -->
                  <inno-button-tooltip v-if="hasPermission(functionUris.changeRelationship)" type="danger" :disabled="crud2.selections.length === 0" @click="changeRelationship">更换应收</inno-button-tooltip>
                </template>
                <template #opts-left>
                  <div>
                    <el-tabs class="demo-tabs">
                      <el-tab-pane :label="`应收明细`" name="0" lazy />
                    </el-tabs>
                  </div>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableRef2"
                v-inno-loading="crud2.loading"
                class="auto-layout-table"
                highlight-current-row
                border
                show-summary
                :summary-method="getSummaries1"
                :data="crud2.data"
                stripe
                :row-class-name="crud2.tableRowClassName"
                @selection-change="crud2.selectionChangeHandler"
                @row-click="crud2.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column label="应收单号" property="billCode" width="200" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject3.billCode" :crud="crud2" :column="column" />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    scope.row.billDate === null
                    ? ''
                    : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="关联单号" property="relateCode" width="100" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject3.relateCode" :crud="crud2" :column="column" />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="订单号" property="orderNo" width="100" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject3.orderNo" :crud="crud2" :column="column" />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="销售子系统" property="saleSystemName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="开票金额" property="creditAmount" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.creditAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="是否确认收入" property="isSureIncomeStr" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.isSureIncomeStr }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="部门" property="businessDeptFullName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="供应商" property="agentName" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="厂家" property="producerName" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="价格类型" property="priceSourceStr" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.priceSourceStr }}</inno-button-copy>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crud2.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crud2" />
              </div>
            </template>
            <template #paneR>
              <!-- 收款关联 -->
              <inno-crud-operation :crud="crud3" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
                <template #default>
                  <!-- 按钮 -->
                </template>
                <template #opts-left>
                  <div>
                    <el-tabs class="demo-tabs">
                      <el-tab-pane :label="`收款关联`" name="0" lazy />
                    </el-tabs>
                  </div>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableRef3"
                v-inno-loading="crud3.loading"
                class="auto-layout-table"
                highlight-current-row
                border
                show-summary
                :summary-method="getSummaries1"
                :data="crud3.data"
                stripe
                :row-class-name="crud3.tableRowClassName"
                @selection-change="crud3.selectionChangeHandler"
                @row-click="crud3.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column label="收款单号" property="creditBillCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="金额" property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="日期" property="abadate" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    scope.row.abadate === null
                    ? ''
                    : dateFormat(scope.row.abadate, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="认款人" property="createdByName" width="90" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.createdByName }}</template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crud3.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crud3" />
              </div>
            </template>
          </inno-split-pane>
        </template>
      </inno-split-pane>
    </div>
    <!-- 发票附件推送邮箱弹窗 -->
    <el-dialog v-model="mailDialogVisible" title="请填写发送邮箱（每次最多支持发送1000张）" style="width: 20%">
      <el-form>
        <el-form-item label="邮箱" required>
          <el-input v-model="customerEmail" v-email-validator style="width: 26.5rem; margin-left: 1rem" clearable placeholder="请输入客户邮箱" @change="validateEmail"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="mailDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="sendLoading" @click="send">发送</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 更换应收关系组件 -->
    <ChangeRelationship
      ref="changeRelationshipRef"
      :companyId="crud.rowData.companyId"
      :customerId="crud.rowData.customerId"
      :creditAmount="crud2.rowData.value"
      :invoiceNo="crud.rowData.invoiceNo"
      :oldCreditBillCode="crud2.rowData.billCode"
      :showDialog="crDialogShow"
      @closeDialog="closeDialogCallBack"
    />
    <!-- 预开票发票关联应收组件 -->
    <el-dialog v-model="preDialogVisible" title=" 预开票发票关联应收">
      <el-form ref="preFormRef">
        <el-row :gutter="12">
          <el-col :span="5">
            <el-form-item label="发票号">
              <span>{{ preFormData.invoiceNo }}</span>
            </el-form-item>
            <el-form-item label="发票金额">
              <span>{{ preFormData.invoiceAmount }}</span>
            </el-form-item>
            <el-form-item label="已勾选应收总金额">
              <span>{{ preFormData.creditValue }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="crud-opts">
          <inno-button-tooltip type="primary" @click="addRows">新增行</inno-button-tooltip>
          <inno-button-tooltip type="danger" @click="delRows">删除行</inno-button-tooltip>
        </div>
        <div style="margin-bottom: 10px"></div>
        <el-table id="tableDetail" ref="tableRef1" class="auto-layout-table" highlight-current-row :data="preFormData.credits" border @selection-change="selectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="应收单" fixed="left" property="lineProperty">
            <template #default="scope">
              <el-select v-model="scope.row.billCode" filterable placeholder="请选择应收单" @change="billCodeSelectChange(scope.row)">
                <el-option v-for="item in creditList" :key="item.billCode" :label="item.billCode" :value="item.billCode"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="金额" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.value" disabled></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="preDialogClose">取消</el-button>
          <el-button type="primary" :loading="sendLoading" @click="association">关联</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 预览附件格式选择 -->
    <el-dialog v-model="attachmentFormatDialogVisible" width="300" title="请选择发票格式">
      <el-form ref="preFormRef">
        <el-select v-model="attachmentFormat" filterable placeholder="请选择发票格式">
          <el-option v-for="item in attachmentFormatList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <div style="margin-bottom: 10px"></div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" :loading="sendLoading" @click="downloadFile()">确定</el-button>
          <el-button @click="attachmentFormatDialogClose">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { parse } from 'qs';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ExportInvoices } from '@/api/financeapi';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import ChangeRelationship from './ChangeRelationship.vue';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
const functionUris = {
  changeRelationship:
    'metadata://fam/finance-ServiceInvoices/functions/excute-changeRelationship',
  export: 'metadata://fam/finance-ServiceInvoices/functions/excute-export'
};
//获取路由
const router = useRouter();
const route = useRoute();
const tableItem = ref<InstanceType<typeof ElTable>>();
const props = defineProps({
  __refresh: Boolean
});
//记录合计字段，取消勾选时以便于还原总合计
const checkTotal = ref(0);
const checkTota2 = ref(0);
const checkTota3 = ref(0);
// 合计金额
const totalInvoiceAmount = ref(0); //开票金额
const totalAWOAmount = ref(0); // 已冲销金额
const totalUWOAmount = ref(0); //剩余冲销金额
const crud = CRUD(
  {
    title: '发票清单',
    url: '/api/InvoiceQuery/GetInvoices',
    idField: 'id',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      searchKey: ''
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          //crud.singleSelection(crud.data[0]);
          checkTotal.value = data.totalInvoiceAmount; //开票金额
          checkTota2.value = data.totalAWOAmount; //已冲销金额
          checkTota3.value = data.totalUWOAmount; //剩余冲销金额
          totalInvoiceAmount.value = data.totalInvoiceAmount; //开票金额
          totalAWOAmount.value = data.totalAWOAmount; //已冲销金额
          totalUWOAmount.value = data.totalUWOAmount; //剩余冲销金额
          // checkTotal.value = crud.data[0].invoiceAmount;
          // checkTota2.value = crud.data[0].alreadyWriteOffAmount;
          // checkTota3.value = crud.data[0].residueWriteOffAmount;
          preFormData.invoiceNo = crud.data[0].invoiceNo;
          preFormData.invoiceAmount = crud.data[0].invoiceAmount;
          preFormData.code = crud.data[0].code;
          preFormData.blueInvoiceNo = crud.data[0].blueInvoiceNo;
          crud3.query.invoiceNo = crud.data[0].invoiceNo;
          crud3.toQuery();
        }
      }
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableItem
  }
);
  const creditSum = reactive({
    creditAmount: 0
  });
  const getDetailSumCount = (type) => {
    request({
      url: '/api/InvoiceQuery/GetInvoiceCreditsSumByInvoiceNo',
      data: {
        invoiceNo: crud.rowData.invoiceNo,
        companyId: crud.rowData.companyId,
        serviceId: crud.query.serviceId
      },
      method: 'post'
    }).then((res) => {
      creditSum.creditAmount = res.data.data.creditAmount;
    });
  };
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '应收明细',
    url: '/api/InvoiceQuery/GetInvoiceCreditsByInvoiceNo',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef2',
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        getDetailSumCount();
        //默认选中第一行
        if (crud2.data.length && crud2.data.length > 0) {
          crud2.singleSelection(crud2.data[0]);
        }
      }
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRef2
  }
);
const tableRef3 = ref<InstanceType<typeof ElTable>>();
const crud3 = CRUD(
  {
    title: '收款关联',
    url: '/api/InvoiceQuery/GetAbatementByCreditBillCode',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      invoiceNo: crud.rowData.invoiceNo
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef3',
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud3.data.length && crud3.data.length > 0) {
          crud3.singleSelection(crud3.data[0]);
        }
      }
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRef3
  }
);

onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();
  }
});
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  crud.query.billDateBeging = firstDay.getTime();
  crud.query.billDateEnd = lastDay.getTime();
  crud.toQuery();
});
const InvoiceTypelist = [
  {
    label: '电子普通发票',
    value: '电子普通发票'
  },
  {
    label: '电子专用发票',
    value: '电子专用发票'
  },
  {
    label: '纸质普通发票',
    value: '纸质普通发票'
  },
  {
    label: '纸质专用发票',
    value: '纸质专用发票'
  },
  {
    label: '增值税普通发票(卷票)',
    value: '增值税普通发票(卷票)'
  },
  {
    label: '数电票(增值税专用发票)',
    value: '数电票(增值税专用发票)'
  },
  {
    label: '数电票(普通发票)',
    value: '数电票(普通发票)'
  }
];
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId',
      props: { queryData: { functionUri: 'metadata://fam' } }
    }
  );
return [
    {
      key: 'invoiceNo',
      label: '发票号',
      show: true
    },
    {
      key: 'invoiceCode',
      label: '发票代码',
      show: true
    },
    {
      key: 'preCustomizeInvoiceCode',
      label: '预开票单号',
      show: true
    },
    {
      key: 'type',
      label: '发票类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: InvoiceTypelist,
      show: true
    },
    {
      key: 'billDateBeging',
      endDate: 'billDateEnd',
      label: '开票日期',
      type: 'daterange',
      show: true,
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ]
    },
    {
      key: 'customerId',
      label: '客户',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      placeholder: '客户搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' }  },
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`,
      valueK: 'businessUnitID',
      labelK: 'businessUnitName',
      props: { KeyWord: 'nameLike', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' }  },
      show: true
    },
    {
      key: 'code',
      label: '开票单号',
      show: true
    },
    ...items,
    {
      key: 'projectName',
      label: '项目名称',
      show: true
    },
    {
      key: 'quantityFilter',
      label: '红蓝发票',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: [
        {
          name: '全部',
          id: 0
        },
        {
          name: '蓝票',
          id: 1
        },
        {
          name: '红票',
          id: -1
        }
      ],
      show: true
    },
    {
      show: true,
      key: 'agentName',
      label: '供应商名称',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
      labelK: 'name',
      valueK: 'name',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { functionUri: 'metadata://fam' }
      }
    },
    {
      key: 'producerName',
      label: '厂家',
      show: true
    },{
      key: 'orderNo',
      label: '订单号',
      show: true
    },
]
});
const queryObject2 = computed(() =>
  Object.fromEntries(queryList2.value.map((item) => [item.key, item]))
);
//高级检索
const queryList2 = computed(() => {
  return [
    {
      key: 'createdBy',
      label: '创建人',
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
      placeholder: '用户名称搜索',
      valueK: 'name',
      labelK: 'displayName',
      props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
      slots: {
        option: ({ item }) => (
          <>
            <span>{item.displayName}</span>
            <span style="float:right">{item.name}</span>
          </>
        )
      }
    }
  ];
});

const queryObject3 = computed(() =>
  Object.fromEntries(queryList3.value.map((item) => [item.key, item]))
);
//高级检索
const queryList3 = computed(() => {
  return [
    {
      key: 'billCode',
      label: '应收单号',
      show: false
    }, {
      key: 'relateCode',
      label: '关联单号',
      show: false
    }, {
      key: 'orderNo',
      label: '订单号',
      show: false
    }
  ]
})
// onMounted(() => {
//   crud.toQuery();
// });
watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud2.query = {
        invoiceNo: crud.rowData.invoiceNo,
        companyId: crud.rowData.companyId,
        serviceId: crud.query.serviceId
      };
      preFormData.invoiceNo = crud.rowData.invoiceNo;
      preFormData.invoiceAmount = crud.rowData.invoiceAmount;
      preFormData.code = crud.rowData.code;
      preFormData.blueInvoiceNo = crud.rowData.blueInvoiceNo;
      crud2.toQuery();
      crud3.query.invoiceNo = crud.rowData.invoiceNo;
      crud3.toQuery();
    }
  },
  { deep: true }
);
const tabhandleClick = () => {
  crud.toQuery();
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      // sums[index] = (
      //   <p>
      //     勾选：
      //     <br />
      //     合计：
      //   </p>
      // );
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        if (column.property === 'invoiceAmount') {
          sums[index] = (
            rbstateFormat(totalInvoiceAmount.value)
            // <p>
            //   {rbstateFormat(checkTotal.value)}
            //   <br />
            //   {rbstateFormat(sums[index])}
            // </p>
          );
        } else if (column.property === 'alreadyWriteOffAmount') {
          sums[index] = (
            rbstateFormat(totalAWOAmount.value)
            // <p>
            //   {rbstateFormat(checkTota2.value)}
            //   <br />
            //   {rbstateFormat(sums[index])}
            // </p>
          );
        } else if (column.property === 'residueWriteOffAmount') {
          sums[index] = (
            rbstateFormat(totalUWOAmount.value)
            // <p>
            //   {rbstateFormat(checkTota3.value)}
            //   <br />
            //   {rbstateFormat(sums[index])}
            // </p>
          );
        }
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
//计算合计
const getSummaries1 = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      if (column.property === 'creditAmount') {
        sums[index] = creditSum.creditAmount;
      } else {
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => Number.isNaN(value))) {
          sums[index] = `${values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!Number.isNaN(value)) {
              return parseFloat((prev + curr).toFixed(4));
            } else {
              return prev;
            }
          }, 0)}`;
          sums[index] = rbstateFormat(sums[index]);
        } else {
          sums[index] = '';
        }
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(2);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};

const exportLoading = ref(false);
  const exportdata = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    exportLoading.value = true;
    var exportquery = JSON.parse(JSON.stringify(crud.query));
    exportquery['page'] = 0;
    exportquery['limit'] = 0;
    try {
      await ExportInvoices(exportquery);
      exportLoading.value = false;
    } catch (err) {
      exportLoading.value = false;
    }    
  });
};

//附件格式弹窗
const attachmentFormatDialogVisible = ref(false);
//附件格式列表
const attachmentFormatList = [
  {
    id: 'pdf',
    name:'pdf格式'
  },
  {
    id: 'xml',
    name:'xml格式'
  },
  {
    id: 'ofd',
    name:'ofd格式'
  }
]
//最终格式
const attachmentFormat = ref("pdf");
const attachmentInvoiceNo = ref('');
const attachmentInvoiceCode = ref('');
//查看附件前
const downloadFileBefore = (invoiceNo,invoiceCode) => {
  attachmentInvoiceNo.value = invoiceNo;
  attachmentInvoiceCode.value = invoiceCode;
  attachmentFormatDialogVisible.value = true;
}
//关闭附件格式选择弹窗
const attachmentFormatDialogClose = () => {
  //attachmentFormat.value = 'pdf';
  attachmentFormatDialogVisible.value = false;
}

const downloadFile = () => {
  console.log('attachmentInvoiceNo:'+ attachmentInvoiceNo.value)
  console.log('attachmentInvoiceCode:'+ attachmentInvoiceCode.value)
  request({
    url:
      '/api/CreditQuery/GetKDFilePath?invoiceNo=' +
      attachmentInvoiceNo.value +
      '&invoiceCode=' +
      attachmentInvoiceCode.value,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          if (attachmentFormat.value === 'xml'){
            FileViewer.show(
              res.data.data.map((i) => i.xmlFileUrl), // 可以为数组和逗号隔开的字符串
              0, // 默认打开的下标
              {} // FileViewer props
            );
          } else if(attachmentFormat.value === 'ofd'){
            FileViewer.show(
              res.data.data.map((i) => i.ofdFileUrl), // 可以为数组和逗号隔开的字符串
              0, // 默认打开的下标
              {} // FileViewer props
            );
          } else{
            FileViewer.show(
              res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
              0, // 默认打开的下标
              {} // FileViewer props
            );
          }
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶附件，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => { });
};
const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};
const crudSelectionChangeHandler = (val) => {
  totalInvoiceAmount.value = 0; //开票金额
  totalAWOAmount.value = 0; //已冲销金额
  totalUWOAmount.value = 0; //剩余冲销金额
  crud.selections = val;
  if (val.length > 0) {
    val.map((item) => {
      totalInvoiceAmount.value += item.invoiceAmount;
      totalAWOAmount.value += item.alreadyWriteOffAmount;
      totalUWOAmount.value += item.residueWriteOffAmount;
    });
  } else {
    totalInvoiceAmount.value = checkTotal.value;
    totalAWOAmount.value = checkTota2.value;
    totalUWOAmount.value = checkTota3.value;
  }

};
let crDialogShow = ref(false);
const changeRelationshipRef = ref(null);
// 更换应收关系
const changeRelationship = () => {
  rkInvoiceCheck()
  .then((result) => {
    if (!result) {
      // 返回值为false
      ElMessage({
        showClose: true,
        message: '发票不能存在已认款的数据',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      // 2.查询同公司同客户金额相同未开票的类型为销售应收的未认款的应收数据
      crDialogShow.value = true;
    }
  })
  .catch((error) => {
    console.error(error);
  });
};
// 关闭组件弹窗回调
const closeDialogCallBack = () => {
  crDialogShow.value = false;
  crud.toQuery();
  crud2.toQuery();
};
//原应收校验
const oldCreditCheck = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/InvoiceQuery/GetWriteOffCreditBillCode',
      method: 'POST',
      data: {
        oldCreditBillCode: crud2.rowData.billCode
      }
    })
      .then((res) => {
        if (res.data.code !== 200) {
          resolve(false);
        } else {
          resolve(true);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};
//所选发票是否已认款
const rkInvoiceCheck = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/InvoiceQuery/GetRecognizeReceiveDetailByInvoiceNos',
      method: 'POST',
      data: {
        invoiceNo: crud.rowData.invoiceNo
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          console.log(JSON.stringify(res.data));
          if (res.data.data === null) {
            resolve(true);
          } else {
            resolve(false);
          }
        } else {
          resolve(false);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};
//导出附件
const mailDialogVisible = ref(false);
const customerEmail = ref('');
const isEmailValid = ref(false);
//校验邮箱
const validateEmail = () => {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  isEmailValid.value = emailRegex.test(customerEmail.value);
};
const exportattach = () => {
  console.log('list',crud.query.companyIdList)
  if (
    crud.query.companyIdList?.length==0
  ) {
    ElMessage({
      showClose: true,
      message: '请选择公司',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  mailDialogVisible.value = true;
};
const sendLoading = ref(false);
const send = () => {
  sendLoading.value = true;
  if (isEmailValid.value) {
    ElMessageBox.confirm('确认打包发送查询出的所有发票吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      crud.query.customerEmail = customerEmail.value;
      request({
        url: '/api/InvoiceQuery/SendEmailInvoices',
        method: 'POST',
        data: crud.query
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '发送成功，请稍后到邮箱中查看',
              type: 'success',
              duration: 3 * 1000
            });
            sendLoading.value = false;
            mailDialogVisible.value = false;
            return;
          } else {
            ElMessage({
              showClose: true,
              message: '发送失败' + res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
            sendLoading.value = false;
            mailDialogVisible.value = false;
            return;
          }
        })
        // 存在前端超时问题，为了优化用户系统反馈，优先关闭窗口
        .catch(() => {
          sendLoading.value = false;
          mailDialogVisible.value = false;
        })
        .finish(() => {
          sendLoading.value = false;
          mailDialogVisible.value = false;
        });
    });
  } else {
    ElMessage({
      showClose: true,
      message: '请正确填写邮箱',
      type: 'error',
      duration: 3 * 1000
    });
    sendLoading.value = false;
    mailDialogVisible.value = false;
    return;
  }
};
// 应收单集合
const preFormRef = ref();
const creditList = ref([]);
// 预开票发票关联应收
const preDialogVisible = ref(false);
const preInvoiceCreditAssociation = () => {
  // 查询符合条件的应收单
  request({
    url: '/api/CreditQuery/GetListALL',
    method: 'POST',
    data: {
      invoiceNo: crud.rowData.invoiceNo,
      companyId: crud.rowData.companyId,
      customerId: crud.rowData.customerId,
      projectName: crud.rowData.projectName,
      isNotHaveInvoice: true
    }
  }).then((res) => {
    if (res.data.code === 200) {
      creditList.value = res.data.data.list;
      if (creditList.value.length === 0) {
        ElMessage({
          showClose: true,
          message: '未找到符合条件的应收单',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
      preDialogVisible.value = true;
    } else {
      ElMessage({
        showClose: true,
        message: '获取应收单列表失败',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
  });
};
// 关联应收组件关闭
const preDialogClose = () => {
  // preFormData.invoiceNo = '';
  // preFormData.invoiceAmount = 0;
  preFormData.creditValue = 0;
  preFormData.credits = [];
  preFormData.blueInvoiceNo = '';
  preFormData.code = '';
  creditList.value = [];
  preFormRef.value.resetFields();
  preDialogVisible.value = false;
};
// 应收单改变事件
const billCodeSelectChange = (row) => {
  var single = creditList.value.filter(
    (item) => item.billCode === row.billCode
  );
  if (single !== null && single.length > 0) {
    row.value = single[0].value;
    preFormData.creditValue += single[0].value;
  }
};
// 预开票表单提交数据
let preFormData = reactive({
  invoiceNo: '',
  code: '',
  blueInvoiceNo: '',
  invoiceAmount: 0,
  creditValue: 0,
  credits: [
    {
      billCode: '',
      value: ''
    }
  ]
});
// 关联
const associationLoading = ref(false);
const association = () => {
  if (!preFormRef.value) return;
  if (preFormData.creditValue > preFormData.invoiceAmount) {
    ElMessage({
      showClose: true,
      message: '选择应收总金额应与发票金额一致',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  associationLoading.value = true;
  // 绑定关系
  request({
    url: '/api/InvoiceQuery/CreatePreInvoiceCredit',
    method: 'POST',
    data: preFormData
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: '关联成功',
        type: 'success',
        duration: 3 * 1000
      });
      preDialogClose();
      associationLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      associationLoading.value = false;
      return;
    }
  });
};
const multipleSelection = ref([]);
// 选择事件
const selectionChange = (val) => {
  multipleSelection.value = val;
};
// 新增行
const addRows = () => {
  preFormData.credits.push({
    billCode: '',
    value: ''
  });
};
// 删除行
const delRows = () => {
  if (multipleSelection.value.length === 0) {
    return;
  }
  preFormData.credits = preFormData.credits.filter(
    (row) => !multipleSelection.value.includes(row)
  );
  multipleSelection.value = [];
  var amount = 0;
  preFormData.credits.map((item) => {
    amount += Number(item.value);
  });
  preFormData.creditValue = amount;
};
// 重推SPD商务平台
const refreshPushSPDLoading = ref(false);
const refreshPushSPD = () => {
  var check = true;
  crud.selections.map((item) => {
    if (item.Type !== '纸质专用发票' && item.companyName !== '贵州致思蒲达企业管理有限公司') {
      check = false;
    }
  })
  // if(!check) {
  //   ElMessage({
  //     showClose: true,
  //     message: '仅可重推贵州致思蒲达企业管理有限公司的纸质专用发票！',
  //     type: 'error',
  //     duration: 3 * 1000
  //   });
  // }
  // 重新推送
  refreshPushSPDLoading.value = true;
  var invoiceNos = crud.selections.map(x => x.invoiceNo);
  request({
    url: '/api/InvoiceQuery/RefreshPushSPD',
    method: 'POST',
    data: invoiceNos
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: '重推成功',
        type: 'success',
        duration: 3 * 1000
      });
      refreshPushSPDLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      refreshPushSPDLoading.value = false;
      return;
    }
  }).finish(() => {
    refreshPushSPDLoading.value = false;
  });
}

//协调服务导出
let downLoading = ref(false);
const downloadAsync = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
</script>
