﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills
{
    public class InputBillSubmitDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 原始Id
        /// </summary>
        public Guid? OriginalId { get; set; }
        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInItemCode { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        public DateTime? StoreInDate { get; set; }
        /// <summary>
        /// 累计数量(已入票数量)
        /// </summary>
        public decimal? Quantity { get; set; }
        /// <summary>
        /// 已入票数(用于提交比对)
        /// </summary>
        public decimal ReceivedNumber { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal? TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal? NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 勾稽的业务类型，1-经销购货入库，2-经销调出，3-寄售转购货，4-购货修订
        /// </summary>
        public int? BusinessType { get; set; }
        /// <summary>
        /// 进项发票Id
        /// </summary>
        public Guid InputBillId { get; set; }


        /// <summary>
        /// 导航属性-提交 每次入票数量记录
        /// </summary>
        public List<InputBillSubmitDetailQuantity>? InputBillSubmitDetailQuantity { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }
    }
}
