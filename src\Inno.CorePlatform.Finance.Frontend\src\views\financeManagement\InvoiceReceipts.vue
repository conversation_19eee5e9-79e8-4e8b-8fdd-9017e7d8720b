<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>发票入账单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0px">
      <inno-query-operation :crud="crud" :query-list.sync="queryList" />

      <inno-split-pane split="horizontal" :default-percent="50">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                  <el-tab-pane :label="`审批中(${tabCount.waitAuditCount})`" name="1" lazy />
                  <el-tab-pane :label="`已完成(${tabCount.complateCount})`" name="99" lazy />
                  <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                  <el-tab-pane :label="`我的审批(${tabCount.myCount})`" name="5000" lazy />
                </el-tabs>
              </div>
            </template>
            <template #default>
              <inno-button-tooltip
                :disabled="
                        !crud.selections.length ||
                        crud.rowData.status !== 0
                      "
                type="success"
                @click="submit"
              >提交</inno-button-tooltip>
              <!-- <inno-button-tooltip v-if="isWaitAudit" type="danger" @click="withdraw">撤回</inno-button-tooltip> -->
              <inno-button-tooltip type="primary" @click="add">创建</inno-button-tooltip>
              <!-- <inno-button-tooltip @click="detail">详情</inno-button-tooltip> -->
              <inno-button-tooltip
                :disabled="
                        !crud.selections.length ||
                        crud.rowData.status !== 0
                      "
                type="warning"
                @click="edit"
              >编辑</inno-button-tooltip>
              <!-- <inno-button-tooltip v-if="isWaitSubmit" type="danger" @click="del">删除</inno-button-tooltip> -->
              <inno-button-tooltip type="primary" @click="downloadAsync(
               '/api/InvoiceReceipts/ExportByCoordinate',
               '发票入账清单',
               crud.query
              )
              ">导出全部</inno-button-tooltip>

              <el-dropdown trigger="click">
                <inno-button-tooltip type="primary">
                  更多操作
                  <el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </inno-button-tooltip>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :disabled="
                        !crud.selections.length ||
                        crud.rowData.status !== 0">
                      <inno-button-tooltip
                        type="text"
                        icon="Delete"
                        :disabled="
                          (!crud.selections.length ||
                          crud.rowData.status !== 0)
                        "
                        @click="del"
                      >删除</inno-button-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="crud.rowData.status !== 1">
                      <inno-button-tooltip type="text" icon="RefreshLeft" :disabled="crud.rowData.status !== 1" :ms-disabled="crud.rowDisabled" @click="withdraw">撤回</inno-button-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <inno-button-tooltip
                        v-if="crud.rowData.status !== 0"
                        icon="View"
                        content="请选择一条数据"
                        :disabled="!crud.selections.length"
                        type="text"
                        @click="auditProcessClick(crud.rowData)"
                      >查看审批过程</inno-button-tooltip>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRef"
              v-inno-loading="crud.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              fit
              border
              :data="crud.data"
              :row-class-name="crud.tableRowClassName"
              @selection-change="crud.selectionChangeHandler"
              @row-click="crud.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="billCode" label="单据" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                  <!-- <el-link style="font-size: 12px" @click="detail(scope.row.id)">{{ scope.row.billCode }}</el-link>  -->
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="billDate" label="单据日期" min-width="110">
                <template #default="scope">
                  {{
                  scope.row.billDate === null
                  ? ''
                  : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="businessDeptFullName" label="核算部门" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="companyName" label="公司" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="serviceName" label="业务单元" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="customerName" label="客户" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="backAmountDays" label="合约回款天数" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.backAmountDays }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="saleAccountPeriodDays" label="销售账期天数" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.saleAccountPeriodDays }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="actualBackAmountDays" label="实际回款天数" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.actualBackAmountDays }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="statusStr" label="状态" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.statusStr }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="approvalTime" label="审批日期" min-width="110">
                <template #default="scope">
                  {{
                  scope.row.approvalTime === null
                  ? ''
                  : dateFormat(scope.row.approvalTime, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column label="创建人" property="createdByName" width="90">
                <template #header="{ column }">
                  <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
                </template>
                <template #default="scope">{{ scope.row.createdByName }}</template>
              </el-table-column>
              <el-table-column label="附件" property="attachFileIds" width="130" show-overflow-tooltip>
                <template #default="scope">
                  <el-link style="font-size: 13px" @click="showAttachFile(scope.row.attachFileIds, scope.row.id)">{{ scope.row.attachFileIds ? '查看附件' : '' }}</el-link>
                </template>
              </el-table-column>
              <el-table-column label="备注" property="remark" width="110" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.remark }}</template>
              </el-table-column>
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crudDetail" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`发票明细`" />
              </el-tabs>
            </template>
            <template #default>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRefDeJd"
              v-inno-loading="crudDetail.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              border
              show-summary
              :summary-method="getSummaries"
              :data="crudDetail.data"
              :row-class-name="crudDetail.tableRowClassName"
              @selection-change="crudDetail.selectionChangeHandler"
              @row-click="crudDetail.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="creditCode" label="应收单号" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.creditCode }}</inno-button-copy>
                </template>
              </el-table-column><el-table-column prop="customerName" label="客户" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
                </template>
              </el-table-column><el-table-column prop="hospitalName" label="终端医院" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.hospitalName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="invoiceNo" label="发票号" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="invoiceCode" label="发票代码" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="invoiceCheckCode" label="发票验证码" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.invoiceCheckCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="invoiceTime" label="开票时间" min-width="110">
                <template #default="scope">
                  {{
                  scope.row.billDate === null
                  ? ''
                  : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column sortable class-name="isSum" prop="invoiceAmount" label="开票金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column sortable class-name="isSum" prop="creditAmount" label="应收金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.creditAmount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column prop="isCancel" label="开票状态" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.isCancel === 1?'已取消':'已开票' }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
                </template>
              </el-table-column>
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crudDetail.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudDetail" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <!-- 文件查看 -->
    <el-dialog v-model="comfile_show" title="附件查看" destroy-on-close :close-on-click-modal="false" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小(kb)">
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.length / 1000 }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
  </div>
</template>

<script lang="tsx" setup>
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import approveProcess from '@/component/ApproveProcess.vue';
import request from '@/utils/request';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';

const route = useRoute();

const isWaitSubmit = ref(false);
const isWaitAudit = ref(false);

const approveProcessRef = ref(null);
const tableRef = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '入账单信息',
    url: '/api/InvoiceReceipts/GetList',
    idField: 'id',
    method: 'post',
    userNames: ['createdBy'],
    query: {
      searchKey: ''
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
        loadTableData();
      }
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDetail = CRUD(
  {
    title: '发票明细',
    url: '/api/InvoiceReceipts/GetDetailsByItemId',
    idField: 'id',
    method: 'post',
    sort: ['createdTime,desc'],
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        getDetailSumCount();
      }
    },
    tablekey: 'tableRef2',
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRefDeJd
  }
);
 const getDetailSumCount = (type) => {
    request({
      url: '/api/InvoiceReceipts/GetDetailsSumByItemId',
      data: {
        invoiceReceiptItemId: crud.rowData.id
      },
      method: 'post'
    }).then((res) => {
      detailSum.invoiceAmountSum = res.data.data.invoiceAmountSum;
      detailSum.creditAmountSum = res.data.data.creditAmountSum;
    });
  };
const queryList = computed(() => [
  {
    key: 'billCode',
    label: '单号',
    show: true
  },
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司搜索',
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'customerId',
    label: '客户',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'billDateBeging',
    endDate: 'billDateEnd',
    label: '单据日期',
    type: 'daterange',
    formart: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'createdBy',
    label: '创建人',
    method: 'post',
    multiple: true,
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
    placeholder: '用户名称搜索',
    valueK: 'name',
    labelK: 'displayName',
    props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
    slots: {
      option: ({ item }) => (
        <>
          <span>{item.displayName}</span>
          <span style="float:right">{item.name}</span>
        </>
      )
    }
  },
  {
    key: 'creditCode',
    label: '应收单号',
    show: true
  },
  {
    key: 'invoiceNo',
    label: '发票号',
    show: true
  }
  // {
  //   key: 'status',
  //   label: '状态',
  //   type: 'select',
  //   labelK: 'name',
  //   valueK: 'id',
  //   dataList: statusTypeEnum,
  //   show: false
  // }
]);
const statusTypeEnum = [
  {
    id: '0',
    name: '待提交'
  },
  {
    id: '1',
    name: '审批中'
  },
  {
    id: '99',
    name: '已完成'
  },
  {
    id: '-1',
    name: '全部'
  }
];
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
onMounted(() => {
  if (route.query && route.query.code) {
    crud.query.code = route.query.code;
  }
  crud.toQuery();
  tableDrag(tableRef);
  tableDrag(tableRefDeJd);
});
watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      var i = 0;
      var j = 0;
      crud.selections.forEach((item) => {
        if (item.statusStr !== '待提交') {
          isWaitSubmit.value = false;
        } else {
          i++;
        }
        if (item.statusStr !== '待审核') {
          isWaitAudit.value = false;
        } else {
          j++;
        }
      });
      if (i === crud.selections.length) {
        isWaitSubmit.value = true;
      }
      if (j === crud.selections.length) {
        isWaitAudit.value = true;
      }
      crudDetail.query = {
        invoiceReceiptItemId: crud.rowData.id
      };
      crudDetail.toQuery();
    }
  },
  { deep: true }
);
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const router = useRouter();
//创建
const add = () => {
  router.push({
    path: 'InvoiceReceiptCreate'
  });
};
//详情
const detail = (id) => {
  router.push({
    path: 'InvoiceReceiptDetail',
    query: {
      id: id
    }
  });
};
//编辑
const edit = () => {
  router.push({
    path: 'InvoiceReceiptCreate',
    query: {
      id: crud.rowData.id
    }
  });
};
//删除
const del = () => {
  ElMessageBox.confirm('确认删除吗，对应明细将会一并删除', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/InvoiceReceipts/Remove',
      method: 'POST',
      data: {
        invoiceReceiptItemId: crud.rowData.id
      }
    }).then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '删除成功',
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    });
  });
};
//提交
const submit = () => {
  request({
    url: '/api/InvoiceReceipts/submit',
    method: 'POST',
    data: {
      invoiceReceiptItemId: crud.rowData.id
    }
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: '提交成功',
        type: 'success',
        duration: 3 * 1000
      });
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.msg != null ? res.data.msg : res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
    }
  });
};
//撤回
const withdraw = () => {
  ElMessageBox.confirm('确认撤回此条数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    var dto = {
      invoiceReceiptItemId: crud.rowData.id,
      createdBy: crud.rowData.createdBy,
      status: crud.rowData.status
    };
    request({
      url: '/api/InvoiceReceipts/withdraw',
      method: 'POST',
      data: dto
    }).then((res) => {
      console.log(JSON.stringify(res));
      if (res.data.item1 === 1) {
        ElMessage({
          showClose: true,
          message: '成功',
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.item2,
          type: 'error',
          duration: 3 * 1000
        });
      }
    });
  });
};
//协调服务下载
let downLoading = ref(false);
const downloadAsync = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
//下载
const download = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportConvertPaymentList(crud.query, '发票入账');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportConvertPaymentList = async (data, filename) => {
  await request({
    url: '/api/InvoiceReceipts/DownLoad',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' },
    //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob'
    //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};
  const detailSum = reactive({
    invoiceAmountSum: 0,
    creditAmount:0,
  });
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum', 'isSumChild'].includes(column.className)) {
      var values = [];
      if (column.className == 'isSum') {
        values = data.map((item) => Number(item[column.property]));
      } else if (column.className == 'isSumChild') {
        // 子实体
        const parts = column.property.split('.');
        //const child = parts[0];
        const prop = parts[1];
        values = data.map((item) => Number(item.debt[prop]));
      }
      if (column.property === 'invoiceAmount') {
        sums[index] = detailSum.invoiceAmountSum;
      } else if (column.property === 'creditAmount') {
        sums[index] = detailSum.creditAmountSum;
      } else {
        if (!values.every((value) => Number.isNaN(value))) {
          sums[index] = `${values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!Number.isNaN(value)) {
              return parseFloat((prev + curr).toFixed(4));
            } else {
              return prev;
            }
          }, 0)}`;
          sums[index] = rbstateFormat(sums[index]);
        } else {
          sums[index] = '';
        }
      }
      } else {
        sums[index] = '';
      }
    
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
//附件
let invoiceReceiptItemId = ref('');
const comfile_show = ref(false);
const showfiles = ref([]);
const showAttachFile = (showAttachFileids, id) => {
  console.log(123123123);
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    invoiceReceiptItemId.value = id;
    request({
      url: `/api/InvoiceReceipts/GetAttachFile`,
      method: 'POST',
      data: {
        invoiceReceiptItemId: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show.value = true;
          showfiles.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'success',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};
const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
};
const deleteFile = (fileid) => {
  ElMessageBox.confirm('是否确定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: `/api/InvoiceReceipts/DeleteAttachFileIds`,
      method: 'POST',
      data: {
        invoiceReceiptItemId: invoiceReceiptItemId.value,
        attachFileId: fileid
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          if (res.data.data == '' || res.data.data.length == 0) {
            crud.toQuery();
            comfile_show.value = false;
          } else {
            showAttachFile(res.data.data, invoiceReceiptItemId.value);
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  });
};

let tabCount = ref({
  waitSubmitCount: 0,
  waitAuditCount: 0,
  complateCount: 0,
  allCount: 0,
  myCount: 0
});
const loadTableData = () => {
  request({
    url: '/api/InvoiceReceipts/GetTabCount',
    data: {
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
//审批过程
const auditProcessClick = (row) => {
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};
</script>
