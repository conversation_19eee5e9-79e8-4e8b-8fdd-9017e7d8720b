<template>
  <el-dialog
    v-model="visible"
    draggable
    destroy-on-close
    :title="fileName"
    center
    align-center
    width="70%"
    @closed="emits('close')"
  >
    <div style="text-align: center; overflow: auto; width: 100%">
      <el-skeleton v-if="loading" animated>
        <template #template>
          <el-skeleton-item variant="image" :style="{ height: dialogHeight, width: '100%' }" />
        </template>
      </el-skeleton>
      <div
        v-if="imgMode && localUrl"
        :style="{
          width: '100%',
          height: dialogHeight,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }"
      >
        <img :src="localUrl" :alt="alt" @error="imgMode = false" />
      </div>
      <div v-else>
        <embed
          v-if="localUrl"
          :style="{ width: '100%', height: dialogHeight }"
          :src="localUrl"
          @load="() => {}"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue';
import request from '@/utils/request';
import type { Ref } from 'vue';

const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  params: {
    type: Object,
    default: () => {
      return null;
    }
  },
  data: {
    type: Object,
    default: () => {
      return null;
    }
  },
  headers: {
    type: Object,
    default: () => {
      return null;
    }
  },
  method: {
    type: String,
    default: 'GET'
  },
  defaultFileName: {
    type: String,
    default: 'fileName'
  },
  alt: {
    type: String,
    default: ''
  }
});

const loading = ref(false);
const visible = ref(true);
const imgMode = ref(true);

const localUrl: Ref<string | null> = ref(null);
const fileName = ref('');

const emits = defineEmits(['close']);
const dialogHeight = window.screen.height * 0.6 + 'px';

const getFileNameInRes = (res: any, defaultFileName: string = 'file') => {
  let fileName = defaultFileName;
  Object.keys(res?.headers || {}).forEach((k: string) => {
    if (k.toLowerCase() === 'content-disposition') {
      const content = res?.headers[k];
      const filenameRegex = /filename\*=.*?''(\2|[^;\n]*)/;
      const matches = filenameRegex.exec(content);
      if (matches != null && matches[1]) {
        fileName = decodeURIComponent(matches[1].replace(/['"]/g, ''));
      }
    }
  });
  return fileName;
};

watch(
  props,
  (v) => {
    fileName.value = '';
    imgMode.value = true;
    if (localUrl.value) {
      URL.revokeObjectURL(localUrl.value);
    }

    if (v) {
      loading.value = true;
      request({
        method: props.method,
        url: props.url,
        responseType: 'blob',
        headers: props.headers || undefined,
        params: props.params || undefined,
        data: props.data || undefined
      })
        .then((res: any) => {
          if (res.status === 200) {
            localUrl.value = URL.createObjectURL(res.data);
            fileName.value = getFileNameInRes(res, props.defaultFileName);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  },
  { immediate: true, deep: true }
);

onUnmounted(() => {
  if (localUrl.value) {
    URL.revokeObjectURL(localUrl.value);
  }
});
</script>
