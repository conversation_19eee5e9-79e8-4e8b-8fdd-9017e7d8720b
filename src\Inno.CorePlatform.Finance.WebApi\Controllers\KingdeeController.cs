﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;


namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 金蝶接口
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class KingdeeController : BaseController
    {
        public override bool EnableParameterLogging { get; set; } = true;
        //private readonly ILogger<KingdeeController> _logger;
        private readonly IPaymentQueryService _paymentQueryService;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IEasyCachingProvider _easyCaching;

        public KingdeeController(
            IPaymentQueryService paymentQueryService,
            IKingdeeApiClient kingdeeApiClient,
            IBDSApiClient bDSApiClient,
            IInvoiceQueryService invoiceQueryService,
            IExchangeRateService exchangeRateService,
            IEasyCachingProvider easyCaching,
            ISubLogService logService) : base(logService)
        {
           
            this._paymentQueryService = paymentQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._exchangeRateService = exchangeRateService;
            this._invoiceQueryService = invoiceQueryService;
            this._bDSApiClient = bDSApiClient;
            _easyCaching = easyCaching;
        }

        

        /// <summary>
        /// 获取金蝶AccessToken
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAccesstoken")]
        [SkipLogging]
        public async Task<BaseResponseData<string>> GetAccesstoken()
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            ret.Data = await _kingdeeApiClient.GetAccesstokenAsync();
            return ret;
        }
        /// <summary>
        /// 汇率查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExchangeRateQuery")]
        [SkipLogging]
        public async Task<BaseResponseData<GetExchangeRateOutput>> ExchangeRateQuery(GetExchangeRateInput input)
        {
            return await _exchangeRateService.GetByExchangeRateWithKD(input);
        }

        /// <summary>
        /// 信用证-保存业务申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveCoreToBizapply")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> SaveCoreToBizapply(saveCoreToBizapplyInput input)
        {
            return await _kingdeeApiClient.SaveCoreToBizapply(input);
        }

        /// <summary>
        /// 信用证-信用证列表查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetLetterCredit")]
        [SkipLogging]
        public async Task<BaseResponseData<List<getLetterCreditOutput>>> GetLetterCredit(getLetterCreditInput input)
        {
            return await _kingdeeApiClient.GetLetterCredit(input);
        }

        /// <summary>
        /// 保存或修改行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveLineNameLineNumber")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> SaveLineNameLineNumber(SaveLineNameLineNumberInput input)
        {
            return await _kingdeeApiClient.SaveLineNameLineNumber(input);
        }

        /// <summary>
        /// 删除行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteLineNameLineNumber")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> DeleteLineNameLineNumber(DeleteLineNameLineNumberInput input)
        {
            return await _kingdeeApiClient.DeleteLineNameLineNumber(input);
        }

        /// <summary>
        /// 发票识别查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RecognitionCheck")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> RecognitionCheck(RecognitionCheckInput input)
        {
            return await _kingdeeApiClient.RecognitionCheck(input);
        }

        /// <summary>
        /// 发票查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("InvoiceCheck")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> InvoiceCheck(InvoiceCheckInput input)
        {
            return await _kingdeeApiClient.InvoiceCheck(input);
        }

        /// <summary>
        /// 查询结算方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetSettlementType")]
        [SkipLogging]
        public async Task<BaseResponseData<SettlementtypeOutput>> GetSettlementType(SettlementtypeInput input)
        {
            return await _kingdeeApiClient.GetSettlementtype(input);
        }

        /// <summary>
        /// 保存或更新付款申请单 billno 编辑时必传
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("SaveOrUpdatePayment")]
        [OperationLog("项目付款申请单")]
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> SaveOrUpdateRefund(SaveOrUpdateRefundInput input)
        {
            
            return await _kingdeeApiClient.SaveOrUpdateRefund(input);
        }

        /// <summary>
        /// 应付付款结算
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [HttpPost("PaymentSettlement")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> PaymentSettlement(List<PaymentSettlementInput> inputs)
        {
            return await _kingdeeApiClient.PaymentSettlement(inputs);
        }

        /// <summary>
        /// 删除付款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("DeletePayment")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> RefundDelete(RefundDeleteInput input)
        {
            return await _kingdeeApiClient.RefundDelete(input);
        }

        /// <summary>
        /// 获取付款回执单地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetPaymentFilePath")]
        [SkipLogging]
        public async Task<BaseResponseData<List<ReceiptNumberModelOutput>>> GetPaymentFilePath(FindFileInput input)
        {
            var ret = await _kingdeeApiClient.SelectTheReceiptNumber(new List<ReceiptNumberModelInput> {
                new ReceiptNumberModelInput {
                     paymentNum = input.billno,
                }
            }, type: input.type);
            return ret;
        }

        /// <summary>
        /// 获取发票地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetInvoiceFilePath")]
        [SkipLogging]
        public async Task<BaseResponseData<List<QueryInvoiceAttachmentOutput>>> GetInvoiceFilePath(InvoiceFileInput input)
        {
            var ret = BaseResponseData<List<QueryInvoiceAttachmentOutput>>.Failed(500, "没有找到发票数据");
            var invoice = await _invoiceQueryService.GetInvoiceByInvoiceNo(input.InvoiceNo);
            if (invoice != null)
            {
                if (!string.IsNullOrEmpty(invoice.Type) && invoice.Type.Contains("数电"))
                {
                    var companys = await _bDSApiClient.GetCompanyInfoAsync(new BDSBaseInput()
                    {
                        ids = new List<string> { invoice.CompanyId.ToString() }
                    });
                    var retDigital = await _kingdeeApiClient.GetDigitalInvoiceFile(new SimData
                    {
                        invoiceNum = input.InvoiceNo,
                        sellerTaxpayerId = companys[0].latestUniCode
                    });
                    if (retDigital.Code == CodeStatusEnum.Success)
                    {
                        ret.Data = new List<QueryInvoiceAttachmentOutput> {
                          new QueryInvoiceAttachmentOutput{
                             invoiceNo=  input.InvoiceNo,
                             address= retDigital.Data.ofdFileUrl,
                             previewAddress= retDigital.Data.invoiceFileUrl,
                             xmlFileUrl=retDigital.Data.xmlFileUrl,
                             ofdFileUrl=retDigital.Data.ofdFileUrl,
                             pdfFileUrl= retDigital.Data.invoiceFileUrl,

                          }
                        };
                        ret.Code = CodeStatusEnum.Success;
                    }

                }
                else
                {
                    ret = await _kingdeeApiClient.QueryInvoiceAttachment(new QueryInvoiceAttachmentInput
                    {
                        invoiceNo = input.InvoiceNo,
                        invoiceCode = input.InvoiceCode ?? "",
                        invoiceType = "XX"
                    });
                }
            }

            return ret;
        }

        /// <summary>
        /// 推送初始化发票至金蝶
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("InvoiceChildInit")]
        [SkipLogging]
        public async Task<BaseResponseData<string>> InvoiceChildInit(InitInvoicesInput input)
        {
            var list = await _invoiceQueryService.GetInitEasInvoices(input);
            return await _kingdeeApiClient.InvoiceChildInit(list);
        }

        /// <summary>
        /// 收入确认单批量保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RevcfmbillBatchSave")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> RevcfmbillBatchSave(List<RevcfmbillBatchSaveInput> input)
        {
            var ret = await _kingdeeApiClient.RevcfmbillBatchSave(input);
            return ret;
        }

        /// <summary>
        /// 银行信息查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("QueryBeBank")]
        [SkipLogging]
        public async Task<BaseResponseData<QueryBeBankOutput>> QueryBeBank(TaxClassCodeInput input)
        {
            var ret = await _kingdeeApiClient.QueryBeBank(input);
            return ret;
        }

        /// <summary>
        /// 税收分类查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("TaxClassCode")]
        [SkipLogging]
        public async Task<BaseResponseData<TaxClassCodeOutput>> TaxClassCode(TaxClassCodeInput input)
        {
            var ret = await _kingdeeApiClient.TaxClassCode(input);
            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetKingdeecheckData")]
        public async Task<BaseResponseData<KindeeCheckBillResData>> GetKingdeecheckData(CheckForKingdeeInput input)
        {
            try
            {
                var ret = await _kingdeeApiClient.GetKingdeeCheckData(input.billCode, input.org_number);
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Success, Data = ret, Message = "获取成功" };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Failed, Message = "获取失败，原因：" + ex.Message };
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetKingdeeTempStoreOutCheckData")]
        [SkipLogging]
        public async Task<BaseResponseData<KindeeCheckBillResData>> GetKingdeeTempStoreOutCheckData(CheckForKingdeeInput input)
        {
            try
            {
                var ret = await _kingdeeApiClient.GetKingdeeTempStoreOutCheckData(input.billCode, input.org_number);
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Success, Data = ret, Message = "获取成功" };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Failed, Message = "获取失败，原因：" + ex.Message };
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetKingdeeCreditCheckData")]
        [SkipLogging]
        public async Task<BaseResponseData<KindeeCheckBillResData>> GetKingdeeCreditCheckData(CheckForKingdeeInput input)
        {
            try
            {
                var ret = await _kingdeeApiClient.GetKingdeeCreditCheckData(input.billCode, input.org_number);
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Success, Data = ret, Message = "获取成功" };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Failed, Message = "获取失败，原因：" + ex.Message };
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetKingdeeTempStoreInCheckData")]
        [SkipLogging]
        public async Task<BaseResponseData<KindeeCheckBillResData>> GetKingdeeTempStoreInCheckData(CheckForKingdeeInput input)
        {
            try
            {
                var ret = await _kingdeeApiClient.GetKingdeeTempStoreInCheckData(input.billCode, input.org_number);
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Success, Data = ret, Message = "获取成功" };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<KindeeCheckBillResData>() { Code = CodeStatusEnum.Failed, Message = "获取失败，原因：" + ex.Message };
            }
        }

        /// <summary>
        /// 库存更换项目-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [HttpPost("BatchSaveBarterDisposeBill")]
        [OperationLog("库存更换项目-库存调用")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBill(List<ChangeProjectInput> inputs)
        {
            var ret = new BaseResponseData<object>();
            var jsonStr = JsonConvert.SerializeObject(inputs);
            var cachekey = "batchSaveBarterDisposeBill_" + inputs[0].BillCode;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    #region 参数组装
                    var ketInput = new List<ApBarterDisposeBillModel>();
                    if (inputs != null && inputs.Any())
                    {
                        foreach (var item in inputs)
                        {
                            var ketSingle = new ApBarterDisposeBillModel();
                            ketSingle.billno = item.BillCode;
                            ketSingle.jfzx_remark = item.Remark;
                            ketSingle.jfzx_org = item.CompanyCode;
                            // 通过添加毫秒数来得到对应的DateTime对象
                            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                            ketSingle.jfzx_bizdate = item.BillDate.HasValue ? unixEpoch.AddMilliseconds(item.BillDate.Value).ToLocalTime() : DateTime.Now;
                            ketSingle.jfzx_type = item.Type;
                            ketSingle.jfzx_remark = item.Remark;
                            var details = new List<ApBarterDisposeEntryModel>();
                            if (item.List != null && item.List.Any())
                            {
                                var productNameIds = item.List.Select(x => x.ProductNameId).ToList();
                                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                                foreach (var detail in item.List)
                                {
                                    var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == detail.ProductNameId);
                                    var jfzx_material = Guid.Empty;
                                    if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                                    {
                                        jfzx_material = thisProductInfo.classificationNewGuid.Value;
                                    }
                                    else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                                    {
                                        jfzx_material = thisProductInfo.classificationGuid.Value;
                                    }
                                    details.Add(new ApBarterDisposeEntryModel
                                    {
                                        jfzx_project = detail.ProjectCode,
                                        jfzx_materialnumber = jfzx_material.ToString().ToUpper(),
                                        jfzx_qty = detail.Quantity,
                                        jfzx_bizorg = detail.BusinessDepId,
                                        //jfzx_invoicenumber = "",
                                        taxrateid_number = detail.TaxRate > 0 ? $"V{detail.TaxRate}".Trim('0').Trim('.') : "V0",
                                        jfzx_ordernumber = detail.OrderNo,
                                        jfzx_standard_total_cost = detail.StandardCost * detail.Quantity,
                                        jfzx_actual_total_cost = detail.NoRateUnitCost * detail.Quantity,
                                        jfzx_isimported = detail.IsImported,
                                        jfzx_project_new = detail.NewProjectCode ??= detail.ProjectCode,
                                        jfzx_supplier = detail.AgentId,
                                        jfzx_goodsType = detail.Mark,
                                        jfzx_productType = detail.ProductType ??= "A",
                                    });
                                }
                                ketSingle.jfzx_materialentity = details;
                            }
                            ketInput.Add(ketSingle);
                        }
                    }
                    #endregion
                    ret = await _kingdeeApiClient.BatchSaveBarterDisposeBill(ketInput);
                    _easyCaching.Remove(cachekey);
                    return ret;
                }
                else
                {
                    ret.Message = "存在并发操作";
                    ret.Code = CodeStatusEnum.Failed;
                    return ret;
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret.Message = "更换失败，原因：" + ex.Message;
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
        }
        /// <summary>
        /// 查询退款数据
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        [HttpPost("QueryKingdeePaymentRefund")]
        [SkipLogging]
        public async Task<BaseResponseData<RefundResponseModelOutput>> QueryKingdeePaymentRefund(QueryRefundDataInput input)
        {
            try
            {
                var ret = await _kingdeeApiClient.QueryKingdeePaymentRefund(input);
                return new BaseResponseData<RefundResponseModelOutput>() { Code = CodeStatusEnum.Success, Data = ret, Message = "获取成功" };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<RefundResponseModelOutput>() { Code = CodeStatusEnum.Failed, Message = "获取失败，原因：" + ex.Message };
            }
        }

        /// <summary>
        /// 库存更换部门-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [HttpPost("BatchSaveBarterDisposeBillByDept")]
        [OperationLog("库存更换核算部门-库存调用")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByDept(List<ChangeDeptInput> inputs)
        {
            var ret = new BaseResponseData<object>();
            var jsonStr = JsonConvert.SerializeObject(inputs);
            var cachekey = "batchSaveBarterDisposeBillByDept_" + inputs[0].BillCode;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    #region 参数组装
                    var ketInput = new List<ApBarterDisposeBillModel>();
                    if (inputs != null && inputs.Any())
                    {
                        foreach (var item in inputs)
                        {
                            var ketSingle = new ApBarterDisposeBillModel();
                            ketSingle.billno = item.BillCode;
                            ketSingle.jfzx_remark = item.Remark;
                            ketSingle.jfzx_org = item.CompanyCode;
                            // 通过添加毫秒数来得到对应的DateTime对象
                            DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                            ketSingle.jfzx_bizdate = item.BillDate.HasValue ? unixEpoch.AddMilliseconds(item.BillDate.Value).ToLocalTime() : DateTime.Now;
                            ketSingle.jfzx_type = item.Type;
                            ketSingle.jfzx_remark = item.Remark;
                            var details = new List<ApBarterDisposeEntryModel>();
                            if (item.List != null && item.List.Any())
                            {
                                var productNameIds = item.List.Select(x => x.ProductNameId).Distinct().ToList();
                                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                                foreach (var detail in item.List)
                                {
                                    var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == detail.ProductNameId);
                                    var jfzx_material = Guid.Empty;
                                    if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                                    {
                                        jfzx_material = thisProductInfo.classificationNewGuid.Value;
                                    }
                                    else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                                    {
                                        jfzx_material = thisProductInfo.classificationGuid.Value;
                                    }
                                    details.Add(new ApBarterDisposeEntryModel
                                    {
                                        jfzx_project = detail.ProjectCode,
                                        jfzx_project_new = detail.NewProjectCode ??= detail.ProjectCode,
                                        jfzx_materialnumber = jfzx_material.ToString().ToUpper(),
                                        jfzx_qty = detail.Quantity,
                                        jfzx_bizorg = detail.BusinessDepId,
                                        jfzx_bizorg_new = detail.NewBusinessDepId,
                                        taxrateid_number = detail.TaxRate > 0 ? $"V{detail.TaxRate}".Trim('0').Trim('.') : "V0",
                                        jfzx_ordernumber = detail.OrderNo,
                                        jfzx_standard_total_cost = detail.StandardCost * detail.Quantity,
                                        jfzx_actual_total_cost = detail.NoRateUnitCost * detail.Quantity,
                                        jfzx_isimported = detail.IsImported,
                                        jfzx_supplier = detail.AgentId,
                                        jfzx_goodsType = detail.Mark,
                                        jfzx_productType = detail.ProductType ??= "A",
                                    });
                                }
                                ketSingle.jfzx_materialentity = details;
                            }
                            ketInput.Add(ketSingle);
                        }
                    }
                    #endregion
                    ret = await _kingdeeApiClient.BatchSaveBarterDisposeBillByDept(ketInput);
                    _easyCaching.Remove(cachekey);
                    return ret;
                }
                else
                {
                    ret.Message = "存在并发操作";
                    ret.Code = CodeStatusEnum.Failed;
                    return ret;
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret.Message = "更换失败，原因：" + ex.Message;
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
        }

        /// <summary>
        /// 暂存更换-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [HttpPost("BatchSaveBarterDisposeBillByStaging")]
        [OperationLog("暂存更换核算部门-暂存调用")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByStaging(List<BatchSaveBarterDisposeBillByStagingInput> inputs)
        {
            var ret = new BaseResponseData<object>();
            //var jsonStr = JsonConvert.SerializeObject(inputs);
            //var cachekey = "batchSaveBarterDisposeBillByStaging_" + inputs[0].BillCode;
            #region 参数组装
            var ketInput = new List<ApBarterDisposeBillModel>();
            if (inputs != null && inputs.Any())
            {

                var allProductNameIds = (inputs.SelectMany(z => z.List).ToList()).Select(z => z.ProductNameId).ToList();
                var allProdcutInfos = await _bDSApiClient.GetProductNameInfoAsync(allProductNameIds);
                foreach (var item in inputs)
                {
                    var ketSingle = new ApBarterDisposeBillModel();
                    ketSingle.billno = item.BillCode;
                    ketSingle.jfzx_remark = item.Remark;
                    ketSingle.jfzx_org = item.CompanyCode;
                    // 通过添加毫秒数来得到对应的DateTime对象
                    DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                    ketSingle.jfzx_bizdate = item.BillDate.HasValue ? unixEpoch.AddMilliseconds(item.BillDate.Value).ToLocalTime() : DateTime.Now;
                    ketSingle.jfzx_type = item.Type;
                    ketSingle.jfzx_remark = item.Remark;
                    var details = new List<ApBarterDisposeEntryModel>();
                    if (item.List != null && item.List.Any())
                    {
                        //var productNameIds = item.List.Select(x => x.ProductNameId).ToList();
                        //var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                        foreach (var detail in item.List)
                        {
                            var thisProductInfo = allProdcutInfos.FirstOrDefault(e => e.productNameId == detail.ProductNameId);
                            var jfzx_material = Guid.Empty;
                            if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                            {
                                jfzx_material = thisProductInfo.classificationNewGuid.Value;
                            }
                            else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                            {
                                jfzx_material = thisProductInfo.classificationGuid.Value;
                            }
                            details.Add(new ApBarterDisposeEntryModel
                            {
                                jfzx_project = detail.ProjectCode,
                                customerId = string.IsNullOrEmpty(detail.CustomerId)?"": detail.CustomerId.ToUpper(),
                                jfzx_project_new = detail.NewProjectCode ??= detail.ProjectCode,
                                jfzx_materialnumber = jfzx_material.ToString().ToUpper(),
                                jfzx_qty = detail.Quantity,
                                jfzx_bizorg = detail.BusinessDepId,
                                jfzx_bizorg_new = detail.NewBusinessDepId,
                                taxrateid_number = detail.TaxRate > 0 ? $"V{detail.TaxRate}".Trim('0').Trim('.') : "V0",
                                jfzx_standard_total_cost = detail.StandardCost * detail.Quantity,
                                jfzx_actual_total_cost = detail.NoRateUnitCost * detail.Quantity,
                                jfzx_supplier = detail.AgentId,
                                jfzx_goodsType = detail.Mark,
                                jfzx_productType = detail.ProductType
                            });
                        }
                        ketSingle.jfzx_materialentity = details;
                    }
                    ketInput.Add(ketSingle);
                }
            }
            #endregion
            return await _kingdeeApiClient.BatchSaveBarterDisposeBillByStaging(ketInput);
            //try
            //{
            //    //var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            //    if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
            //    {
            //        //await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    
            //        //await _easyCaching.RemoveAsync(cachekey);
            //        return ret;
            //    }
            //    else
            //    {
            //        ret.Message = "存在并发操作";
            //        ret.Code = CodeStatusEnum.Failed;
            //        return ret;
            //    }
            //}
            //catch (Exception ex)
            //{
            //    _easyCaching.Remove(cachekey);
            //    ret.Message = "更换失败，原因：" + ex.Message;
            //    ret.Code = CodeStatusEnum.Failed;
            //    return ret;
            //}
        }

        /// <summary>
        /// 查询合同台账-财务端
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("QueryContractBill")]
        [SkipLogging]
        public async Task<BaseResponseData<QueryContractBillByKingdeeOutput>> QueryContractBill(QueryContractBillInput input)
        {
            var ret = new BaseResponseData<QueryContractBillByKingdeeOutput>();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "batchSaveBarterDisposeBillByStaging_" + jsonStr;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    #region 入参组装
                    var ketInput = new QueryContractBillByKingdeeInput();
                    ketInput.PageSize = input.PageSize;
                    ketInput.PageNo = input.PageNo;
                    var ketMainInput = new ContractQueryInput();
                    ketMainInput.CompanyNumber = input.NameCode;
                    ketMainInput.ContractingPartyNumber = input.AgentId.HasValue ? input.AgentId.Value.ToString() : string.Empty;
                    ketInput.Data = ketMainInput;
                    #endregion
                    ret = await _kingdeeApiClient.QueryContractBill(ketInput);
                    _easyCaching.Remove(cachekey);
                    return ret;
                }
                else
                {
                    ret.Message = "存在并发操作";
                    ret.Code = CodeStatusEnum.Failed;
                    return ret;
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret.Message = "查询失败，原因：" + ex.Message;
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
        }

    }
}
