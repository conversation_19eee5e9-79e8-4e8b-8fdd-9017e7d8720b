﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCustomizeInvoiceSubDetailPoFiled3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "PurchaseCode",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(2000)",
                maxLength: 2000,
                nullable: false,
                comment: "顺序号(必填)",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldComment: "顺序号(必填)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "PurchaseCode",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                comment: "顺序号(必填)",
                oldClrType: typeof(string),
                oldType: "nvarchar(2000)",
                oldMaxLength: 2000,
                oldComment: "顺序号(必填)");
        }
    }
}
