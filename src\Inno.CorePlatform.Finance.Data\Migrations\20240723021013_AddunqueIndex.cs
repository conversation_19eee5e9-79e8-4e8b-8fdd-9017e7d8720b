﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddunqueIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "RebateProvisionItem",
                type: "nvarchar(450)",
                nullable: true,
                comment: "单据号",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "单据号");

            migrationBuilder.CreateIndex(
                name: "IX_RecognizeReceiveItem_Code",
                table: "RecognizeReceiveItem",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RebateProvisionItem_BillCode",
                table: "RebateProvisionItem",
                column: "BillCode",
                unique: true,
                filter: "[BillCode] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentAutoItem_Code",
                table: "PaymentAutoItem",
                column: "Code",
                unique: true); 

            migrationBuilder.CreateIndex(
                name: "IX_CustomizeInvoiceItem_Code",
                table: "CustomizeInvoiceItem",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomizeInvoiceClassify_BillCode",
                table: "CustomizeInvoiceClassify",
                column: "BillCode",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_RecognizeReceiveItem_Code",
                table: "RecognizeReceiveItem");

            migrationBuilder.DropIndex(
                name: "IX_RebateProvisionItem_BillCode",
                table: "RebateProvisionItem");

            migrationBuilder.DropIndex(
                name: "IX_PaymentAutoItem_Code",
                table: "PaymentAutoItem");

            migrationBuilder.DropIndex(
                name: "IX_Payment_Code",
                table: "Payment");

            migrationBuilder.DropIndex(
                name: "IX_CustomizeInvoiceItem_Code",
                table: "CustomizeInvoiceItem");

            migrationBuilder.DropIndex(
                name: "IX_CustomizeInvoiceClassify_BillCode",
                table: "CustomizeInvoiceClassify");

            migrationBuilder.AlterColumn<string>(
                name: "BillCode",
                table: "RebateProvisionItem",
                type: "nvarchar(max)",
                nullable: true,
                comment: "单据号",
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true,
                oldComment: "单据号");
        }
    }
}
