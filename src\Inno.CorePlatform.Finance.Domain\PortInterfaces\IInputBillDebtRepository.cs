﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    public interface IInputBillDebtRepository: IRepositorySupportCrudAndUow<InputBillDebt, Guid>
    {
        Task<int> AddManyAsync(List<InputBillDebt> inputBillDebts);
    }
}
