<template>
  <el-dialog v-model="againCreateDialog" title="添加进项发票明细" style="height: 730px" width="1300" destroy-on-close append-to-body :close-on-click-modal="false" draggable @closed="GBhandleClose" align-center>
    <div class="detail-class">
      <div class="crud-box">
          <inno-crud-operation :crud="crudBillDatillAdd"></inno-crud-operation>
        </div>
      <el-tabs v-model="activeName" class="app-page-tabs" @tab-change="search">
        <el-tab-pane label="经销购货入库单" name="1" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <el-table-column prop="businessCode" label="业务单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期" min-width="110">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <!--<el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true" />-->
          <!--<el-table-column prop="companyName" label="公司名称" :show-overflow-tooltip="true" />
  <el-table-column prop="agentName" label="供应商" :show-overflow-tooltip="true" />-->
          <el-table-column prop="productNo" min-width="110" label="货号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductNo" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductId" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="model" sortable label="型号" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.model" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="specification" sortable label="规格" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.specification" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="remainingQuantity" sortable label="数量" min-width="100">
            <template #header>
              <span>
                数量
                <el-tooltip content="数量=原始数量-已入票数量-本单勾稽数量" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="showUseQuantity" sortable label="锁定数量" min-width="100" /> -->
          <!-- <el-table-column prop="showUseQuantity" sortable label="金额" min-width="100" /> -->

          <!-- <el-table-column prop="invoicedQuantity" v-if="activeName !== '3' && activeName !== '5'" sortable label="已入票数量" min-width="140" /> -->
          <!-- <el-table-column prop="availableQuantity" v-if="activeName !== '3' && activeName !== '5'" sortable label="剩余可入票数量" min-width="140" /> -->
          <!-- <el-table-column prop="polyInvoiceQuantity" sortable label="金额" min-width="140" /> -->
          <el-table-column prop="currentMatchQuantity" sortable label="本次入票数量" min-width="150" class-name="isSum">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.currentMatchQuantity"
                @input="(val) => { selectItems(scope.row); updateNormalMatchedAmount(scope.row); }"
                :max="scope.row.remainingQuantity"
                :min="0"
                :controls="false"
                :precision="0"
              />
            </template>
          </el-table-column>
          <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="160" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column  prop="noTaxCost" sortable label="不含税单价(元)" width="150" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column  width="160" prop="purchaseOrderCode" label="采购订单号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.purchaseOrderCode }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="producerName" sortable label="注册人/备案人" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
            </template>
             <template #header="{ column }">
              <inno-header-filter :config="queryObject.producerName" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="contractNo" sortable label="采购合同单号" min-width="200" show-overflow-tooltip/>
          <el-table-column prop="producerOrderNo" sortable label="厂家订单号" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
            </template>
             <!-- <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProducerOrderNo" :crud="crudBillDatillAdd" :column="column" />
            </template> -->
          </el-table-column>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="经销调出" name="4" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <el-table-column prop="businessCode" label="业务单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="purchaseOrderCode" label="采购单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.purchaseOrderCode }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期" min-width="110">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <el-table-column prop="productNo" min-width="110" label="货号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductNo" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductId" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="model" sortable label="型号" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.model" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="specification" sortable label="规格" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.specification" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="remainingQuantity" sortable label="数量" min-width="100">
            <template #header>
              <span>
                数量
                <el-tooltip content="数量=原始数量-已入票数量-本单勾稽数量" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="showUseQuantity" sortable label="锁定数量" min-width="100" /> -->
          <!-- <el-table-column prop="showUseQuantity" sortable label="金额" min-width="100" /> -->

          <!-- <el-table-column prop="invoicedQuantity" v-if="activeName !== '3' && activeName !== '5'" sortable label="已入票数量" min-width="140" /> -->
          <!-- <el-table-column prop="availableQuantity" v-if="activeName !== '3' && activeName !== '5'" sortable label="剩余可入票数量" min-width="140" /> -->
          <!-- <el-table-column prop="polyInvoiceQuantity" sortable label="金额" min-width="140" /> -->
          <el-table-column prop="currentMatchQuantity" sortable label="本次入票数量" min-width="150" class-name="isSum">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.currentMatchQuantity"
                @input="(val) => { selectItems(scope.row); updateNormalMatchedAmount(scope.row); }"
                :max="scope.row.remainingQuantity"
                :min="0"
                :controls="false"
                :precision="0"
              />
            </template>
          </el-table-column>
          <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="160" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" width="150" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <!-- <el-table-column prop="contractNo" sortable label="采购合同单号" min-width="200" show-overflow-tooltip/> -->
          <el-table-column prop="producerName" sortable label="注册人/备案人" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
            </template>
             <template #header="{ column }">
              <inno-header-filter :config="queryObject.producerName" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="寄售转购货" name="2" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <el-table-column prop="businessCode" label="业务单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期" min-width="110">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <!--<el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true" />-->
          <!--<el-table-column prop="companyName" label="公司名称" :show-overflow-tooltip="true" />
  <el-table-column prop="agentName" label="供应商" :show-overflow-tooltip="true" />-->
          <el-table-column prop="productNo" min-width="110" label="货号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductNo" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductId" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="model" sortable label="型号" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.model" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="specification" sortable label="规格" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.specification" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="remainingQuantity" sortable label="数量" min-width="100">
            <template #header>
              <span>
                数量
                <el-tooltip content="数量=原始数量-已入票数量-本单勾稽数量" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="currentMatchQuantity" sortable  label="本次入票数量" min-width="150" class-name="isSum">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.currentMatchQuantity"
                @input="(val) => { selectItems(scope.row); updateNormalMatchedAmount(scope.row); }"
                :max="scope.row.remainingQuantity"
                :min="0"
                :controls="false"
                :precision="0"
              />
            </template>
          </el-table-column>
          <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="160" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" width="150" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          
          <el-table-column prop="producerName" sortable label="注册人/备案人" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
            </template>
             <template #header="{ column }">
              <inno-header-filter :config="queryObject.producerName" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="购货修订" name="5" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <!-- <el-table-column fixed="left" width="55">
      <template #default="scope">
        <inno-table-checkbox :checked="!!scope.row.thisQuantity" />
      </template>
  </el-table-column>-->
          <el-table-column prop="businessCode" label="业务单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期" min-width="110">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <!--<el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true" />-->
          <!--<el-table-column prop="companyName" label="公司名称" :show-overflow-tooltip="true" />
  <el-table-column prop="agentName" label="供应商" :show-overflow-tooltip="true" />-->
          <el-table-column prop="productNo" min-width="110" label="货号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductNo" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductId" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="model" sortable label="型号" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.model" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="specification" sortable label="规格" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.specification" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="businessAmount" sortable label="金额" min-width="120">
            <template #header>
              <span>
                金额
                <el-tooltip content="金额=原始金额-已入票金额-本单勾稽金额" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="invoicedAmount" v-if="activeName === '3' || activeName === '5'" sortable label="已入票金额" min-width="120" /> -->
          <el-table-column prop="currentMatchAmount" sortable label="本次入票金额" width="140">
            <template #default="scope">
                <el-input-number
                  v-model="scope.row.currentMatchAmount"
                  :controls="false"
                  @input="()=>{ selectItems(scope.row); updateMatchedAmount(scope.row)}"
                  @change="checkNumber(scope.row)"
                  @focus.stop="()=>{ }"
                  :precision="4"
                />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="producerName" sortable label="注册人/备案人" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
            </template>
             <template #header="{ column }">
              <inno-header-filter :config="queryObject.producerName" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="服务费" name="3" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <!-- <el-table-column fixed="left" width="55">
      <template #default="scope">
        <inno-table-checkbox :checked="!!scope.row.thisQuantity" />
      </template>
  </el-table-column>-->
          <el-table-column prop="businessCode" label="业务单号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <el-table-column prop="businessAmount" sortable label="金额">
            <template #header>
              <span>
                金额
                <el-tooltip content="金额=原始金额-已入票金额-本单勾稽金额" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="invoicedAmount" v-if="activeName === '3' || activeName === '5'" sortable label="已入票金额" min-width="120" /> -->
          <el-table-column prop="currentMatchAmount" sortable label="本次入票金额">
            <template #default="scope">
                <el-input-number
                  v-model="scope.row.currentMatchAmount"
                  :controls="false"
                  @input="()=>{ selectItems(scope.row); updateMatchedAmount(scope.row)}"
                  @change="checkNumber(scope.row)"
                  @focus.stop="()=>{ }"
                  :precision="4"
                />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" show-overflow-tooltip></el-table-column>
          <el-table-column prop="contractNo" sortable label="采购合同单号" show-overflow-tooltip/>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="换货转退货" name="6" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <el-table-column prop="businessCode" label="业务单号" min-width="140" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期" min-width="110">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <!--<el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true" />-->
          <!--<el-table-column prop="companyName" label="公司名称" :show-overflow-tooltip="true" />
  <el-table-column prop="agentName" label="供应商" :show-overflow-tooltip="true" />-->
          <el-table-column prop="productNo" min-width="110" label="货号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductNo" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" width="120" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.ProductId" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column prop="model" sortable label="型号" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.model" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="specification" sortable label="规格" min-width="100" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.specification" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="remainingQuantity" sortable label="数量" min-width="100">
            <template #header>
              <span>
                数量
                <el-tooltip content="数量=原始数量-已入票数量-本单勾稽数量" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="currentMatchQuantity" sortable  label="本次入票数量" min-width="150" class-name="isSum">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.currentMatchQuantity"
                @input="(val) => { selectItems(scope.row); updateNormalMatchedAmount(scope.row); }"
                :max="scope.row.remainingQuantity"
                :min="0"
                :controls="false"
                :precision="0"
              />
            </template>
          </el-table-column>
          <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="160" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" width="150" class-name="isSumAmount">
            <template #default="scope">
              <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column prop="producerName" sortable label="注册人/备案人" min-width="160" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.producerName }}</inno-button-copy>
            </template>
             <template #header="{ column }">
              <inno-header-filter :config="queryObject.producerName" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>
        <el-tab-pane label="确认损失应付" name="7" lazy>
          <inno-query-operation :crud="crudBillDatillAdd" :query-list.sync="queryList" />
          <el-table ref="tableRefBillDatillAdd"
          v-inno-loading="crudBillDatillAdd.loading"
          highlight-current-row
          border
          stripe
          class="tabTableL-class"
          :data="crudBillDatillAdd.data"
          height="390px"
          show-summary
          :summary-method="getSummaries"
          @row-click="crudBillDatillAdd.rowClick"
          :row-class-name="crudBillDatillAdd.tableRowClassName"
          @selection-change="crudBillDatillAddSelectionChangeHandler"
          :key="tableKey">
          <el-table-column type="selection" fixed="left" width="55" />
          <!-- <el-table-column fixed="left" width="55">
      <template #default="scope">
        <inno-table-checkbox :checked="!!scope.row.thisQuantity" />
      </template>
  </el-table-column>-->
          <el-table-column prop="businessCode" label="业务单号" :show-overflow-tooltip="true">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              <el-tooltip v-if="scope.row.rebate === 1" class="box-item" effect="dark" content="此入库单使用了返利，请记得勾选对应的购货修订到进项票中" placement="top-start">
                <el-icon style="color: red; margin-left: 10px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.purchaseOrderCode" :crud="crudBillDatillAdd" :column="column" />
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="businessDate" label="业务日期">
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.businessDateStart" :crud="crudBillDatillAdd" :column="column" />
            </template>
            <template #default="scope">
              {{
              scope.row.businessDate === null
              ? ''
              : dateFormat(scope.row.businessDate, 'YYYY-MM-DD')
              }}
            </template>
          </el-table-column>
          <el-table-column prop="businessAmount" sortable label="金额">
            <template #header>
              <span>
                金额
                <el-tooltip content="金额=原始金额-已入票金额-本单勾稽金额" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="invoicedAmount" v-if="activeName === '3' || activeName === '5'" sortable label="已入票金额" min-width="120" /> -->
          <el-table-column prop="currentMatchAmount" sortable label="本次入票金额">
            <template #default="scope">
                <el-input-number
                  v-model="scope.row.currentMatchAmount"
                  :controls="false"
                  @input="()=>{ selectItems(scope.row); updateMatchedAmount(scope.row)}"
                  @change="checkNumber(scope.row)"
                  @focus.stop="()=>{ }"
                  :precision="4"
                />
            </template>
          </el-table-column>
          <!-- <el-table-column prop="taxRate" sortable label="税率(%)" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="contractNo" sortable label="采购合同单号" min-width="200" show-overflow-tooltip/> -->
        </el-table>
        </el-tab-pane>
        

        <div class="app-page-footer background">
          已选择
          {{ crudBillDatillAdd.selections.length }}
          条
          <div class="flex-1" />
          <inno-crud-pagination :crud="crudBillDatillAdd" :pageSizes="[10, 20, 50, 100, 200, 500, 1000]" />
        </div>
      </el-tabs>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="GBhandleClose">取 消</el-button>
        <el-button type="primary" @click="save(false)">保 存</el-button>
        <el-button type="primary" @click="save(true)">保存并关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElTable, ElMessage, ElLoading } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { getTaxRateList } from '@/api/financeapi';
import { ref, computed, watch, nextTick } from 'vue';
import {
  numeral,
  asyncNumeral
} from '@inno/inno-mc-vue3/lib/components/numeral';
import { Decimal } from 'decimal.js';
import request from '@/utils/request';
import { selectEmits } from 'element-plus/es/components/select-v2/src/defaults';

const emits = defineEmits(['closeDialog']);
let againCreateDialog = ref(false);
const props = defineProps({
  CompanyId: {
    type: String,
    default: '',
    required: true
  },
  AgentId: {
    type: String,
    default: '',
    required: true
  },
  InputBillId: {
    type: String,
    default: '',
    required: true
  },
  mergeInputBillDetailId: {
    type: String,
    default: '',
    required: true
  },
  showDialog: {
    type: Boolean,
    default: false
  },
  amount: {
    type: Number,
    default: 0
  },
  kingdeeDetails: {
    type: Array,
    required: true
  },
  invoiceInfo: {
    type: Object,
    required: true
  }
});
interface TaxRateItem {
  name: any; // 或其他具体类型
  value: any;
}
const taxRateList = ref<TaxRateItem[]>([]);
const thisinvoiceInfo = ref({});
const activeName = ref('1');
let isRefresh = ref(false);
const tableRefBillDatillAdd = ref<InstanceType<typeof ElTable>>();
const tableKey = ref(0);
const crudBillDatillAdd = CRUD(
  {
    title: '经销购货入库单',
    url: '/api/MergeInputBill/getCacheDetails',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {

    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'cacheDetails',
      total: 'total',
    },
  //   pageConfig: {
  //    pageIndex: 'pageIndex',
  //    pageSize: 'pageSize'
  //  },
    hooks: {
      [CRUD.HOOK.beforeRefresh]: (_crud: any) => {
        if(activeName.value === '1'){
          _crud.resultKey.list = 'cacheDetails.distributionPurchase';
        } else if(activeName.value === '4'){
          _crud.resultKey.list = 'cacheDetails.distributionTransfer';
        } else if(activeName.value === '2'){
          _crud.resultKey.list = 'cacheDetails.consignmentToPurchase';
        } else if(activeName.value === '5'){
          _crud.resultKey.list = 'cacheDetails.purchaseRevision';
        } else if(activeName.value === '3'){
          _crud.resultKey.list = 'cacheDetails.serviceFeeProcurement';
        } else if(activeName.value === '6'){
          _crud.resultKey.list = 'cacheDetails.exchangeToReturn';
        } else if(activeName.value === '7'){
          _crud.resultKey.list = 'cacheDetails.lossRecognition';
        }

        return true;
      },
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        if(_crud.data.length && _crud.data.length > 0){
          let list:any = []
          _crud.data.forEach((element) => {
            if(activeName.value !== '3' && activeName.value  !== '5' && activeName.value  !== '7'){
              if (element.currentMatchQuantity !== 0) {
                tableRefBillDatillAdd.value?.toggleRowSelection(element, true);
                list.push(element);
              }
            }else{
              if (element.currentMatchAmount !== 0) {
                tableRefBillDatillAdd.value?.toggleRowSelection(element, true);
                list.push(element);
              }
            }
          });
          // crudBillDatillAdd.singleSelection(list);
          
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    tablekey: 'tableRefBillDatillAdd'
  },
},
  {
    table: tableRefBillDatillAdd
  }
  
);
const queryList = computed(() => [
  {
    key: 'businessCode',
    label: '业务单号',
    show: true
  },
  {
    key: 'purchaseOrderCode',
    label: '采购单号',
    show: true
  },
  {
    key: 'model',
    label: '型号',
    show: true
  },
  {
    key: 'specification',
    label: '规格',
    show: true
  },
  {
    key: 'productName',
    label: '产品名称',
    show: true
  },
  // {

  {
    key: 'ProductNo',
    label: '货号',
    show: true
  },
  {
    key: 'producerName',
    label: '注册人/备案人',
    show: true
  },
  {
    key: 'businessDateStart',
    endDate: 'businessDateEnd',
    label: '单据日期',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'ProducerOrderNo',
    label: '厂家订单号',
    show: true
  },
  {
    key: 'taxRate',
    label: '税率',
    type: 'select',
    labelK: 'name',
    valueK: 'value',
    dataList: taxRateList.value,
    show: true
  },
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
const GBhandleClose = () => {
  isRefresh.value = false;
  handleClose();

};
const handleClose = () => {
  againCreateDialog.value = false;
  crudBillDatillAdd.selections = [];
  activeName.value = '1';
  emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
  console.log(' againCreateDialog.value', againCreateDialog.value);
};
const amountSign = ref(true);
// watch(
//   () => props.showDialog,
//   (newVulue) => {
//     againCreateDialog.value = newVulue;
//     if (againCreateDialog.value) {
//       crudBillDatillAdd.query.companyId = props.CompanyId;
//       crudBillDatillAdd.query.types = 1;
//       crudBillDatillAdd.query.InputBillId = props.InputBillId;
//       crudBillDatillAdd.query.agentId = props.AgentId;
//       crudBillDatillAdd.query.isStoreIn = props.amount >= 0;
//       if (activeName.value === '经销调出') {
//         crudBillDatillAdd.query.isStoreIn = false;
//       }
//       QueryTable();
//     }
//   }
// );
const activeLabel = ref('经销购货入库单');
const setToggleRowSelection = () => {
  crudBillDatillAdd.data.map((v, index) => {
    if (v.thisQuantity != 0) {
      tableRefBillDatillAdd.value?.toggleRowSelection(v, true);
    }
  });
};
const QueryTable = async () => {
  crudBillDatillAdd.page.page = 1;
  await crudBillDatillAdd.refresh();
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
const checkOpt = (row) => {
  if (+row.thisQuantity == 0) {
    tableRefBillDatillAdd.value?.toggleRowSelection(row, false);
    return;
  }
  tableRefBillDatillAdd.value?.toggleRowSelection(row, true);
};
const checkNumber = async (row) => {
  if (!row.currentMatchAmount || row.currentMatchAmount === '') {
    return;
  }
  if (activeName.value === '3' || activeName.value === '5'  || activeName.value === '7') {
    if ((row.businessAmount < 0)) {
      if(row.currentMatchAmount > 0){
        row.currentMatchAmount = 0;
      }else if(row.currentMatchAmount <  row.businessAmount){
        row.currentMatchAmount = row.businessAmount;
      }
    }else{
      if(row.currentMatchAmount < 0){
        row.currentMatchAmount = 0;
      }else if(row.currentMatchAmount >  row.businessAmount){
        row.currentMatchAmount = row.businessAmount;
      }
    }
      
  }

};

// 更新匹配金额
const updateMatchedAmount = (row) => {
  // 对于服务费和购货修订，匹配金额等于匹配数量（实际上是金额）
  if (activeName.value === '3' || activeName.value === '5' || activeName.value === '7') {
    row.matchedAmount = row.currentMatchAmount;
  }
};

// 更新普通业务类型的匹配金额
const updateNormalMatchedAmount = (row) => {
  // 对于普通业务类型，匹配金额 = 匹配数量 × 含税单价
  if (activeName.value !== '3' && activeName.value !== '5' && activeName.value !== '7' && row.taxCost) {
    row.matchedAmount = row.currentMatchQuantity * row.taxCost;
  }
};

// const crudBillDatillAddSelectionChangeHandler = async (val) => {
//   console.log('选择变更处理器被调用，选中项数量:', val.length);

//   // 记录选中项的ID，用于调试
//   if (val.length > 0) {
//     console.log('选中项ID:', val.map(v => v.id).join(', '));
//   }

//   // 如果只选中了一项，为其设置默认值
//   if (val.length === 1) {
//     const selectedItem = val[0];

//     // 设置默认匹配数量/金额（如果当前为0）
//     if ((activeName.value !== '3' && activeName.value !== '5') && selectedItem.currentMatchQuantity == 0) {
//       // 普通业务类型，设置默认匹配数量
//       selectedItem.currentMatchQuantity = selectedItem.remainingQuantity - selectedItem.invoicedQuantity;
//     }

//     if ((activeName.value === '3' || activeName.value === '5') && selectedItem.currentMatchAmount == 0) {
//       // 服务费或购货修订，设置默认匹配金额
//       selectedItem.currentMatchAmount = selectedItem.businessAmount - selectedItem.invoicedAmount;
//     }

//     // 计算匹配金额
//     if ((activeName.value === '3' || activeName.value === '5') && selectedItem.currentMatchAmount > 0) {
//       // 对于服务费和购货修订，匹配金额等于匹配数量（实际上是金额）
//       selectedItem.matchedAmount = selectedItem.currentMatchAmount;
//     } else if (activeName.value !== '3' && activeName.value !== '5' && selectedItem.taxCost && selectedItem.currentMatchQuantity > 0) {
//       // 对于普通业务类型，匹配金额 = 匹配数量 × 含税单价
//       selectedItem.matchedAmount = selectedItem.currentMatchQuantity * selectedItem.taxCost;
//     }
//   }

//   // 调用CRUD的选择变更处理器
//   crudBillDatillAdd.selectionChangeHandler(val);
// };
const crudBillDatillAddSelectionChangeHandler = async (val) => {
  console.log('crudBillDatillAddSelectionChangeHandler',val)
  let valIds = val.map((v) => {
    return v.matchKey;
  });
  crudBillDatillAdd.data.forEach((item) => {
    if (valIds.indexOf(item.matchKey) > -1) {
      if ((activeName.value !== '3' && activeName.value !== '5' && activeName.value !== '7') && item.currentMatchQuantity === 0) {
        
        //如果是购货修订，用绝对值减
        item.currentMatchQuantity = item.remainingQuantity;
        item.isCheckd = false;
      }
      if ((activeName.value === '3' || activeName.value === '5' || activeName.value === '7') && item.currentMatchAmount === 0) {
        //如果是购货修订，用绝对值减
        item.currentMatchAmount = item.businessAmount;
        item.isCheckd = false;
      }
      // 初始化匹配金额
      if ((activeName.value === '3' || activeName.value === '5' || activeName.value === '7') && item.currentMatchAmount > 0) {
        // 对于服务费和购货修订，匹配金额等于匹配数量（实际上是金额）
        item.matchedAmount = item.currentMatchAmount;
      } else if (activeName.value !== '3' && activeName.value !== '5' && activeName.value !== '7' && item.taxCost && item.currentMatchQuantity > 0) {
        // 对于普通业务类型，匹配金额 = 匹配数量 × 含税单价
        item.matchedAmount = item.currentMatchQuantity * item.taxCost;
      }
    }
  });
  crudBillDatillAdd.selectionChangeHandler(val);
};

const save = async (isColse) => {
  // 记录当前选中项数量，用于调试
  console.log('保存方法被调用，当前选中项数量:', crudBillDatillAdd.selections.length);

  if(crudBillDatillAdd.selections.length === 0){
    ElMessage({
      showClose: true,
      message: '请选择一条数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }

  // 记录选中项的ID，用于调试
  console.log('选中项ID:', crudBillDatillAdd.selections.map(item => item.id).join(', '));

  if(activeName.value !== '3' && activeName.value !== '5' && activeName.value !== '7'){
    let quantityItems = crudBillDatillAdd.selections.find(el=> Number(el.currentMatchQuantity) === 0);
    if(quantityItems ){
      ElMessage({
        showClose: true,
        message: '本次入票数量不能为0！',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    }
  }else{
    let amountItems = crudBillDatillAdd.selections.find(el=> Number(el.currentMatchAmount) === 0);
    if(amountItems ){
      ElMessage({
        showClose: true,
        message: '金额不能为0！',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    }
  }

  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  let goInputBillId = props.InputBillId;
  let matchDetails:any = []

  // 确保只处理用户选择的记录
  // 如果选中多条记录，记录警告日志
  if(crudBillDatillAdd.selections.length > 1) {
    console.warn('警告：选中了多条记录，数量:', crudBillDatillAdd.selections.length);
  }

  // 处理选中的记录
  crudBillDatillAdd.selections.forEach((item:any) => {
    console.log('处理选中项:', item.id, '业务单号:', item.businessCode);

    // 设置必要的属性
    item.mergeInputBillDetailId = props.mergeInputBillDetailId;
    item.businessType = activeName.value;

    // 确保服务费和购货修订的匹配金额正确设置
    if (activeName.value === '3' || activeName.value === '5' || activeName.value === '7') {
      item.matchedAmount = item.currentMatchAmount;
    } else if (item.taxCost) {
      // 对于其他业务类型，匹配金额 = 匹配数量 × 含税单价
      item.matchedAmount = item.currentMatchQuantity * item.taxCost;
    }

    matchDetails.push(item);
  });

  // 记录最终要提交的明细数量
  console.log('最终要提交的明细数量:', matchDetails.length);

  // 构建请求数据
  let postData = {
    mergeInputBillId: props.InputBillId,
    matchDetails
  };
  request({
      url: '/api/MergeInputBill/saveMatch',
      data:postData,
      method: 'post'
    })
    .then((res) => {

      if (res.data.code == 200) {
        loading.close();
      if (!isColse) {
        QueryTable();
      } else {
        isRefresh.value = true;
        handleClose();
      }
        ElMessage({
          showClose: true,
          message: '操作成功！',
          type: 'success',
          duration: 3 * 1000
        });
      }else{
        loading.close();
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      console.log(err);
      loading.close();
    });
};
const getAddList = (id:any) => {
  crudBillDatillAdd.query.mergeInputBillId = id;
  crudBillDatillAdd.query.businessType = '1';
  crudBillDatillAdd.data = [];
  crudBillDatillAdd.toQuery();
  getTaxRate()
  againCreateDialog.value = true;
};
const selectItems = (item:any) =>{
  if(item){
    // 清除之前的所有选择
    crudBillDatillAdd.data.forEach(row => {
      if(row.id !== item.id) {
        tableRefBillDatillAdd.value?.toggleRowSelection(row, false);
      }
    });
    tableRefBillDatillAdd.value?.toggleRowSelection(item, true);
  }
}
defineExpose({
  isRefresh,
  getAddList
});
const search = (val:any) => {
  crudBillDatillAdd.query = {};
  crudBillDatillAdd.query.businessType = val;
  // 重置查询条件
  crudBillDatillAdd.query.mergeInputBillId = props.InputBillId;
  crudBillDatillAdd.data = [];
  crudBillDatillAdd.toQuery();
};

// 处理行点击事件
const handleRowClick = (row) => {
  console.log('行点击事件触发，行ID:', row.id);

  // 清除所有选择
  crudBillDatillAdd.data.forEach(item => {
    tableRefBillDatillAdd.value?.toggleRowSelection(item, false);
  });

  // 只选择当前行
  tableRefBillDatillAdd.value?.toggleRowSelection(row, true);

  // 如果是普通业务类型且匹配数量为0，设置默认值
  if ((activeName.value !== '3' && activeName.value !== '5' && activeName.value !== '7') && row.currentMatchQuantity == 0) {
    row.currentMatchQuantity = row.remainingQuantity;
    // 更新匹配金额
    if (row.taxCost) {
      row.matchedAmount = row.currentMatchQuantity * row.taxCost;
    }
  }

  // 如果是服务费或购货修订且匹配金额为0，设置默认值
  if ((activeName.value === '3' || activeName.value === '5' || activeName.value === '7') && row.currentMatchAmount == 0) {
    row.currentMatchAmount = row.businessAmount;
    row.matchedAmount = row.currentMatchAmount;
  }
};
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param;
    const sums: string[] = [];
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '总合计';
        return;
      }
        // 如果没有后端返回的合计数据，使用前端计算
        if (column.property == 'currentMatchQuantity') {
          const values = crudBillDatillAdd.selections.map((item) => item.currentMatchQuantity || 0);
          const total = values.reduce(
            (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
            new Decimal(0)
          );
          sums[index] = total;
        } else if (column.property == 'currentMatchAmount') {
          const values = crudBillDatillAdd.selections.map((item) => item.currentMatchAmount || 0);
          const total = values.reduce(
            (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
            new Decimal(0)
          );
          sums[index] = total;
        } else if (column.property == 'remainingQuantity') {
          const values = data.map((item) => item.remainingQuantity || 0);
          const total = values.reduce(
            (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
            new Decimal(0)
          );
          sums[index] = total;
        } 
        // else if (column.property == 'businessAmount') {
        //   const values = crudBillDatillAdd.selections.map((item) => item.businessAmount || 0);
        //   const total = values.reduce(
        //     (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
        //     new Decimal(0)
        //   );
        //   sums[index] = total;
        // } 
        else if (column.property == 'taxCost' && activeName.value !== '5') {
          const values = crudBillDatillAdd.selections.map((item) => (new Decimal(item.taxCost).mul(new Decimal(item.currentMatchQuantity))));
          // const values = data.map((item) => item.noTaxAmount || 0);
          const total = values.reduce(
            (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
            new Decimal(0)
          );
          sums[index] = asyncNumeral(total, '0,0.0000');
        } else {
          sums[index] = '';
        }
    });
    return sums;
  };
  // 获取税率字典值
  const getTaxRate = async() => {
    const type = 'StandardTaxRate';
    taxRateList.value.splice(0);
    await getTaxRateList(type).then((res: any) => {
      if (res.data.code === 200) {
        // list = res.data.data;
        res.data.data.map((item: any) => {
          if (item.depth > 1) {
            taxRateList.value.push({
              value: item.dictionaryName,
              name: item.dictionaryName
            });
          }
        });
      }
    });
  }
</script>
<style scoped>
.detail-class {
  position: relative;
  display: flex;
  flex: 8;
  max-height: 650px;
  min-height: 400px;
  padding: 5px;
}
.tabTableL-class {
  margin-top: 10px;
}
.desc-text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 165px;
}
:deep(.my-label) {
  min-width:100px !important;
}
.remark-box{
  max-width: 520px;
  overflow: hidden; /* 隐藏溢出内容 */
  white-space: nowrap; /* 防止文本换行 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.crud-box{
  position: absolute;
  right: 6px;
  top: 8px;
  z-index: 999999999999999999999999;
}
</style>
