﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addAdvanceFundBusinessCheckDetailAgentName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "Rate",
                table: "AdvancePaymentDebtDetail",
                type: "decimal(18,18)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AgentName",
                table: "AdvanceFundBusinessCheckDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BatchpaymentCode",
                table: "AdvanceFundBusinessCheckDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AgentName",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "BatchpaymentCode",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.AlterColumn<decimal>(
                name: "Rate",
                table: "AdvancePaymentDebtDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,18)",
                oldNullable: true);
        }
    }
}
