import request from '@/utils/request';

// 获取公司系统月度
export function getCompanySysMonth(id: string) {
    const url = `api/bff/GetSysmonth?companyId=${id}`;
    return request({
      url,
      method: 'get'
    });
}
// 获取应收列表
export function GetDetailsInfo(data: any) {
    const url = `/api/AdvancePaymentQuery/GetDetailsInfo`;
    return request({
      url,
      method: 'post',
      data
    });
}
// 获取应付列表
export function GetDebtByCreditIds(data: any) {
    const url = `/api/LossRecognitionQuery/GetDebtByCreditIds`;
    return request({
      url,
      method: 'post',
      data
    });
}
// 创建接口
export function create(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/Save',
    method: 'post',
    data
  });
}
// 提交
export function submit(data: any) {
  const url = `/api/AdvancePaymentQuery/SubmitOA`;
  return request({
    url,
    method: 'post',
    data
  });
}
// 对接获取详情接口
export function getItemById(data: any) {
  return request({
    url: `/api/AdvancePaymentQuery/GetItemById`,
    method: 'post',
    data
  });
}
// 获取明细
export function GetProductDetails(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/GetProductDetails',
    method: 'post',
    data
  });
}
// 获取付款计划明细
export function GetDebtDetails(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/GetDebtDetails',
    method: 'post',
    data
  });
}

// 删除付款计划明细
export function DeleteAdvancePaymentDebtDetails(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/DeleteAdvancePaymentDebtDetails',
    method: 'post',
    data
  });
}
// 分摊到货
export function AllocateToGoods(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/AllocateToGoods',
    method: 'post',
    data
  });
}
// 分摊到货
export function ReSetDate(data: any) {
  return request({
    url: '/api/AdvancePaymentQuery/ReSetDate',
    method: 'post',
    data
  });
}
