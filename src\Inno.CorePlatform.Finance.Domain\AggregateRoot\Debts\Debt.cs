﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot
{
    public class Debt : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public Debt() { }
        /// <summary>
        /// 冲销状态
        /// </summary>  
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 单号
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 应付类型
        /// </summary>
        public DebtTypeEnum? DebtType { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public InvoiceStatusEnum? InvoiceStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>

        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }

        /// <summary>
        /// 应付值
        /// </summary> 
        public decimal Value { get; set; }
        /// <summary>
        /// 是否自动批量
        /// </summary>
        public int? Auto { get; set; }
        /// <summary>
        /// 自动批量类型
        /// </summary> 
        public string? AutoType { get; set; }

        /// <summary>
        /// 自动批量类型名称
        /// </summary>
        [MaxLength(100)]
        public string? AutoTypeName { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>    
        public string? RelateCode { get; set; }

        public Guid? PaymentId { get; set; }

        public string? AccountPeriodScale { get; set; }


        public string? PerPaymentCodes { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary> 
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary> 
        public string? OrderNo { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary> 
        public string? ProjectCode { get; set; }

        public virtual List<DebtDetail> DebtDetails { get; set; }
        /// <summary>
        /// 币种代码 
        /// </summary>

        public string? CoinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>

        public string? CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary> 
        public decimal? RMBAmount { get; set; }

        public string? PurchaseContactNo { get; set; }
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        [MaxLength(500)]
        public string? CustomerName { get; set; }

        /// <summary>
        ///  是否内部供应商(2:建发集团内部客户（非建发致新内部）12:建发致新内部客户（全资）11:建发致新内部客户（合资）)，0/null=外部供应商
        /// </summary>
        public int? IsInnerAgent { get; set; }

        /// <summary>
        ///  损失供应商承担金额
        /// </summary> 
        public decimal? LossAgentBearValue { get; set; }
        /// <summary>
        /// 返利单号
        /// </summary> 
        public string? RebateNo { get; set; }
        /// <summary>
        /// 修订范围
        /// </summary>
        public ReviseRangeEnum? ReviseRange { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        [Comment("原始订单号")]
        [MaxLength(200)]
        public string? OriginOrderNo { get; set; }

        public int? Mark { get; set; }

        /// <summary>
        /// 是否内部交易
        /// </summary>
        public bool? IsInternalTransactions { get; set; } = false;
    }
}
