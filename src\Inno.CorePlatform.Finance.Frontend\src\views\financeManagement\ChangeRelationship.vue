<template>
  <el-dialog v-model="createDialog" title="调整发票应收关系" style="height: auto" width="90%" destroy-on-close close-on-click-modal="false" draggable @closed="handleClose">
    <inno-query-operation v-model:query-list="queryList" :crud="crud" style="margin-bottom: 10px;margin-top: -8px;"/>
    <el-table
        ref="tableRef0"
        v-inno-loading="crud.loading"
        class="auto-layout-table"
        highlight-current-row
        border
        :data="crud.data"
        show-summary
        :summary-method="getSummaries"
        stripe
        :row-class-name="crud.tableRowClassName"
        height="450"
        @sort-change="crud.sortChange"
        @selection-change="crud.selectionChangeHandler"
        @row-click="crud.singleSelection"
      >
      <el-table-column type="selection" fixed="left" width="55"></el-table-column>
      <el-table-column label="应收单号" property="billCode" min-width="220" fixed="left" show-overflow-tooltip sortable>
        <template #header="{ column }">
          <inno-header-filter :config="queryObject.newBillCode" :crud="crud" :column="column" isInput />
        </template>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="单据日期" property="billDate" width="90" show-overflow-tooltip sortable>
        <template #default="scope">
          {{
          scope.row.billDate === null
          ? ''
          : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
          }}
        </template>
      </el-table-column>
      <el-table-column label="金额" property="value" class-name="isSum" sortable width="120" show-overflow-tooltip>
        <template #default="scope">
          <inno-numeral :value="scope.row.value" format="0,0.00" />
        </template>
      </el-table-column>
      <el-table-column label="应收类型" property="service" width="125" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="关联单号" property="relateCode" min-width="200" show-overflow-tooltip sortable>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="订单号" property="orderNo" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="公司" property="company" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="客户" min-width="200" property="customer" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="销售子系统" property="saleSystemName" width="110" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="终端医院" min-width="200" property="hospitalName" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.hospitalName }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column label="业务单元" property="service" width="125" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
        </template>
      </el-table-column>

      
      
      <!-- <el-table-column label="部门" property="deptName" show-overflow-tooltip sortable>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.deptName }}</inno-button-copy>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="核算部门" property="businessDeptFullName" width="120" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="项目名称" property="projectName" width="120" show-overflow-tooltip>
        <template #default="scope">
          <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column class-name="isSum" label="已冲销" property="abatmentAmount" width="120" show-overflow-tooltip>
        <template #default="scope">
          <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
        </template>
      </el-table-column>
      <el-table-column class-name="isSum" label="余额" property="leftAmount" width="80" show-overflow-tooltip>
        <template #default="scope">
          <inno-numeral v-if="scope.row.value < 0 && scope.row.leftAmount > 0" :value="-scope.row.leftAmount" format="0,0.00" />
          <inno-numeral v-else :value="scope.row.leftAmount" format="0,0.00" />
        </template>
      </el-table-column>
      <el-table-column label="确认收入" property="isSureIncome" width="95" show-overflow-tooltip>
        <template #default="scope">{{ scope.row.isSureIncome === 1 ? '是' : '否' }}</template>
      </el-table-column>

      <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>
        <template #default="scope">{{ scope.row.createdByName }}</template>
      </el-table-column> -->
    </el-table>
    <div class="app-page-footer background">
      已选择 {{ crud.selections.length }} 条
      <div class="flex-1" />
      <inno-crud-pagination :crud="crud" />
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="debouncedSave()">保 存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElTable, ElMessage, ElMessageBox } from 'element-plus';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import request from '@/utils/request';
const emits = defineEmits(['closeDialog']);
import { ref, computed, watch, nextTick } from 'vue';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { Decimal } from 'decimal.js';
import { debounce } from 'lodash-es'; // 引入 lodash-es 的防抖函数
import {
  whetherGroup,
  DebtTypeEnum,
  CreditTypeEnum,
  AbatedStatus,
  InvoiceStatus
} from '@/api/metaInfo';
let createDialog = ref(false);
const props = defineProps({
  invoiceNo: {
    type: String,
    default: '',
    required: true
  },
  oldCreditBillCode: {
    type: String,
    default: '',
    required: true
  },
  companyId: {
    type: String,
    default: '',
    required: true
  },
  customerId: {
    type: String,
    default: '',
    required: true
  },
  creditAmount: {
    type: Number,
    default: 0,
    required: true
  },
  showDialog: {
    type: Boolean,
    default: false
  }
});
watch(
  () => props.showDialog,
  (newVulue) => {
    createDialog.value = newVulue;
    if (createDialog.value) {
      crud.query.companyId = props.companyId;
      crud.query.customerId = props.customerId;
      crud.query.creditAmount = props.creditAmount;
      crud.query.billCode = props.oldCreditBillCode;
      crud.query.invoiceNo = props.invoiceNo;
      crud.toQuery();
    }
  }
);
const handleClose = () => {
  createDialog.value = false;
  emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
  console.log(' createDialog.value', createDialog.value);
};
const tableRef0 = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '应收单',
    url: '/api/CreditQuery/GetCompliantRelationshipList',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: { },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    props:{
      searchToggle:true
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        // if (crud.data.length && crud.data.length > 0) {
        //   crud.singleSelection(crud.data[0]);
        // }
      }
    },
    optShow: {
      exportCurrentPage: false
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0
  }
);

// 保存
const save = () => {
  if(crud.selections.length === 0){
    ElMessage({
      showClose: true,
      message: '请至少选择一条数据！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  ElMessageBox.confirm('此操作将调整发票对应应收数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    crud.loading = true;
    saveLoading.value = true;
    request({
      url: '/api/InvoiceQuery/ChangeRelationship',
      method: 'POST',
      data: {
        invoiceNo: props.invoiceNo,
        newCreditBillCodes: crud.selections.map(x=>x.billCode),
        oldCreditBillCode: props.oldCreditBillCode,
      }
    }).then((res) => {
      if (res.data.code === 200) {
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: '操作成功!',
          type: 'success',
          duration: 3 * 1000
        });
        crud.loading = false;
        saveLoading.value = false;
        handleClose();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        crud.loading = false;
        saveLoading.value = false;
      }
    });
  });
}

// 创建防抖函数，注意这里只是定义了防抖函数，并没有调用它
const debouncedSave = debounce(save, 300);
const saveLoading = ref(false);
//高级检索
const queryList = computed(() => {
  return [
    {
      key: 'newBillCode',
      label: '应收单号',
      show: true
    },
    {
      key: 'orderNo',
      label: '订单号',
      show: true
    },
    {
      key: 'creditTypes',
      label: '应收类型',
      type: 'select',
      multiple: true,
      labelK: 'name',
      valueK: 'id',
      dataList: CreditTypeEnum,
      show: true
    },
  ]
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//合计
interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'value') {
      const values = crud.selections.map((item) => item.value || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
  });
  return sums;
};
</script>