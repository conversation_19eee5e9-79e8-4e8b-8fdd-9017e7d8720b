﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice
{
    public class InvoiceCredit : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary>
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }


        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary> 
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 税额
        /// </summary> 
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 应收单金额
        /// </summary>  
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string Type { get; set; }

        /// <summary>
        /// 相关联应收单Id
        /// </summary> 
        public Guid? CreditId { get; set; }

        /// <summary>
        /// 运营制作开票Code
        /// </summary>   
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        public Credit? Credit { get; set; }
        /// <summary>
        /// 是否取消
        /// </summary> 
        public bool? IsCancel { get; set; }
        /// <summary>
        /// 是否发票入账（1=是，反之不是）
        /// </summary>
        public int? IsInvoiceReceipt { get; set; }

        /// <summary>
        /// 标记
        /// </summary>
        public InvoiceCreditMarkEnum? Mark { get; set; }
    }
}
