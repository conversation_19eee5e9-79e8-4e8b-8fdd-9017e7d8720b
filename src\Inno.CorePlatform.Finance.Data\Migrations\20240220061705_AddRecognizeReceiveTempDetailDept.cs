﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRecognizeReceiveTempDetailDept : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullName",
                table: "RecognizeReceiveTempDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "核算部门名称路径");

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullPath",
                table: "RecognizeReceiveTempDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "核算部门Id路径");

            migrationBuilder.AddColumn<int>(
                name: "BusinessDeptId",
                table: "RecognizeReceiveTempDetail",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BusinessDeptFullName",
                table: "RecognizeReceiveTempDetail");

            migrationBuilder.DropColumn(
                name: "BusinessDeptFullPath",
                table: "RecognizeReceiveTempDetail");

            migrationBuilder.DropColumn(
                name: "BusinessDeptId",
                table: "RecognizeReceiveTempDetail");
        }
    }
}
