<template>
  <div class="app-page-container">
    <!-- <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>收款认领</el-breadcrumb-item>
      </el-breadcrumb>
    </div>-->
    <div class="app-page-body" style="padding-top: 0">
      <el-tabs v-model="activeName" class="app-page-tabs" @tab-change="tabhandleClick">
        <el-tab-pane label="认款单" name="recognize">
          <inno-query-operation v-model:query-list="queryList" :crud="crud" />
          <inno-split-pane split="horizontal" :min-percent="40" :default-percent="60" style="padding: 0px">
            <template #paneL="{ full, onFull }">
              <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 45px">
                <template #opts-left>
                  <el-tabs v-model="crud.query.abatedStatus" class="demo-tabs" @tab-change="tabhandleChildClick">
                    <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                    <el-tab-pane :label="`审批中(${tabCount.waitExecuteCount})`" name="1" lazy />
                    <el-tab-pane :label="`已完成(${tabCount.completedCount})`" name="99" lazy />
                    <el-tab-pane :label="`部分撤销(${tabCount.partCancel})`" name="-2" lazy />
                    <el-tab-pane :label="`已撤销(${tabCount.auditingCount})`" name="-1" lazy />
                    <el-tab-pane :label="`全部(${tabCount.allCount})`" name lazy />
                  </el-tabs>
                </template>
                <template #default>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip v-if="crud.rowData.status === 0 && hasPermission(functionUris.add)"
                                       content="请至少选择一条数据"
                                       class="filter-item"
                                       type="primary"
                                       :loading="crud.delAllLoading"
                                       :disabled="crud.selections.length === 0"
                                       @click="openAttachment(crud.rowData)">附件</inno-button-tooltip>
                  <inno-button-tooltip 
                   type="primary"
                   :loading="exportBillLoading"
                   @click="exportBill()">导出数据</inno-button-tooltip>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table ref="tableItem"
                        v-inno-loading="crud.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        
                        :data="crud.data"
                        stripe
                        fit
                        border
                        show-summary
                        :summary-method="getSummaries"
                        :row-class-name="crud.tableRowClassName"
                        @sort-change="crud.sortChange"
                        @selection-change="crud.selectionChangeHandler"
                        @row-click="
                        (e)=>
                {
                crud.singleSelection(e);
                getDetailData(e);
                }
                "
                >
                <el-table-column fixed="left" width="45">
                  <template #default="scope">
                    <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
                  </template>
                </el-table-column>
                <el-table-column label="认款单号" property="code" min-width="200" show-overflow-tooltip sortable>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.code" :crud="crud" :column="column" isInput />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="收款单号" property="receiveCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="暂收款单号" property="relateCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" property="value" label="本次认款金额" min-width="90" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" property="receiveValue" label="收款金额" min-width="90" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.receiveValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="收款类型" property="type" show-overflow-tooltip></el-table-column>
                <el-table-column label="核算部门" property="businessDeptFullName" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="公司" property="companyName" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="客户" property="customerNme" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.customerNme }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="收款日期" min-width="120" property="receiveDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.receiveDate, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="银行类型"
                                 property="bankName"
                                 show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.bankName }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="银行账户"
                                 property="bankNum"
                                 show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.bankNum }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="结算方式" property="settletype" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.settletype }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="到期日" min-width="120" property="draftBillExpireDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    dateFormat(scope.row.draftBillExpireDate, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="项目名称" property="projectName" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="项目编码" property="projectCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column property="statusDescription" label="状态" width="80" />
                <el-table-column property="classifyDescription" label="类型" width="80" />
                <el-table-column label="创建日期" width="120" property="createdTime" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="创建人" property="createdByName" width="90" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
                  </template>
                  <template #default="scope">
                    {{ scope.row.createdByName }}
                  </template>
                </el-table-column>
                <el-table-column label="附件" property="attachFileIds" width="130" show-overflow-tooltip>
                  <template #default="scope">
                    <el-link style="font-size: 13px"
                             @click="
                        showAttachFile(scope.row.attachFileIds, scope.row.id)
                      ">{{ scope.row.attachFileIds ? '查看附件' : '' }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70" fixed="right">
                  <template #default="scope">
                    <el-link v-if="scope.row.receiveCode" style="font-size: 12px" type="primary" @click="downloadFile(scope.row.receiveCode)">回执单</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crud.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crud" />
              </div>
            </template>
            <template #paneR="{ full, onFull }">
              <inno-crud-operation :crud="crudDetail" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs v-model="setDetailTab" class="demo-tabs">
                    <el-tab-pane :label="`明细信息`" name="-1"></el-tab-pane>
                  </el-tabs>
                </template>
                <template v-if="!crud.query?.id" #default>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table id="tableDetail"
                        ref="tableDetail"
                        v-inno-loading="crudDetail.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        
                        :data="crudDetail.data"
                        stripe
                        fit
                        border
                        :row-class-name="crudDetail.tableRowClassName"
                        @sort-change="crudDetail.sortChange"
                        @selection-change="crudDetail.selectionChangeHandler"
                        @row-click="(e) => clickdetailRow(e)">
                <el-table-column type="selection" fixed="left" width="55" />
                <!-- 认款明细应收详情 -->
                <!-- 新增用于显示带展开折叠功能的子表格的列 -->
                <el-table-column type="expand" fixed="left">
                  <template #default="{ row }">
                    <div style="padding-left: 54px" v-if="row.typeDescription !== '初始应收'">
                      <el-table
                        :data="row.creditInfo"
                        class="auto-layout-table"
                        stripe
                        border
                        fit
                      >
                        <el-table-column label="应收单号" property="billCode" width="240" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>  
                          </template>
                        </el-table-column>
                        <el-table-column label="应收类型" property="creditTypeStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>  
                          </template>
                        </el-table-column>
                        <el-table-column label="单据日期" property="billDateStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billDateStr }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.orderNo }}</template>
                        </el-table-column>
                        <el-table-column label="项目" property="projectName"  show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.projectName }}</template>
                        </el-table-column>
                        <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.serviceName }}</template>
                        </el-table-column>
                        <el-table-column label="金额" property="value" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.value }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="本次认款金额" property="currentValue" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.currentValue }}</inno-button-copy>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="发票号/订单号/应收单号" property="code" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="认款人" property="createdByName" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="classifyType === 1" label="实际客户" property="customerNme" show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">{{ scope.row.customerNme }}</div>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1"
                                 label="终端客户"
                                 property="hospitalName"
                                 width="140"
                                 show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">
                      {{ scope.row.hospitalName }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="认款类型" property="typeDescription" show-overflow-tooltip></el-table-column>

                <el-table-column v-if="classifyType === 2" label="项目名称" property="projectName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="项目单号" property="projectCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="认款金额" property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="认款日期" min-width="120" property="recognizeDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.recognizeDate, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <!-- <el-table-column
    label="业务单元"
    min-width="120"
    property="serviceName"
    show-overflow-tooltip
  >
    <template #default="scope">
      <inno-button-copy :link="false">
        {{ scope.row.serviceName }}
      </inno-button-copy>
    </template>
  </el-table-column>-->
                <el-table-column v-if="classifyType === 1" label="是否跳号" property="isSkip" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.isSkip ? '是' : '否' }}
                  </template>
                </el-table-column>
                <el-table-column label="细分类型" property="classifyDescription" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.classifyDescription }}
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="收款类型" property="collectionType" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    options.filter(
                    (p) => p.value === scope.row.collectionType + ''
                    ) === null
                    ? ''
                    : options.filter(
                    (p) => p.value === scope.row.collectionType + ''
                    )[0].label
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="备注" property="note" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.note }}
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="发票号/订单号/应收单号日期" property="codeTime" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.codeTime }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="票面金额" property="invoiceAmount" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                认款金额合计：
                <inno-numeral :value="totaldetail(crudDetail.data)" format="0,0.00" />
                <div class="flex-1" />
              </div>
            </template>
          </inno-split-pane>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog v-model="DeptDialogVisible" title="请选择核算部门">
      <el-form>
        <el-form-item label="核算部门">
          <inno-department-select
            v-model="newDepart.id"
            v-model:name="newDepart.name"
            v-model:path="newDepart.path"
            v-model:fullName="newDepart.fullName"
            v-model:item="newDepart.item"
            functionUri="metadata://fam"
          ></inno-department-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="DeptDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createOpt">创建</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="comfile_upload" title="上传附件" destroy-on-close :close-on-click-modal="false" draggable>
      <inno-file-uploader
        v-model="flieList"
        list-type="text"
        drag
        multiple
        bizType="finance"
        fileMode="large"
        appId="fam"
        :limitType="[
          'doc',
          'docx',
          'pdf',
          'xls',
          'xlsx',
          'png',
          'jpg',
          'jpeg',
          'gif'
        ]"
        :folders="folders"
        :beforeUpload="fileBeforeUpload"
        :on-success="fileOnSuccess"
        style="display: flex; justify-content: center"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点此上传文件</em>
        </div>
        <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template>-->
      </inno-file-uploader>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosefile">取消</el-button>
          <el-button type="primary" @click="savefile">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 文件查看 -->
    <el-dialog v-model="comfile_show" title="附件查看" destroy-on-close :close-on-click-modal="false" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小(b)" />
        <el-table-column label="操作">
          <template #default="scope">
            <span @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <RecognizeSelectComponent
      ref="RecognizeSelectRef"
      :showDialog="receiveSelDialogShow"
      :createdType="createdType"
      :paymentAutoItemId="selectId"
      @closeDialog="closeDialogCallBack"
      @refreshIndex="refreshIndexCallBack"
    ></RecognizeSelectComponent>
    <RecognizeSureTypeComponent
      ref="RecognizeSureTypeRef"
      :showDialog="receivesureTypeDialogShow"
      :createdType="createdType"
      :paymentAutoItemId="selectId"
      @closeDialog="closeDialogCallBack"
      @refreshIndex="refreshIndexCallBack"
    ></RecognizeSureTypeComponent>
    <RecognizeCreatedComponent
      ref="RecognizeCreatedRef"
      :showDialog="createdDialogShow"
      :itemId="selectId"
      :detailId="crudDetail.rowData.id"
      :isedit="createdType"
      :classifyType="classifyType"
      @closeDialog="closeCreatedDialogCallBack"
      @refreshIndex="refreshDetailCallBack"
    ></RecognizeCreatedComponent>
    <RecognizeImportComponent :showDialog="importDialogShow" :itemId="selectId" @closeDialog="closeImportDialogCallBack" @refreshIndex="refreshIndexCallBack"></RecognizeImportComponent>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';
import comfile from '@/component/com-files.vue';
import { backendUrl, gatewayUrl } from '@/public-path';
import { ElTable, ElLoading } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { getTreeList, getDepartTree } from '@/api/bdsData';
import RecognizeSelectComponent from '@/views/recognizeReceive/selected.vue';
import RecognizeSureTypeComponent from '@/views/recognizeReceive/sureType.vue';
import RecognizeCreatedComponent from '@/views/recognizeReceive/created.vue';
import RecognizeImportComponent from '@/views/recognizeReceive/import.vue';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
const setDetailTab = '-1';
let files = [];
const projectItem = ref({});
const RecognizeCreatedRef = ref();
let dataListType = reactive([]);
let selectId = ref('');
let comfileShow = ref(false);
let businessDeptsIdstr = ref('');
let companyId = ref('');
const currentUser = useUserStore();
// let tabCount = ref({
//   //已撤销
//   revokedCount: 0,
//   //待提交
//   waitSubmitCount: 0,
//   //待执行
//   waitexcutedCount: 0,
//   //已完成
//   FineshedCount: 0,
//   //已完成
//   allcount: 0
// });
const statusTypeEnum = [
  {
    id: '-1',
    name: '已撤销'
  },
  {
    id: '0',
    name: '待提交'
  },
  {
    id: '1',
    name: '审批中'
  },
  {
    id: '99',
    name: '已完成'
  }
  // ,
  // {
  //   id: '',
  //   name: '全部'
  // }
];
const options = [
  {
    value: '100',
    label: '销售回款'
  },
  {
    value: '101',
    label: '预收款'
  },
  {
    value: '102',
    label: '退采购付款'
  },
  {
    value: '104',
    label: '代收款'
  },
  {
    value: '108',
    label: '赔款'
  },
  {
    value: '109',
    label: '收回之前支付的押金'
  },
  {
    value: '110',
    label: '收回之前支付的投标保证金'
  },
  {
    value: '111',
    label: '收回之前支付的履约保证金'
  },
  {
    value: '115',
    label: '工会经费返还'
  },
  {
    value: '116',
    label: '政府补贴'
  },
  {
    value: '117',
    label: '即征即退'
  },
  {
    value: '118',
    label: '个税返还'
  },
  {
    value: '119',
    label: '税费返还'
  },
  {
    value: '120',
    label: '出口退税'
  },
  {
    value: '121',
    label: '所得税退税'
  },
  {
    value: '122',
    label: '保险理赔收入'
  },
  {
    value: '123',
    label: '收到保证金'
  },
  {
    value: '124',
    label: '收到招标押金'
  },
  {
    value: '127',
    label: '收回之前支付的海关保证金'
  },
  {
    value: '129',
    label: '收回之前支付的医院保证金'
  },
  {
    value: '998',
    label: '未知'
  },
  {
    value: '999',
    label: '其他'
  }
];
const itemClassifys = [
  {
    value: 1,
    label: '货款'
  },
  {
    value: 2,
    label: '暂收款'
  }
];

let createdType = ref(0);
let dataListBusinessDept = reactive([]);
let submitLoading = ref(false);
//获取路由
const router = useRouter();
const route = useRoute();
const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const functionUris = {
  add: 'metadata://fam/RecognizeReceive-index/functions/add-recognize',
  sureType:
    'metadata://fam/RecognizeReceive-index/functions/sureType-recognize',

  del: 'metadata://fam/RecognizeReceive-index/functions/del-recognize',
  revocation: 'metadata://fam/finance-bulkpayment/functions/revocation-payment',
  excute: 'metadata://fam/finance-bulkpayment/functions/excute-payment'
};
let receiveSelDialogShow = ref(false);
let receivesureTypeDialogShow = ref(false);
let createdDialogShow = ref(false);
let importDialogShow = ref(false);
let currUserName = ref('');
currUserName.value = window.userName;
// onMounted(() => {
//   crud.toQuery();
// });
const crud = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/GetListByServiceId',
    idField: 'id',
    method: 'post',
    sort: ['createdTime,desc'],
    query: {
      status: '',
      id: '',
      abatedStatus: '',
      username: currUserName.value
    },
    props: {
      // 默认隐藏搜索
      searchToggle: true
    },
    optShow: {
      exportCurrentPage: false
    },
    userNames: ['createdBy'],
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        console.log(crud.data);
        console.log('当前登录用户：' + JSON.stringify(currentUser));
        loadTabCount();
        //默认选中第一行
        if (crud.data.length) {
          nextTick(() => {
            crud.singleSelection(crud.data[0]);
            selectId.value = crud.data[0].id;
            // crudDetail.toQuery();
            getDetailData(crud.data[0]);
          });
        } else {
          crudDetail.data = [];
        }
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
let DeptDialogVisible = ref(false);
const crudDetail = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/getdetail',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    tablekey: 'tablekeyDetail',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const activeName = ref('recognize');
const clickdetailRow = (e) => {
  crudDetail.singleSelection(e);
};
const classify = ref(1);
let newDepart = reactive({
  id: '',
  name: '',
  path: '',
  fullName: '',
  item: {}
});
const classifyType = ref();
const getDetailData = (e) => {
  crudDetail.query.id = e.id;
  selectId.value = e.id;
  businessDeptsIdstr.value = e.businessDept_IdPaths;
  companyId.value = e.companyId;
  //货款,暂收款
  crudDetail.query.classify = e.classify;
  classifyType.value = e.classify;
  crudDetail.toQuery();
};
const importExcel = (e) => {
  importDialogShow.value = true;
};
let tabCount = ref({
  waitSubmitCount: 0,
  auditingCount: 0,
  waitExecuteCount: 0,
  completedCount: 0,
  allCount: 0,
  partCancel: 0
});
const closeImportDialogCallBack = () => {
  importDialogShow.value = false;
};
onMounted(() => {
  //获取项目类型
  getTreeList('', 'ProjectType').then((res) => {
    dataListType.push(...res.data.data[0].children);
  });
  // 获取事业部
  getDepartTree('metadata://fam').then((res) => {
    const getid = (list) => {
      return list.map((i) => {
        return {
          ...i,
          disabled: i.extraInfo.disabled ?? false,
          _id: i.id + '_' + i.name + '_' + i.extraInfo.deptShortName,
          children: i.extraInfo.children
            ? getid(i.extraInfo.children)
            : undefined
        };
      });
    };
    const tree1 = res.data.data;
    dataListBusinessDept.push(...getid(tree1));
    // 表头拖拽必须在这里执行
    tableDrag(tableItem, crud.tablekey);
    tableDrag(tableDetail, crudDetail.tablekey);
  });
  if (route.query && route.query.id) {
    crud.query.id = route.query.id;
  }
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  crud.query.billDateS = firstDay.getTime();
  crud.query.billDateE = lastDay.getTime();
  crud.toQuery();
});
const props = defineProps({
  __refresh: Boolean
});
onActivated(() => {
  if (props.__refresh) {
    receiveSelDialogShow.value = false;
    // crud.toQuery();
  }
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId',
      props: { queryData: { functionUri: 'metadata://fam' } }
    }
  );
  return [
    {
      key: 'code',
      label: '认款单号',
      show: true
    },
    {
      key: 'receivedetailcode',
      label: '明细单号',
      show: true
    },
    {
      key: 'receivecode',
      label: '收款单号',
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/bdsapi/api/businessUnits/meta`,
      placeholder: '业务单元名称搜索',
      valueK: 'id',
      labelK: 'name',
      props: { KeyWord: 'nameLike', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
      show: false
    },
    ...items,
    {
      key: 'billDateS',
      endDate: 'billDateE',
      label: '创建日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    },
    {
      key: 'customers',
      label: '客户',
      type: 'remoteSelect',
      multiple: true,
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
      show: true
    },
    {
      key: 'itemClassify',
      label: '类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: itemClassifys,
      show: false
    },
    {
      key: 'customerName',
      label: '实际客户',
      show: true
    },
    {
      key: 'hospitalName',
      label: '终端客户',
      show: true
    },
    {
      key: 'classify',
      label: '收款类型',
      type: 'select',
      labelK: 'label',
      valueK: 'label',
      dataList: options,
      show: false
    }
  ]
});
//合计
const total = (arr) => {
  var result = 0;
  arr.forEach((element) => {
    result += element.sum;
  });
  return result;
};
//合计2
const totaldetail = (arr) => {
  var result = 0;
  arr.forEach((element) => {
    result += element.value;
  });
  return result;
};
const RecognizeSelectRef = ref();
const RecognizeSureTypeRef = ref();

const closeDialogCallBack = () => {
  receiveSelDialogShow.value = false;
  receivesureTypeDialogShow.value = false;
};
const closeCreatedDialogCallBack = () => {
  createdDialogShow.value = false;
};
const refreshIndexCallBack = () => {
  crud.toQuery();
};
const refreshDetailCallBack = () => {
  crudDetail.toQuery();
};
const tabhandleClick = (tabName) => {
  if (tabName == 'recognize') {
    crud.toQuery();
  }
};
const tabhandleChildClick = (tabName) => {
  crud.query.status = tabName;
  crud.toQuery();
};

//附件
let flieList = ref('');
let comfile_upload = ref(false);
const openAttachment = (row) => {
  flieList = ref('');
  comfile_upload.value = true;
};
const handleClosefile = () => {
  comfile_upload.value = false;
};
let recognizeReceiveItemId = ref('');
const comfile_show = ref(false);
const showfiles = ref([]);
const showAttachFile = (showAttachFileids, id) => {
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    recognizeReceiveItemId.value = id;
    request({
      url: `/api/RecognizeReceiveQuery/GetAttachFile`,
      method: 'POST',
      data: {
        recognizeReceiveItemId: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show.value = true;
          showfiles.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'success',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};
const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
};
const deleteFile = (fileid) => {
  ElMessageBox.confirm('是否确定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: `/api/RecognizeReceive/DeleteAttachFileIds`,
      method: 'POST',
      data: {
        recognizeReceiveItemId: recognizeReceiveItemId.value,
        attachFileId: fileid
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          if (res.data.data == '' || res.data.data.length == 0) {
            crud.toQuery();
            comfile_show.value = false;
          } else {
            showAttachFile(res.data.data, recognizeReceiveItemId.value);
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  });
};
const savefile = () => {
  if (flieList.value == '' || flieList.value.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：请上传文件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  request({
    url: `/api/RecognizeReceive/AttachFileIds`,
    method: 'POST',
    data: {
      recognizeReceiveItemId: crud.selections[0].id,
      AttachFileIds: flieList.value
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_upload.value = false;
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: '保存成功!',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
};
const downloadFile = (code) => {
  request({
    url: '/api/RecognizeReceiveQuery/GetKDFilePath?code=' + code,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          window.open(res.data.data[0].previewAddress);
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶回执单，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
  //导出数据
  const exportBillLoading = ref(false);
  const exportBill = () => {
    ElMessageBox.confirm('是否导出符合条件的所有数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      exportBillLoading.value = true;
      return request({
        url: '/api/RecognizeReceiveQuery/ExportService',
        data: crud.query,
        method: 'POST',
        dataType: 'json',
        headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
        responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
      })
        .then((res) => {
          const xlsx =
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
          const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
          a.download = '导出收款单清单文件' + new Date().getTime() + '.xlsx';
          a.href = window.URL.createObjectURL(blob);
          a.click();
          a.remove();
        })
        .catch((err) => {
          throw '请求错误';
        })
        .finally(() => {
          exportBillLoading.value = false;
        });
    });
  }
  
  //获取页签数量
const loadTabCount = () => {
  request({
    url: '/api/RecognizeReceiveQuery/getTabCountByService',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
}
</script>
<style scoped lang="scss">
.app-page-tabs {
  flex: 1;
  :deep(.el-tabs__content) {
    display: flex;
    .el-tab-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
}
</style>
