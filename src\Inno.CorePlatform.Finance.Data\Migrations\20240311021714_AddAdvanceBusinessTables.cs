﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddAdvanceBusinessTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdvanceBusinessApply",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "垫资单号"),
                    ProjectName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProjectCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OldZXCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BillDate = table.Column<DateTime>(type: "date", nullable: false),
                    BusinessDeptFullPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptFullName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ExpectedAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalAmountOfMonth = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ReceivableAmountOfNon = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ReceivableAmountOfTimeout = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ServiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ServiceName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsInvoice = table.Column<bool>(type: "bit", nullable: false),
                    ServiceGroup = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProvidePayDays = table.Column<int>(type: "int", nullable: true),
                    HospitalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HospitalName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SalesVolume = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ReturnMoneyDays = table.Column<int>(type: "int", nullable: true),
                    IsVerify = table.Column<bool>(type: "bit", nullable: false),
                    NonVerifyRemark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsTakeOver = table.Column<bool>(type: "bit", nullable: false),
                    UnrecycledReceivableAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    TimeOutReceivableAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    PreUnrecycledReceivableAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    PreTimeOutReceivableAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    SupplyChainDiscounts = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    EndDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RateOfYear = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Ratio = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    RealUseDays = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RealSupplyChainDiscounts = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalDiscounts = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ExpectAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ExpectInterestAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    OperateNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StatusName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ApplyNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvanceBusinessApply", x => x.Id);
                },
                comment: "垫资申请单");

            migrationBuilder.CreateTable(
                name: "AdvanceFundBusinessCheckItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Operator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    UserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvanceFundBusinessCheckItem", x => x.Id);
                },
                comment: "垫资盘点单");

            migrationBuilder.CreateTable(
                name: "AdvanceBusinessDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AdvanceBusinessApplyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountPeriod = table.Column<int>(type: "int", nullable: false),
                    ReceivePeriod = table.Column<int>(type: "int", nullable: false),
                    Discount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    BaseDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    SCFDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    SPDDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    CreditId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DebtDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreditCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreditDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreditValue = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    SalesTaxRate = table.Column<decimal>(type: "decimal(12,2)", nullable: true),
                    InvoiceDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DebtCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DebtDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DebtValue = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    ReceiveCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ReceiveDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpectReceiveDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PaymentCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpectPaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ADFDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvanceBusinessDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdvanceBusinessDetail_AdvanceBusinessApply_AdvanceBusinessApplyId",
                        column: x => x.AdvanceBusinessApplyId,
                        principalTable: "AdvanceBusinessApply",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "垫资评价明细");

            migrationBuilder.CreateTable(
                name: "AdvanceFundBusinessCheckDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AdvanceBusinessApplyCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ServiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ServiceName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    HospitalId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HospitalName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsCheckedHospital = table.Column<int>(type: "int", nullable: false),
                    AccountPeriod = table.Column<int>(type: "int", nullable: false),
                    ReceivePeriod = table.Column<int>(type: "int", nullable: false),
                    AdvanceFundBusinessDays = table.Column<int>(type: "int", nullable: false),
                    Discount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    BaseDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    SCFDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    SPDDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    CreditId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DebtDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreditCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreditDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreditValue = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    InvoiceDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DebtCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DebtDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DebtValue = table.Column<decimal>(type: "decimal(12,2)", nullable: false),
                    ReceiveCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ReceiveDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpectReceiveDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PaymentCode = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpectPaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SalesTaxRate = table.Column<decimal>(type: "decimal(12,2)", nullable: true),
                    IsProcessAllMage = table.Column<int>(type: "int", nullable: true),
                    ADFDiscount = table.Column<decimal>(type: "decimal(12,2)", nullable: true),
                    AdvanceFundBusinessCheckItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvanceFundBusinessCheckDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdvanceFundBusinessCheckDetail_AdvanceFundBusinessCheckItem_AdvanceFundBusinessCheckItemId",
                        column: x => x.AdvanceFundBusinessCheckItemId,
                        principalTable: "AdvanceFundBusinessCheckItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "垫资盘点明细");

            migrationBuilder.CreateIndex(
                name: "IX_AdvanceBusinessDetail_AdvanceBusinessApplyId",
                table: "AdvanceBusinessDetail",
                column: "AdvanceBusinessApplyId");

            migrationBuilder.CreateIndex(
                name: "IX_AdvanceFundBusinessCheckDetail_AdvanceFundBusinessCheckItemId",
                table: "AdvanceFundBusinessCheckDetail",
                column: "AdvanceFundBusinessCheckItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdvanceBusinessDetail");

            migrationBuilder.DropTable(
                name: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropTable(
                name: "AdvanceBusinessApply");

            migrationBuilder.DropTable(
                name: "AdvanceFundBusinessCheckItem");
        }
    }
}
