﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addIsInternalTransactions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsInternalTransactions",
                table: "Debt",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "<PERSON>",
                table: "Debt",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsInternalTransactions",
                table: "Credit",
                type: "bit",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsInternalTransactions",
                table: "Debt");

            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "Debt");

            migrationBuilder.DropColumn(
                name: "IsInternalTransactions",
                table: "Credit");
        }
    }
}
