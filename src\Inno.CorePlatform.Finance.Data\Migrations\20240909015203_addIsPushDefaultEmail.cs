﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addIsPushDefaultEmail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsPushDefaultEmail",
                table: "CustomizeInvoiceClassify",
                type: "bit",
                nullable: true,
                comment: "是否推送默认邮箱");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsPushDefaultEmail",
                table: "CustomizeInvoiceClassify");
        }
    }
}
