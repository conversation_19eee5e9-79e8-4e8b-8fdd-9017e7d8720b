﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice
{
    public class InvoiceReceiptItem
    { 
        /// <summary>
        /// 单号
        /// </summary> 
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary> 
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary> 
        public string NameCode { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary> 
        public Guid ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary> 
        public string ServiceName { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary> 
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        public string CustomerName { get; set; }

        /// <summary>
        /// 回款天数
        /// </summary> 
        public int BackAmountDays { get; set; }

        /// <summary>
        /// 销售账期天数
        /// </summary> 
        public int SaleAccountPeriodDays { get; set; }

        /// <summary>
        /// 实际回款天数
        /// </summary> 
        public int ActualBackAmountDays { get; set; }

        /// <summary>
        /// 状态
        /// </summary> 
        public StatusEnum Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary> 
        public string? Remark { get; set; }
         
        public Guid Id { get; set; }

        public DateTimeOffset CreatedTime { get; set; } = DateTimeOffset.UtcNow;

        public DateTimeOffset? UpdatedTime { get; set; } = DateTimeOffset.UtcNow;
         
        public string CreatedBy { get; set; } = "none";
         
        public string? UpdatedBy { get; set; }
        /// <summary>
        /// 审批日期字段
        /// </summary>
        public DateTime? ApprovalTime { get; set; }
    }
}
