﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("CustomizeInvoiceRedDetail")]
    [Comment("运营制作开票单红冲明细")]
    [Serializable]
    public class CustomizeInvoiceRedDetailPo : BasePo
    {
        /// <summary>
        ///  数量
        /// </summary>  
        [Column(TypeName = "decimal(38,20)")]
        [Comment("数量")]
        public decimal Quantity { get; set; }
        /// <summary>
        ///  单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        [Comment("单价")]
        public decimal Price { get; set; }
        /// <summary>
        ///  原明细id
        /// </summary>
        [Comment("原明细id")]
        public string? OriginDetailId { get; set; }
        /// <summary>
        ///  应收单号
        /// </summary>
        [Comment("应收单号")]
        public string CreditBillCode { get; set; }

        /// <summary>
        ///  货号
        /// </summary>
        [Comment("货号")]
        [MaxLength(200)]
        public string? ProductNo { get; set; }


        /// <summary>
        /// 运营制作开票单Id(新)
        /// </summary>        
        public Guid? CustomizeInvoiceItemId { get; set; }

        /// <summary>
        /// 运营制作开票单Code(新)
        /// </summary>   
        public string? CustomizeInvoiceItemCode { get; set; }


        /// <summary>
        /// 运营制作开票单Id(旧)
        /// </summary>        
        public Guid? OldCustomizeInvoiceItemId { get; set; }

        /// <summary>
        /// 运营制作开票单Code(旧)
        /// </summary>   
        public string? OldCustomizeInvoiceItemCode { get; set; }

        /// <summary>
        /// 是否开票
        /// </summary>
        public bool? IsInvoiced { get; set; }

        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>
        [Comment("货品名称（开票名称）")]
        [MaxLength(200)]
        public string? ProductName { get; set; }
        /// <summary>
        /// 原始品名（不变）
        /// </summary>
        [Comment("原始品名（不变）")]
        [MaxLength(200)]
        public string? OriginProductName { get; set; }

        /// <summary>
        ///  计量单位
        /// </summary>
        [Comment("计量单位")]
        [MaxLength(200)]
        public string? PackUnit { get; set; }

        /// <summary>
        ///  原始计量单位
        /// </summary>
        [Comment("原始计量单位")]
        [MaxLength(1000)]
        public string? OriginPackUnit { get; set; }

        /// <summary>
        ///  规格型号
        /// </summary>
        [Comment("规格型号")]
        [MaxLength(1000)]
        public string? Specification { get; set; }
        /// <summary>
        /// 原始规格型号
        /// </summary>
        [Comment("原始规格型号")]
        [MaxLength(1000)]
        public string? OriginSpecification { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }
        /// <summary>
        /// 是否高值
        /// </summary>
        public int? IFHighValue { get; set; }
        /// <summary>
        /// 0或者空=红冲，1=作废
        /// </summary>
        public int? Classify { get; set; }
    }
}
