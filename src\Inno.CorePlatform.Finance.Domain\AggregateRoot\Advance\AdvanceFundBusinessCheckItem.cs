﻿using Inno.CorePlatform.Finance.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance
{
    public class AdvanceFundBusinessCheckItem:EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public DateTime BillDate { get; set; }

        public string Code { get; set; }
        
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; }

        public DateTime CreateTime { get; set; }

        public string Operator { get; set; }

        public string UserName { get; set; }

    }
}
