using System.Globalization;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 公司日期服务类，实现 ICompanyDateService 接口
    /// 提供根据公司信息获取实际业务日期的功能
    /// </summary>
    public class CompanyDateService : ICompanyDateService
    {
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;

        /// <summary>
        /// 
        /// </summary>
        public CompanyDateService(
            IBDSApiClient bDSApiClient,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService)
        {
            _bDSApiClient = bDSApiClient;
            _inventoryQueryService = inventoryQueryService;
        }

        /// <summary>
        /// 获取实际业务日期的异步方法
        /// 根据公司ID获取系统月份，并结合当前时间与库存状态判断实际使用的业务日期
        /// </summary>
        /// <param name="companyId">公司唯一标识符，字符串类型</param>
        /// <returns>返回实际使用的业务日期，DateTime类型</returns>
        public async Task<DateTime> GetActualDateAsync(string companyId)
        {
            if (string.IsNullOrWhiteSpace(companyId))
            {
                return DateTime.Now.Date;
            }

            // 获取系统月份（来自外部接口）
            var sysMonthDate = await _bDSApiClient.GetSystemMonth(companyId);

            if (!DateTime.TryParse(sysMonthDate, out var sysMonthParsed))
            {
                return DateTime.Now.Date;
            }

            var sysMonth = sysMonthParsed.ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");

            // 解析当前时间和系统月份
            if (!DateTime.TryParseExact(currentMonth, "yyyy-MM", CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out var currentMonthParsed) ||
                !DateTime.TryParseExact(sysMonth, "yyyy-MM", CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out var sysMonthOnly))
            {
                return DateTime.Now.Date;
            }

            DateTime actualDate = DateTime.Now.Date;

            // 判断是否当前月份大于系统月份
            if (currentMonthParsed > sysMonthOnly)
            {
                var guidCompanyId = Guid.TryParse(companyId, out var parsedId) ? parsedId : Guid.Empty;

                if (guidCompanyId == Guid.Empty)
                {
                    return DateTime.Now.Date;
                }

                // 查询库存信息
                var inventory = await _inventoryQueryService.FirstOrDefaultAsync(
                    t => t.SysMonth == sysMonth && t.CompanyId == guidCompanyId);

                // 如果存在且状态为99，则使用系统月份作为实际日期
                if (inventory != null && inventory.Status == 99)
                {
                    DateTime.TryParse(sysMonth, out actualDate);
                }
            }

            return actualDate;
        }
    }
}
