﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("ReconciliationLetterItem")]
    [Comment("财务对账函表")]
    [Index("BillCode", IsUnique = true)]
    public class ReconciliationLetterItemPo : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(200)]
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 欠款金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ArrearsAmount { get; set; }

        /// <summary>
        /// 回款金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ReceivedAmount { get; set; }

        /// <summary>
        /// 合计金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary> 
        [Column(TypeName = "date")]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 截止日期
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime Deadline { get; set; }

        /// <summary>
        /// 对账涵模板
        /// </summary>
        public ReconciliationLetterEnum ReconciliationLetterTemplate { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 询证函确认附件Ids
        /// </summary>
        [Comment("询证函确认附件Ids")]
        public string? ConfirmAttachFileIds { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }
        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
    }
}
