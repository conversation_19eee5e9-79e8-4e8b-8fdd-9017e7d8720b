﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRelateCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ChangeAmount",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RelateCode",
                table: "RecognizeReceiveItem",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChangeAmount",
                table: "ReconciliationStockDetail");

            migrationBuilder.DropColumn(
                name: "RelateCode",
                table: "RecognizeReceiveItem");
        }
    }
}
