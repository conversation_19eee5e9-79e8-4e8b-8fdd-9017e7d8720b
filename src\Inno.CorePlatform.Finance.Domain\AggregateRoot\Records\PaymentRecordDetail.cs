﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Records
{
    public class PaymentRecordDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 付款盘点Id
        /// </summary>
        public Guid PaymentRecordItemId { get; set; }
        /// <summary>
        /// 付款Id
        /// </summary>
        public Guid PaymentId { get; set; }
        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedValue { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal Value { get; set; }

    }
}
