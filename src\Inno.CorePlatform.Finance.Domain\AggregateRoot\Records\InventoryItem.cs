﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Records
{
    public class InventoryItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public Guid Id { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司完整名称
        /// </summary>
        public string CompanyLongName { get; set; }

        /// <summary>
        /// 盘点月度
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 状态：0-待启动库存盘点，1-库存盘点中，2-库存盘点完成，99-完成
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 库存盘点单号
        /// </summary>
        public string Store { get; set; }
        /// <summary>
        /// 暂存盘点单号
        /// </summary>
        public string TempStore { get; set; }
        /// <summary>
        /// 跟台盘点单号
        /// </summary>
        public string Operation { get; set; }
        /// <summary>
        /// 换货盘点单号
        /// </summary>
        public string Exchange { get; set; }
        /// <summary>
        /// 第三方库存盘点单号
        /// </summary>
        public string ThirdStore { get; set; }

        /// <summary>
        /// 应收盘点单号
        /// </summary>
        public string? CreditRecordCode { get; set; }
        /// <summary>
        /// 已签收待开票盘点
        /// </summary>
        public string? ReceivedNoInvoiceRecordCode{ get; set; }
        /// <summary>
        /// 应收盘点单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 应付盘点单号
        /// </summary>
        public string? DebtRecordCode { get; set; }

        /// <summary>
        /// 付款盘点单号
        /// </summary>
        public string? PaymentRecordCode { get; set; }

        /// <summary>
        /// 待确认收入盘点单号
        /// </summary>
        public string? SureIncomeCode { get; set; }
        /// <summary>
        /// 垫资盘点单号
        /// </summary>
        public string? AdvanceRecordCode { get; set; }

        /// <summary>
        /// 是否完成实盘
        /// </summary>
        public bool IsActualInventoryCompleted { get; set; } = false;

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTimeOffset? FinishTime { get; set; }
    }
}
