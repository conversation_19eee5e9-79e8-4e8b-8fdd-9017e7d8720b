﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;


namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceController : ControllerBase
    {
        private ILogger<InvoiceController> _logger;
        private IInvoiceCreditQueryService _invoiceCreditQueryService;
        private IInvoiceQueryService _invoiceQueryService;
        private IInvoiceReceiptQueryService _invoiceRepeiptQueryService;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IBaseAppService _baseAppService;
        public InvoiceController(ILogger<InvoiceController> logger,
            IInvoiceCreditQueryService invoiceCreditQueryServic, IInvoiceQueryService invoiceQueryService,IInvoiceReceiptQueryService invoiceRepeiptQueryService, IEasyCachingProvider easyCaching, IBaseAppService baseAppService
            )
        {
            this._logger = logger;
            this._invoiceCreditQueryService = invoiceCreditQueryServic;
            _invoiceQueryService = invoiceQueryService;
            _easyCaching = easyCaching;
            _baseAppService = baseAppService;
            _invoiceRepeiptQueryService = invoiceRepeiptQueryService;
        }
        /// <summary>
        /// 发票查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetInvoices")]

        public async Task<BaseResponseData<List<Invoice>>> GetInvoices(InvoiceQueryWebApiInput input)
        {
            var ret = BaseResponseData<List<Invoice>>.Success("操作成功！");
            ret.Data = await _invoiceCreditQueryService.GetInvoices(input);
            return ret;
        }

        /// <summary>
        /// SPD发票审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SPDApprove")]

        public async Task<BaseResponseData<int>> SPDApprove(List<SPDApproveInput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "invoice_spdapprove" + input.FirstOrDefault().invoice_num;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    await _baseAppService.CreateSubLog(SubLogSourceEnum.SPDApprove, jsonStr, "admin", "发票-SPD审核");
                    ret.Data = await _invoiceQueryService.SPDApprove(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 发票清单导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("InvoiceDownLoad")]
        public async Task<ResponseData<InvoicesExportListCoordinateOutput>> InvoiceDownLoad([FromBody] InvoicesQueryInput query)
        {
            try
            {
                var retList = new List<InvoicesExportListCoordinateOutput>();
                var (list, count) = await _invoiceQueryService.GetInvoiceListAsync(query);
                var input = new SPDInvoiceQueryInput()
                {
                    InvoiceNos = list.Where(p => !string.IsNullOrEmpty(p.InvoiceNo)).Select(p => p.InvoiceNo).Distinct().ToList(),
                    CompanyIds = list.Where(p => p.CompanyId.HasValue).Select(p => p.CompanyId.ToString()).Distinct().ToList(),
                };
                var (invoiceCrdits, cnt) = await _invoiceQueryService.GetInvoiceCredits(input);
                foreach (var item in list)
                {
                    if (item.CompanyId.HasValue && !string.IsNullOrEmpty(item.InvoiceNo))
                    {
                        var invoicesSingle = item.Adapt<InvoicesExportListCoordinateOutput>();
                        var details = invoiceCrdits.Where(x => x.InvoiceNo == item.InvoiceNo && x.CompanyId == item.CompanyId).ToList();
                        if (details!=null && details.Any())
                        {
                            invoicesSingle.BillCode = details[0].BillCode;
                            invoicesSingle.BillDateStr = details[0].BillDate.HasValue ? details[0].BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                            invoicesSingle.Value = details[0].CreditAmount;
                            invoicesSingle.OrderNo = details[0].OrderNo;
                            invoicesSingle.ServiceName = details[0].ServiceName;
                            retList.Add(invoicesSingle);
                            foreach (var detail in details)
                            {
                                //第一条详情在上面已经添加了，跳过
                                if (detail.BillCode == details[0].BillCode)
                                {
                                    continue;
                                }
                                var currentDetail = new InvoicesExportListCoordinateOutput();
                                currentDetail.StatusStr = string.Empty;
                                currentDetail.ChangedStatusStr = string.Empty;
                                currentDetail.BillCode = detail.BillCode;
                                currentDetail.BillDateStr = detail.BillDate.HasValue ? detail.BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                                currentDetail.Value = detail.CreditAmount;
                                currentDetail.OrderNo = detail.OrderNo;
                                currentDetail.ServiceName = detail.ServiceName;
                                retList.Add(currentDetail);
                            }
                        }
                        else
                        {
                            retList.Add(invoicesSingle);
                        }
                    }
                }
                return new ResponseData<InvoicesExportListCoordinateOutput>
                {
                    Code = 200,
                    Data = new Data<InvoicesExportListCoordinateOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 发票入账导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("InvoiceReceiptDownLoad")]
        public async Task<ResponseData<InvoiceReceiptDownLoadOutput>> InvoiceReceiptDownLoad(InvoiceReceiptItemQueryInput input)
        {
            try
            {
                var retList = new List<InvoiceReceiptDownLoadOutput>();
                var (list, count) = await _invoiceRepeiptQueryService.GetListAsync(input);
                var ids = list.Select(x => x.Id).ToList();
                var detailInput = new InvoiceReceiptDetailQueryInput()
                {
                    InvoiceReceiptItemIds = ids,
                    limit = int.MaxValue,
                };
                var (dlist, dcount) = await _invoiceRepeiptQueryService.GetDetailsByItemIdAsync(detailInput);
                foreach (var item in list)
                {
                    var single = item.Adapt<InvoiceReceiptDownLoadOutput>();
                    var currentDetails = dlist.Where(x => x.InvoiceReceiptItemId == item.Id).ToList();
                    if (currentDetails != null && currentDetails.Any())
                    {
                        foreach (var detail in currentDetails)
                        {
                            if (detail.Id != currentDetails[0].Id)
                            {
                                single = new InvoiceReceiptDownLoadOutput();
                            }
                            single.CreditCode = detail.CreditCode;
                            //single.CustomerName = detail.CustomerName;
                            single.HospitalName = detail.HospitalName;
                            single.InvoiceNo = detail.InvoiceNo;
                            single.InvoiceCode = detail.InvoiceCode;
                            single.InvoiceCheckCode = detail.InvoiceCheckCode;
                            single.InvoiceTime = detail.InvoiceTime;
                            single.InvoiceAmount = detail.InvoiceAmount;
                            single.CreditAmount = detail.CreditAmount;
                            single.IsCancel = detail.IsCancel;
                            //single.Remark = detail.Remark;
                            retList.Add(single);
                        }
                    }
                    else
                    {
                        retList.Add(single);
                    }
                }
                return new ResponseData<InvoiceReceiptDownLoadOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceReceiptDownLoadOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
