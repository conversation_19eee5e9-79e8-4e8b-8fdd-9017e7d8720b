using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using System;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces
{
    /// <summary>
    /// 盘点完成服务接口 - 负责盘点完成后的业务逻辑编排
    /// </summary>
    public interface IInventoryCompletionService
    {
        /// <summary>
        /// 检查并自动完成盘点（如果满足条件）
        /// </summary>
        /// <param name="inventory">盘点记录</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        Task CheckAndAutoCompleteInventoryAsync(InventoryDTO inventory, string sysMonth);

        /// <summary>
        /// 完成盘点并处理后续操作（更新状态、系统月度、发送广播）
        /// </summary>
        /// <param name="inventory">盘点记录</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="completeReason">完成原因</param>
        /// <returns></returns>
        Task CompleteInventoryAndHandlePostActionsAsync(InventoryDTO inventory, string sysMonth, string completeReason);



        /// <summary>
        /// 发送盘点完成通知广播
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="inventoryId">盘点单ID</param>
        /// <returns></returns>
        Task SendInventoryCompleteNotificationAsync(Guid companyId, string sysMonth, Guid inventoryId);


    }
}
