﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 退款处理表
    /// </summary>
    [Table("RefundItem")]
    public class RefundItemPo : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }


        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        public string PayModel { get; set; }

        /// <summary>
        /// 退款类型
        /// </summary>
        public string RefundType { get; set; }

        /// <summary>
        ///银行账号
        /// </summary>
        public string BankAccount { get; set; }

        /// <summary>
        ///账号名称
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        ///联行号
        /// </summary>
        public string BankNo { get; set; }

        /// <summary>
        /// 银行支行名称
        /// </summary>
        public string BankBranchName { get; set; }

        /// <summary>
        /// 开户行编码
        /// </summary>
        public string BankBranchNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 收款人类型(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? E_payeetype { get; set; } = "bd_customer";

        /// <summary>
        /// 退款总金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RefundAllMoney { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string MoneyNumber { get; set; }

        /// <summary>
        /// 只有待提交和已完成
        /// </summary>
        public RefundStatusEnum Status { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceivablesNumber { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary> 
        [Comment("项目名称")]
        [MaxLength(200)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        [Comment("项目单号")]
        [MaxLength(200)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 转账附言
        /// </summary>
        [Comment("转账附言")]
        [MaxLength(400)]
        public string? TransferPostscript { get; set; }
    }
}
