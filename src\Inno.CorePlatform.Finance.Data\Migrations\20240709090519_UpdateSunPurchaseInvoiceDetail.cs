﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSunPurchaseInvoiceDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "SXH",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                comment: "顺序号(必填)",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldComment: "顺序号(必填)");

            migrationBuilder.AddColumn<Guid>(
                name: "ProductId",
                table: "SunPurchaseInvoiceDetail",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                comment: "货号Id");

            migrationBuilder.AddColumn<string>(
                name: "ProductNo",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                comment: "货号");

            migrationBuilder.AddColumn<string>(
                name: "PurchaseCode",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                comment: "顺序号(必填)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "SunPurchaseInvoiceDetail");

            migrationBuilder.DropColumn(
                name: "ProductNo",
                table: "SunPurchaseInvoiceDetail");

            migrationBuilder.DropColumn(
                name: "PurchaseCode",
                table: "SunPurchaseInvoiceDetail");

            migrationBuilder.AlterColumn<string>(
                name: "SXH",
                table: "SunPurchaseInvoiceDetail",
                type: "nvarchar(max)",
                nullable: false,
                comment: "顺序号(必填)",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldComment: "顺序号(必填)");
        }
    }
}
