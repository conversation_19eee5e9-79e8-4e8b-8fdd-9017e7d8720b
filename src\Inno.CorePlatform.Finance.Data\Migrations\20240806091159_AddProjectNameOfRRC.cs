﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddProjectNameOfRRC : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "RecognizeReceiveItem",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "项目单号");

            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "RecognizeReceiveItem",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "Invoice",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "Invoice",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "项目单号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "RecognizeReceiveItem");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "RecognizeReceiveItem");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "Invoice",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "Invoice",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true,
                oldComment: "项目单号");
        }
    }
}
