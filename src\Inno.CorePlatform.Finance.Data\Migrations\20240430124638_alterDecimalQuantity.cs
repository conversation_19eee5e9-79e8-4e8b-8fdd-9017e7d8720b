﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class alterDecimalQuantity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "IncomeOfNoTax",
                table: "ReconciliationStockDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Income",
                table: "ReconciliationStockDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "CostOfNoTax",
                table: "ReconciliationStockDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Cost",
                table: "ReconciliationStockDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "IncomeOfNoTax",
                table: "ReconciliationIncomeDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Income",
                table: "ReconciliationIncomeDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "CostOfNoTax",
                table: "ReconciliationIncomeDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Cost",
                table: "ReconciliationIncomeDetail",
                type: "decimal(21,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,10)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "IncomeOfNoTax",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Income",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "CostOfNoTax",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Cost",
                table: "ReconciliationStockDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "IncomeOfNoTax",
                table: "ReconciliationIncomeDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Income",
                table: "ReconciliationIncomeDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "CostOfNoTax",
                table: "ReconciliationIncomeDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Cost",
                table: "ReconciliationIncomeDetail",
                type: "decimal(18,10)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(21,10)",
                oldNullable: true);
        }
    }
}
