﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 应收开票明细临时表
    /// </summary>
    [Table("CreditInvoiceDetailTemp")]
    public class CreditInvoiceDetailTempPo : BasePo
    {
        /// <summary>
        /// 应收明细Id
        /// </summary>
        [Comment("应收明细Id")]
        public Guid? CreditDetailId { get; set; }
        [ForeignKey("CreditDetailId")]
        public virtual CreditDetailPo? CreditDetail { get; set; }
        /// <summary>
        /// 原明细Id
        /// </summary>

        [Comment("原明细Id")]
        public string? OriginDetailId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        [Comment("货号")]
        [MaxLength(500)]
        public string? ProductNo { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        [Comment("包装规格")]
        public string? OriginalPackSpec { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        [Comment("品名")]
        public string? ProductName { get; set; }
        /// <summary>
        /// 原始品名（不变）
        /// </summary>
        [Comment("原始品名（不变）")]
        public string? OriginProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Comment("单位")]
        public string? PackUnit { get; set; }

        /// <summary>
        /// 原始单位
        /// </summary>
        [Comment("原始单位")]
        public string? OriginPackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [Comment("规格")]
        public string? Specification { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Comment("数量")]
        [Column(TypeName = "decimal(20,10)")]
        public decimal Quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        [Comment("单价")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }
        /// <summary>
        /// 原始单价
        /// </summary>
        [Comment("原始单价")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal? OriginalPrice { get; set; }
        /// <summary>
        /// 原始成本
        /// </summary>
        [Comment("原始成本")]
        public decimal? OriginalCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        [Comment("金额")]
        [Column(TypeName = "decimal(18,4)")]
        public decimal Value { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Comment("税率")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税收分类编码
        /// </summary>
        [Comment("税收分类编码")]
        public string? TaxTypeNo { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        [Comment("应收单号")]
        [MaxLength(500)]
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        [Comment("关联单号")]
        [MaxLength(500)]
        public string? RelateCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        [Comment("关联单号")]
        [MaxLength(500)]
        public string? OrderNo { get; set; }

        /// <summary>
        /// 客户Id 
        /// </summary>
        [Comment("客户Id")]
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [Comment("客户名称")]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        [Comment("公司")]
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        [Comment("公司名称")]
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>  
        [Comment("公司Code")]
        public string? NameCode { get; set; }
        [Comment("OriginalId")]
        public Guid? OriginalId { get; set; }
        /// <summary>
        /// 原始规格型号
        /// </summary>
        [Comment("原始规格型号")]
        public string? OriginSpecification { get; set; }
        /// <summary>
        /// 货号Id
        /// </summary>
        [Comment("货号Id")]
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        [Comment("供应商Id")]
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高价值(0:否,1:是)
        /// </summary>
        public int? IFHighValue { get; set; }
        /// <summary>
        /// 未开票金额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? NoInvoiceAmount { get; set; }
        public Guid? BatchId { get; set; }
        public Guid? SaleDetailId { get; set; }
        /// <summary>
        /// 业务单元Id
        /// </summary>  
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        public Guid? ProjectId { get; set; }

        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; } 
    }
}
