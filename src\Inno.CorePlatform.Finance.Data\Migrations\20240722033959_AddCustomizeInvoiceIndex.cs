﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCustomizeInvoiceIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CustomizeInvoiceIndex",
                table: "CustomizeInvoiceDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomizeInvoiceIndex",
                table: "CustomizeInvoiceDetail");
        }
    }
}
