﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("RefundDetail")]
    [Comment("退款明细")]
    public class RefundDetailPo : BasePo
    {
        /// <summary>
        /// 退款单Id
        /// </summary>
        [Comment("应付单Id")]
        public Guid? RefundItemId { get; set; }

        [ForeignKey("RefundItemId")]
        public virtual RefundItemPo? RefundItem { get; set; }

        /// <summary>
        /// 负数应收单号
        /// </summary>
        public string? MinusNumber { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RefundMoney { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }

        /// <summary>
        /// 收款单金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ReceiveAmount { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary> 
        [MaxLength(200)]
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 付款单金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }

    }
}
