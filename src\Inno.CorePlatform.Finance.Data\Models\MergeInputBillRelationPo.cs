﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 合并进项发票与原始进项发票关系
    /// </summary>
    [Table("MergeInputBillRelation")]
    public class MergeInputBillRelationPo : BasePo
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        [ForeignKey("MergeInputBillId")]
        public virtual MergeInputBillPo MergeInputBill { get; set; }

        /// <summary>
        /// 原始进项发票ID
        /// </summary>
        public Guid InputBillId { get; set; }

        /// <summary>
        /// 原始进项发票
        /// </summary>
        [ForeignKey("InputBillId")]
        public virtual InputBillPo InputBill { get; set; }
    }
}
