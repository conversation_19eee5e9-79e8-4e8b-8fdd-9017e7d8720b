﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addCreditCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RecognizeReceiveDetailCredit_Credit_CreditId",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.AlterColumn<Guid>(
                name: "CreditId",
                table: "RecognizeReceiveDetailCredit",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<string>(
                name: "CreditCode",
                table: "RecognizeReceiveDetailCredit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "InvoiceNo",
                table: "RecognizeReceiveDetailCredit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "RecognizeReceiveItemId",
                table: "RecognizeReceiveDetailCredit",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceSubDetail",
                type: "decimal(38,20)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,0)",
                oldComment: "数量");

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceRedDetail",
                type: "decimal(38,20)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,0)",
                oldComment: "数量");

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceDetail",
                type: "decimal(38,20)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,0)",
                oldComment: "数量");

            migrationBuilder.AddForeignKey(
                name: "FK_RecognizeReceiveDetailCredit_Credit_CreditId",
                table: "RecognizeReceiveDetailCredit",
                column: "CreditId",
                principalTable: "Credit",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RecognizeReceiveDetailCredit_Credit_CreditId",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.DropColumn(
                name: "CreditCode",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.DropColumn(
                name: "InvoiceNo",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.DropColumn(
                name: "RecognizeReceiveItemId",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.AlterColumn<Guid>(
                name: "CreditId",
                table: "RecognizeReceiveDetailCredit",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceSubDetail",
                type: "decimal(38,0)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,20)",
                oldComment: "数量");

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceRedDetail",
                type: "decimal(38,0)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,20)",
                oldComment: "数量");

            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "CustomizeInvoiceDetail",
                type: "decimal(38,0)",
                nullable: false,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(38,20)",
                oldComment: "数量");

            migrationBuilder.AddForeignKey(
                name: "FK_RecognizeReceiveDetailCredit_Credit_CreditId",
                table: "RecognizeReceiveDetailCredit",
                column: "CreditId",
                principalTable: "Credit",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
