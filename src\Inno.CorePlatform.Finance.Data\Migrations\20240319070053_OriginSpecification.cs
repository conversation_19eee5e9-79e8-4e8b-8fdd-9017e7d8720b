﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class OriginSpecification : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OriginSpecification",
                table: "CustomizeInvoiceSubDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始规格型号");

            migrationBuilder.AddColumn<string>(
                name: "OriginSpecification",
                table: "CustomizeInvoiceDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始规格型号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OriginSpecification",
                table: "CustomizeInvoiceSubDetail");

            migrationBuilder.DropColumn(
                name: "OriginSpecification",
                table: "CustomizeInvoiceDetail");
        }
    }
}
