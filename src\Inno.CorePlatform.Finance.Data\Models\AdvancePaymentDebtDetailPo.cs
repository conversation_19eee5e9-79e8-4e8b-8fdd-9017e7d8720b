﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 提前付款垫资应付明细
    /// </summary>    
    [Table("AdvancePaymentDebtDetail")]
    public class AdvancePaymentDebtDetailPo : BasePo
    {
        /// <summary>
        /// 提前付款垫资Id
        /// </summary>
        public Guid AdvancePaymentItemId { get; set; }

        /// <summary>
        /// 提前付款垫资
        /// </summary>
        [ForeignKey("AdvancePaymentItemId")]
        public virtual AdvancePaymentItemPo AdvancePaymentItem { get; set; }

        /// <summary>
        /// 应付（付款计划）单号
        /// </summary>
        [MaxLength(200)]
        public string DebtBillNo { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期 4=验收账期 5=质保账期
        /// </summary>
        [Comment("账期类型")]
        public int AccountPeriodType { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary> 
        [Comment("开票日期")]
        [MaxLength(200)]
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 回款天数 
        /// </summary>
        public int? ReturnDays { get; set; }

        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? EstimateReturnDate { get; set; }

        /// <summary>
        /// 实际支付日期
        /// </summary>
        public DateTime? ActualPaymentDate { get; set; }

        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? AdvancePaymentDays { get; set; }

        /// <summary>
        /// 月利率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthRate { get; set; }

        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? FinanceDiscount { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 付款金额 
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? PaymentAmount { get; set; }

        /// <summary>
        /// 垫资金额 
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AdvanceAmount { get; set; }

        /// <summary>
        /// 含税垫资毛利
        /// </summary>
        [Column("AdvanceCreditTaxAmount", TypeName = "decimal(18,2)")]
        public decimal? AdvanceTaxAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditValue { get; set; }

        /// <summary>
        /// 收付比 
        /// </summary> 
        [Column(TypeName = "decimal(18,18)")]
        public decimal? Rate { get; set; }
    }
}
