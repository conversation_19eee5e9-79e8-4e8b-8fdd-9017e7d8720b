﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("TempStoreTokKngdeeLog")]
    [Comment("暂存出入库记录提交给金蝶日志表")]
    public class TempStoreToKingdeeLogPo : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        [MaxLength(200)]
        public string? Code { get; set; }

        public string? Classify { get; set; }

        public string? PreRequestBody { get; set; }

        /// <summary>
        /// 记账日期
        /// </summary> 
        public DateTime? AccountingDateTime { get; set; }
         
        /// <summary>
        /// 推送日期
        /// </summary> 
        public DateTime? PushDateTime { get; set; }

        /// <summary>
        /// 推送内容
        /// </summary> 
        public string? RequestBody { get; set; }


        /// <summary>
        /// 返回内容
        /// </summary> 
        public string? ResponseBody { get; set; }

    }
}
