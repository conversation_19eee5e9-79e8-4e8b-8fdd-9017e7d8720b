﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice
{
    public class SunPurchaseInvoiceDetail
    {
        public Guid InvoiceId { get; set; }
  
        /// <summary>
        /// 配送明细对应的顺序号，同一个配送 单下必须唯一
        /// </summary> 
        public string SXH { get; set; }

        /// <summary>
        ///是否无配送发票 0：否；1：是 选择“否”的必须与配送名字一一关 联，选择“是”的则无需与配送明细关联
        /// </summary>  
        public string SFWPSFP { get; set; }

        /// <summary>
        ///无配送发票说明
        /// </summary>  
        public string? WPSFPSM { get; set; }

        /// <summary>
        ///是否冲红 标识是否是由于退货而产生的冲红  发票明细；0：否，1：是
        /// </summary>  
        public string SFCH { get; set; }

        /// <summary>
        ///耗材统编代码
        /// </summary>  
        public string HCTBDM { get; set; }

        /// <summary>
        ///耗材国家规格明细27位码（20位码+7 位规格明细码），若本次采购的耗材  是骨科集采范围内的耗材，则必须填  写27位码
        /// </summary>  
        public string? HCXFDM { get; set; }

        /// <summary>
        ///耗材统编代码在企业本地的编码，如 收产品编码等，多个企业本地代码可 对应到同一耗材统编代码上
        /// </summary>  
        public string? QYBDDM { get; set; }

        /// <summary>
        ///医疗器械具体规格型号的详细说明
        /// </summary>  
        public string? GGXHSM { get; set; }

        /// <summary>
        ///关联明细编号
        /// </summary>  
        public string? GLMXBH { get; set; }

        /// <summary>
        ///销售订单号
        /// </summary>   
        public string? XSDDH { get; set; }

        /// <summary>
        ///生产批号
        /// </summary>  
        [MaxLength(200)]
        public string SCPH { get; set; }

        /// <summary>
        ///生产日期
        /// </summary>  
        public DateTime SCRQ { get; set; }

        /// <summary>
        ///有效日期
        /// </summary>  
        public DateTime YXRQ { get; set; }

        /// <summary>
        ///商品数量
        /// </summary>   
        public decimal SPSL { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>   
        public decimal WSDJ { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>   
        public decimal HSDJ { get; set; }

        /// <summary>
        ///税率
        /// </summary>   
        public decimal SL { get; set; }

        /// <summary>
        ///税额
        /// </summary>   
        public decimal SE { get; set; }

        /// <summary>
        ///含税金额
        /// </summary>   
        public decimal HSJE { get; set; }

        /// <summary>
        ///批发价
        /// </summary>   
        public decimal PFJ { get; set; }

        /// <summary>
        ///零售价
        /// </summary>   
        public decimal LSJ { get; set; }

        /// <summary>
        ///注册证号 食药监批文的注册证号
        /// </summary>   
        public string ZCZH { get; set; }
    }
}
