﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRebateProvision : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RebateProvisionDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ConfirmationDate = table.Column<DateTime>(type: "Date", nullable: true),
                    ConfirmTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ActualarrivalDate = table.Column<DateTime>(type: "Date", nullable: true),
                    Policydeadline = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RebateType = table.Column<int>(type: "int", nullable: true),
                    CouponDate = table.Column<DateTime>(type: "Date", nullable: true),
                    RebateAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    RebateTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Redinvoice = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PeriodSummary = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NextAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    NextTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    NextRebateMethod = table.Column<int>(type: "int", nullable: true),
                    NextInvoiceOrCoupon = table.Column<int>(type: "int", nullable: true),
                    BizOrg = table.Column<int>(type: "int", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "项目id"),
                    ProjectName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "项目名称"),
                    ProjectCode = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "项目单号"),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AgentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AgentName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RebateProvisionDetail", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RebateProvisionItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BillDate = table.Column<DateTime>(type: "date", nullable: true, comment: "单据日期"),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RebateProvisionItem", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RebateProvisionDetail");

            migrationBuilder.DropTable(
                name: "RebateProvisionItem");
        }
    }
}
