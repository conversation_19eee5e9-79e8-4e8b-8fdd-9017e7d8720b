<template>
    <el-dialog
      v-model="createDialog"
      draggable
      title="暂收款撤销"
      :before-close="handleClose"
      :destroy-on-close="true"
      close-on-click-modal="false"
      modal-class="position-fixed"
      width="80%"
      hight="70%"
    >
      <div>
        <el-form
          ref="formRef"
          label-position="right"
        >
          <div style="margin-bottom: 10px">
            <el-table
                  id="tableDetail"
                  ref="tableDetail"
                  class="auto-layout-table"
                  highlight-current-row
                  :data="crudDetail.data"
                  v-inno-loading="crudDetail.loading"
                  stripe
                  fit
                  border
                  :row-class-name="crudDetail.tableRowClassName"
                  @sort-change="crudDetail.sortChange"
                  @selection-change="crudDetail.selectionChangeHandler"
                  @row-click="(e) => clickdetailRow(e)"
                >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column label="客户名称" fixed="left" min-width="90"  property="customerName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="认款日期" fixed="left" property="recognizeDate" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.recognizeDate, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column label="暂收款金额"  property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="已转货款金额" property="useValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.useValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="已撤销金额" property="cancelValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.cancelValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="余额" property="surplusValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.surplusValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="本次撤销金额" property="currentValue" show-overflow-tooltip>
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.currentValue"
                            type="number"
                            @input="computeAmount(scope.row)"
                        ></el-input>
                        </template>
                </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button
            type="primary"
            :loading="cancelLoading"
            @click="partCancelFun"
          >
            撤销
          </el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import {
    ref,
    watch,
    reactive,
    nextTick
  } from 'vue';
  import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
  import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
  import request from '@/utils/request';
  import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
  import { FormRules } from 'element-plus';
  import { backendUrl, gatewayUrl } from '@/public-path';
  import { Decimal } from 'decimal.js';
  import _ from 'lodash';
  const props = defineProps({
    showDialog: Boolean,
    itemId: { type: String, default: '' }
  });
  let createDialog = ref(false);
  const tableDetail = ref();
  const formRef = ref();
  const emits = defineEmits(['closeDialog', 'refreshIndex']);
  const handleClose = () => {
    createDialog.value = false;
    emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
  };
  const crudDetail = CRUD(
    {
      title: '应用',
      url: '/api/RecognizeReceiveQuery/getdetail',
      method: 'post',
      idField: 'id',
      userNames: ['createdBy'],
      tablekey: 'tablekeyDetail',
      query: {},
      resultKey: {
        list: 'list',
        total: 'total'
      }
    },
    {
      table: tableDetail
    }
  );
  watch(
    () => props.showDialog,
    (newValue, oldValue) => {
      if (newValue) {
        createDialog.value = true;
        crudDetail.query.id = props.itemId;
        crudDetail.query.classify = "2";
        crudDetail.query.status = "1";
        crudDetail.toQuery();
      }
    }
  )
  const clickdetailRow = (e) => {
    crudDetail.singleSelection(e);
  };
  //撤销明细
  const cancelLoading = ref(false);
  const partCancelFun = () => {
    console.log(props.itemId);
    if (crudDetail.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: "请至少勾选一条需要撤销的明细",
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    var id = props.itemId;
    ElMessageBox.confirm('此操作会删除暂收款转货款生成的所有未提交的认款单，是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      request({
        url: '/api/RecognizeReceive/cancelreceivetempdetail',
        method: 'post',
        data: {
          itemId: id,
          details: crudDetail.selections
        }
      })
        .then((res) => {
          if (res.data.code == 200) {
            ElMessage({
              showClose: true,
              message: '操作成功',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
          emits('refreshIndex');
          crudDetail.selections = [];
          handleClose();
        })
        .finally(() => {
          loading.close();
        });
    });
}

//计算行当前认款金额
const computeAmount = (row) => {
    if (row.currentValue > row.surplusValue) {
        ElMessage({
              showClose: true,
              message: "撤销金额不能大于余额",
              type: 'warning',
              duration: 3 * 1000
        });
        row.currentValue = row.surplusValue;
        return;
    }
}
</script>
  