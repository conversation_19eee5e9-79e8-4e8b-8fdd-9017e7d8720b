﻿
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Domain.Enums;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Azure;
using Namotion.Reflection;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml;
using System;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 盘点项管理
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InventoryItemController : ControllerBase
    {
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IInventoryQueryService _inventoryQueryService;
        private readonly IInventoryCompletionService _inventoryCompletionService;
        private readonly ILogger<InventoryItemController> _logger;

        /// <summary>
        /// 盘点项管理构造函数
        /// </summary>
        public InventoryItemController(
            IInventoryMgmAppService inventoryMgmAppService,
            IInventoryQueryService inventoryQueryService,
            IInventoryCompletionService inventoryCompletionService,
            ILogger<InventoryItemController> logger)
        {
            _inventoryMgmAppService = inventoryMgmAppService;
            _inventoryQueryService = inventoryQueryService;
            _inventoryCompletionService = inventoryCompletionService;
            _logger = logger;
        }

        /// <summary>
        /// 查询未完成盘点的公司
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetNonInventoryItems")]
        public async Task<BaseResponseData<List<OutInventoryItemOutput>>> GetNonInventoryItems(OutInventoryItemInput input)
        {
            return await _inventoryMgmAppService.GetNoFinishBillForInventory(input);
        }

        /// <summary>
        /// 应收盘点导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreditDownLoad")]
        public async Task<ResponseData<CreditExportOutput>> CreditDownLoad(RecordBaseQueryDto input)
        {
            var list = new List<CreditExportOutput>();
            var ret = await _inventoryQueryService.CreditDownLoad(input);
            if (ret.Data == null || !ret.Data.Any())
            {
                return new ResponseData<CreditExportOutput>
                {
                    Code = 200,
                    Data = new Data<CreditExportOutput>
                    {
                        List = list,
                        Total = 0,
                    }
                };
            }
            foreach (var item in ret.Data)
            {
                var entity = new CreditExportOutput();
                entity.Code = item.Code;
                entity.BillDate = item.BillDate;
                entity.CreditCode = item.CreditCode;
                entity.CreditBillDate = item.CreditBillDate;
                entity.CompanyName = item.CompanyName;
                entity.CustomerName = item.CustomerName;
                entity.Vaule = item.Vaule;
                entity.AbatedVaule = item.AbatedVaule;
                entity.Balance = item.Balance;
                entity.ServiceName = item.ServiceName;
                entity.OrderNo = item.OrderNo;
                entity.BusinessDeptFullName = item.BusinessDeptFullName;
                entity.BusinessDeptId = item.BusinessDeptId;
                entity.ProjectName = item.ProjectName;
                entity.ProjectCode = item.ProjectCode;
                list.Add(entity);
            }
            return new ResponseData<CreditExportOutput>
            {
                Code = 200,
                Data = new Data<CreditExportOutput>
                {
                    List = list,
                    Total = list.Count(),
                }
            };
        }

        /// <summary>
        /// 订货系统应付盘点导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreditDownLoadDh")]
        public async Task<ResponseData<DebtDownLoadDhOutput>> CreditDownLoadDh(RecordBaseQueryDto input)
        {
            var retList = new List<DebtDownLoadDhOutput>();
            var (list,count) = await _inventoryQueryService.GetCreditRecordListAsync(input);
            var newList = list.Adapt<List<DebtDownLoadDhOutput>>();
            var ids = list.Select(x => x.Id).ToList();
            var query = new DetailRecordBaseQueryDto();

            query.limit = 1000;
            query.ids = ids;
            query.customerId = input.customerId;
            query.customerIds = input.customerIds;
            var (details, dcount) = await _inventoryQueryService.GetCreditRecordDetailsListByIds(query);
            if (details!=null && details.Any())
            {
                foreach (var detail in details)
                {
                    var currentItem = newList.Where(x => x.Id == detail.CreditRecordItemId).FirstOrDefault();
                    if (currentItem != null)
                    {
                        var single = currentItem.Adapt<DebtDownLoadDhOutput>();
                        //single.BillDateStr = currentItem.BillDateStr;
                        //single.CompanyName = currentItem.CompanyName;
                        //single.IsConfirmStr = currentItem.IsConfirmStr;
                        single.BillCode = detail.Credit.BillCode;
                        single.AbatedValue = detail.AbatedValue;
                        single.Value = detail.Value;
                        single.ServiceName = detail.Credit.ServiceName;
                        single.CustomerName = detail.Credit.CustomerName;
                        single.Balance = detail.Value;
                        single.Typename = detail.typename;
                        retList.Add(single);
                    }
                }
            }
            else
            {
                retList = newList;
            }
            return new ResponseData<DebtDownLoadDhOutput>
            {
                Code = 200,
                Data = new Data<DebtDownLoadDhOutput>
                {
                    List = retList,
                    Total = retList.Count(),
                }
            };
        }

        /// <summary>
        /// 批量更新盘点单号字段
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("UpdateInventoryItemFields")]
        public async Task<BaseResponseData<UpdateInventoryItemFieldsResponseDto>> UpdateInventoryItemFields(
            [FromBody] UpdateInventoryItemFieldsRequestDto request)
        {
            try
            {
                _logger.LogWarning("开始批量 更新盘点 单号 - 公司: {CompanyId}, 月度: {SysMonth}, 字段数量: {FieldCount}",
                    request.CompanyId, request.SysMonth, request.FieldUpdates.Count);

                // 参数验证
                if (request.CompanyId == Guid.Empty)
                {
                    return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Failed(400, "公司ID不能为空");
                }

                if (string.IsNullOrEmpty(request.SysMonth))
                {
                    return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Failed(400, "系统月度不能为空");
                }

                if (request.FieldUpdates.Count == 0)
                {
                    return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Failed(400, "更新字段列表不能为空");
                }

                // 检查盘点记录是否存在
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(request.CompanyId, request.SysMonth);

                if (inventory == null)
                {
                    return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Failed(404, $"未找到公司 {request.CompanyId} 在 {request.SysMonth} 的盘点记录");
                }

                // 构建字段更新字典
                var fieldUpdates = new Dictionary<string, string>();
                var failedFields = new List<string>();
                var storeInventoryResults = new List<StoreInventorySpecialFormatDto>();

                // 分离库存盘点和其他类型的更新
                var storeInventoryUpdates = request.FieldUpdates
                    .Where(f => f.ActionType == InventoryActionType.CreateStockInventory)
                    .ToList();
                var otherUpdates = request.FieldUpdates
                    .Where(f => f.ActionType != InventoryActionType.CreateStockInventory)
                    .ToList();

                // 处理非库存盘点的更新
                foreach (var fieldUpdate in otherUpdates)
                {
                    try
                    {
                        var fieldName = GetFieldNameByActionType(fieldUpdate.ActionType);
                        if (!string.IsNullOrEmpty(fieldName))
                        {
                            if (fieldName== "IsActualInventoryCompleted")
                            {
                                fieldUpdates[fieldName] = fieldUpdate.IsActualInventoryCompleted.ToString();
                                _logger.LogInformation("准备更新字段 {FieldName} = {IsActualInventoryCompleted}", fieldName, fieldUpdate.IsActualInventoryCompleted);
                            }
                            else
                            {

                                fieldUpdates[fieldName] = fieldUpdate.InventoryCode;
                                _logger.LogInformation("准备更新字段 {FieldName} = {InventoryCode}", fieldName, fieldUpdate.InventoryCode);
                            }
                          
                        }
                        else
                        {
                            failedFields.Add($"未知的动作类型: {fieldUpdate.ActionType}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFields.Add($"处理动作类型 {fieldUpdate.ActionType} 失败: {ex.Message}");
                        _logger.LogError(ex, "处理动作类型 {ActionType} 失败", fieldUpdate.ActionType);
                    }
                }

                // 处理库存盘点的特殊逻辑
                if (storeInventoryUpdates.Count > 0)
                {
                    try
                    {
                        storeInventoryResults = await ProcessStoreInventorySpecialFormat(
                            request.CompanyId, storeInventoryUpdates);

                        // 如果有库存盘点，需要更新Store字段（存储全部盘点结果的JSON格式）
                        if (storeInventoryResults.Count > 0)
                        {
                            var storeInventoryJson = System.Text.Json.JsonSerializer.Serialize(storeInventoryResults);
                            fieldUpdates["Store"] = storeInventoryJson;
                            _logger.LogInformation("库存盘点 特殊处理 完成，更新Store字段 = {StoreInventoryJson}",
                                storeInventoryJson);
                        }
                        else
                        {
                            fieldUpdates["Store"] = "[]";
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFields.Add($"处理库存盘点特殊格式失败: {ex.Message}");
                        _logger.LogError(ex, "处理库存盘点 特殊格式 失败");
                    }
                }

                // 执行批量更新
                if (fieldUpdates.Count > 0)
                {
                    await _inventoryMgmAppService.UpdateInventoryItemFields(inventory.Id, fieldUpdates);
                    _logger.LogInformation("批量更新 盘点单号 成功 - 盘点单ID: {InventoryId}, 更新字段数: {FieldCount}",
                        inventory.Id, fieldUpdates.Count);

                    // 检查是否需要自动完成盘点
                    await _inventoryCompletionService.CheckAndAutoCompleteInventoryAsync(inventory, request.SysMonth);
                }

                var responseDto = new UpdateInventoryItemFieldsResponseDto
                {
                    Success = true,
                    Message = "盘点单号更新成功",
                    UpdatedFieldCount = fieldUpdates.Count,
                    FailedFields = failedFields,
                    StoreInventoryResults = storeInventoryResults.Count > 0 ? storeInventoryResults : null
                };

                return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Success(responseDto, "盘点单号 更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新 盘点单号 失败 - 公司: {CompanyId}, 错误: {ErrorMessage}",
                    request.CompanyId, ex.Message);
                return BaseResponseData<UpdateInventoryItemFieldsResponseDto>.Failed(500, $"更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据动作类型获取对应的字段名
        /// </summary>
        /// <param name="actionType"></param>
        /// <returns></returns>
        private static string GetFieldNameByActionType(InventoryActionType actionType)
        {
            return actionType switch
            {
                InventoryActionType.CreateStockInventory => "Store",
                InventoryActionType.CreateTinyInventory => "TempStore",
                InventoryActionType.CreateSginyInventory => "Operation",
                InventoryActionType.CreateExchangeInventory => "Exchange",
                InventoryActionType.CreateSureIncomeInventory => "SureIncomeCode",
                InventoryActionType.CreateCreditRecordInventory => "CreditRecordCode",
                InventoryActionType.CreateReceivedNoInvoiceInventory => "ReceivedNoInvoiceRecordCode",
                InventoryActionType.CreateDebtRecordInventory => "DebtRecordCode",
                InventoryActionType.UpdateActualInventoryCompleted => "IsActualInventoryCompleted",
                InventoryActionType.CreatePaymentRecordInventory => "PaymentRecordCode",
                InventoryActionType.CreateAdvanceRecordInventory => "AdvanceRecordCode",
                _ => string.Empty
            };
        }

        /// <summary>
        /// 处理库存盘点的特殊格式转换
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="storeInventoryUpdates">库存盘点更新列表</param>
        /// <returns>转换后的特殊格式列表</returns>
        private Task<List<StoreInventorySpecialFormatDto>> ProcessStoreInventorySpecialFormat(
            Guid companyId, List<InventoryFieldUpdateDto> storeInventoryUpdates)
        {
            var results = new List<StoreInventorySpecialFormatDto>();

            _logger.LogInformation("开始处理 库存盘点 特殊格式 转换 - 公司: {CompanyId}, 盘点单数量: {Count}",
                companyId, storeInventoryUpdates.Count);

            // 为每个库存盘点单号创建对应的记录
            foreach (var update in storeInventoryUpdates)
            {
                if (!string.IsNullOrEmpty( update.InventoryCode))
                {
                    var result = new StoreInventorySpecialFormatDto
                    {
                        CompanyId = companyId,
                        StoreCheckCode = update.InventoryCode,
                        StoreCheckStatus = update.IsActualInventoryCompleted.HasValue?update.IsActualInventoryCompleted.Value:false, // true=完成，false=未实盘
                        Total = storeInventoryUpdates.Count // 总数为所有库存盘点的数量
                    };

                    results.Add(result);
                    _logger.LogInformation("创建库存盘点 特殊格式 记录 - 盘点单号: {StoreCheckCode}, 状态: {Status}",
            result.StoreCheckCode, result.StoreCheckStatus);
                }
            }

            _logger.LogInformation("库存盘点 特殊格式 转换完成 - 生成记录数: {Count}", results.Count);

            return Task.FromResult(results);
        }




    }
}

