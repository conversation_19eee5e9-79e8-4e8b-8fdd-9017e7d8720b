﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCreditDetailOriginalId2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Originalid",
                table: "CreditDetail",
                newName: "OriginalId");

            migrationBuilder.AlterColumn<Guid>(
                name: "OriginalId",
                table: "CreditDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "销售的原始明细Id",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OriginalId",
                table: "CreditDetail",
                newName: "Originalid");

            migrationBuilder.AlterColumn<Guid>(
                name: "Originalid",
                table: "CreditDetail",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true,
                oldComment: "销售的原始明细Id");
        }
    }
}
