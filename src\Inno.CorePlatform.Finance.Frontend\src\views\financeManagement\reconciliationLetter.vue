<template>
  <div class="app-page-container">
    <div v-if="idValue === undefined" class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>财务对账函</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-if="idValue === undefined" v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane split="horizontal" :default-percent="60">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation v-if="idValue === undefined" :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                <el-tab-pane :label="`待审核(${tabCount.waitAuditCount})`" name="1" lazy />
                <el-tab-pane :label="`已完成(${tabCount.complateCount})`" name="99" lazy />
                <el-tab-pane :label="`已拒绝(${tabCount.refuseCount})`" name="66" lazy />
                <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                <el-tab-pane :label="`我的审核(${tabCount.myCount})`" name="5000" lazy />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip
                v-if="crud.rowData.status === 0"
                content="请至少选择一条数据"
                type="primary"
                icon="Close"
                :loading="crud.delAllLoading"
                :disabled="crud.selections.length === 0"
                @click="deleteItem(crud.rowData)"
              >删除</inno-button-tooltip>
              <!-- v-if="crud.rowData.status===0" -->
              <inno-button-tooltip
                v-if="crud.rowData.status === 0"
                slot="reference"
                content="请至少选择一条数据"
                class="filter-item"
                type="primary"
                icon="Check"
                :loading="submitLoading"
                :disabled="crud.selections.length === 0"
                @click="submit(crud.rowData)"
              >提交</inno-button-tooltip>
              <el-button type="primary" @click="createLetter" icon="Plus">新增</el-button>

              <el-button v-if="crud.rowData.status === 0" type="primary" style="margin-left: 15px" :disabled="!crud.selections.length" icon="editPen" @click="editLetter(crud.rowData)">编辑</el-button>
              <el-button
                v-if="crud.rowData.status === 0"
                type="primary"
                style="margin-left: 15px"
                :disabled="!crud.selections.length"
                :loading="crud.delAllLoading"
                icon="Refresh"
                @click="resetLetter(crud.rowData)"
              >重新同步</el-button>
              <el-button
                v-if="crud.rowData.oaRequestId !== null"
                icon="View"
                content="请选择一条数据"
                :disabled="!crud.selections.length"
                type="primary"
                @click="auditProcessClick(crud.rowData)"
              >查看审批过程</el-button>
              <inno-button-tooltip v-if="crud.rowData.status === 99" type="danger" icon="Plus" :disabled="!crud.selections.length" @click="openAttachment_letter(crud.rowData)">上传回函件</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef0"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crud.data"
            stripe
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55"></el-table-column>
            <el-table-column property="billCode" label="单号" width="250" fixed="left" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户名称" property="customerName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="对账函模版" property="reconciliationLetterTemplate" width="120" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.reconciliationLetterTemplate === 1
                ? '带发票明细'
                :(scope.row.reconciliationLetterTemplate === 2?'不带发票明细':'货号明细')
                }}
              </template>
            </el-table-column>
            <el-table-column label="合计金额" property="totalAmount" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.totalAmount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="回款金额" property="receivedAmount" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.receivedAmount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="欠款金额" property="arrearsAmount" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.arrearsAmount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="起始日期" property="startDate" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                scope.row.startDate === null||dateFormat(scope.row.startDate, 'YYYY-MM-DD')==='1901-01-01'
                ? ''
                : dateFormat(scope.row.startDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="截止日期" property="deadline" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                scope.row.deadline === null
                ? ''
                : dateFormat(scope.row.deadline, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>

            <el-table-column label="查看附件" property="attachFileIds" width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-link
                  style="font-size: 13px"
                  type="primary"
                  @click="showAttachFile(scope.row.attachFileIds, scope.row.id,scope.row.isCorporateCompany,scope.row.status)"
                >{{ scope.row.attachFileIds ? '查看附件' : '' }}</el-link>
              </template>
            </el-table-column>

            <el-table-column label="备注" property="remark" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="status" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ getStatus(scope.row.status) }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" property="createdTime" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                scope.row.createdTime === null
                ? ''
                : dateFormat(scope.row.createdTime, 'YYYY-MM-DD HH:mm:ss')
                }}
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="90">
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>

            <el-table-column label="询证函回函件" property="confirmAttachFileIds" width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-link
                  style="font-size: 13px"
                  type="primary"
                  @click="
                    showAttachFile_letter(
                      scope.row.confirmAttachFileIds,
                      scope.row.id
                    )
                  "
                >{{ scope.row.confirmAttachFileIds ? '查看' : '' }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1"></div>
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
            <template #opts-left>
              <el-tabs v-model="setDetailTab" @tab-change="tabDetailActiveClick">
                <el-tab-pane :label="`发票明细`" name="invoiceInfo"></el-tab-pane>
                <el-tab-pane :label="`应收明细`" name="creditInfo"></el-tab-pane>
                <el-tab-pane :label="`货号明细`" name="productInfo"></el-tab-pane>
              </el-tabs>
            </template>
            <template #right>
              <el-button type="primary" @click="onFull">
                <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
              </el-button>
            </template>
          </inno-crud-operation>
          <!-- 发票明细 -->
          <template v-if="setDetailTab === 'invoiceInfo'">
            <el-table
              ref="tableRef1"
              v-inno-loading="crud1.loading"
              class="auto-layout-table"
              highlight-current-row
              border
              show-summary
              :summary-method="getSummaries"
              :data="crud1.data"
              style="min-height: 180px;"
              stripe
              row-key="id"
            >
              <el-table-column label="开票日期" property="billDate" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.billCode=="其它应收金额"?"":dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
              </el-table-column>
              <el-table-column label="发票号码" property="billCode" width="120" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="发票金额" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="已收金额" property="receivedValue" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.receivedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="未收金额" property="nonReceivedValue" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.nonReceivedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="70" fixed="right" v-if="crud.rowData.status === 0||crud.rowData.status === 66">
                <template #default="scope">
                  <el-link style="font-size: 12px" type="primary" @click="deleteDetail(scope.row.id,scope.row.nonReceivedValue)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
            <div class="app-page-footer background">
              <div class="flex-1" />
              <inno-crud-pagination :crud="crud1" />
            </div>
          </template>
          <!-- 应收明细 -->
          <template v-if="setDetailTab === 'creditInfo'">
            <el-table
              ref="tableRef2"
              v-inno-loading="crud2.loading"
              class="auto-layout-table"
              highlight-current-row
              border
              show-summary
              :summary-method="getSummaries"
              :data="crud2.data"
              stripe
              row-key="id"
            >
              <el-table-column label="应收日期" property="billDate" width="120" show-overflow-tooltip>
                <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
              </el-table-column>

              <el-table-column label="应收单号" property="billCode" width="120" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="应收金额" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="已收金额" property="receivedValue" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.receivedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" label="未收金额" property="nonReceivedValue" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.nonReceivedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="70" fixed="right" v-if="crud.rowData.status === 0||crud.rowData.status === 66">
                <template #default="scope">
                  <el-link style="font-size: 12px" type="primary" @click="deleteDetail(scope.row.id,scope.row.nonReceivedValue)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
            <div class="app-page-footer background">
              <div class="flex-1" />
              <inno-crud-pagination :crud="crud2" />
            </div>
          </template>
          <!-- 货号明细 -->
          <template v-if="setDetailTab === 'productInfo'">
            <el-table
              ref="tableRef3"
              v-inno-loading="crud3.loading"
              class="auto-layout-table"
              highlight-current-row
              border
              show-summary
              :summary-method="getSummaries"
              :data="crud3.data"
              stripe
              row-key="id"
            >
              <el-table-column label="日期" property="billDate" width="120" show-overflow-tooltip sortable>
                <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
              </el-table-column>
              <el-table-column label="业务类型" property="businessType" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.businessType }}</template>
              </el-table-column>
              <el-table-column label="随货单号" property="shipmentCode" minWidth="250" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.shipmentCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单号" property="billCode" minWidth="250" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="业务员" property="salesmanName" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.salesmanName }}</template>
              </el-table-column>
              <el-table-column label="货号" property="productNo" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.productNo }}</template>
              </el-table-column>
              <el-table-column label="规格" property="specification" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.specification }}</template>
              </el-table-column>
              <el-table-column label="批号" property="batchNo" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.batchNo }}</template>
              </el-table-column>
              <el-table-column label="单价" width="120" property="price" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.price" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="数量" property="quantity" width="120" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.quantity }}</template>
              </el-table-column>
              <el-table-column class-name="isSum" label="金额" property="amount" width="120" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.amount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="开票时间" property="invoiceTime" width="120" show-overflow-tooltip sortable>
                <template #default="scope">{{ dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD') }}</template>
              </el-table-column>
              <el-table-column label="应收单号" property="receivableCode" minWidth="250" show-overflow-tooltip sortable>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.receivableCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="备注" property="remark" minWidth="250" show-overflow-tooltip sortable>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
                </template>
              </el-table-column>
              <!-- <el-table-column label="操作" width="70" fixed="right" v-if="crud.rowData.status === 0||crud.rowData.status === 66">
                <template #default="scope">
                  <el-link style="font-size: 12px" type="primary" @click="deleteDetail(scope.row.id,scope.row.nonReceivedValue)">删除</el-link>
                </template>
              </el-table-column>-->
            </el-table>
            <div class="app-page-footer background">
              <div class="flex-1" />
              <inno-crud-pagination :crud="crud3" />
            </div>
          </template>
        </template>
      </inno-split-pane>
    </div>
    <!-- 上传回函件 -->
    <el-dialog v-model="comfile_upload_letter" title="上传回函件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <inno-file-uploader
        v-model="flieList_letter"
        list-type="text"
        drag
        multiple
        bizType="finance"
        fileMode="large"
        appId="fam"
        :limitType="[
          'doc',
          'docx',
          'pdf',
          'xls',
          'xlsx',
          'png',
          'jpg',
          'jpeg',
          'gif'
        ]"
        :folders="folders"
        :beforeUpload="fileBeforeUpload"
        :on-success="fileOnSuccess"
        style="display: flex; justify-content: center"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点此上传文件</em>
        </div>
      </inno-file-uploader>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosefile">取消</el-button>
          <el-button type="primary" @click="savefile_letter">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 查看回函件 -->
    <el-dialog v-model="comfile_show_letter" title="查看回函件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-table :data="showfiles_letter" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">{{ format(scope.row.length) }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile_letter(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 查看附件 -->
    <el-dialog v-model="comfile_show" title="查看附件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">{{ format(scope.row.length) }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
            <el-button v-if="isCorporateCompany" style="margin-left: 10px;" type="primary" @click="printFileInfo(scope.row.id)">打印</el-button>
            <!-- <span style="cursor: pointer" @click="deleteFile_letter(scope.row.id)">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
    <el-dialog v-model="createDialogVisible" :title="formData.operateType === 'insert' ? '创建对账函' : '编辑对账函'" width="900" hight="70%" destroy-on-close :close-on-click-modal="false" draggable>
      <el-form ref="submitFormRef" :inline="true" :model="formData" :rules="letterformRules">
        <el-form-item label="核算部门:" prop="newDepart.id" style="width: 43%; margin-left: 10px">
          <inno-department-select
            ref="innoDepartmentSelectRef"
            v-model="formData.newDepart.id"
            v-model:name="formData.newDepart.name"
            v-model:path="formData.newDepart.path"
            v-model:fullName="formData.newDepart.fullName"
            v-model:item="formData.newDepart.item"
            functionUri="metadata://fam"
            :disabled="formData.operateType === 'update'"
            @change="businessDeptsChange"
          ></inno-department-select>
        </el-form-item>
        <el-form-item label="公司名称:" prop="company.id" style="width: 40%">
          <el-select v-model="formData.company" value-key="id" :disabled="formData.operateType === 'update'" filterable placeholder="请选择公司">
            <el-option v-for="item in CompanyList" :key="item.id" :label="item.name" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customer.id" style="width: 44%">
          <inno-remote-select
            v-model="formData.customer"
            isObject
            :is-guid="2"
            placeholder="请选择客户名称"
            :queryData="{
              functionUri: 'metadata://fam'
            }"
            :url="`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
          />
        </el-form-item>
        <el-form-item label="对账函模版:" prop="reconciliationLetterTemplate" style="width: 40%">
          <el-select v-model="formData.reconciliationLetterTemplate" value-key="id" placeholder="请选择模板" clearable @change="templateChange">
            <el-option label="带发票明细" value="1" />
            <el-option label="不带发票明细" value="2" />
            <el-option label="货号明细" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="截止日期:" prop="deadline" style="width: 88%" v-if="formData.reconciliationLetterTemplate!='3'">
          <el-date-picker v-model="formData.deadline" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择日期" clearable style="width: 100%" />
        </el-form-item>
        <el-form-item label="日期范围:" prop="deadline" style="width: 88%" v-if="formData.reconciliationLetterTemplate==='3'">
          <el-date-picker v-model="formData.deadlineRange" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注内容:" prop="remark" style="width: 88%">
          <el-input v-model="formData.remark" type="textarea" maxlength="200" rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelCreateLetter">取消</el-button>
          <el-button type="primary" @click="submitLetter">保存</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 打印采购订单审批表 -->
    <viewfile
      v-if="showPrintApprovalForm"
      url="/api/ReconciliationLetter/GetReconciliationLetterPDF"
      :params="{
        id: crud.rowData.id
      }"
      method="post"
      defaultFileName
      @close="showPrintApprovalForm = false"
    />
  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import request from '@/utils/request';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';
import { ReconciliationLetterTmpEnum } from '@/api/metaInfo';
import viewfile from './components/ViewFile.vue';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import approveProcess from '@/component/ApproveProcess.vue';
import { queryCheckedByDept } from '@/api/bdsData';
import { FormRules } from 'element-plus';
import { useRoute } from 'vue-router';
const route = useRoute();
const idValue = route.query.id; //获取地址栏id参数
const tableRef0 = ref<InstanceType<typeof ElTable>>();
const addRef = ref<InstanceType<typeof ElForm>>();
const dialogVisible = ref(false);
const splitDlgShow = ref(false);
let dataListBusinessDept = reactive([]);
const selectDetailId = ref('');
const splitAmount = ref('');
const approveProcessRef = ref(null);
const printId  = ref(null);
//上传回函件
let flieList_letter = ref('');
let comfile_upload_letter = ref(false);
const comfile_show_letter = ref(false);
  const showPrintApprovalForm = ref(false);
  const isCorporateCompany = ref(false); //是不是法人公司
const showfiles_letter = ref([]);
const openAttachment_letter = (row) => {
  flieList_letter = ref('');
  comfile_upload_letter.value = true;
};
//上传附件
const comfile_show = ref(false);
const showfiles = ref([]);
const handleClosefile = () => {
  comfile_upload_letter.value = false;
};
const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
}; 
//创建对账函
const createDialogVisible = ref(false);
const submitFormRef = ref();
const formData = reactive({
  company: { id: '', name: '', extraInfo: { nameCode: '' } },
  customer: { id: '', name: '' },
  newDepart: {
    id: '',
    name: '',
    path: '',
    fullName: '',
    item: {}
  },
  deadline: '',
  deadlineRange:[],
  reconciliationLetterTemplate: '',
  remark: '',
  billCode: '',
  operateType: '',
  businessArea: ''
});
watch(
  () => formData.deadlineRange,
  (newVal) => {
    if (formData.reconciliationLetterTemplate === '3' && newVal.length > 0) {
      formData.deadline = newVal[1];
    }  
  }
); 
const letterformRules = reactive<FormRules>({ 
  'company.id': [
    {
      required: true,
      message: '请选择公司',
      trigger: 'change'
    }
  ],
  'customer.id': [
    {
      required: true,
      message: '请选择客户',
      trigger: 'change'
    }
  ],
  reconciliationLetterTemplate: [
    {
      required: true,
      message: '请选择模板',
      trigger: 'change'
    }
  ],
  deadline: [
    {
      required: true,
      message: '请选择日期',
      trigger: 'blur'
    }
  ],
  remark: [
    {
      required: true,
      message: '请输入备注',
      trigger: 'blur'
    }
  ]
});
let CompanyList = ref([]);
const businessDeptsChange = (node, notclear) => {
  if (!notclear) {
    formData.company = { id: '', name: '', extraInfo: { nameCode: '' } };
  }
  if (node !== undefined) {
    //  formData.id = node.data.id;
    queryCheckedByDept(node).then((res) => {
      CompanyList.value = res.data.data;
    });
  }
};
const innoDepartmentSelectRef = ref();
// 递归方法检查树结构中是否存在指定的parentId
const checkIfParentExists = (tree, targetParentId) => {
  let resultData = {
    result: false,
    data: {}
  };
  // 遍历当前层级的每个节点
  for (const node of tree) {
    // 如果找到了匹配的parentId，返回true
    if (node.id === targetParentId) {
      return (resultData = {
        result: true,
        data: node
      });
    }
    // 如果当前节点有子节点，继续递归检查子节点
    if (node.children && node.children.length > 0) {
      resultData = checkIfParentExists(node.children, targetParentId);
      // 只要有一个分支返回true，整个结果就为true
      if (resultData.result) break;
    }
  }
  return resultData;
};
// 创建
const createLetter = () => {
  createDialogVisible.value = true;
  initLetterform();
  formData.operateType = 'insert';
};
const initLetterform = () => {
  formData.company = { id: '', name: '', extraInfo: { nameCode: '' } };
  formData.customer = { id: '', name: '' };
  formData.newDepart = {
    id: '',
    name: '',
    path: '',
    fullName: '',
    item: {}
  };
  formData.deadline = '';
  formData.deadlineRange = [];
  formData.reconciliationLetterTemplate = '';
  formData.remark = '';
  formData.billCode = '';
};
const cancelCreateLetter = () => {
  createDialogVisible.value = false;
};
watch(
  () => splitAmount.value,
  () => {
    splitAmount.value = splitAmount.value
      .replace(/[^\d.]/g, '')
      .replace(/\.{2,}/g, '.')
      .replace('.', '$#$')
      .replace(/\./g, '')
      .replace('$#$', '.')
      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
      .replace(/^\./g, '');
  }
);

const crudPermission = ref({
  add: ['permission.add.uri']
  //download: ['permission.export.uri']
});
const crud = CRUD(
  {
    title: '对账函管理列表',
    url: '/api/ReconciliationLetterQuery/GetList',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: { status: '-1', id: idValue === undefined ? '' : idValue },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadTableData();
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
      }
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0
  }
);
  const detailSumCount = reactive({
    valueSum : 0,
    nonReceivedValueSum : 0,
    receivedValueSum : 0,
  });
const tableRef1 = ref<InstanceType<typeof ElTable>>();
const crud1 = CRUD(
  {
    title: '发票明细',
    url: '/api/ReconciliationLetterQuery/GetListDetail',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        getDetailSumCount(1); 
      }
    },
    tablekey: 'tableRef1'
  },
  {
    table: tableRef1
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '应收明细',
    url: '/api/ReconciliationLetterQuery/GetListDetail',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => { 
        getDetailSumCount(2); 
      }
    },
    tablekey: 'tableRef2'
  },
  {
    table: tableRef2
  }
);
  const tableRef3 = ref < InstanceType < typeof ElTable >> ();
  const crud3 = CRUD(
    {
      title: '货号明细',
      url: '/api/ReconciliationLetterQuery/GetListProductDetail',
      method: 'post',
      idField: 'id',
      query: {},
      userNames: ['salesman'],
      resultKey: {
        list: 'list',
        total: 'total'
      },
      hooks: {
        [CRUD.HOOK.afterRefresh]: () => {
          getDetailSumCount(3); 
        }
      },
      tablekey: 'tableRef3'
    },
    {
      table: tableRef3
    }
  );
const queryList = computed(() => [ 
  {
    key: 'billCode',
    label: '单号',
    show: true
  },
  {
    key: 'deadlineDateS',
    endDate: 'deadlineDateE',
    label: '截止日期',
    type: 'daterange',
    show: true
  },
  {
    show: true,
    key: 'companyId',
    label: '公司',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data' }
  },
  {
    key: 'customerId',
    label: '客户',
    type: 'remoteSelect',
    //multiple: true,
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'reconciliationLetterTmp',
    label: '对账函模版',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: ReconciliationLetterTmpEnum,
    show: true
  }
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//上传回函件
const savefile_letter = () => {
  if (flieList_letter.value == '' || flieList_letter.value.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：请上传文件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }

  request({
    url: `/api/ReconciliationLetter/AttachFileIds_letter`,
    method: 'POST',
    data: {
      reconciliationLetterItemId: crud.selections[0].id,
      fileIds: flieList_letter.value
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_upload_letter.value = false;
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: '保存成功!',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
};
//删除回函件
const deleteFile_letter = (fileid) => {
  if (crud.selections[0].status === 0) {
    ElMessageBox.confirm('是否确定?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: `/api/ReconciliationLetter/DeleteAttachFileIds_letter`,
        method: 'POST',
        data: {
          reconciliationLetterItemId: crud.selections[0].id,
          confirmAttachFileId: fileid
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '操作成功！',
              type: 'success',
              duration: 3 * 1000
            });
            if (res.data.data == '' || res.data.data.length == 0) {
              crud.toQuery();
              comfile_show_letter.value = false;
            } else {
              showAttachFile_letter(res.data.data, crud.selections[0].id);
            }
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    });
  } else {
    ElMessage({
      showClose: true,
      message: '已提交状态不允许删除附件!',
      type: 'warning',
      duration: 3 * 1000
    });
  }
};
const showAttachFile_letter = (showAttachFileids, id) => {
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    request({
      url: `/api/ReconciliationLetter/GetAttachFile_letter`,
      method: 'POST',
      data: {
        reconciliationLetterItemId: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show_letter.value = true;
          showfiles_letter.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};
//附件
  const showAttachFile = (showAttachFileids, id,isCorCompany,status) => {
    crud.singleSelection(crud.selections[0])
    if (isCorCompany == true && status == 99) {//必须是法人公司和已完成
      isCorporateCompany.value = true;
    } else {
      isCorporateCompany.value=false
    }
   
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    request({
      url: `/api/ReconciliationLetter/GetAttachFile`,
      method: 'POST',
      data: {
        reconciliationLetterItemId: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show.value = true;
          showfiles.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};
watch(
  () => crud.selections,
  (n, o) => {
    crud1.data = [];
    crud1.page.total = 0;
    crud2.data = [];
    crud2.page.total = 0;
    crud3.data = [];
    crud3.page.total = 0; 
    if(crud.rowData.reconciliationLetterTemplate == 3){
      setDetailTab.value = 'productInfo';
      crud3.query = {reconciliationLetterItemId: crud.rowData.id,Classify:3, limit: 20 };
      crud3.toQuery();
    }else if(crud.rowData.reconciliationLetterTemplate == 2){ 
      setDetailTab.value = 'creditInfo';
      crud2.query = {reconciliationLetterItemId: crud.rowData.id,Classify: 2,limit: 20 };
      crud2.toQuery();   
    } else if (crud.rowData.reconciliationLetterTemplate == 1) {  
      setDetailTab.value = 'invoiceInfo';
      crud1.query = {reconciliationLetterItemId: crud.rowData.id,   Classify: 1,limit: 20 };
      crud1.toQuery(); 
    }   
  },
  { deep: true }
);

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (column.property === 'value') {
        sums[index] = detailSumCount.valueSum;
      } else if (column.property === 'receivedValue') {
        sums[index] = detailSumCount.receivedValueSum;
      } else if (column.property === 'nonReceivedValue') {
        sums[index] = detailSumCount.nonReceivedValueSum;
      } else {
        if (!values.every((value) => Number.isNaN(value))) {
          sums[index] = `${values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!Number.isNaN(value)) {
              return parseFloat((prev + curr).toFixed(4));
            } else {
              return prev;
            }
          }, 0)}`;
          sums[index] = rbstateFormat(sums[index]);
        } else {
          sums[index] = '';
        }
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
let tabCount = ref({
  allCount: 0,
  waitSubmitCount: 0,
  waitAuditCount: 0,
  refuseCount: 0,
  complateCount: 0,
  myCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/ReconciliationLetterQuery/GetTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
const getDetailSumCount = (type) => {
  request({
    url: '/api/ReconciliationLetterQuery/GetDetailSumCount',
    data: {
      reconciliationLetterItemId: crud.rowData.id,
      Classify: type,
    },
    method: 'post'
  }).then((res) => {
    detailSumCount.valueSum = res.data.data.valueSum;
    detailSumCount.nonReceivedValueSum = res.data.data.nonReceivedValueSum;
    detailSumCount.receivedValueSum = res.data.data.receivedValueSum;
  });
};
const searchCrud = () => {
  crud.query.status = '-1';
  crud.toQuery();
};
let downLoading = ref(false);
//删除对账函
const deleteItem = (row) => {
  var ids = crud.selections.map((t) => {
    return t.id;
  });
  ElMessageBox.confirm('此操作将删除当前单据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/ReconciliationLetter/del',
      method: 'DELETE',
      data: ids
    }).then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '删除成功',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      crud.toQuery(); //刷新页面
    });
  });
};

//删除对账函明细
const deleteDetail = (id,value) => {
  ElMessageBox.confirm('此操作将删除当前明细, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/ReconciliationLetter/DeleteDetail?id='+id+'&itemId='+crud.rowData.id,
      method: 'GET'
    }).then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '删除成功',
          type: 'success',
          duration: 3 * 1000
        });
        crud.rowData.arrearsAmount = crud.rowData.arrearsAmount - value
        if (setDetailTab.value == "invoiceInfo") { 
            crud1.toQuery(); //刷新页面 
        } else if (setDetailTab.value == "creditInfo") {
            crud2.toQuery(); //刷新页面
        }else if (setDetailTab.value == "productInfo") {
            crud3.toQuery(); //刷新页面
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    

    });
  });
}; 
//提交
let submitLoading = ref(false);
const submit = (row) => {
  var id = row.id;
  submitLoading.value = true;
  request({
    url: '/api/ReconciliationLetter/submit',
    method: 'PUT',
    params: { id: id }
  }).then((res) => {
    if (res.data.code == '200') {
      ElMessage({
        showClose: true,
        message: '提交成功',
        type: 'success',
        duration: 3 * 1000
      });
      submitLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      submitLoading.value = false;
    }
  });
};
//重新同步对账函
let resetLoading = ref(false);
const resetLetter = (row) => {
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  var id = row.id;
  resetLoading.value = true;
  request({
    url: '/api/ReconciliationLetter/reset',
    method: 'PUT',
    params: { id: id }
  }).then((res) => {
    if (res.data.code == '200') {
      ElMessage({
        showClose: true,
        message: '同步成功',
        type: 'success',
        duration: 3 * 1000
      }); 
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      }); 
    }
  }).finally(() => {
    loading.close();
    resetLoading.value = false;
  });
};
onMounted(() => {
  crud.toQuery();
});
//提交
const submitLetter = () => {
  if (formData.operateType == 'insert') {
    if (formData.newDepart.id == undefined || formData.newDepart.id == '') {
      ElMessage({
        showClose: true,
        message: '请选择核算部门',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    } 
    let parentData = {
      data: {
        extraInfo: {
          deptShortName: null
        }
      }
    };
    if (
      formData.newDepart.item.extraInfo.deptShortName === null &&
      formData.newDepart.fullName.indexOf('/') != -1
    ) {
      parentData = checkIfParentExists(
        innoDepartmentSelectRef.value.list,
        formData.newDepart.item.parentId
      );
      // 限制20次递归
      let i = 20;
      while (parentData.data.extraInfo.deptShortName == null && i > 0) {
        parentData = checkIfParentExists(
          innoDepartmentSelectRef.value.list,
          parentData.data.parentId
        );
        i--;
      }
    }
    formData.businessArea =
      parentData?.data?.extraInfo.deptShortName ||
      formData.newDepart?.item?.extraInfo.deptShortName ||
      '';
  } else {
    if (formData.reconciliationLetterTemplate == '带发票明细') {
      formData.reconciliationLetterTemplate = '1';
    }
    if (formData.reconciliationLetterTemplate == '不带发票明细') {
      formData.reconciliationLetterTemplate = '2';
    }
    if (formData.reconciliationLetterTemplate == '货号明细') {
      formData.reconciliationLetterTemplate = '3';
    }
  }
  if (!submitFormRef.value) return;
  submitFormRef.value.validate((valid, field) => {
    if (valid) {
      let param = {
        companyId: formData.company.id,
        companyName: formData.company.name,
        nameCode: formData.company.extraInfo.nameCode,
        customerId: formData.customer.id,
        customerName: formData.customer.name,
        deadline: formData.deadline,
        reconciliationLetterTemplate: formData.reconciliationLetterTemplate,
        remark: formData.remark,
        businessArea: formData.businessArea,
        operateType: formData.operateType,
        billCode: formData.billCode,
        deadlineRange: formData.deadlineRange,
        businessDeptId: formData.newDepart.id,
      };
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      request({
        url: '/api/ReconciliationLetter/Create',
        method: 'POST',
        data: param
      })
        .then((res) => {
          loading.close();

          if (res.data.code == '200') {
            ElMessage({
              showClose: true,
              message: '保存成功',
              type: 'success',
              duration: 3 * 1000
            });
            createDialogVisible.value = false;
            crud.toQuery();
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((t) => {
          loading.close();
        });
    }
  });
};
//编辑
const editLetter = (row) => { 
  createDialogVisible.value = true;
  CompanyList.value = [];
  formData.operateType = 'update';
  formData.billCode = row.billCode;
  formData.newDepart.id = '';
  formData.company.id = row.companyId;
  formData.company.name = row.companyName;
  formData.company.extraInfo.nameCode = row.nameCode;
  CompanyList.value.push(formData.company);
  formData.customer.name = row.customerName;
  formData.customer.id = row.customerId;
  formData.reconciliationLetterTemplate =row.reconciliationLetterTemplate+'';
  if(row.reconciliationLetterTemplate =='3' ){ 
    formData.deadlineRange=[row.startDate,row.deadline];
  }else if(row.reconciliationLetterTemplate =='1'){ 
  formData.deadline = row.deadline;
  }else if(row.reconciliationLetterTemplate =='2'){   
    formData.deadline = row.deadline;
  }  
  formData.remark = row.remark; 
  //businessDeptsChange(row.orgNum, true);//加载核算部门
};
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
//文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
};
const getStatus = (status) => {
  var statusText = '';
  switch (status) {
    case 0:
      statusText = '待提交';
      break;
    case 1:
      statusText = '待审核';
      break;
    case 66:
      statusText = '已拒绝';
      break;
    case 99:
      statusText = '已完成';
      break;
    case 5000:
      statusText = '我的审核';
      break;
    default:
  }
  return statusText;
};
//审批过程
const auditProcessClick = (row) => {
  //审批过程
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
const printFileInfo = (id:any) =>{
  printId.value = id;
  showPrintApprovalForm.value = true;
}

const setDetailTab = ref('invoiceInfo');
//切换
const tabDetailActiveClick = async (tab: any) => { 
  if (crud.data && crud.data.length > 0) {
    if (tab === 'invoiceInfo') {
      crud1.query = { reconciliationLetterItemId: crud.rowData.id,Classify:1, limit: 20 };
      crud1.toQuery();
    } else if (tab === 'creditInfo') {
      crud2.query = { reconciliationLetterItemId: crud.rowData.id,Classify:2, limit: 20 };
      crud2.toQuery();
    } else if (tab === 'productInfo') {
      crud3.query = {reconciliationLetterItemId: crud.rowData.id,Classify:3, limit: 20 };
      crud3.toQuery();
    } 
  }
  // setDetailTab.value = tab.props.name;
};
const templateChange = () => {
    formData.deadline = '';
    formData.deadlineRange =[];
};
</script>

<style scoped lang="scss">
.mycard_css {
  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  :deep(.el-tabs__content) {
    padding: 0px;
  }
}
:deep(.el-table__expand-column) {
  position: absolute;
  display: none;
}
</style>
