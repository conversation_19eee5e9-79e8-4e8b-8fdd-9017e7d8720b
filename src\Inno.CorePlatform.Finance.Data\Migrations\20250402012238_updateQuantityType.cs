﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class updateQuantityType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "Quantity",
                table: "ReconciliationLetterProductDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "数量",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "数量");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "Quantity",
                table: "ReconciliationLetterProductDetail",
                type: "int",
                nullable: true,
                comment: "数量",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "数量");
        }
    }
}
