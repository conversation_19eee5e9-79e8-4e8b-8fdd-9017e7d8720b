<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>消费者待开票申请</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation
        :crud="crud"
        :permission="functionUris"
        :hiddenColumns="[]"
        hidden-opts-left
      >
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <inno-split-pane :min-percent="20" split="horizontal" style="padding: 0 15px 15px">
      <template #paneL="{ full, onFull }">
        <inno-query-operation v-model:query-list="queryList" :crud="crud" />
        <inno-crud-operation
          style="padding: 0px 0px 6px 0px"
          :crud="crud"
          rightAdjust
          :permission="functionUris"
          :hiddenColumns="[]"
          border
          hidden-opts-right
        >
          <template #opts-left>
            <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="changeTabClick">
              <el-tab-pane :label="`待开发票(${tabModel.waitAuditCount})`" name="0"></el-tab-pane>
              <el-tab-pane :label="`已开发票(${tabModel.finishedCount})`" name="1"></el-tab-pane>
              <el-tab-pane :label="`全部(${tabModel.allCount})`" name=""></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <inno-button-tooltip
              type="primary"
              icon="Refresh"
              @click.stop="synchronousData"
            >
              同步数据
            </inno-button-tooltip>
            <inno-button-tooltip
              v-if="crud.rowData.status === 1 && hasPermission(functionUris.audit)"
              type="primary"
              icon="Document"
              @click.stop="openApprove"
              :loading="btnLoading"
            >
              审批
            </inno-button-tooltip>
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <el-table
          ref="tableItem"
          v-inno-loading="crud.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crud.data"
          stripe
          @sort-change="crud.sortChange"
          @selection-change="crud.selectionChangeHandler"
          @row-click=" (e) => { getDetailData(e);}"
        >
          <!-- <el-table-column fixed="left" width="55">
            <template #default="scope">
              <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
            </template>
          </el-table-column> -->
          <el-table-column type="index" fixed="left" width="50" />
          <el-table-column fixed="left" label="应收单号" property="receivableNo" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.receivableNo }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column fixed="left" label="订单号" property="orderNo" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.orderNo }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column fixed="left" label="销售订单号" property="salesOrderNo" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.salesOrderNo }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column fixed="left" label="原始订单号(天猫)" property="originalOrderNo" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.originalOrderNo }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="单据状态" property="orderStatus" show-overflow-tooltip>
            <template #default="scope">
              <el-tag type="warning" v-if="scope.row.orderStatus === 0">已拉取</el-tag>
              <el-tag type="danger" v-if="scope.row.orderStatus === 1">待开票</el-tag>
              <el-tag type="success" v-if="scope.row.orderStatus === 2">已开票</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="开票状态" property="status" show-overflow-tooltip>
            <template #default="scope">
              <el-tag type="warning" v-if="scope.row.status === 0">未开票</el-tag>
              <el-tag type="success" v-if="scope.row.status === 1">已开票</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column label="公司" property="company" show-overflow-tooltip />
          <el-table-column label="客户" property="customer" show-overflow-tooltip />
          <el-table-column label="发票抬头" property="invoiceTitle" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.invoiceTitle }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="纳税人识别号" property="taxpayerNo" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link>
                {{ scope.row.taxpayerNo }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="地址" property="address" show-overflow-tooltip />
          <el-table-column label="电话" property="phone" show-overflow-tooltip />
          <el-table-column label="开户行" property="bank" show-overflow-tooltip />
          <el-table-column label="银行账号" property="bankAccount" show-overflow-tooltip />
          <el-table-column label="货品总价" property="totalGoodsAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.totalGoodsAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="优惠金额" property="discountAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.discountAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="订单总价" property="orderTotalAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.orderTotalAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="积分红包金额" property="pointsAmount" width="120" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.pointsAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="实付金额" property="actualPaidAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.actualPaidAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="需开票金额" property="orderTotalAmount " show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.orderTotalAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="已开票金额" property="invoicedAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.invoicedAmount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="备注" property="remark" show-overflow-tooltip />
          <el-table-column
            label="申请日期"
            property="applyDate"
            sortable="custom"
            show-overflow-tooltip
            width="110"
          >
            <template #default="scope">
              {{ dateFormat(scope.row.applyDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="申请人" property="applicant" show-overflow-tooltip /> -->
          <el-table-column
            label="开票日期"
            min-width="150"
            property="invoiceDate"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ dateFormat(scope.row.invoiceDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="开票人" property="invoicePerson" width="100" show-overflow-tooltip />
        </el-table>
        <div class="app-page-footer background">
          已选择 {{ crud.selections.length }} 条
          <div class="flex-1" />

          <inno-crud-pagination :crud="crud" />
        </div>
      </template>
      <template #paneR="{ full, onFull }">
        <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
          <template #opts-left>
            <el-tabs v-model="setDetailTab" class="demo-tabs" @tab-change="tabDetailActiveClick">
              <el-tab-pane :label="`申请明细`" name="1"></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <!-- 申请明细 -->
        <el-table
          v-if="setDetailTab === '1'"
          ref="tableDetail"
          v-inno-loading="crudDetail.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crudDetail.data"
          stripe
          show-summary
          :summary-method="getSummaries"
          @sort-change="crudDetail.sortChange"
          @selection-change="crudDetail.selectionChangeHandler"
          @row-click="crudDetail.rowClick"
        >
          <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip>
            <template #default="scope">
              {{ (crudDetail.page.page - 1) * crudDetail.page.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="goodsName"
            label="货物或应税劳务服务名称(开票名称)"
            fixed="left"
            sortable
            show-overflow-tooltip
            min-width="180"
          >
          </el-table-column>
          <el-table-column
            prop="specification"
            label="规格型号"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            label="单位"
            property="unit"
            width="120"
            sortable
            show-overflow-tooltip
          />
          <el-table-column
            prop="quantity"
            label="数量"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            prop="unitPrice"
            label="单价"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <inno-numeral :value="scope.row.unitPrice" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column
            prop="amount"
            label="金额"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <inno-numeral :value="scope.row.amount" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column
            prop="taxRate"
            :label="'税率(%)'"
            min-width="100"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="taxAmount"
            label="税额"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <inno-numeral :value="scope.row.taxAmount" format="0,0.0000" />
            </template>
          </el-table-column>
        </el-table>
        <div class="app-page-footer background">
          <!-- 已选择 {{ crudDetail.selections.length }} 条 -->
          <p>共 {{ crudDetail.data.length }} 条</p>
          <div class="flex-1" />
          <!-- <inno-crud-pagination :crud="crudDetail" /> -->
           
          
        </div>
      </template>
    </inno-split-pane>
    <el-dialog v-model="setFormVisible" title="审批" width="500">
      <el-form :model="setForm" :rules="setRules" ref="setRef">
        <el-form-item label="审批结果" prop="isApproved">
          <el-radio-group v-model="setForm.isApproved">
            <el-radio value="1">审批通过</el-radio>
            <el-radio   value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input v-model="setForm.auditRemark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveCancel">取消</el-button>
          <el-button type="primary" @click="approveConfirm(setRef)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, Ref, onBeforeMount, onMounted, onActivated, computed, reactive, provide, nextTick } from 'vue';
import { ElTable, TableColumnCtx, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { useColumnStrategies } from '@inno/inno-mc-vue3/lib/utils/hooks';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { pageRedirect } from '@/utils/utils';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import _, { constant } from 'lodash';
import { formatDate, normalizeDate } from '@vueuse/shared';
import { Loading } from 'element-plus/es/components/loading/src/service';
import { Decimal } from 'decimal.js';

interface ListItem {
  value: string;
  label: string;
}
//服务商审批意见
interface AuditOpinionItem {
  auditOpinion: string;
  createTime: string;
  userName: string;
}

const { username } = useUserStore();

const setDetailTab = ref('1');
//获取路由
const router = useRouter();
//数据策略权限
const functionUris = {
  audit: 'metadata://sia/aier-prereturn-apply',
};

//选项卡对象
const tabModel = ref({
  waitAuditCount:0,
  refuseCount:0,
  allCount:0,
  finishedCount:0,
});
//合计统计数据
const detailFooterStatistics = reactive<{
  totalQty: number;
  totalStoreInQty: number;
  totalReceiveQty: number;
  totalTariffAmount: number;
  totalImportAddAmount: number;
  totalUnitCost: number;
  totalSaleAmount: number;
  totalOriginalAmount: number;
  totalEstimatedAmount: number;
  totalInQty:number;
}>({
  totalQty: 0,
  totalStoreInQty: 0,
  totalReceiveQty: 0,
  totalTariffAmount: 0,
  totalImportAddAmount: 0,
  totalUnitCost: 0,
  totalSaleAmount: 0,
  totalOriginalAmount: 0,
  totalEstimatedAmount: 0,
  totalInQty: 0
});
const selectTemplateModel = reactive<{
  type: string;
}>({
  type: '00000009'
});
const dlgProjectVisable = ref(false);
const projectFormModel = {
  projectName: '',
  projectId: ''
};
//退货明细打印是否显示
const showPrintDetail = ref(false);
//选择退货明细打印模板类型
const dialogFormVisible = ref(false);
const setFormVisible = ref(false);
const setBtnLoading = ref(false);
const btnLoading = ref(false);
const downloadBtnLoading = ref(false);
const timeRef = ref();
const setRef = ref();
const producerList = ref([]);
const agentList = ref([]);
const serviceList = ref([]);
const projectList = ref([]);
const timeForm = reactive({
  startTime:''
})
const expandRowKeys = ref()
const setForm = reactive({
  auditRemark:undefined,
  isApproved:undefined,
})
const rules = reactive<FormRules<any>>({
  startTime: [
    {
      required: true,
      message: '请选择同步时间',
      trigger: 'change',
    },
  ]
})
// 表单验证
const setRules = reactive<FormRules>({
  isApproved: [
    {
      required: true,
      message: '请选择审批结果',
      trigger: 'change',
    },
  ],
})
const matchProjectList = ref<ListItem[]>([]);
const selectProjectId = ref('');
const showGroupCreateTypeModal = ref(false);
const groupCreateType = ref(1);
const appleType = ref('');
const strategies = ref({
  hidden: []
});
const auditOpinionShow = ref(false);
const auditOpinionList = ref<AuditOpinionItem[]>([]);


interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'amount') {
      const values = data.map((item) => item.amount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'originalAmount') {
      const values = data.map((item) => (item.originUnitPrice * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalOriginalAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } else if (column.property === 'estimatedAmount') {
      const values = data.map((item) => (item.unitCost * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalEstimatedAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } 
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
    if (column.property === 'quantity') {
      const total = values.reduce((prev, curr) => (prev  + curr), 0);
      sums[index] = total;
      return;
    }
    if (column.property === 'arrivedQty') {
      sums[index] = detailFooterStatistics.totalReceiveQty;
      return;
    }
    if (column.property === 'storeInQty') {
      sums[index] = detailFooterStatistics.totalStoreInQty;
      return;
    }
    if (column.property === 'originUnitPrice') {
      sums[index] = detailFooterStatistics.totalOriginalAmount;
      return;
    }
    if (column.property === 'storeInDetails.quantity') {
      sums[index] = detailFooterStatistics.totalInQty;
      return;
    }
    
    // if (
    //   column.property === 'quantity' ||
    //   column.property === 'arrivedQty' ||
    //   column.property === 'storeInQty'
    // ) {
    //   sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //   return;
    // }
  });
  return sums;
};


const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const tableInApplyDetail = ref<InstanceType<typeof ElTable>>();

const crud = CRUD(
  {
    title: '应用',
    url: `${window.gatewayUrl}v1.0/ic-backend/api/wangdiantong/trade/page`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    query: { status: '1' },
    tablekey: 'tablekey',
    userNames: ['createdBy', 'updatedBy'],
    optShow: {
      add: false,
      edit: false,
      del: false,
      download: false,
      reset: true
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
        showProjectModel();
      },
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          // appleType.value = crud.data[0].applyTypeName;
          crud.singleSelection(crud.data[0]);
          crudDetail.query.id = crud.rowData.id;
          crudDetail.toQuery(); 
        }else {
          crudDetail.data = [];
        }
        getTabCount();
        // getMyAuditTabCount();
      }
    },
    pageConfig: {
      // 分页参数按项目可以单独配置
      pageIndex: 'pageIndex',
      pageSize: 'pageSize'
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
const crudDetail = CRUD(
  {
    title: '应用',
    url: `${window.gatewayUrl}v1.0/ic-backend/api/wangdiantong/trade/details`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'get',
    tablekey: 'tablekeyCopy', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    page: {
      isPage: false
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const crudInApplyDetail = CRUD(
  {
    title: '应用',
    url: `${window.gatewayUrl}v1.0/ic-backend/api/SpdStoreInForB/page`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    sort: ['createdTime,desc'],
    method: 'get',
    query: {},
    tablekey: 'tablekeyInApply', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
        // detailFooterStatistics.totalInQty = data.footer.totalQty;
      }
    }
  },
  {
    table: tableInApplyDetail
  }
);

const getTabCount = () => {
  request({
    url: `${window.gatewayUrl}v1.0/ic-backend/api/wangdiantong/trade/getTradeOrderTab`,
    method: 'post',
    data: crud.query
  })
    .then((res) => {
      // tabModel.value.tabAwaitSubAudit = res.data.data.awaitSubAuditCount;
      tabModel.value.waitAuditCount = res.data.data.status0Count;
      tabModel.value.allCount = res.data.data.totalCount;
      tabModel.value.finishedCount = res.data.data.status1Count;
      // tabModel.value.finishedCount = res.data.data.finished;
      console.log(res.data.data.waitSubmitCount);
    })
    .catch((error) => {
      console.log(error);
    });
};



const changeTabClick = () => {
  crudDetail.data = [];
  crudInApplyDetail.data = [];
  crud.toQuery();
};

const getDetailData = (e:any) => {
  crudDetail.query.id = e.id;
  crudDetail.toQuery();
  crud.singleSelection(e);
};

function showProjectModel() {
  dlgProjectVisable.value = true;
}

const props = defineProps({
  __refresh: Boolean
});

onBeforeMount(() => {
  crud.toQuery();
});

onMounted(() => {
  // 表头拖拽必须在
  tableDrag(tableItem);
  tableDrag(tableDetail);
  // tableDrag(tableInApplyDetail);
  const strategiesConfig = {
    functionUri: 'metadata://sia/storein-apply/routes/index-search',
    url:
      window.gatewayUrl +
      `v1.0/sia-backend/api/purchaseapply/getStrategy?functionUri=metadata://sia/storein-apply/routes/index-search`,
    method: 'get'
  };
  strategies.value = useColumnStrategies(strategiesConfig);
  // test();
});

onActivated(() => {
  crud.toQuery();
});
//高级检索
const queryList = computed(() => [
  {
    key: 'receivableNo',
    label: '应收单号',
    show: true
  },
  {
    key: 'orderNo',
    label: '订单号',
    show: true
  },
  {
    key: 'salesOrderNo',
    label: '销售订单号',
    show: true
  },
  {
    key: 'originalOrderNo',
    label: '原始订单号',
    show: true
  },
  // {
  //   key: 'companyId',
  //   label: '公司',
  //   method: 'post',
  //   type: 'remoteSelect',
  //   url: `${window.gatewayUrl}v1.0/bdsapi/api/companies/meta`,
  //   placeholder: '公司名称搜索',
  //   valueK: 'id',
  //   labelK: 'name',
  //   props: { KeyWord: 'name', resultKey: 'data.data' },
  //   show: false
  // },
  // {
  //   key: 'customerId',
  //   label: '客户',
  //   show: true,
  //   type: 'remoteSelect',
  //   method: 'post',
  //   url: `${window.gatewayUrl}v1.0/bdsapi/api/customers/meta`,
  //   placeholder: '客户名称搜索'
  // },
  {
    key: 'invoiceTitle',
    label: '发票抬头',
    show: false
  },
  {
    key: 'taxpayerNo',
    label: '纳税人识别号',
    show: false
  },
  {
    key: 'invoicePerson',
    label: '开票人',
    method: 'post',
    multiple: false,
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
    placeholder: '用户名称搜索',
    valueK: 'name',
    labelK: 'displayName',
    props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
    slots: {
      option: ({ item }) => (
        <>
          <span>{item.displayName}</span>
          <span style="float:right">{item.name}</span>
        </>
      )
    }
  },
  {
    key: 'applyDateStart',
    endDate: 'applyDateEnd',
    label: '申请日期',
    type: 'daterange',
    defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
    props: { format: 'YYYY-MM-DD' }
  },
  {
    key: 'invoiceDateStart',
    endDate: 'invoiceDateEnd',
    label: '开票日期',
    type: 'daterange',
    defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
    props: { format: 'YYYY-MM-DD' }
  },  
  
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

//切换
const tabDetailActiveClick = async (tab: any) => {
  if (tab === '1' && crud.rowData.id) {
    crudDetail.query.id = crud.rowData.id;
    crudDetail.toQuery();
    crud.singleSelection(crud.rowData);
  } else if(tab === '2' && crud.rowData.code) {
    crudInApplyDetail.query.code = crud.rowData.code;
    crudInApplyDetail.toQuery();
    crud.singleSelection(crud.rowData);
  }
};

const approveConfirm = async(formEl:any) =>{
  if (!formEl) return
    let checkResult = await formEl.validate((valid:any, fields:any) => {return valid})
     if (!checkResult) {
        return false;
    }
  let postData = {
    id: crud.rowData.id,
    isApproved: setForm.isApproved === '1'? true : setForm.isApproved === '2' ? false :null,
    auditRemark: setForm.auditRemark
  }
  request({
    url: `/api/AierPreReturnApply/Audit`,
    method: 'POST',
    data: postData
  }).then(res=>{
    if(res.data.code ===200){
      ElMessage.success({ showClose: true, message: '审批成功！' });
      crud.toQuery();
      setFormVisible.value = false;
      approveCancel();
    }else{
      ElMessage.error({ showClose: true, message: res.data.message });
    }
  }).catch((error)=>{
    ElMessage.error({ showClose: true, message: error });
  })
  
 
}
const approveCancel = () =>{
  setFormVisible.value = false;
  setForm.isApproved = undefined;
  setForm.auditRemark = undefined;
}
const openApprove = () =>{
  if(!crud.rowData.id || crud.rowData.id === ''){
    ElMessage.warning({ showClose: true, message: '请选择需要审批的数据！' });
    return false;
  }
  setFormVisible.value = true;
}
const synchronousData = () =>{
  ElMessageBox.confirm(
    '是否同步最新数据?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      request({
        url: `${window.gatewayUrl}v1.0/ic-backend/api/wangdiantong/manual-get-trade-order-invoice`,
        method: 'POST',
      }).then(res=>{
        if(res.data.code ===200){
          ElMessage.success({ showClose: true, message: '同步成功！' });
          crud.toQuery();
        }else{
          ElMessage.error({ showClose: true, message: res.data.message });
        }
      }).catch((error)=>{
        ElMessage.error({ showClose: true, message: error });
      })
    })
}
</script>
<style scoped>
:deep(.el-tabs__item) {
  padding: 0 12px;
}
:deep(.right-adjust) {
  display: block;
  max-width: 42%;
  display: flex;
  flex-wrap: wrap;
  height: auto;
  justify-content: flex-end;
  margin-left: 20px;
  align-items: center;
}
:deep(.crud-opts-border) {
  justify-content: space-between;
}
/* :deep(.el-button) {
  margin-top: 8px;
} */
:deep(.crud-opts) {
  justify-content: space-between;
}
</style>
