﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 合并进项发票关联应付
    /// </summary>
    [Table("MergeInputBillDebt")]
    public class MergeInputBillDebtPo : BasePo
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        [ForeignKey("MergeInputBillId")]
        public virtual MergeInputBillPo MergeInputBill { get; set; }

        /// <summary>
        /// 应付单ID
        /// </summary>
        public Guid DebtId { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string DebtCode { get; set; }

        /// <summary>
        /// 关联应付金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DebtAmount { get; set; }
    }
}
