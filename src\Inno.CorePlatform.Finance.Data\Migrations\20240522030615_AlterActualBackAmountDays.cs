﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AlterActualBackAmountDays : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "ActualBackAmountDays",
                table: "InvoiceReceiptItem",
                type: "int",
                nullable: true,
                comment: "实际回款天数",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "实际回款天数");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "ActualBackAmountDays",
                table: "InvoiceReceiptItem",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "实际回款天数",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "实际回款天数");
        }
    }
}
