﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate
{
    public class PaymentAutoAgentBankInfo : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 批量付款单Id
        /// </summary>
        public Guid PaymentAutoItemId { get; set; }
     
        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
        /// <summary>
        /// 账号名称
        /// </summary>
        public string? AccountName { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string? Account { get; set; }
        /// <summary>
        /// 开户行名称
        /// </summary>
        public string? BankName { get; set; }
        /// <summary>
        /// 开户行编码
        /// </summary>
        public string? BankCode { get; set; }

        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary> 
        public string? PayClassify { get; set; }
        /// <summary>
        /// 转账附言
        /// </summary>
        public string? TransferDiscourse { get; set; }

        #region 境外供应商支付
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        public string? Contractno { get; set; }

        /// <summary>
        /// 境外/跨境支付0:否,1:是
        /// </summary>
        public int? Paymentabroad { get; set; }

        /// <summary>
        /// 交易编码
        /// </summary>
        public string? Transactioncoding { get; set; }

        /// <summary>
        /// 交易附言
        /// </summary>
        public string? Postscript { get; set; }

        /// <summary>
        /// 本笔款项是否为保税货物下推0:否,1:是
        /// </summary>
        public int? Ynpush { get; set; }

        /// <summary>
        /// 国内外费用承担方A:汇款人,B:收款人,C:共同人
        /// </summary>
        public string? CostBearingParty { get; set; }

        /// <summary>
        /// 海关进口货物报关商品
        /// </summary>
        public string? importGoods { get; set; }

        #endregion
        /// <summary>
        /// 附件Ids
        /// </summary> 
        public string? AttachFileIds { get; set; }
    }
}
