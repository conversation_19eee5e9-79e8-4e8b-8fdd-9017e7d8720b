﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 汇率表
    /// </summary>
    [Table("ExchangeRate")]
    [Comment("汇率表")]
    public class ExchangeRatePo : BasePo
    {
        /// <summary>
        /// 失效日期
        /// </summary>

        [Column(TypeName = "date")]
        public DateTime Expirydate { get; set; }
        /// <summary>
        /// 直接汇率值
        /// </summary>

        [Column(TypeName = "decimal(18,10)")]
        public decimal Excval { get; set; }
        /// <summary>
        /// 间接汇率值
        /// </summary>

        [Column(TypeName = "decimal(18,10)")]
        public decimal Indirectexrate { get; set; }
        /// <summary>
        /// 生效日期
        /// </summary>

        [Column(TypeName = "date")]
        public DateTime Effectdate { get; set; }
        /// <summary>
        /// 原币.名称
        /// </summary>

        [MaxLength(100)]
        public string OrgcurName { get; set; }
        /// <summary>
        /// 目标币.名称
        /// </summary>

        [MaxLength(100)]
        public string CurName { get; set; }
    }
}
