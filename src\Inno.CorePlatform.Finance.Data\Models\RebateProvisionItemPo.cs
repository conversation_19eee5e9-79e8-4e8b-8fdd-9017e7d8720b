﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 返利计提
    /// </summary>
    [Table("RebateProvisionItem")]
    [Index("BillCode", IsUnique = true)]
    public class RebateProvisionItemPo : BasePo
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        [Column(TypeName = "date")]
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 单据号
        /// </summary>
        [Comment("单据号")] 
        public string? BillCode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum Status { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public ProvisionTypeEnum ProvisionType { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 核算部门Code
        /// </summary>
        public string? BusinessDeptShortName { get; set; }
    }
}
