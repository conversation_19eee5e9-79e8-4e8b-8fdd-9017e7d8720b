﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddMarkOfReconciliation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "<PERSON>",
                table: "ReconciliationStockDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "<PERSON>",
                table: "ReconciliationIncomeDetail",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "ReconciliationStockDetail");

            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "ReconciliationIncomeDetail");
        }
    }
}
