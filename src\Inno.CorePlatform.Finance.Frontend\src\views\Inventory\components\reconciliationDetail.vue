<template>
  <el-dialog v-model="dialogVisible" width="83%" draggable>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation :crud="crud" />
      <inno-split-pane split="horizontal" :default-percent="60">
        <template #paneL>
          <inno-crud-operation
            :crud="crud"
            :hiddenColumns="[]"
            hidden-opts-right
            style="padding: 0"
          >
            <template #opts-left>
              <el-tabs
                v-model="crud.query.abatedStatus"
                class="demo-tabs"
              >
                <el-tab-pane :label="`按供应商和客户合计`" name="0" lazy />
              </el-tabs>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef0"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            border
            :data="crud.data"
            stripe
            show-summary
            :summary-method="getSummaries"
            height="20vh"
          >
            <el-table-column
              label="客户"
              property="companyName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.companyName }}
              </template>
            </el-table-column>
            <el-table-column
              label="供应商"
              property="agentName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.agentName }}
              </template>
            </el-table-column>
            <el-table-column
              label="含税收入"
              property="income"
              show-overflow-tooltip
              class-name="isSum"
              width="auto"
            >
              <template #default="scope">
                {{ rbstateFormat(scope.row.income) }}
              </template>
            </el-table-column>
            <el-table-column
              label="不含税收入"
              property="incomeOfNoTax"
              class-name="isSum"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ rbstateFormat(scope.row.incomeOfNoTax) }}
              </template>
            </el-table-column>
            <el-table-column
              label="含税成本"
              property="cost"
              class-name="isSum"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ rbstateFormat(scope.row.cost) }}
              </template>
            </el-table-column>
            <el-table-column
              label="不含税成本"
              property="costOfNoTax"
              class-name="isSum"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ rbstateFormat(scope.row.costOfNoTax) }}
              </template>
            </el-table-column>
            <el-table-column
              label="金蝶数据"
              property="kisData"
              class-name="isSum"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ rbstateFormat(scope.row.kisData) }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              property="rmbAmount"
              class-name="isSum"
              show-overflow-tooltip
            >
              <template #default="scope">
                <el-button type="text" @click="getDetails(scope.row)">
                  向下穿透
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            <div class="flex-1"></div>
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <el-tabs type="border-card" style="height: 100%" class="mycard_css">
            <el-tab-pane label="单据明细">
              <el-table
                ref="tableRef1"
                v-inno-loading="crud1.loading"
                class="auto-layout-table"
                highlight-current-row
                
                border
                show-summary
                :summary-method="getSummaries"
                :data="crud1.data"
                height="30vh"
                stripe
                @selection-change="crud1.selectionChangeHandler"
                @row-click="crud1.rowClick"
              >
                <el-table-column
                  label="客户"
                  property="companyName"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ scope.row.companyName }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="供应商"
                  property="agentName"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ scope.row.agentName }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="单号"
                  property="saleOrderNo"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.saleOrderNo }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column
                  label="含税收入"
                  property="accountPeriodTypeStr"
                  class-name="isSum"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ rbstateFormat(scope.row.accountPeriodTypeStr) }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="不含税收入"
                  property="incomeOfNoTax"
                  show-overflow-tooltip
                  class-name="isSum"
                >
                  <template #default="scope">
                    {{ rbstateFormat(scope.row.incomeOfNoTax) }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="含税成本"
                  property="cost"
                  show-overflow-tooltip
                  class-name="isSum"
                >
                  <template #default="scope">
                    {{ rbstateFormat(scope.row.cost) }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="不含税成本"
                  property="costOfNoTax"
                  class-name="isSum"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ rbstateFormat(scope.row.costOfNoTax) }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="金蝶数据"
                  property="kisData"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ rbstateFormat(scope.row.kisData) }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                <div class="flex-1"></div>
                <inno-crud-pagination :crud="crud1" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </template>
      </inno-split-pane>
    </div>
    <slot name="pagination"></slot>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';

const props = defineProps({
  reconciliationItemId: { type: String, default: '' }
});
const emits = defineEmits(['submit']);

const reconciliationId = ref('');

const tabsNameVal = ref('');

const crud = CRUD(
  {
    title: '按供应商和客户合计',
    url: '/api/ReconciliationStockQuery/GetListByAgent',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef0'
  },
  {
    table: 'tableRef0'
  }
);
const crud1 = CRUD(
  {
    title: '',
    url: '/api/ReconciliationStockQuery/GetListByCustomer',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef1'
  },
  {
    table: 'tableRef1'
  }
);
watch(
  () => props.reconciliationItemId,
  (newVal: any, oldVal) => {
    if (newVal === oldVal) return;
    reconciliationId.value = newVal;
    crud.query = { reconciliationItemId: newVal };
    crud.toQuery();
  },
  { deep: true }
);

const dialogVisible = ref(false);

const openDialog = () => {
  dialogVisible.value = true;
};

const closeDialog = () => {
  dialogVisible.value = false;
};
// 获取按供应商和客户合计
const getCrud = (id: String, tabsName: String) => {
  tabsNameVal.value = tabsName;
  if (tabsName === '存货') { // 根据不同tab调取不同url
    crud.url = '/api/ReconciliationStockQuery/GetListByAgent';
  } else if (tabsName === '收入成本') {
    crud.url = '/api/ReconciliationIncomeQuery/GetListByAgent';
  }
  crud.toQuery();
};

const submit = () => {
  emits('submit');
  closeDialog();
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
// 获取单据明细
const getDetails = (rowData: Object) => {
  if (tabsNameVal.value === '货存') { // 根据不同tab调取不同url
    crud1.url = '/api/ReconciliationStockQuery/GetListByCustomer';
  } else if (tabsNameVal.value === '收入成本') {
    crud1.url = '/api/ReconciliationIncomeQuery/GetListByCustomer';
  }
  crud1.query = {
    reconciliationItemId: reconciliationId.value,
    agentId: rowData.agentId,
    companyId: rowData.companyId
  };
  crud1.toQuery();
};

onActivated(() => {});
onMounted(() => {});

defineExpose({
  openDialog,
  closeDialog,
  getCrud
});
</script>
