<template>
  <el-dialog
    v-model="createDialog"
    draggable
    title="部分撤销"
    :before-close="handleClose"
    :destroy-on-close="true"
    close-on-click-modal="false"
    modal-class="position-fixed"
    width="80%"
    hight="70%"
  >
    <div>
      <el-form
        ref="formRef"
        label-position="right"
      >
        <div style="margin-bottom: 10px">
          <el-table
                id="tableDetail"
                ref="tableDetail"
                class="auto-layout-table"
                highlight-current-row
                :data="crudDetail.data"
                v-inno-loading="crudDetail.loading"
                stripe
                fit
                border
                :row-class-name="crudDetail.tableRowClassName"
                @sort-change="crudDetail.sortChange"
                @selection-change="crudDetail.selectionChangeHandler"
                @row-click="(e) => clickdetailRow(e)"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <!-- 认款明细应收详情 -->
                <!-- 新增用于显示带展开折叠功能的子表格的列 -->
                <el-table-column type="expand" fixed="left">
                  <template #default="{ row }">
                    <div style="padding-left: 54px" v-if="row.typeDescription !== '初始应收'">
                      <el-table
                        :data="row.creditInfo"
                        class="auto-layout-table"
                        stripe
                        border
                        fit
                      >
                        <el-table-column label="应收单号" property="billCode" width="240" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>  
                          </template>
                        </el-table-column>
                        <el-table-column label="应收类型" property="creditTypeStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>  
                          </template>
                        </el-table-column>
                        <el-table-column label="单据日期" property="billDateStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billDateStr }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.orderNo }}</template>
                        </el-table-column>
                        <el-table-column label="项目" property="projectName"  show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.projectName }}</template>
                        </el-table-column>
                        <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.serviceName }}</template>
                        </el-table-column>
                        <el-table-column label="金额" property="value" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.value }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="本次认款金额" property="currentValue" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.currentValue }}</inno-button-copy>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="发票号/订单号/应收单号" width="240" property="code" show-overflow-tooltip fixed="left">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="50" property="statusDescription" show-overflow-tooltip fixed="left">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.statusDescription }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="实际客户" property="customerName" width="140" show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">{{ scope.row.customerName }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="终端客户" property="hospitalName" width="140" show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">{{ scope.row.hospitalName }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="认款类型" property="typeDescription" width="80" show-overflow-tooltip></el-table-column>

                <el-table-column label="认款金额" property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="认款日期" min-width="120" property="recognizeDate" width="120" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.recognizeDate, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.serviceName }}</template>
                </el-table-column>
                <el-table-column label="是否跳号" property="isSkip" width="70" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.isSkip ? '是' : '否' }}</template>
                </el-table-column>
                <el-table-column label="细分类型" property="classifyDescription" width="100" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.classifyDescription }}</template>
                </el-table-column>
                <el-table-column label="备注" property="note" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.note }}</template>
                </el-table-column>
              </el-table>
        </div>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          :loading="cancelLoading"
          @click="partCancelFun"
        >
          撤销
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ref,
  watch,
  reactive,
  nextTick
} from 'vue';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import request from '@/utils/request';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { FormRules } from 'element-plus';
import { backendUrl, gatewayUrl } from '@/public-path';
import { Decimal } from 'decimal.js';
import _ from 'lodash';
const props = defineProps({
  showDialog: Boolean,
  itemId: { type: String, default: '' }
});
let createDialog = ref(false);
const tableDetail = ref();
const formRef = ref();
const emits = defineEmits(['closeDialog', 'refreshIndex']);
const handleClose = () => {
  createDialog.value = false;
  emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
};
const crudDetail = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/getdetail',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    tablekey: 'tablekeyDetail',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
watch(
  () => props.showDialog,
  (newValue, oldValue) => {
    if (newValue) {
      createDialog.value = true;
      crudDetail.query.id = props.itemId;
      crudDetail.query.classify = "1";
      crudDetail.query.status = "1";
      crudDetail.toQuery();
    }
  }
)
const clickdetailRow = (e) => {
  crudDetail.singleSelection(e);
};
//撤销明细
const cancelLoading = ref(false);
const partCancelFun = () => {
  console.log(crudDetail.selections.length);
  if (crudDetail.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: "请至少勾选一条需要撤销的明细",
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  var id = props.itemId;
  ElMessageBox.confirm('此操作将撤销已勾选的认款明细, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    request({
      url: '/api/RecognizeReceive/cancelreceivedetail',
      method: 'post',
      data: {
        id: id,
        detailIds: crudDetail.selections.map((item) => item.id)
      }
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
        emits('refreshIndex');
        crudDetail.selections = [];
        handleClose();
      })
      .finally(() => {
        loading.close();
      });
  });
}
</script>
