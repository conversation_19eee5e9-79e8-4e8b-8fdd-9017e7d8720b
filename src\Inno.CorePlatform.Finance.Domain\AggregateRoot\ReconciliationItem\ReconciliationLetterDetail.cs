﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.ReconciliationItem
{
    public class ReconciliationLetterDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 财务对账函Id
        /// </summary>
        public Guid ReconciliationLetterItemId { get; set; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public ReconciliationLetterEnum Classify { get; set; }

        /// <summary>
        /// 明细单据日期（开票日期、应收日期）
        /// </summary>  
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 明细单据号（开票日期、应收日期）
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 明细金额（开票金额、应收金额）
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 已收明细金额（开票金额、应收金额）
        /// </summary>
        public decimal ReceivedValue { get; set; }

        /// <summary>
        /// 未收明细金额（开票金额、应收金额）
        /// </summary>
        public decimal NonReceivedValue { get; set; }
    }
}
