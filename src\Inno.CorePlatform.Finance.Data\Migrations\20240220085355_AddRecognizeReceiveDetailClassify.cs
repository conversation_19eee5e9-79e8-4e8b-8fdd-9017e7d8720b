﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRecognizeReceiveDetailClassify : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Classify",
                table: "RecognizeReceiveTempDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Classify",
                table: "RecognizeReceiveDetail",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Classify",
                table: "RecognizeReceiveTempDetail");

            migrationBuilder.DropColumn(
                name: "Classify",
                table: "RecognizeReceiveDetail");
        }
    }
}
