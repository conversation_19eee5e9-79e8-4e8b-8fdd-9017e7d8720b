<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}

.el-form-item {
  :deep(.el-form-item__label) {
    width: 100px;
  }
}
</style>

<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>发票填报</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL>
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button slot="reference" :disabled="crud.selections.length === 0" v-if="crud.rowData.sunPurchaseStatusStr === '待填报'" @click="ignore">忽略发票</el-button>
              <el-button slot="reference" type="primary" :disabled="crud2.selections.length === 0" v-if="crud.rowData.sunPurchaseStatusStr === '待填报'" @click="filling">发票填报</el-button>
              <!-- <el-button slot="reference" type="warning" @click="exportdata">导出数据</el-button> -->
            </template>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.sunPurchaseStatus" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`待填报(${tabCount.waitSubmitCount})`" name="0" lazy />
                  <el-tab-pane :label="`已完成(${tabCount.complateCount})`" name="99" lazy />
                  <el-tab-pane :label="`已忽略(${tabCount.rejectCount})`" name="88" lazy />
                  <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table ref="tableItem"
                    v-inno-loading="crud.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    :data="crud.data"
                    stripe
                    fit1
                    border
                    show-summary
                    :summary-method="getSummaries"
                    :row-class-name="crud.tableRowClassName"
                    @sort-change="crud.sortChange"
                    @selection-change="crud.selectionChangeHandler"
                    @row-click="crud.singleSelection">
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column label="发票号" property="invoiceNo" fixed="left" show-overflow-tooltip sortable min-width="100">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="填报发票号" property="sunPurchaseInvoiceNo" fixed="left" show-overflow-tooltip sortable min-width="140">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.sunPurchaseInvoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票代码" property="invoiceCode" fixed="left" show-overflow-tooltip sortable min-width="140">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="填报发票代码" property="sunPurchaseInvoiceCode" fixed="left" show-overflow-tooltip sortable min-width="140">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.sunPurchaseInvoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="开票金额" property="invoiceAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="invoiceTime" label="发票日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.invoiceTime === null
                ? ''
                : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.createdTime === null
                ? ''
                : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="申开单号" property="code" show-overflow-tooltip sortable min-width="100">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="医院" property="customerName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" width="300" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="sunPurchaseStatusStr" min-width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.sunPurchaseStatusStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票填报人" property="updatedBy" width="90" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.updatedByName }}
              </template>
            </el-table-column>
            <el-table-column label="阳采发票状态" property="sunPurchaseInvoiceStatus" width="100" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.sunPurchaseInvoiceStatus }}
              </template>
            </el-table-column>
            <el-table-column label="发票状态操作" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-button type="text" :loading="scope.row.loading" @click="sync(scope.row)">更新</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <inno-crud-operation :crud="crud2" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button slot="reference" :disabled="crud.rowData.sunPurchaseStatusStr !=='待填报' || crud2.selections.length === 0" type="primary" @click="editdetail">编辑</el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs class="demo-tabs">
                  <el-tab-pane :label="`订单信息`" name="0" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table ref="tableRef2"
                    v-inno-loading="crud2.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    border
                    :data="crud2.data"
                    stripe
                    :row-class-name="crud2.tableRowClassName"
                    @selection-change="crud2.selectionChangeHandler"
                    @row-click="crud2.singleSelection">
            <el-table-column label="阳采采购单号" property="purchaseCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="阳采销售订单号" property="xsddh" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.xsddh }}</inno-button-copy>
              </template>
            </el-table-column>
            <!-- <el-table-column label="顺序号" property="sxh" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.sxh }}</inno-button-copy>
          </template>
        </el-table-column>-->
            <el-table-column label="货号" property="productNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="企业本地代码" property="qybddm" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.qybddm }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="销售单价" property="hsdj" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.hsdj" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="采购数量" property="spsl" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.spsl }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="注册证号" property="zczh" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.zczh }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="生产批号" property="scph" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.scph }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="关联明细编号" property="glmxbh" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.glmxbh }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="耗材统编代码" property="hctbdm" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.hctbdm }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="规格型号说明" property="ggxhsm" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.ggxhsm }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="小计" property="subtotal" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.subtotal }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud2.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud2" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <!-- 发票填报组件 -->
    <el-dialog v-model="invoiceFillingDialog" title="发票填报" style="width:36.4%">
      <el-form ref="invoiceFillingformRef" :model="invoiceFillingData" :rules="invoiceFillingformRules">
        <el-form-item label="配送点" prop="psdbm" required>
          <el-select v-model="invoiceFillingData.psdbmOpt" clearable filterable placeholder="请选择配送点编码" @change="psdbmChange">
            <el-option v-for="item in psdbms" :key="item.id" :label="item.name" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发票号" prop="finishInvoiceNo" required>
          <el-input v-model="invoiceFillingData.finishInvoiceNo" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="发票代码" prop="finishInvoiceCode" required>
          <el-input v-model="invoiceFillingData.finishInvoiceCode" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="发票备注">
          <el-input v-model="invoiceFillingData.remark" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="invoiceFillingDialog = false">取消</el-button>
          <el-button type="primary" :loading="fillloading" @click="invoiceFillingSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 发票填报确认组件 -->
    <el-dialog v-model="invoiceFillingConfirmDialog" title="发票填报确认" style="width:36.4%">
      <el-form ref="invoiceFillingConfirmformRef" :model="invoiceFillingData" :rules="invoiceFillingConfirmformRules">
        <el-form-item label="采购单编号" prop="purchaseCode" required>
          <el-input v-model="invoiceFillingData.purchaseCode"></el-input>
        </el-form-item>
        <el-form-item label="发票备注">
          <el-input v-model="invoiceFillingData.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="invoiceFillingConfirmDialog = false">取消</el-button>
          <el-button type="primary" :loading="fillloading" @click="invoiceFillingSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 编辑发票上报详细组件 -->
    <el-dialog v-model="editInvoiceFillingDialog" title="编辑发票上报详细信息" style="width:36.4%">
      <el-form>
        <el-form-item label="阳采采购单号">
          <el-input v-model="editInvoiceFillingData.purchaseCode"></el-input>
        </el-form-item>
        <el-form-item label="阳采销售订单号">
          <el-input v-model="editInvoiceFillingData.xsddh"></el-input>
        </el-form-item>
        <el-form-item label="货号">
          <el-input v-model="editInvoiceFillingData.productNo"></el-input>
        </el-form-item>
        <el-form-item label="耗材统编代码">
          <el-input v-model="editInvoiceFillingData.hctbdm"></el-input>
        </el-form-item>
        <!-- 
        <el-form-item label="采购数量">
          <el-input v-model="editInvoiceFillingData.spsl"></el-input>
        </el-form-item>-->
        <el-form-item label="注册证号">
          <el-input v-model="editInvoiceFillingData.zczh"></el-input>
        </el-form-item>
        <!-- <el-form-item label="医院本地编码">
          <el-input v-model="editInvoiceFillingData.qybddm"></el-input>
        </el-form-item>-->
        <el-form-item label="生产批号">
          <el-input v-model="editInvoiceFillingData.scph"></el-input>
        </el-form-item>
        <el-form-item label="关联明细编号">
          <el-input v-model="editInvoiceFillingData.glmxbh"></el-input>
        </el-form-item>
        <el-form-item label="规格型号说明">
          <el-input v-model="editInvoiceFillingData.ggxhsm"></el-input>
        </el-form-item>
        <el-form-item label="销售单价">
          <el-input-number v-model="editInvoiceFillingData.hsdj" :min="0" :precision="2" style="width: 200px;"></el-input-number>
        </el-form-item>
        <el-form-item label="价格小计">
          <el-input v-model="hsdjSpslProduct" disabled style="width: 200px;"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editInvoiceFillingDialog = false">取消</el-button>
          <el-button type="primary" :loading="editfillloading" @click="editInvoiceFillingSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable,ElLoading } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag, getUserNames } from '@inno/inno-mc-vue3/lib/crud';
import request from '@/utils/request';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ExportInvoices } from '@/api/financeapi';
import { FormRules } from 'element-plus';
const functionUris = {
  export: 'metadata://fam/finance-InvoiceCredit/functions/excute-export'
};
const tableItem = ref<InstanceType<typeof ElTable>>();
const props = defineProps({
  __refresh: Boolean
});
const crud = CRUD(
  {
    title: '发票清单',
    url: '/api/InvoiceQuery/GetInvoices',
    idField: 'id',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      searchKey: '',
      sunPurchaseStatus: '0', //待填报,
      IsSimple: 1
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
        getUserNamesFn();
        loadTableData();                                                                
      }
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableItem
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '订单信息',
    url: '/api/InvoiceQuery/GetSunPurchaseInvoiceDetailByInvoiceNo',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list', 
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud2.data.length && crud2.data.length > 0) {
          crud2.singleSelection(crud2.data[0]);
        }                                                            
      }
    },
    tablekey: 'tableRef2',
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableRef2
  }
);
onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();
  }
});
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  crud.toQuery();
});
const InvoiceTypelist = [
  {
    label: '电子普通发票',
    value: '电子普通发票'
  },
  {
    label: '电子专用发票',
    value: '电子专用发票'
  },
  {
    label: '纸质普通发票',
    value: '纸质普通发票'
  },
  {
    label: '纸质专用发票',
    value: '纸质专用发票'
  },
  {
    label: '增值税普通发票(卷票)',
    value: '增值税普通发票(卷票)'
  },
  {
    label: '数电票(增值税专用发票)',
    value: '数电票(增值税专用发票)'
  },
  {
    label: '数电票(普通发票)',
    value: '数电票(普通发票)'
  }
];
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  return [
    {
      key: 'invoiceNo',
      label: '发票号',
      show: true
    },
    {
      key: 'invoiceCode',
      label: '发票代码',
      show: true
    },
    {
      key: 'type',
      label: '发票类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: InvoiceTypelist,
      show: true
    },
    {
      key: 'billDateBeging',
      endDate: 'billDateEnd',
      label: '开票日期',
      type: 'daterange',
      show: true,
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ]
    },
    {
      key: 'customerId',
      label: '客户',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      placeholder: '客户搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`,
      valueK: 'businessUnitID',
      labelK: 'businessUnitName',
      props: { KeyWord: 'nameLike', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'code',
      label: '开票申请单号',
      show: true
    },
    {
      key: 'companyId',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      placeholder: '公司搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'shipmentCode',
      label: '三方单号',
      show: true
    },
    {
      key: 'updatedBy',
      label: '发票填报人',
      multiple: true,
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
      placeholder: '用户名称搜索',
      valueK: 'name',
      labelK: 'displayName',
      props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
      slots: {
        option: ({ item }) => (
          <>
            <span>{item.displayName}</span>
            <span style="float:right">{item.name}</span>
          </>
        )
      }
    }
  ];
});

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud2.query = {
        invoiceNo: crud.rowData.invoiceNo,
      };
      crud2.toQuery();
    }
  },
  { deep: true }
);

let tabCount = ref({
  waitSubmitCount: 0,
  rejectCount: 0,
  complateCount:0,
  allCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/InvoiceQuery/GetSunPurchaseTabCount',
    data: {
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
 
const exportdata = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => { 
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
    var exportquery = JSON.parse(JSON.stringify(crud.query));
    exportquery['page'] = 0;
    exportquery['limit'] = 0;
    ExportInvoices(exportquery);
    loading.close();
  });
};
const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};
//表单规则
const invoiceFillingformRef = ref();
const invoiceFillingformRules = reactive<FormRules>({
  'psdbmOpt.id': [
    {
      required: true,
      message: '请选择配送点',
      trigger: 'change'
    }
  ],
  finishInvoiceNo: [
    {
      required: true,
      message: '请填写发票号',
      trigger: 'blur'
    }
  ],
  finishInvoiceCode: [
    {
      required: true,
      message: '请填写发票代码',
      trigger: 'blur'
    }
  ],
  psdh: [
    {
      required: true,
      message: '请选择配送单',
      trigger: 'change'
    }
  ]
});
const invoiceFillingConfirmformRef = ref();
const invoiceFillingConfirmformRules = reactive<FormRules>({
  purchaseCode: [
    {
      required: true,
      message: '请填写采购单编号',
      trigger: 'blur'
    }
  ]
});
//同步状态
const sync = (row) => {
  row.loading = true;
  if (row.sunPurchaseStatusStr === '已完成') {
    request({
      url: '/api/InvoiceQuery/SyncSunPurchaseInvoiceStatus',
      data: {
        invoiceNo: row.invoiceNo
      },
      method: 'post'
    }).then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'success',
          duration: 3 * 1000
        });
        row.loading = false;
        crud.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        row.loading = false;
      }
    });
  }
  else {
    ElMessage({
      showClose: true,
      message: '操作成功,当前阳采状态为'+ row.sunPurchaseStatusStr +'！',
      type: 'success',
      duration: 3 * 1000
    });
    row.loading = false;
    return;
  }
}
//忽略发票
const ignore = () => {
  request({
    url: '/api/InvoiceQuery/IgnoreSunPurchaseInvoice',
    data: {
      invoiceNo: crud.rowData.invoiceNo
    },
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'success',
        duration: 3 * 1000
      });
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
    }
  });
}
//发票填报弹窗
const invoiceFillingDialog = ref(false);
//发票填报确认弹窗
const invoiceFillingConfirmDialog = ref(false);
//发票填报配送点编码集合
const psdbms = ref([]);
//发票填报 Vo
const invoiceFillingData = ref({
  invoiceNo: '',
  finishInvoiceNo: '',
  finishInvoiceCode: '',
  psdbmOpt: {},
  psdbm: '',
  yybm:'',
  psdh:'',
  purchaseCode: '',
  cglx:'',
  remark:''
})
//配送点编码更改事件
const psdbmChange = () => {
  invoiceFillingData.value.psdbm = invoiceFillingData.value.psdbmOpt.id
}
//发票填报
const filling = () => {
  invoiceFillingData.value.finishInvoiceNo = crud.rowData.invoiceNo
  invoiceFillingData.value.invoiceNo = crud.rowData.invoiceNo
  invoiceFillingData.value.finishInvoiceCode = crud.rowData.invoiceCode
  //判断有无关联阳采采购单
  // if (crud.rowData.purchaseCode !== null && crud.rowData.purchaseCode !== '') {
  //   //有
  //   invoiceFillingDialog.value = true;
  // } else {
  //   //无
  //   invoiceFillingConfirmDialog.value = true;
  // }
  // 获取配送编码等数据
  request({
    url: '/api/InvoiceQuery/GetPsdByPurchaseCodeForPm',
    data: {
      purchaseCode: crud2.rowData.purchaseCode
    },
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      if (res.data.data === null) {
        ElMessage({
          showClose: true,
          message: '未获取到采购单号'+crud2.rowData.purchaseCode+'的配送数据',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
      psdbms.value = res.data.data.deliveryPoints
      if (psdbms.value.length > 0) {
        invoiceFillingData.value.psdbm = res.data.data.deliveryPoints[0].id
      }
      invoiceFillingData.value.cglx = res.data.data.purchaseOrderType
      invoiceFillingData.value.yybm = res.data.data.hospitalCode
      invoiceFillingDialog.value = true;
      curd.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
    }
  });
}
//填报提交
const fillloading = ref(false);
const invoiceFillingSubmit = () => {
  if (invoiceFillingDialog.value) {
    //填报
    if (!invoiceFillingformRef.value) return;
    invoiceFillingformRef.value.validate(async (valid, fields) => {
      if (valid) {
        submitOpt();
      }
    })
  } else if (invoiceFillingConfirmDialog.value) {
    //填报确认
    if (!invoiceFillingConfirmformRef.value) return;
    invoiceFillingConfirmformRef.value.validate(async (valid, fields) => {
      if (valid) {
        submitOpt();
      }
    })
  }
}
//调用同一接口
const submitOpt = () => {
  console.log(invoiceFillingData.value.psdbm)
  fillloading.value = true;
  request({
    url: '/api/InvoiceQuery/SubmitSunPurchaseInvoice',
    data: invoiceFillingData.value,
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'success',
        duration: 3 * 1000
      });
      invoiceFillingDialog.value = false;
      invoiceFillingConfirmDialog.value = false;
      editInvoiceFillingDialog.value = false;
      fillloading.value = false;
      crud2.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      fillloading.value = false;
      invoiceFillingDialog.value = false;
      invoiceFillingConfirmDialog.value = false;
    }
  });
}
//编辑发票上报详细 Vo
const editInvoiceFillingDialog = ref(false);
const editInvoiceFillingData = ref({
  id:'',
  hctbdm: '',
  qybddm: '',
  zczh: '',
  hsdj: 0,
  spsl: 0,
})
//编辑明细
const editdetail = () => {
  console.log(JSON.stringify(crud2.rowData))
  editInvoiceFillingData.value = crud2.rowData;
  editInvoiceFillingDialog.value = true;
}
//编辑发票上报详细提交
const editfillloading = ref(false);
const editInvoiceFillingSubmit = () => {
  editfillloading.value = true;
  request({
    url: '/api/InvoiceQuery/EditSunPurchaseInvoiceDetail',
    data: editInvoiceFillingData.value,
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'success',
        duration: 3 * 1000
      });
      editfillloading.value = false;
      editInvoiceFillingDialog.value = false;
      crud2.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      editfillloading.value = false;
    }
  });
}
  const getUserNamesFn = async () => {
    crud.data = await getUserNames(crud.data, ['updatedBy']);
  }

// 定义计算属性  
const hsdjSpslProduct = computed(() => {  
  return (editInvoiceFillingData.value.hsdj * editInvoiceFillingData.value.spsl).toFixed(2);  
});  
</script>
