import { nextTick } from 'vue';
import CRUD from '@inno/inno-mc-vue3/lib/crud';
import {
  tabCount,
  selectId,
  getDetailData,
  crudDetail,
  tableItem
} from './index.vue';

export const crud = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/getlist',
    idField: 'id',
    method: 'post',
    sort: ['createdTime,desc'],
    query: {
      status: '',
      id: '',
      abatedStatus: ''
    },
    props: {
      // 默认隐藏搜索
      searchToggle: true
    },
    userNames: ['createdBy'],
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        console.log(crud.data);
        if (crud.data.length > 0) {
          tabCount.value.auditingCount = crud.data[0].auditingCount;
          tabCount.value.completedCount = crud.data[0].completedCount;
          tabCount.value.waitExecuteCount = crud.data[0].waitExecuteCount;
          tabCount.value.waitSubmitCount = crud.data[0].waitSubmitCount;
          tabCount.value.allCount =
            tabCount.value.auditingCount +
            tabCount.value.completedCount +
            tabCount.value.waitExecuteCount +
            tabCount.value.waitSubmitCount;
        }
        if (crud.length <= 0 && crud.query.status == '') {
          tabCount.value.auditingCount = 0;
          tabCount.value.completedCount = 0;
          tabCount.value.waitExecuteCount = 0;
          tabCount.value.waitSubmitCount = 0;
          tabCount.value.allCount = 0;
        }

        // else {
        //   tabCount.value.auditingCount = 0;
        //   tabCount.value.completedCount = 0;
        //   tabCount.value.waitExecuteCount = 0;
        //   tabCount.value.waitSubmitCount = 0;
        //   tabCount.value.allCount = 0;
        // }
        // loadTableData();
        //默认选中第一行
        if (crud.data.length) {
          nextTick(() => {
            crud.singleSelection(crud.data[0]);
            selectId.value = crud.data[0].id;
            // crudDetail.toQuery();
            getDetailData(crud.data[0]);
          });
        } else {
          crudDetail.data = [];
        }
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
