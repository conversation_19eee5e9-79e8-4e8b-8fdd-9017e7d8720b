﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain
{
    public class DomainConstants
    {
        #region PubSub
        /// <summary>
        /// 默认的PubSub名称
        /// </summary>
        public const string Default_PubSubName = "pubsub-default";

        /// <summary>
        /// 批量打包下载发票
        /// </summary>
        public const string Batch_Download_Invoice = "finance-finance-batchDownloadInvoice";

        /// <summary>
        /// 财务异步记录日志广播
        /// </summary>
        public const string FINANCE_SAVE_SUBLOG = "finance-finance-saveSubLog";
        #endregion

        /// <summary>
        /// 变更核算部门
        /// </summary>
        public const string CHANGE_BUSSINESS_DEPT_TYPE_NAME = "更换核算部门";
        /// <summary>
        /// 是否自动生成
        /// </summary>
        public const int CHANGE_BUSSINESS_DEPT_AUTO = 1;

        /// <summary>
        /// 变更核算部门负数单类型
        /// </summary>
        public const string CHANGE_BUSSINESS_DEPT_MINUSTYPE = "98";

        /// <summary>
        /// 变更核算部门新单类型
        /// </summary>
        public const string CHANGE_BUSSINESS_DEPT_NEWTYPE = "99";
        /// <summary>
        /// 应付执行类别
        /// </summary>
        public const string DebtExecuteType = "debt";
        /// <summary>
        /// 金蝶回调
        /// </summary>
        public const string OPERATION_KingdeeCallBack = "金蝶回调";
    }
}
