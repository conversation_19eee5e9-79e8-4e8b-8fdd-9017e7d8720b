<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-debtQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>订货系统应付盘点管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud">
        <el-input slot="right" v-model="crud.query.searchKey" placeholder="模糊查询单号/公司" suffix-icon="el-icon-search" style="width: 160px; margin-right: 10px" @change="crud.toQuery"></el-input>
      </inno-crud-operation>
    </div>

    <div class="app-page-body" style="padding-top: 0px">
      <inno-query-operation :crud="crud" :query-list.sync="queryList" />

      <inno-split-pane split="horizontal" :default-percent="50">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`盘点主体信息`" />
              </el-tabs>
            </template>
            <template #default>
              <!-- v-auth="functionUris.confirm" -->
              <inno-button-tooltip type="success" v-if="hasPermission(functionUris.confirm)" @click="confirm">确认</inno-button-tooltip>
              <!-- v-auth="functionUris.export" -->
              <inno-button-tooltip type="warning" v-if="hasPermission(functionUris.export)" :loading="downLoading" @click="
                  downloadAsync(
                    'api/InventoryQuery/CreditDownLoadByCoordinate',
                    '盘点清单',
                    crud.query
                  )
                ">导出应付盘点</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRef"
              v-inno-loading="crud.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              border
              :data="crud.data"
              @selection-change="crud.selectionChangeHandler"
              @row-click="crud.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="companyName" label="盘点公司" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="billDate" label="盘点日期" min-width="110">
                <template #default="scope">
                  {{
                  scope.row.billDate === null
                  ? ''
                  : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column label="是否确认" property="isConfirm" width="80" show-overflow-tooltip>
                <template #default="scope">
                  <el-tag :type="scope.row.isConfirm === 1 ? 'success' : 'error'" disable-transitions>
                    {{ scope.row.isConfirm === 1 ? '已确认' : '未确认' }}
                  </el-tag>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="code" label="盘点单号" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                </template>
              </el-table-column> -->
              <!-- <el-table-column show-overflow-tooltip prop="createdTime" label="生成日期" min-width="110">
                  <template #default="scope">
                    {{
                    scope.row.createdTime === null
                    ? ''
                    : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                    }}
                  </template>
              </el-table-column>-->
              <!-- <el-table-column prop="userName" label="操作用户" min-width="110" /> -->
              <!-- <el-table-column label="操作人" property="createdByName" width="110" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.createdByName }}</template>
              </el-table-column> -->
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`盘点详细信息`" />
              </el-tabs>
            </template>
            <template #default>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRefDeJd"
              v-inno-loading="crudDeJd.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              border
              show-summary
              :summary-method="getSummaries"
              :data="crudDeJd.data"
              :row-class-name="crudDeJd.tableRowClassName"
              @selection-change="crudDeJd.selectionChangeHandler"
              @row-click="crudDeJd.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="billCode" label="单据编号" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="customerName" label="客户单位" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.customerName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.serviceName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="typename" label="单据类型" min-width="110" />
              <el-table-column class-name="isSumChild" sortable prop="credit.value" label="金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.credit.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="abatedValue" label="冲销金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.abatedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="value" label="余额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <!-- <el-table-column sortable prop="invoiceNo" label="发票号码" min-width="110">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="amount" label="发票金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.amount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="invoiceAmount" label="开票金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="invoiceTime" label="开票日期" min-width="110">
                  <template #default="scope">
                    {{
                    scope.row.invoiceTime === null
                    ? ''
                    : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                    }}
                  </template>
              </el-table-column> -->
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crudDeJd.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudDeJd" />
          </div>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
//import { useB2cStore } from '@inno/inno-mc-vue3/template/stores/b2c';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';

const route = useRoute();
//const b2c = useB2cStore();

const currentUser = useUserStore();
//const currentUser = {"$id":"user","avatar":"https://static.innostic.com/web/static/img/head.png","introduction":"","name":"liushisan","roles":[],"token":"eyJhbGciOiJSUzI1NiIsImtpZCI6IjEtSDJEMVdfblNRd3A2R0xfTXhWenhxd1YtUWlUQlNmZWk5N3dfYkJBbVEiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ABOdmxKL26DG-NC3qbDq3hZJOA7rXdyGa3JA-PxPA21O82_XXLWsk9BLgdc35bT20kH7wvb3_sjNjDUBHdq4Bm8qsyHFV45qX-sZ_OksGxaVrNmEAi62cbOL33HTFTzBxjOTJo3knTMKbXFCzI5aCNL9uEp9XbI9IMr5_ZDQKRDXgUgR7O2kYYRULo8xjzOdg7rLoF5vK68DC_gTQ9NMipJu12JdBHnzxH4Py-xQIoimasFNIE0ybOFjxz4o241uRWqvfjmmwIVU8ma2Jxt1ZnwV3Jfzs_N6ipJHIVRGr9P2N-OpkCg2AZDX-wkPRRjHQ-DkIRyUdzJ_IxHJxio7qg","userId":"701462ad-8d8d-45b6-8a08-b4a283e892a4","username":"h_liushisan","institutionName":"920医院","institutionId":"0ad48e7d-c0f4-46d3-a634-17b655b8444e","institutions":[{"isEsign":null,"esignMobilePhoneNum":null,"esignId":null,"id":"0ad48e7d-c0f4-46d3-a634-17b655b8444e","name":"920医院"},{"isEsign":null,"esignMobilePhoneNum":null,"esignId":null,"id":"a48bd7b8-110b-4110-81cb-12b262361fe2","name":"306医院"},{"isEsign":null,"esignMobilePhoneNum":null,"esignId":null,"id":"42acad22-7483-4502-a9cf-7ab346ab796b","name":"3PL库房"}],"saleSystemId":"01975df9-948e-42c3-e8eb-08dc75718c9c","saleSystems":[{"id":"87df7bcf-37e7-4c19-e61e-08dbba88c5cc","name":"石顺谨测试用"},{"id":"5e80554a-6661-4a98-2c61-08dbca3a0ef0","name":"彭理专用"},{"id":"c4412b4c-35f2-4985-2083-08dbdb4a1106","name":"销售二期专用"},{"id":"8eda3eea-2c25-4660-3458-08dc05bca298","name":"刘怡佳销售子系统二"},{"id":"557321c5-4269-40ba-06ad-08dc4b08acee","name":"LYJ盘点子系统"},{"id":"580384c1-62b8-49e1-37e9-08dc4b1bb404","name":"LYJ销售子系统NNN"},{"id":"6aee8a9d-e97d-457b-ecb3-08dc4d3fe469","name":"重庆医科大学附属第一医院"},{"id":"adfa046d-cd13-4bbf-5cf7-08dc754c7417","name":"福州建发销售子系统"},{"id":"01975df9-948e-42c3-e8eb-08dc75718c9c","name":"建发致新福建销售子系统"},{"id":"52ee6e47-41c0-43bb-928a-08dcb2098919","name":"哈尔滨-经销销售"},{"id":"8ae24126-b94c-453c-b8f0-08dcbc2e9a22","name":"wms盘点销售子系统"},{"id":"2bb15169-87b2-41d7-17ea-08dcc1c9323e","name":"LYj销售子系统KKK"},{"id":"4513369a-3c84-4e6a-3e08-08dcc7f1d874","name":"刘怡佳销售子系统"},{"id":"2b00450f-4d7f-47b5-4d7d-08dcecba4891","name":"测试账期Y-厂家代客0"},{"id":"3ad4bde0-403b-423e-1178-08dcfa199d3f","name":"刘怡佳西安子系统A"},{"id":"080a8e20-65b0-410d-d41a-08dd0a254b6b","name":"春立-1125"},{"id":"3672b1a4-d131-4b22-e0e2-08dd2495624f","name":"销售子系统经销lyj2301"},{"id":"3ef57e07-69b6-499b-266f-08dd24ace79a","name":"销售子系统寄售lyj1801"}],"groupPurchaseId":"","_isOptionsAPI":true}
const tableRef = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '应收盘点主体信息',
    url: '/api/InventoryQuery/GetCreditRecordList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      code: '',
      customerId: currentUser.institutionId,
      customerIds: currentUser.institutions.map(x=>x.id),
      saleSystemId: currentUser.saleSystemId,
      saleSystemIds: currentUser.saleSystems.map(x=>x.id)
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    },
    method: 'post',
    userNames: ['createdBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDeJd = CRUD(
  {
    title: '应收盘点详细信息',
    url: '/api/InventoryQuery/GetCreditRecordDetailList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      customerId: currentUser.institutionId,
      customerIds: currentUser.institutions.map(x=>x.id),
      isFilterCompany:true
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDeJd
  }
);

const queryList = computed(() => [
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司搜索',
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data'
    },
    show: true
  },
  {
    key: 'billDateBeging',
    endDate: 'billDateEnd',
    label: '盘点日期',
    type: 'daterange',
    formart: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  }
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
onMounted(() => {
  if (route.query && route.query.code) {
    crud.query.code = route.query.code;
    crud.query.customerId = currentUser.institutions.id;
  }
  crud.toQuery();
  tableDrag(tableRef);
  tableDrag(tableRefDeJd);
});
watch(
  () => crud.rowData.id,
  (n, o) => {
    console.log('当前登录用户信息：' + JSON.stringify(currentUser))
    crud.query.customerId = currentUser.institutionId
    if (n != null) {
      crudDeJd.query = {
        searchKey: '',
        isFilterCompany:true,
        customerId: currentUser.institutionId,
        customerIds:currentUser.institutions.map(x=>x.id),
        itemId: crud.rowData.id
      };
      crudDeJd.toQuery();
    }
  },
  { deep: true }
);
const functionUris = {
  confirm: 'metadata://fam/Inventory-creditRecordDh/functions/creditRecordDh-confirm',
  export: 'metadata://fam/Inventory-creditRecordDh/functions/creditRecordDh-export'
};
//下载
const download = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportConvertPaymentList(crud.query, '应收盘点');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportConvertPaymentList = async (data, filename) => {
  await request({
    url: '/api/InventoryQuery/CreditDownLoad',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum','isSumChild'].includes(column.className)) {
      var values = [];
      if (column.className == 'isSum')
      {
         values = data.map((item) => Number(item[column.property]));
      }
      else if (column.className == 'isSumChild') {
        // 子实体
        const parts = column.property.split('.');
        //const child = parts[0];
        const prop = parts[1];
        values = data.map((item) => Number(item.credit[prop]));
      }
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
//协调服务导出
let downLoading = ref(false);
const downloadAsync = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  data.customerIds = currentUser.institutions.map(x=>x.id);
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
//确认
const confirm = () => {
  ElMessageBox.confirm(
    '是否确认此条盘点信息',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    request({
    url: '/api/InventoryQuery/ConfirmCreditRecordItem?id='+crud.rowData.id,
    method: 'get',
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '确认成功',
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        return false;
      }
    })
  })
}
</script>
