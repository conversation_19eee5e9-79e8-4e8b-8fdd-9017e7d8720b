﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addCustomizeInvoiceDetailId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "BillDate",
                table: "RefundItem",
                type: "datetime2",
                nullable: true,
                comment: "单据日期",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldComment: "单据日期");

            migrationBuilder.AddColumn<Guid>(
                name: "CustomizeInvoiceDetailId",
                table: "CustomizeInvoiceCredit",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                comment: "开票明细Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomizeInvoiceDetailId",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.AlterColumn<DateTime>(
                name: "BillDate",
                table: "RefundItem",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                comment: "单据日期",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldComment: "单据日期");
        }
    }
}
