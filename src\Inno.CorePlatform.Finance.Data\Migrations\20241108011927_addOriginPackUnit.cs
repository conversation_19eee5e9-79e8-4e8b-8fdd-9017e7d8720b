﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addOriginPackUnit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceSubDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始计量单位");

            migrationBuilder.AddColumn<string>(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始计量单位");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceSubDetail");

            migrationBuilder.DropColumn(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceDetail");
        }
    }
}
