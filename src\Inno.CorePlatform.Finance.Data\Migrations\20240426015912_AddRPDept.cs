﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRPDept : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullName",
                table: "RebateProvisionItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullPath",
                table: "RebateProvisionItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptId",
                table: "RebateProvisionItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptShortName",
                table: "RebateProvisionItem",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BusinessDeptFullName",
                table: "RebateProvisionItem");

            migrationBuilder.DropColumn(
                name: "BusinessDeptFullPath",
                table: "RebateProvisionItem");

            migrationBuilder.DropColumn(
                name: "BusinessDeptId",
                table: "RebateProvisionItem");

            migrationBuilder.DropColumn(
                name: "BusinessDeptShortName",
                table: "RebateProvisionItem");
        }
    }
}
