using Dapr;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Dapr.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using System.Text;
using Inno.CorePlatform.Common.Utility.Strings;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 盘点事件订阅处理器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class InventoryEventSubController : BaseApiController<InventoryEventSubController>
    {
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly ITinyApiClientOfResponseData _tinyApiClientOfResponseData;
        private readonly ISginyApiClient _sginyApiClient;
        private readonly IInventoryApiClientOfResponseData _inventoryApiClientOfResponseData;
        private readonly IApplyBFFService _applyBFFService;
        private readonly IRebateProvisionQueryService _rebateProvisionQueryService;
        private readonly IAutoInventoryService _autoInventoryService;
        private readonly IAutoRebateProvisionService _autoRebateProvisionService;
        private readonly IInventoryCompletionService _inventoryCompletionService;
        private readonly ILossRecognitionAppService _lossRecognitionAppService;

        public InventoryEventSubController(
            ILogger<InventoryEventSubController> logger,
            DaprClient daprClient,
            IEDAFailureMsgClient edaFailureMsgClient,
            IEasyCachingProvider easyCaching,
            IInventoryMgmAppService inventoryMgmAppService,
            ITinyApiClientOfResponseData tinyApiClientOfResponseData,
            ISginyApiClient sginyApiClient,
            IBDSApiClient bDSApiClient,
            IInventoryApiClientOfResponseData inventoryApiClientOfResponseData,
            IApplyBFFService applyBFFService,
            IRebateProvisionQueryService rebateProvisionQueryService,
            IAutoInventoryService autoInventoryService,
            IAutoRebateProvisionService autoRebateProvisionService,
            IInventoryCompletionService inventoryCompletionService,
            ILossRecognitionAppService lossRecognitionAppService)
            : base(daprClient, logger, edaFailureMsgClient)
        {
            _easyCaching = easyCaching;
            _inventoryMgmAppService = inventoryMgmAppService;
            _tinyApiClientOfResponseData = tinyApiClientOfResponseData;
            _sginyApiClient = sginyApiClient;
            // _bDSApiClient = bDSApiClient; // 已移除，系统月度更新在 AutoInventoryService 中处理
            _inventoryApiClientOfResponseData = inventoryApiClientOfResponseData;
            _applyBFFService = applyBFFService;
            _rebateProvisionQueryService = rebateProvisionQueryService;
            _autoInventoryService = autoInventoryService;
            _autoRebateProvisionService = autoRebateProvisionService;
            _inventoryCompletionService = inventoryCompletionService;
            _lossRecognitionAppService = lossRecognitionAppService;
        }

        /// <summary>
        /// 处理创建其他盘点事件（统一处理器）
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        [HttpPost("CreateOtherCheck")]
        [Topic("pubsub-default", "finance-inventory-createothercheck")]
        public async Task<ActionResult> CreateOtherCheck(CreateOtherCheckEventDto eventDto)
        {
            var jsonStr = JsonConvert.SerializeObject(eventDto);
            var cacheKey = $"finance-inventory-createothercheck_{eventDto.EventId}";

            try
            {
                // 防重复处理
                var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
                if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                {
                    _logger.LogInformation($"事件已处理过，跳过: {eventDto.EventId}");
                    return Ok();
                }

                await _easyCaching.SetAsync(cacheKey, jsonStr, TimeSpan.FromMinutes(10));
                _logger.LogInformation($"开始处理创建其他盘点事件: {jsonStr}, 待执行动作数量: {eventDto.Actions.Count}");

                // 按顺序处理每个盘点动作，收集所有字段更新，最后批量更新
                var actionResults = new List<(string ActionName, bool IsSuccess, string Message, string FieldValue, long Duration)>();
                var actionsToExecute = eventDto.Actions.Where(a => a.NeedExecute).OrderBy(a => a.Order).ToList();
                var fieldUpdates = new Dictionary<string, string>(); // 收集所有字段更新

                _logger.LogInformation($"开始执行 {actionsToExecute.Count} 个盘点动作");

                foreach (var action in actionsToExecute)
                {
                    var actionStartTime = DateTimeOffset.UtcNow;
                    try
                    {
                        string result = string.Empty;
                        string fieldName = string.Empty;

                        // 直接处理不同的动作类型
                        (result, fieldName) = action.ActionType switch
                        {
                            InventoryActionType.CreateTinyInventory => (await ProcessCreateTinyInventoryWithoutUpdate(eventDto), "TempStore"),
                            InventoryActionType.CreateSginyInventory => (await ProcessCreateSginyInventoryWithoutUpdate(eventDto), "Operation"),
                            InventoryActionType.CreateExchangeInventory => (await ProcessCreateExchangeInventoryWithoutUpdate(eventDto), "Exchange"),
                            InventoryActionType.CreateSureIncomeInventory => (await ProcessCreateSureIncomeInventoryWithoutUpdate(eventDto), "SureIncomeCode"),
                            InventoryActionType.CreateCreditRecordInventory => (await ProcessCreateCreditRecordInventoryWithoutUpdate(eventDto), "CreditRecordCode"),
                            InventoryActionType.CreateReceivedNoInvoiceInventory => (await ProcessCreateReceivedNoInvoiceInventoryWithoutUpdate(eventDto), "ReceivedNoInvoiceRecordCode"),
                            InventoryActionType.CreateDebtRecordInventory => (await ProcessCreateDebtRecordInventoryWithoutUpdate(eventDto), "DebtRecordCode"),
                            InventoryActionType.CreatePaymentRecordInventory => (await ProcessCreatePaymentRecordInventoryWithoutUpdate(eventDto), "PaymentRecordCode"),
                            InventoryActionType.CreateAdvanceRecordInventory => (await ProcessCreateAdvanceRecordInventoryWithoutUpdate(eventDto), "AdvanceRecordCode"),
                            _ => throw new NotSupportedException($"不支持的盘点动作类型: {action.ActionType}")
                        };

                        var duration = (long)(DateTimeOffset.UtcNow - actionStartTime).TotalMilliseconds;
                        actionResults.Add((action.ActionName, true, fieldName, result, duration));

                        // 收集字段更新，而不是立即更新数据库
                        fieldUpdates[fieldName] = result;
                        _logger.LogInformation("盘点动作 执行成功: {ActionName}, 字段: {FieldName}, 值: {Result}, 耗时: {Duration}ms",
                            action.ActionName, fieldName, result, duration);
                    }
                    catch (Exception ex)
                    {
                        // 记录失败但继续处理其他动作
                        var duration = (long)(DateTimeOffset.UtcNow - actionStartTime).TotalMilliseconds;
                        var errorMsg = $"盘点动作 执行失败: {ex.Message}";
                        actionResults.Add((action.ActionName, false, errorMsg, "", duration));
                        _logger.LogError(ex, "盘点动作 执行失败 但将继续处理其他动作: {ActionName}, 错误: {ErrorMessage}, 耗时: {Duration}ms",
                            action.ActionName, ex.Message, duration);
                    }
                }

                // 批量更新所有字段，避免实体跟踪冲突
                if (fieldUpdates.Any())
                {
                    try
                    {
                        await _inventoryMgmAppService.UpdateInventoryItemFields(eventDto.InventoryItemId, fieldUpdates);
                        _logger.LogInformation("批量更新盘点单字段成功 - 盘点单ID: {InventoryItemId}, 更新字段数: {FieldCount}, 字段: {Fields}",
                            eventDto.InventoryItemId, fieldUpdates.Count, string.Join(", ", fieldUpdates.Select(kv => $"{kv.Key}={kv.Value}")));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量更新盘点单字段失败 - 盘点单ID: {InventoryItemId}, 错误: {ErrorMessage}",
                            eventDto.InventoryItemId, ex.Message);
                        throw new Exception($"批量更新盘点单字段失败: {ex.Message}", ex);
                    }
                }

                // 统计执行结果
                var successCount = actionResults.Count(r => r.IsSuccess);
                var failedActions = actionResults.Where(r => !r.IsSuccess).ToList();
                var totalDuration = actionResults.Sum(r => r.Duration);

                if (failedActions.Any())
                {
                    var errorMessage = $"部分盘点动作执行失败，成功: {successCount}/{actionResults.Count}，失败动作详情: {string.Join("; ", failedActions.Select(a => $"{a.ActionName}({a.Message})"))}";
                    _logger.LogError(errorMessage);

                    // 聚合失败信息，生成一个统一的失败消息
                    await SendAggregatedFailureMsg(eventDto, failedActions, errorMessage);
                }
                else
                {
                    _logger.LogInformation($"所有盘点动作执行成功，共 {successCount} 个动作，总耗时: {totalDuration}ms");
                }

                _easyCaching.Remove(cacheKey);
                _logger.LogInformation($"创建其他盘点事件处理完成: {eventDto.EventId}, 成功: {actionResults.Count - failedActions.Count}/{actionResults.Count}");
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理创建其他盘点事件失败: {jsonStr}");
                _easyCaching.Remove(cacheKey);

                // 使用基类方法写入重试队列
                await SendEDAFailureMsg(jsonStr, "finance-inventory-createothercheck", ex, "/api/InventoryEventSub/CreateOtherCheck");

                return Ok();
            }
        }

        /// <summary>
        /// 发送聚合的失败消息到重试队列
        /// </summary>
        /// <param name="eventDto">原始事件数据</param>
        /// <param name="failedActions">失败的动作列表</param>
        /// <param name="aggregatedErrorMessage">聚合的错误消息</param>
        /// <returns></returns>
        private async Task SendAggregatedFailureMsg(CreateOtherCheckEventDto eventDto,
            List<(string ActionName, bool IsSuccess, string Message, string FieldValue, long Duration)> failedActions,
            string aggregatedErrorMessage)
        {
            try
            {
                // 创建只包含失败动作的重试事件
                var retryEventDto = new CreateOtherCheckEventDto
                {
                    EventId = Guid.NewGuid(), // 生成新的事件ID用于重试
                    CompanyId = eventDto.CompanyId,
                    InventoryItemId = eventDto.InventoryItemId,
                    SysMonth = eventDto.SysMonth,
                    CompanyCode = eventDto.CompanyCode,
                    CompanyName = eventDto.CompanyName,
                    UserName = eventDto.UserName,
                    Actions = failedActions.Select(fa => new InventoryActionDto
                    {
                        ActionName = fa.ActionName,
                        ActionType = GetActionTypeFromName(fa.ActionName),
                        NeedExecute = true,
                        Order = 1
                    }).ToList()
                };

                var retryJsonStr = JsonConvert.SerializeObject(retryEventDto);

                // 创建聚合的异常信息
                var aggregatedException = new AggregateException(
                    $"多个盘点动作失败: {aggregatedErrorMessage}",
                    failedActions.Select(fa => new Exception($"{fa.ActionName}: {fa.Message}")).ToArray()
                );

                await SendEDAFailureMsg(retryJsonStr, "finance-inventory-createothercheck", aggregatedException, "/api/InventoryEventSub/CreateOtherCheck");

                _logger.LogInformation("聚合失败消息已发送到重试队列 - 原事件ID: {OriginalEventId}, 重试事件ID: {RetryEventId}, 失败动作数: {FailedCount}",
                    eventDto.EventId, retryEventDto.EventId, failedActions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送聚合失败消息时发生异常 - 事件ID: {EventId}, 失败动作数: {FailedCount}",
                    eventDto.EventId, failedActions.Count);
            }
        }

        /// <summary>
        /// 根据动作名称获取动作类型
        /// </summary>
        /// <param name="actionName">动作名称</param>
        /// <returns>动作类型</returns>
        private static InventoryActionType GetActionTypeFromName(string actionName)
        {
            // 移除actionName中的"创建"和"更新"前缀，统一处理
            var normalizedActionName = actionName.Replace("创建", "").Replace("更新", "");

            return normalizedActionName switch
            {
                "暂存盘点" => InventoryActionType.CreateTinyInventory,
                "跟台盘点" => InventoryActionType.CreateSginyInventory,
                "换货盘点" => InventoryActionType.CreateExchangeInventory,
                "待确认收入盘点" => InventoryActionType.CreateSureIncomeInventory,
                "应收盘点" => InventoryActionType.CreateCreditRecordInventory,
                "已签收待开票盘点" => InventoryActionType.CreateReceivedNoInvoiceInventory,
                "应付盘点" => InventoryActionType.CreateDebtRecordInventory,
                "付款盘点" => InventoryActionType.CreatePaymentRecordInventory,
                "垫资盘点" => InventoryActionType.CreateAdvanceRecordInventory,
                "实盘完成状态" => InventoryActionType.UpdateActualInventoryCompleted,
                _ => throw new NotSupportedException($"不支持的动作名称: {actionName}")
            };
        }

        /// <summary>
        /// 处理开启盘点事件（自动盘点触发）
        /// </summary>
        /// <param name="eventDto">盘点事件数据，如果为空则使用默认值</param>
        /// <returns></returns>
        [HttpPost("StartInventory")]
        public async Task<BaseResponseData<InventoryEventResultDto>> StartInventory([FromBody] StartInventoryEventDto? eventDto = null)
        {
            // 初始化事件参数
            eventDto = InitializeEventDto(eventDto);
            var cacheKey = $"integration-finance-startinventory_{eventDto.SysMonth}_{eventDto.TriggerTime:yyyyMMddHHmmss}";

            _logger.LogInformation("开始盘点-接收 系统月度:{SysMonth} 缓存开关:{EnableRedisCache}", eventDto.SysMonth, eventDto.EnableRedisCache);

            try
            {
                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false && await IsEventAlreadyProcessed(cacheKey))
                {
                    _logger.LogInformation("开始盘点-重复跳过 系统月度:{SysMonth}", eventDto.SysMonth);
                    return CreateSuccessResult(eventDto.SysMonth, "事件已处理过", 0);
                }

                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false)
                {
                    await _easyCaching.SetAsync(cacheKey, JsonConvert.SerializeObject(eventDto), TimeSpan.FromMinutes(30));
                }

                var (success, message, processedCompanyIds) = await _autoInventoryService.ProcessStartInventoryEventAsync(eventDto);

                if (success)
                {
                    _logger.LogInformation("开始盘点-成功 系统月度:{SysMonth} 公司数:{Count}", eventDto.SysMonth, processedCompanyIds.Count);
                    return CreateSuccessResult(eventDto.SysMonth, message, processedCompanyIds.Count);
                }
                else
                {
                    _logger.LogError("开始盘点-失败 系统月度:{SysMonth} 原因:{Message}", eventDto.SysMonth, message);
                    await SendEDAFailureMsg(JsonConvert.SerializeObject(eventDto), "finance-inventory-startinventory",
                        new Exception($"开始盘点失败: {message}"), "/api/InventoryEventSub/StartInventory");
                    return CreateFailureResult(eventDto.SysMonth, message, 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点-异常 系统月度:{SysMonth}", eventDto.SysMonth);
                await SendEDAFailureMsg(JsonConvert.SerializeObject(eventDto), "finance-inventory-startinventory", ex, "/api/InventoryEventSub/StartInventory");
                return CreateFailureResult(eventDto.SysMonth, $"处理异常: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// 初始化事件参数
        /// </summary>
        private StartInventoryEventDto InitializeEventDto(StartInventoryEventDto? eventDto)
        {
            var currentSysMonth = DateTime.Now.ToString("yyyy-MM");
            var currentTime = DateTime.Now;

            if (eventDto == null)
            {
                return new StartInventoryEventDto
                {
                    InventoryMonth = currentSysMonth,
                    TriggerTime = currentTime,
                    Remark = "自动调度触发"
                };
            }

            if (string.IsNullOrEmpty(eventDto.InventoryMonth))
            {
                eventDto.InventoryMonth = currentSysMonth;
            }

            return eventDto;
        }

        /// <summary>
        /// 检查事件是否已处理
        /// </summary>
        private async Task<bool> IsEventAlreadyProcessed(string cacheKey)
        {
            var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
            return hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value);
        }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        private BaseResponseData<InventoryEventResultDto> CreateSuccessResult(string? sysMonth, string message, int processedCount)
        {
            var result = BaseResponseData<InventoryEventResultDto>.Success(message);
            result.Data = new InventoryEventResultDto
            {
                Success = true,
                Message = message,
                SysMonth = sysMonth ?? string.Empty,
                EventType = "StartInventory",
                ProcessedCompanyCount = processedCount
            };
            return result;
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        private BaseResponseData<InventoryEventResultDto> CreateFailureResult(string? sysMonth, string message, int processedCount)
        {
            var result = BaseResponseData<InventoryEventResultDto>.Failed(500, message);
            result.Data = new InventoryEventResultDto
            {
                Success = false,
                Message = message,
                SysMonth = sysMonth ?? string.Empty,
                EventType = "StartInventory",
                ProcessedCompanyCount = processedCount
            };
            return result;
        }

        /// <summary>
        /// 处理盘点生成事件（finance-inventory-generate）
        /// </summary>
        /// <param name="eventDto">盘点生成事件数据</param>
        /// <returns></returns>
        [HttpPost("InventoryGenerate")]
        [Topic("pubsub-default", "finance-inventory-generate")]
        public async Task<BaseResponseData<InventoryEventResultDto>> InventoryGenerate([FromBody] InventoryGenerateEventDto eventDto)
        {

            var cacheKey = $"integration-finance-inventory-generate_{eventDto.SysMonth}_{DateTime.Now:yyyyMMddHH}";

            _logger.LogInformation("财务-接收事件 finance-inventory-generate 系统月度:{SysMonth} 公司数:{Count} 缓存开关:{EnableRedisCache}", eventDto.SysMonth, eventDto.CompanyIds.Count, eventDto.EnableRedisCache);

            try
            {
                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false && await IsEventAlreadyProcessed(cacheKey))
                {
                    _logger.LogInformation("盘点生成-重复跳过 系统月度:{SysMonth}", eventDto.SysMonth);
                    return CreateInventoryGenerateSuccessResult(eventDto.SysMonth, "事件已处理过", eventDto.CompanyIds.Count);
                }

                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false)
                {
                    await _easyCaching.SetAsync(cacheKey, JsonConvert.SerializeObject(eventDto), TimeSpan.FromMinutes(30));
                }

                var (success, message) = await _autoInventoryService.ProcessInventoryGenerateEventAsync(eventDto);

                if (success)
                {
                    _logger.LogInformation("盘点生成-成功 系统月度:{SysMonth} 公司数:{Count}", eventDto.SysMonth, eventDto.CompanyIds.Count);
                    return CreateInventoryGenerateSuccessResult(eventDto.SysMonth, message, eventDto.CompanyIds.Count);
                }
                else
                {
                    _logger.LogError("盘点生成-失败 系统月度:{SysMonth} 原因:{Message}", eventDto.SysMonth, message);
                    await SendEDAFailureMsg(JsonConvert.SerializeObject(eventDto), "finance-inventory-generate",
                        new Exception($"盘点生成失败: {message}"), "/api/InventoryEventSub/InventoryGenerate");
                    return CreateInventoryGenerateFailureResult(eventDto.SysMonth, message, eventDto.CompanyIds.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "盘点生成-异常 系统月度:{SysMonth}", eventDto.SysMonth);
                await SendEDAFailureMsg(JsonConvert.SerializeObject(eventDto), "finance-inventory-generate", ex, "/api/InventoryEventSub/InventoryGenerate");
                return CreateInventoryGenerateFailureResult(eventDto.SysMonth, $"处理异常: {ex.Message}", eventDto.CompanyIds.Count);
            }
        }

        /// <summary>
        /// 创建盘点生成成功结果
        /// </summary>
        private BaseResponseData<InventoryEventResultDto> CreateInventoryGenerateSuccessResult(string? sysMonth, string message, int processedCount)
        {
            var result = BaseResponseData<InventoryEventResultDto>.Success(message);
            result.Data = new InventoryEventResultDto
            {
                Success = true,
                Message = message,
                SysMonth = sysMonth ?? string.Empty,
                EventType = "InventoryGenerate",
                ProcessedCompanyCount = processedCount
            };
            return result;
        }

        /// <summary>
        /// 创建盘点生成失败结果
        /// </summary>
        private BaseResponseData<InventoryEventResultDto> CreateInventoryGenerateFailureResult(string? sysMonth, string message, int processedCount)
        {
            var result = BaseResponseData<InventoryEventResultDto>.Failed(500, message);
            result.Data = new InventoryEventResultDto
            {
                Success = false,
                Message = message,
                SysMonth = sysMonth ?? string.Empty,
                EventType = "InventoryGenerate",
                ProcessedCompanyCount = processedCount
            };
            return result;
        }

        /// <summary>
        /// 处理盘点完成检查事件（自动触发）
        /// </summary>
        /// <param name="eventDto">盘点完成检查事件数据，如果为空则使用默认值</param>
        /// <returns></returns>
        [HttpPost("CompleteInventoryCheck")]
        public async Task<BaseResponseData<InventoryEventResultDto>> CompleteInventoryCheck([FromBody] CompleteInventoryCheckEventDto? eventDto = null)
        {
            // 预计算当前系统月度，避免重复调用
            var currentSysMonth = DateTime.Now.ToString("yyyy-MM");
            var currentTime = DateTime.Now;

            // 如果参数为空或系统月度为空，使用当前时间对应的系统月度
            if (eventDto == null)
            {
                eventDto = new CompleteInventoryCheckEventDto
                {
                    SysMonth = currentSysMonth,
                    TriggerTime = currentTime,
                    Remark = "自动调度触发 CompleteInventoryCheck"
                };
            }
            else if (string.IsNullOrEmpty(eventDto.SysMonth))
            {
                eventDto.SysMonth = currentSysMonth;
            }

            // 统一关键字：完成盘点检查
            _logger.LogInformation("完成盘点检查-开始处理 系统月度:{SysMonth} 触发时间:{TriggerTime} 缓存开关:{EnableRedisCache}",
                eventDto.SysMonth, eventDto.TriggerTime, eventDto.EnableRedisCache);

            var jsonStr = JsonConvert.SerializeObject(eventDto);
            var cacheKey = $"integration-finance-completeinventory_{eventDto.SysMonth}_{eventDto.TriggerTime:yyyyMMddHHmmss}";

            try
            {
                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false)
                {
                    var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
                    if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                    {
                        _logger.LogWarning("完成盘点检查-重复跳过 系统月度:{SysMonth}", eventDto.SysMonth);
                        return CreateInventoryEventResult(true, $"事件已处理过,cacheKey{cacheKey}", eventDto.SysMonth, 0, 0);
                    }

                    await _easyCaching.SetAsync(cacheKey, jsonStr, TimeSpan.FromMinutes(30));
                }

                // 使用AutoInventoryService处理事件
                var (success, message, completedCompanyIds) = await _autoInventoryService.ProcessCompleteInventoryCheckEventAsync(eventDto);

                if (success)
                {
                    _logger.LogInformation("完成盘点检查-处理成功 系统月度:{SysMonth} 完成公司数:{Count} 完成公司列表:{@CompanyIds} 处理消息:{Message}",
                        eventDto.SysMonth, completedCompanyIds.Count, completedCompanyIds, message);

                    return CreateInventoryEventResult(true, message, eventDto.SysMonth, completedCompanyIds.Count, completedCompanyIds.Count);
                }
                else
                {
                    var detailedMessage = $"[ERROR_CODE:COMPLETE_INVENTORY_CHECK_FAILED] 完成盘点检查-处理失败 系统月度:{eventDto.SysMonth} 原因:{message}";
                    _logger.LogError("[ERROR_CODE:COMPLETE_INVENTORY_CHECK_FAILED] 完成盘点检查-处理失败 系统月度:{SysMonth} 错误:{Message} 触发时间:{TriggerTime} 备注:{Remark}",
                        eventDto.SysMonth, message, eventDto.TriggerTime, eventDto.Remark);

                    // 发送失败消息到重试队列，以便运维中心重试
                    try
                    {
                        await SendEDAFailureMsg(jsonStr, "finance-inventory-completeinventorycheck",
                            new Exception($"完成盘点检查处理失败: {message}"), "/api/InventoryEventSub/CompleteInventoryCheck");
                        _logger.LogInformation("[ERROR_CODE:COMPLETE_INVENTORY_CHECK_FAILED] 完成盘点检查失败消息已发送到重试队列 - 系统月度: {SysMonth}", eventDto.SysMonth);
                    }
                    catch (Exception retryEx)
                    {
                        _logger.LogError(retryEx, "[ERROR_CODE:COMPLETE_INVENTORY_CHECK_RETRY_FAILED] 发送完成盘点检查失败消息到重试队列失败 - 系统月度: {SysMonth}, 错误: {ErrorMessage}",
                            eventDto.SysMonth, retryEx.Message);
                    }

                    var additionalData = new
                    {
                        OriginalError = message,
                        CheckTime = eventDto.TriggerTime,
                        Remark = eventDto.Remark,
                        ProcessedCompanyCount = completedCompanyIds.Count
                    };

                    return CreateInventoryEventResult(false, detailedMessage, eventDto.SysMonth, 0, 0, additionalData);
                }
            }
            catch (Exception ex)
            {
                var detailedErrorMessage = $"[ERROR_CODE:COMPLETE_INVENTORY_CHECK_EXCEPTION] 完成盘点检查-异常 系统月度:{eventDto.SysMonth} 异常:{ex.GetType().Name}";
                _logger.LogError(ex, "[ERROR_CODE:COMPLETE_INVENTORY_CHECK_EXCEPTION] 完成盘点检查-异常 系统月度:{SysMonth} 错误类型:{ExceptionType} 错误消息:{Message} 触发时间:{TriggerTime} 备注:{Remark} 堆栈跟踪:{StackTrace}",
                    eventDto.SysMonth, ex.GetType().Name, ex.Message, eventDto.TriggerTime, eventDto.Remark, ex.StackTrace?.Split('\n').Take(5).ToArray());

                // 发送异常消息到重试队列，以便运维中心重试
                await SendEDAFailureMsg(jsonStr, "finance-inventory-completeinventorycheck", ex, "/api/InventoryEventSub/CompleteInventoryCheck");
                _logger.LogInformation("[ERROR_CODE:COMPLETE_INVENTORY_CHECK_EXCEPTION] 完成盘点检查异常消息已发送到重试队列 - 系统月度: {SysMonth}", eventDto.SysMonth);

                return CreateInventoryEventResult(false, detailedErrorMessage, eventDto.SysMonth, 0, 0);
            }
        }

        /// <summary>
        /// 处理盘点完成通知事件（处理后续业务逻辑，如损失确认等）
        /// 注意：盘点状态更新已在CompleteInventoryCheck中完成，此方法只处理后续业务
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        [HttpPost("FinishInventory")]
        [Topic("pubsub-default", "finance-inventory-finishinventory")]
        public async Task<ActionResult> FinishInventory(InventoryFinishEventDto eventDto)
        {
            if (eventDto == null)
            {
                _logger.LogError("结束盘点 - 接收完成通知事件失败 - 事件数据为空");
                return BadRequest("事件数据不能为空");
            }

            _logger.LogInformation("结束盘点 - 接收完成通知事件 - 系统月度:{SysMonth} 公司数量:{CompanyCount} 公司列表:{@CompanyIds} 新系统月度:{NewSystemPeriod} 系统月度是否已更新:{SystemPeriodUpdated} 缓存开关:{EnableRedisCache}",
                eventDto.SysMonth, eventDto.CompanyIds.Count, eventDto.CompanyIds, eventDto.NewSystemPeriod, eventDto.SystemPeriodUpdated, eventDto.EnableRedisCache);
            var jsonStr = JsonConvert.SerializeObject(eventDto);
            var cacheKey = $"finance-inventory-finishinventory_{eventDto.SysMonth}_{string.Join("-", eventDto.CompanyIds)}";

            try
            {
                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false)
                {
                    var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
                    if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                    {
                        _logger.LogInformation("结束盘点 - 接收完成通知事件跳过 - 系统月度: {SysMonth}, 公司数量: {CompanyCount}, 原因: 已处理过,cacheKey:{cacheKey}",
                            eventDto.SysMonth, eventDto.CompanyIds.Count, cacheKey);
                        return Ok();
                    }

                    await _easyCaching.SetAsync(cacheKey, jsonStr, TimeSpan.FromMinutes(10));
                }
                _logger.LogInformation("结束盘点 - 接收完成通知事件 - Topic: finance-inventory-finishinventory, 系统月度: {SysMonth}, 公司数量: {CompanyCount}, 系统月度已更新: {SystemPeriodUpdated}, 新系统月度: {NewSystemPeriod}",
                    eventDto.SysMonth, eventDto.CompanyIds.Count, eventDto.SystemPeriodUpdated, eventDto.NewSystemPeriod ?? "null");

                // 处理盘点完成后的后续业务逻辑（损失确认等）
                try
                {
                    _logger.LogInformation("结束盘点 - 开始处理后续业务 - 系统月度: {SysMonth}, 公司数量: {CompanyCount}, 主要业务: 损失确认处理",
                        eventDto.SysMonth, eventDto.CompanyIds.Count);

                    var successCount = 0;
                    var failedCompanies = new List<Guid>();

                    // 批量处理每个公司的后续业务
                    foreach (var companyId in eventDto.CompanyIds)
                    {
                        try
                        {
                            _logger.LogInformation("结束盘点 - 处理公司后续业务 - 公司: {CompanyId}, 系统月度: {SysMonth}",
                                companyId, eventDto.SysMonth);

                            // 处理损失确认单据（如果需要）
                            if (eventDto.SystemPeriodUpdated && !string.IsNullOrEmpty(eventDto.NewSystemPeriod))
                            {
                                try
                                {
                                    _logger.LogInformation("结束盘点 - 损失确认 处理开始 - 公司: {CompanyId}, 当前月度: {CurrentMonth}, 目标月度: {TargetSysMonth}",
                                        companyId, eventDto.SysMonth, eventDto.NewSystemPeriod);

                                    var lossRecognitionResult = await _lossRecognitionAppService.ResumeLossRecognitionAfterInventory(
                                        companyId, eventDto.NewSystemPeriod);

                                    if (lossRecognitionResult.Code == CodeStatusEnum.Success)
                                    {
                                        _logger.LogInformation("结束盘点 - 损失确认 处理成功 - 公司: {CompanyId}, 目标月度: {TargetSysMonth}, 处理结果: {Result}",
                                            companyId, eventDto.NewSystemPeriod, lossRecognitionResult.Message ?? "成功");
                                    }
                                    else
                                    {
                                        _logger.LogWarning("结束盘点 - 损失确认 处理失败 - 公司: {CompanyId}, 目标月度: {TargetSysMonth}, 错误代码: {Code}, 错误信息: {Error}",
                                            companyId, eventDto.NewSystemPeriod, lossRecognitionResult.Code, lossRecognitionResult.Message);
                                    }
                                }
                                catch (Exception lossEx)
                                {
                                    _logger.LogError(lossEx, "结束盘点 - 损失确认 处理异常 - 公司: {CompanyId}, 目标月度: {TargetSysMonth}, 异常类型: {ExceptionType}, 错误: {Message}",
                                        companyId, eventDto.NewSystemPeriod, lossEx.GetType().Name, lossEx.Message);
                                    // 损失确认失败不影响整体流程，但需要详细记录以便后续处理
                                }
                            }
                            else
                            {
                                _logger.LogInformation("结束盘点 - 损失确认 处理跳过 - 公司: {CompanyId}, 原因: 系统月度未更新或新月度为空, SystemPeriodUpdated: {SystemPeriodUpdated}, NewSystemPeriod: {NewSystemPeriod}",
                                    companyId, eventDto.SystemPeriodUpdated, eventDto.NewSystemPeriod ?? "null");
                            }

                            successCount++;
                            _logger.LogInformation("结束盘点 - 公司后续业务处理成功 - 公司: {CompanyId}, 系统月度: {SysMonth}",
                                companyId, eventDto.SysMonth);
                        }
                        catch (Exception companyEx)
                        {
                            _logger.LogError(companyEx, "结束盘点 - 公司后续业务处理失败 - 公司: {CompanyId}, 系统月度: {SysMonth}, 错误: {ErrorMessage}",
                                companyId, eventDto.SysMonth, companyEx.Message);
                            failedCompanies.Add(companyId);
                        }
                    }

                    _logger.LogInformation("结束盘点 - 后续业务处理完成 - 系统月度: {SysMonth}, 总公司数: {TotalCount}, 成功: {SuccessCount}, 失败: {FailedCount}, 损失确认处理覆盖率: {CoverageRate:P2}",
                        eventDto.SysMonth, eventDto.CompanyIds.Count, successCount, failedCompanies.Count,
                        eventDto.CompanyIds.Count > 0 ? (double)successCount / eventDto.CompanyIds.Count : 0);

                    if (failedCompanies.Any())
                    {
                        _logger.LogWarning("结束盘点 - 部分公司后续业务处理失败 - 系统月度: {SysMonth}, 失败公司ID: {FailedCompanies}, 建议检查损失确认相关配置",
                            eventDto.SysMonth, string.Join(", ", failedCompanies));
                    }
                    else
                    {
                        _logger.LogInformation("结束盘点 - 所有公司后续业务处理成功 - 系统月度: {SysMonth}, 处理公司数: {Count}",
                            eventDto.SysMonth, successCount);
                    }
                }
                catch (Exception businessEx)
                {
                    _logger.LogError(businessEx, "结束盘点 - 后续业务处理异常 - 系统月度: {SysMonth}, 错误: {ErrorMessage}",
                        eventDto.SysMonth, businessEx.Message);
                    throw new Exception($"后续业务处理失败: {businessEx.Message}", businessEx);
                }

                _easyCaching.Remove(cacheKey);
                _logger.LogInformation("结束盘点 - 完成通知事件处理成功 - 系统月度: {SysMonth}, 处理公司数: {CompanyCount}",
                    eventDto.SysMonth, eventDto.CompanyIds.Count);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "结束盘点 - 完成通知事件处理失败 - 系统月度: {SysMonth}, 错误: {JsonStr}", eventDto.SysMonth, jsonStr);

                // 只有明确传入false时才禁用缓存
                if (eventDto.EnableRedisCache != false)
                {
                    _easyCaching.Remove(cacheKey);
                }

                // 使用基类方法写入重试队列
                await SendEDAFailureMsg(jsonStr, "finance-inventory-finishinventory", ex, "/api/InventoryEventSub/FinishInventory");

                return Ok();
            }
        }

        /// <summary>
        /// 获取盘点进度
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        [HttpGet("GetInventoryProgress")]
        public async Task<ActionResult<InventoryProgressResponseDto>> GetInventoryProgress(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation("获取盘点进度 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);

                var progress = await _autoInventoryService.GetInventoryProgressAsync(companyId, sysMonth);
                return Ok(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取盘点进度失败 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);
                return StatusCode(500, new { success = false, message = $"获取失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 处理创建返利计提事件
        /// </summary>
        /// <param name="eventDto">返利计提事件数据，如果为空则使用默认值</param>
        /// <returns></returns>
        [HttpPost("CreateRebateProvision")]
        public async Task<BaseResponseData<InventoryEventResultDto>> CreateRebateProvision([FromBody] RebateProvisionEventDto? eventDto = null)
        {
            _logger.LogWarning("创建返利计提-接收时间:{ReceiveTime}", DateTime.Now);

            // 如果参数为空或系统月度为空，使用当前时间对应的系统月度
            if (eventDto == null)
            {
                var currentSysMonth = DateTime.Now.ToString("yyyy-MM");
                eventDto = new RebateProvisionEventDto
                {
                    SysMonth = currentSysMonth,
                    EventTime = DateTime.Now,
                    Description = "自动调度触发"
                };
            }
            else if (string.IsNullOrEmpty(eventDto.SysMonth))
            {
                eventDto.SysMonth = DateTime.Now.ToString("yyyy-MM");
            }

            var jsonStr = JsonConvert.SerializeObject(eventDto);
            var cacheKey = $"integration-finance-createrebateprovision_{eventDto.SysMonth}_{eventDto.EventTime:yyyyMMddHHmmss}";

            try
            {
                // 防重复处理
                var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
                if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                {
                    _logger.LogInformation("创建返利计提事件已处理过，跳过: {SysMonth}, 事件时间: {EventTime}",
                        eventDto.SysMonth, eventDto.EventTime);
                    return BaseResponseData<InventoryEventResultDto>.Success(new InventoryEventResultDto
                    {
                        Success = true,
                        Message = "事件已处理过",
                        SysMonth = eventDto.SysMonth,
                        EventType = "CreateRebateProvision",
                        ProcessedCompanyCount = 0
                    });
                }

                await _easyCaching.SetAsync(cacheKey, jsonStr, TimeSpan.FromMinutes(30));
                _logger.LogInformation("开始处理创建返利计提事件: {EventData}", jsonStr);

                // 默认设置为月末计提
                var provisionType = ProvisionTypeEnum.EndMonth;
                _logger.LogInformation("系统月度 {SysMonth}，默认设置为月末计提", eventDto.SysMonth);

                // 转换为内部事件DTO
                var internalEventDto = CreateRebateProvisionEventDto.FromRebateProvisionEventDto(eventDto, provisionType);

                // 使用AutoRebateProvisionService处理事件
                var (success, message, processedCompanyIds) = await _autoRebateProvisionService.ProcessCreateRebateProvisionEventAsync(internalEventDto);

                if (success)
                {
                    _logger.LogInformation("创建返利计提事件处理成功 - 消息: {Message}, 处理公司数: {Count}", message, processedCompanyIds.Count);
                    var result = BaseResponseData<InventoryEventResultDto>.Success(message);
                    result.Data = new InventoryEventResultDto
                    {
                        Success = true,
                        Message = message,
                        SysMonth = eventDto.SysMonth,
                        EventType = "CreateRebateProvision",
                        ProcessedCompanyCount = processedCompanyIds.Count
                    };
                    return result;
                }
                else
                {
                    _logger.LogError("创建返利计提事件处理失败 - 消息: {Message}", message);
                    var result = BaseResponseData<InventoryEventResultDto>.Failed(500, message);
                    result.Data = new InventoryEventResultDto
                    {
                        Success = false,
                        Message = message,
                        SysMonth = eventDto.SysMonth,
                        EventType = "CreateRebateProvision",
                        ProcessedCompanyCount = 0
                    };
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理创建返利计提事件时发生异常: {EventData}", jsonStr);
                var errorMessage = $"处理失败: {ex.Message}";
                var result = BaseResponseData<InventoryEventResultDto>.Failed(500, errorMessage);
                result.Data = new InventoryEventResultDto
                {
                    Success = false,
                    Message = errorMessage,
                    SysMonth = eventDto.SysMonth,
                    EventType = "CreateRebateProvision",
                    ProcessedCompanyCount = 0
                };
                return result;
            }
        }

        /// <summary>
        /// 处理暂存盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateTinyInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 传递当前盘点单的系统月度和用户信息
            var request = new TinyInventoryCreateRequestDto
            {
                CompanyId = eventDto.CompanyId,
                SysMonth = eventDto.SysMonth,
                UserId = eventDto.UserId,
                UserName = eventDto.UserName
            };

            var sginyRes = await _tinyApiClientOfResponseData.CreateTinyInventory(request);
            var tempCode = sginyRes?.StocktakingCode ?? "";

            if (string.IsNullOrEmpty(tempCode))
            {
                throw new Exception("暂存盘点创建失败，返回的盘点单号为空");
            }

            _logger.LogInformation("暂存盘点创建成功 - 盘点单号: {TempCode}", tempCode);
            return tempCode;
        }
        /// <summary>
        /// 处理跟台盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateSginyInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 传递当前盘点单的系统月度和用户信息
            var request = new SginyInventoryCreateRequestDto
            {
                CompanyId = eventDto.CompanyId,
                SysMonth = eventDto.SysMonth,
                UserId = eventDto.UserId,
                UserName = eventDto.UserName
            };

            var sginyRes = await _sginyApiClient.CreateSignyInventory(request);
            var operationCode = sginyRes?.InventoryNo ?? "";

            if (string.IsNullOrEmpty(operationCode))
            {
                throw new Exception("跟台盘点创建失败，返回的盘点单号为空");
            }

            _logger.LogInformation("跟台盘点创建成功 - 盘点单号: {OperationCode}", operationCode);
            return operationCode;
        }

        /// <summary>
        /// 处理换货盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateExchangeInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 传递当前盘点单的系统月度和用户信息
            var request = new ExchangeInventoryCreateRequestDto
            {
                CompanyId = eventDto.CompanyId,
                SysMonth = eventDto.SysMonth,
                UserId = eventDto.UserId,
                UserName = eventDto.UserName
            };

            var exchangeRes = await _inventoryApiClientOfResponseData.CreateExchangeInventory(request);
            var exchangeCode = exchangeRes?.Data ?? "";

            if (string.IsNullOrEmpty(exchangeCode))
            {
                throw new Exception("换货盘点创建失败，返回的盘点单号为空");
            }

            _logger.LogInformation("换货盘点创建成功 - 盘点单号: {ExchangeCode}", exchangeCode);
            return exchangeCode;
        }

        /// <summary>
        /// 处理待确认收入盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateSureIncomeInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 传递当前盘点单的系统月度和用户信息
            var request = new SureIncomeInventoryCreateRequestDto
            {
                CompanyId = eventDto.CompanyId,
                SysMonth = eventDto.SysMonth,
                UserId = eventDto.UserId,
                UserName = eventDto.UserName
            };

            var sureIncomeRes = await _inventoryApiClientOfResponseData.CreateSureIncomeInventory(request);
            var sureIncomeCode = sureIncomeRes?.Data ?? "";

            if (string.IsNullOrEmpty(sureIncomeCode))
            {
                throw new Exception("待确认收入盘点创建失败，返回的盘点单号为空");
            }

            _logger.LogInformation("待确认收入盘点创建成功 - 盘点单号: {SureIncomeCode}", sureIncomeCode);
            return sureIncomeCode;
        }

        /// <summary>
        /// 处理应收盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateCreditRecordInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 调用 Application 层的应收盘点逻辑
            var (executeCount, creditCode) = await _inventoryMgmAppService.CreditRecordInventory(
                eventDto.CompanyId,
                eventDto.SysMonth ?? "",
                eventDto.CompanyCode ?? "",
                eventDto.CompanyName ?? "",
                eventDto.UserName ?? "");

            if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(creditCode)))
            {
                throw new Exception("应收盘点创建失败");
            }
            else if (executeCount == -1)
            {
                _logger.LogInformation("应收盘点跳过 - 无符合条件的数据，返回空单号");
                return ""; // 返回空字符串表示跳过
            }

            _logger.LogInformation("应收盘点创建成功 - 盘点单号: {CreditCode}", creditCode);
            return creditCode;
        }

        /// <summary>
        /// 处理已签收待开票盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateReceivedNoInvoiceInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 调用 Application 层的已签收待开票盘点逻辑
            var (executeCount, receivedCode) = await _inventoryMgmAppService.ReceivedNoInvoiceInventory(
                eventDto.CompanyId,
                eventDto.SysMonth ?? "",
                eventDto.CompanyCode ?? "",
                eventDto.CompanyName ?? "",
                eventDto.UserName ?? "");

            if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(receivedCode)))
            {
                throw new Exception("已签收待开票盘点创建失败");
            }
            else if (executeCount == -1)
            {
                _logger.LogInformation("已签收待开票盘点跳过 - 无符合条件的数据，返回空单号");
                return ""; // 返回空字符串表示跳过
            }

            _logger.LogInformation("已签收待开票盘点创建成功 - 盘点单号: {ReceivedCode}", receivedCode);
            return receivedCode;
        }

        /// <summary>
        /// 处理应付盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateDebtRecordInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 调用 Application 层的应付盘点逻辑
            var (executeCount, debtCode) = await _inventoryMgmAppService.DebtRecordInventory(
                eventDto.CompanyId,
                eventDto.SysMonth ?? "",
                eventDto.CompanyCode ?? "",
                eventDto.CompanyName ?? "",
                eventDto.UserName ?? "");

            if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(debtCode)))
            {
                throw new Exception("应付盘点创建失败");
            }
            else if (executeCount == -1)
            {
                _logger.LogInformation("应付盘点 跳过 - 无符合条件的数据，返回空单号");
                return ""; // 返回空字符串表示跳过
            }

            _logger.LogInformation("应付盘点 创建成功 - 盘点单号: {DebtCode}", debtCode);
            return debtCode;
        }

        /// <summary>
        /// 处理付款盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreatePaymentRecordInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 调用 Application 层的付款盘点逻辑
            var (executeCount, paymentCode) = await _inventoryMgmAppService.PaymentRecordInventory(
                eventDto.CompanyId,
                eventDto.SysMonth ?? "",
                eventDto.CompanyCode ?? "",
                eventDto.CompanyName ?? "",
                eventDto.UserName ?? "");

            if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(paymentCode)))
            {
                throw new Exception("付款盘点创建失败");
            }
            else if (executeCount == -1)
            {
                _logger.LogInformation("付款盘点跳过 - 无符合条件的数据，返回空单号");
                return ""; // 返回空字符串表示跳过
            }

            _logger.LogInformation("付款盘点创建成功 - 盘点单号: {PaymentCode}", paymentCode);
            return paymentCode;
        }

        /// <summary>
        /// 处理垫资盘点创建但不更新数据库
        /// </summary>
        private async Task<string> ProcessCreateAdvanceRecordInventoryWithoutUpdate(CreateOtherCheckEventDto eventDto)
        {
            // 调用 Application 层的垫资盘点逻辑
            var (executeCount, advanceCode) = await _inventoryMgmAppService.AdvanceRecordInventory(
                eventDto.CompanyId,
                eventDto.SysMonth ?? "",
                eventDto.CompanyCode ?? "",
                eventDto.CompanyName ?? "",
                eventDto.UserName ?? "");

            if (executeCount == 0 || (executeCount > 0 && string.IsNullOrEmpty(advanceCode)))
            {
                throw new Exception("垫资盘点创建失败");
            }
            else if (executeCount == -1)
            {
                _logger.LogInformation("垫资盘点跳过 - 无符合条件的数据，返回空单号");
                return ""; // 返回空字符串表示跳过
            }

            _logger.LogInformation("垫资盘点创建成功 - 盘点单号: {AdvanceCode}", advanceCode);
            return advanceCode;
        }

        /// <summary>
        /// 检查盘点完成情况
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        private async Task<bool> CheckInventoryCompletion(Guid companyId, string sysMonth)
        {
            try
            {
                // 获取盘点单信息
                var inventoryList = await _inventoryMgmAppService.GetInventoryList(new InventoryQueryDto
                {
                    CompanyId = companyId,
                    SysMonth = sysMonth
                });

                if (!inventoryList.Item1.Any())
                {
                    _logger.LogWarning($"未找到公司 {companyId} 的 盘点记录");
                    return false;
                }

                var inventory = inventoryList.Item1.First();

                // 检查盘点状态，如果已经完成则直接返回成功
                if (inventory.Status == 99) // 99表示已完成
                {
                    _logger.LogInformation("公司 {CompanyId} 盘点已完成 - 月度: {SysMonth}, 盘点单ID: {InventoryId}, 状态: {Status}",
                        companyId, sysMonth, inventory.Id, inventory.Status);
                    return true;
                }

                // 检查所有盘点单号是否已生成
                var requiredFields = new[]
                {
                    inventory.Store,           // 库存盘点
                    inventory.TempStore,       // 暂存盘点
                    inventory.Operation,       // 跟台盘点
                    inventory.Exchange,        // 换货盘点
                    inventory.SureIncomeCode,  // 待确认收入盘点
                    inventory.CreditRecordCode,// 应收盘点
                    inventory.ReceivedNoInvoiceRecordCode, // 已签收待开票盘点
                    inventory.DebtRecordCode,  // 应付盘点
                    inventory.PaymentRecordCode, // 付款盘点
                    inventory.AdvanceRecordCode  // 垫资盘点
                };

                var missingFields = requiredFields.Where(field => string.IsNullOrEmpty(field)).ToList();
                if (missingFields.Any())
                {
                    _logger.LogWarning($"公司 {companyId} 存在 {missingFields.Count} 个未生成的 盘点单号");
                    return false;
                }

                // 检查返利计提状态
                var rebateCheckResult = await CheckRebateProvisionStatus(companyId, sysMonth);
                if (!rebateCheckResult)
                {
                    _logger.LogWarning($"公司 {companyId} 返利计提未提交");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查公司 {companyId} 盘点完成情况失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查返利计提状态
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        private async Task<bool> CheckRebateProvisionStatus(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation($"开始检查公司 {companyId} 返利计提状态，月度: {sysMonth}");

                // 解析系统月度
                var sysDate = DateTime.Parse(sysMonth + "-01");

                // 查询该公司该月度的返利计提记录
                var queryInput = new RebateProvisionItemQueryInput
                {
                    CompanyId = companyId,
                    BillDateBeging = sysDate.ToString("yyyy-MM-dd"),
                    BillDateEnd = sysDate.AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd"),
                    Status = -1, // 查询所有状态
                    page = 1,
                    limit = 100
                };

                var (rebateList, count) = await _rebateProvisionQueryService.GetListAsync(queryInput);

                if (!rebateList.Any())
                {
                    // 如果没有返利计提记录，认为通过检查（可能该公司该月度无需返利计提）
                    _logger.LogInformation($"公司 {companyId} 在 {sysMonth} 无返利计提记录，检查通过");
                    return true;
                }

                // 检查所有返利计提记录是否都已完成
                var incompleteRebates = rebateList.Where(r => r.Status != StatusEnum.Complate).ToList();

                if (incompleteRebates.Any())
                {
                    _logger.LogWarning($"公司 {companyId} 在 {sysMonth} 有 {incompleteRebates.Count} 个未完成的返利计提记录");
                    foreach (var incompleteRebate in incompleteRebates)
                    {
                        _logger.LogWarning($"未完成的返利计提: ID={incompleteRebate.Id}, 单据号={incompleteRebate.BillCode}, 状态={incompleteRebate.Status}");
                    }
                    return false;
                }

                _logger.LogInformation($"公司 {companyId} 在 {sysMonth} 的所有返利计提记录都已完成，检查通过");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查返利计提状态失败 - 公司: {companyId}, 月度: {sysMonth}, 错误: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// 创建盘点事件结果对象，避免重复代码
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="processedCount">处理的公司数量</param>
        /// <param name="completedCount">完成的公司数量</param>
        /// <param name="additionalData">额外数据</param>
        /// <returns></returns>
        private BaseResponseData<InventoryEventResultDto> CreateInventoryEventResult(
            bool success,
            string message,
            string sysMonth,
            int processedCount,
            int completedCount,
            object? additionalData = null)
        {
            var result = success
                ? BaseResponseData<InventoryEventResultDto>.Success(message)
                : BaseResponseData<InventoryEventResultDto>.Failed(500, message);

            result.Data = new InventoryEventResultDto
            {
                Success = success,
                Message = message,
                SysMonth = sysMonth,
                EventType = "CompleteInventoryCheck",
                ProcessedCompanyCount = processedCount,
                CompletedCompanyCount = completedCount,
                AdditionalData = additionalData
            };

            return result;
        }

    }
}
