﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits
{
    public class CreditRecordDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {

        /// <summary>
        /// 应收盘点Id
        /// </summary>
        public Guid CreditRecordItemId { get; set; }
        /// <summary>
        /// 应收Id
        /// </summary>
        public Guid CreditId { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedValue { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 是否长期 0,否 1，是
        /// </summary>
        public int IsLongTerm { get; set; } = 0;

    }
}
