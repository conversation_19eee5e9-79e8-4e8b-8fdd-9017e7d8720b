<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>对账管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane split="horizontal" :default-percent="60">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.abatedStatus" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`对账管理列表`" name="0" lazy />
              </el-tabs>
            </template>
            <template #default>
              <el-button type="primary" @click="createReconciliationList">创建</el-button>
              <el-button type="danger" icon="Delete" :disabled="!crud.selections.length" @click="del">删除</el-button>
              <el-button type="warning" icon="Download" :disabled="!crud.selections.length" @click="exportClick">导出公司数据</el-button>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef0"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            border
            :data="crud.data"
            stripe
            :row-class-name="crud.tableRowClassName"
            @row-click="crud.singleSelection"
          >
            <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.companyName }}</template>
            </el-table-column>
            <el-table-column label="月度" property="sysMonth" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.sysMonth }}</template>
            </el-table-column>
            <el-table-column label="创建时间" property="createdTime" show-overflow-tooltip>
              <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            <div class="flex-1"></div>
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <div class="custom-tabs">
            <el-tabs type="border-card" style="height: 100%" class="mycard_css" @tab-click="handleClick">
              <el-tab-pane label="收入成本">
                <el-table
                  ref="tableRef2"
                  v-inno-loading="crud1.loading"
                  class="auto-layout-table"
                  highlight-current-row
                  border
                  :data="crud1.data"
                  stripe
                  height="100%"
                  :row-class-name="crud1.tableRowClassName"
                  @selection-change="crud1.selectionChangeHandler"
                  @row-click="crud1.rowClick"
                >
                  <el-table-column label="不含税收入" property="incomeOfNoTax" show-overflow-tooltip>
                    <template #default="scope">{{ rbstateFormat(scope.row.incomeOfNoTax) }}</template>
                  </el-table-column>

                  <el-table-column label="含税收入" property="income" show-overflow-tooltip>
                    <template #default="scope">{{ rbstateFormat(scope.row.income) }}</template>
                  </el-table-column>
                  <el-table-column label="不含税成本" property="costOfNoTax" show-overflow-tooltip>
                    <template #default="scope">{{ rbstateFormat(scope.row.costOfNoTax) }}</template>
                  </el-table-column>
                  <el-table-column label="含税成本" property="cost" show-overflow-tooltip>
                    <template #default="scope">{{ rbstateFormat(scope.row.cost) }}</template>
                  </el-table-column>

                  <!-- <el-table-column label="操作" width="auto">
                    <template #default>
                      <el-button type="text" @click="showReconciliationDetail">
                        向下穿透
                      </el-button>
                    </template>
                  </el-table-column>-->
                </el-table>
                <div class="app-page-footer background">
                  <div class="flex-1"></div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="存货" name="存货">
                <el-table
                  ref="tableRef1"
                  v-inno-loading="crud2.loading"
                  class="auto-layout-table"
                  highlight-current-row
                  
                  border
                  height="100%"
                  :data="crud2.data"
                  :row-class-name="crud2.tableRowClassName"
                  stripe
                  row-key="id"
                >
                  <!-- <el-table-column
                    label="含税成本"
                    property="cost"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      {{ rbstateFormat(scope.row.cost) }}
                    </template>
                  </el-table-column>-->
                  <el-table-column label="不含税成本" property="costOfNoTax" show-overflow-tooltip>
                    <template #default="scope">{{ rbstateFormat(scope.row.costOfNoTax) }}</template>
                  </el-table-column>

                  <!-- <el-table-column label="操作" width="auto">
                    <template #default>
                      <el-button type="text" @click="showReconciliationDetail">
                        向下穿透
                      </el-button>
                    </template>
                  </el-table-column>-->
                </el-table>
                <div class="app-page-footer background">
                  <div class="flex-1"></div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div class="custom-button-container">
              <el-button type="primary" :disabled="crud.selections.length === 0" @click="pullDetailsData">重新拉取明细</el-button>
            </div>
          </div>
        </template>
      </inno-split-pane>
      <el-dialog v-model="createDreconVisible" title="添加对账列表" width="400" draggable>
        <el-form ref="formDataRef" :model="formData" :rules="formDataRules" v-inno-loading="crud.loading">
          <el-form-item label="公司:" prop="company.id">
            <inno-remote-select
              v-model="formData.company"
              isObject
              placeholder="请选择或填写公司"
              :queryData="{
                  functionUri: 'metadata://fam'
                }"
              :url="gatewayUrl + 'v1.0/bdsapi/api/companies/meta'"
              :style="{ width: inputWidth + 'px' }"
            />
          </el-form-item>
          <el-form-item label="月度:" prop="sysMonth">
            <el-date-picker v-model="formData.sysMonth" type="month" format="YYYY-MM" value-format="YYYY-MM" placeholder="请选择月度" clearable :style="{ width: inputWidth + 'px' }" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="submitData" :loading="crud.loading">确定</el-button>
            <el-button @click="createDreconVisible = false" :loading="crud.loading">取消</el-button>
          </span>
        </template>
      </el-dialog>
      <reconciliationDetail ref="reconciliationDetailRef" :reconciliationItemId="reconciliationItemId" />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive
} from 'vue';
import request from '@/utils/request';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import reconciliationDetail from '@/views/Inventory/components/reconciliationDetail.vue';
import { ElTable, ElForm, ElMessageBox, ElMessage,ElLoading } from 'element-plus';
import {
  pullRemoteData,
  addReconciliationItem
} from '@/views/Inventory/api/reconciliation';


// input宽度统一管理
const inputWidth = 280;

const reconciliationItemId = ref('');

const tabsName = ref('收入成本');

// 添加对账列表form
const formData = ref({
  company: {},
  sysMonth: ''
});
const formDataRules = reactive<FormRules<RuleForm>>({
  // 设置表单校验
  'company.id': [{ required: true, message: '请选择公司', trigger: 'change' }],
  sysMonth: [{ required: true, message: '请选择日期', trigger: 'blur' }]
});
// 添加对账列表控制显示隐藏
const createDreconVisible = ref(false);
// 获取tableRef0元素实例
const tableRef0 = ref<InstanceType<typeof ElTable>>();

const crudPermission = ref({
  add: ['permission.add.uri'],
  download: ['permission.export.uri']
});
// 对账管理列表
const crud = CRUD(
  {
    title: '对账管理列表',
    url: '/api/ReconciliationItemQuery/GetList',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
           crud.singleSelection(crud.data[0]);
        }
      }
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0
  }
);
const tableRef1 = ref<InstanceType<typeof ElTable>>();
// 收入成本列表
const crud1 = CRUD(
  {
    title: '收入成本',
    url: '/api/ReconciliationIncomeQuery/GetList',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {},
    tablekey: 'tableRef1'
  },
  {
    table: tableRef1
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
// 存货列表
const crud2 = CRUD(
  {
    title: '存货',
    url: '/api/ReconciliationStockQuery/GetList',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef2'
  },
  {
    table: tableRef2
  }
);
const queryList = computed(() => [
  {
    key: 'companyName',
    label: '公司',
    type: 'input',
    show: true
  }
]);
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableRef0);
  crud.toQuery();
});
//监听 crud.rowData.id
watch(
  () => crud.rowData.id,
  (n, o) => {
    crud1.data = [];
    crud2.data = [];
    if (n != null) {
      reconciliationItemId.value = n;
      crud1Query();
      crud2Query();
    }
  },
  { immediate: true, deep: true }
);

const functionUris = {
  confirmAbatement:
    'metadata://fam/credit-debt-Query/functions/confirmAbatement'
};

const handleClick = (tab: String) => {
  tabsName.value = tab.props.label;
};

const reconciliationDetailRef = ref();
const showReconciliationDetail = () => {
  // 打开穿透dialog
  reconciliationDetailRef.value.openDialog();
  reconciliationDetailRef.value.getCrud(crud.rowData.id, tabsName.value);
};

const crud1Query = () => {
  // 收入成本查询
  crud1.query = { reconciliationItemId: crud.rowData.id, limit: 2000 };
  crud1.toQuery(); //
};

const crud2Query = () => {
  // 存货查询
  crud2.query = { reconciliationItemId: crud.rowData.id, limit: 2000 };
  crud2.toQuery();
};

const formDataRef = ref(); // 获取添加form表单实例
const submitData = () => {
  formDataRef.value.validate(async (valid: Object) => {
    if (valid) {
      crud.loading = true;
      formData.value.companyId = formData.value.company.id;
      formData.value.companyName = formData.value.company.name;
      const res = await addReconciliationItem(formData.value);
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '添加成功',
          type: 'success',
          duration: 3 * 1000
        });
        let pullData = {
          reconciliationItemId: res.data.data,
          companyId: formData.value.companyId,
          sysMonth: formData.value.sysMonth,
          companyName: formData.value.companyName,

        };
        const response = await pullRemoteData(pullData);
        if (response.data.code !== 200) {
          ElMessage({
            showClose: true,
            message: response.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        } else {
              ElMessage({
                showClose: true,
                message: '明细拉取成功',
                type: 'success',
                duration: 3 * 1000
              });
        }
       
        crud.toQuery(); // 获取crud数据
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      crud.loading = false;
      formData.value = { company: {}, sysMonth: '' };
      formData.value.company = {};
      createDreconVisible.value = false;
    }
  });
};

const tabhandleClick = () => {
  crud.toQuery();
};
const searchCrud = () => {
  crud.query.abatedStatus = '-1';
  crud.toQuery();
};
// 合计金额千分位
const rbstateFormat = (cellValue: Number) => {
  return asyncNumeral(cellValue, '0,0.00');
};
// 创建
const createReconciliationList = () => {
  createDreconVisible.value = true;
};
// 拉取明细
const pullDetailsData = async () => {
  crud1.loading = true;
  crud2.loading = true;
  const formData = {
    companyId: crud.rowData.companyId,
    companyName: crud.rowData.companyName,
    sysMonth: crud.rowData.sysMonth,
    reconciliationItemId: crud.rowData.id
  };
  try {
    const res = await pullRemoteData(formData);
    crud1.loading = false;
    crud2.loading = false;
    if (res.data.code !== 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
    }else{
      ElMessage({
        showClose: true,
        message: '操作成功',
        type: 'success',
        duration: 3 * 1000
      });
      crud1Query(); // 对账管理列表列表
      crud2Query(); // 存货查询
    }
  } catch (error) {
    // 捕获到的异常会被传递到这里
    crud1.loading = false;
    crud2.loading = false;
  }
};

//下载
const exportClick = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try { 
      await ExportReconciliationList({
        "ReconciliationItemId": crud.rowData.id,
        "CompanyName": crud.rowData.companyName
      }, crud.rowData.companyName + '对账导出数据');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportReconciliationList = async (data, filename) => {
  await request({
    url: '/api/ReconciliationItemQuery/GetExportList',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};
const del = () => {
  if (crud.selections && crud.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请选择要删除的发票分类',
      type: 'error',
      duration: 3 * 1000
    });
  } else {
    if (window.userName !== crud.selections[0].createdBy) {
      ElMessage({
        showClose: true,
        message: '只有创建人才能操作删除!',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    ElMessageBox.confirm(
      '此操作将删除对账以及关联的单数据, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      request({
        url: `/api/ReconciliationItem/del`,
        method: 'POST',
        data: { ReconciliationItemId: crud.rowData.id }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '删除成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    });
  }
};
</script>

<style>
.custom-tabs {
  position: relative;
}

.custom-button-container {
  position: absolute;
  top: 0px;
  right: 0px;
  transform: translate(-25%, 25%);
}
</style>
