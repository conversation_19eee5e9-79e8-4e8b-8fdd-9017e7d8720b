﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 发票入账单
    /// </summary>
    [Table("InvoiceReceiptItem")]
    public class InvoiceReceiptItemPo : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(200)]
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        [Column(TypeName = "date")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        [Comment("公司Id")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [Comment("公司名称")]
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        [Comment("公司Code")]
        public string NameCode { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        [Comment("业务单元Id")]
        public Guid ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        [Comment("业务单元名称")]
        public string ServiceName { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        [Comment("客户Id")]
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [Comment("客户")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 回款天数
        /// </summary>
        [Comment("回款天数")]
        public int BackAmountDays { get; set; }

        /// <summary>
        /// 销售账期天数
        /// </summary>
        [Comment("销售账期天数")]
        public int SaleAccountPeriodDays { get; set; }

        /// <summary>
        /// 实际回款天数
        /// </summary>
        [Comment("实际回款天数")]
        public int? ActualBackAmountDays { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Comment("状态")]
        public StatusEnum Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Comment("备注")]
        public string? Remark { get; set; }

        /// <summary>
        /// 发票入账单明细
        /// </summary>
        public virtual List<InvoiceReceiptDetailPo> InvoiceReceiptDetails { get; set; } = new List<InvoiceReceiptDetailPo>();

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }

        /// <summary>
        /// 审批日期字段
        /// </summary>
        public DateTime? ApprovalTime { get; set; }
    }
}
