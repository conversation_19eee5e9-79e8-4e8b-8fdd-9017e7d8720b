using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    /// <summary>
    /// 销售单号变更事件入参
    /// </summary>
    public class SaleBillCodeChangeInput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public string CompanyId { get; set; } = string.Empty;
        
        /// <summary>
        /// 消息ID，用于幂等处理
        /// </summary>
        public string MsgId { get; set; } = string.Empty;
        
        /// <summary>
        /// 单号变更列表
        /// </summary>
        public List<BillCodeChangeDetail> ChangeList { get; set; } = new List<BillCodeChangeDetail>();
    }

    /// <summary>
    /// 单号变更明细
    /// </summary>
    public class BillCodeChangeDetail
    {
        /// <summary>
        /// 原始单号
        /// </summary>
        public string OriginalBillCode { get; set; } = string.Empty;
        
        /// <summary>
        /// 新单号
        /// </summary>
        public string NewBillCode { get; set; } = string.Empty;
    }
}
