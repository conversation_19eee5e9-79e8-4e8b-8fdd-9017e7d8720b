﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCustomizeInvoiceSubDetailPoFiled : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AgentId",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Classify",
                table: "CustomizeInvoiceRedDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomizeInvoiceItemCode",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "IFHighValue",
                table: "CustomizeInvoiceRedDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OldCustomizeInvoiceItemCode",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OldCustomizeInvoiceItemId",
                table: "CustomizeInvoiceRedDetail",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始计量单位");

            migrationBuilder.AddColumn<string>(
                name: "OriginProductName",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "原始品名（不变）");

            migrationBuilder.AddColumn<string>(
                name: "OriginSpecification",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "原始规格型号");

            migrationBuilder.AddColumn<string>(
                name: "PackUnit",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "计量单位");

            migrationBuilder.AddColumn<string>(
                name: "ProductName",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                comment: "货品名称（开票名称）");

            migrationBuilder.AddColumn<string>(
                name: "Specification",
                table: "CustomizeInvoiceRedDetail",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                comment: "规格型号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AgentId",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "Classify",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "CustomizeInvoiceItemCode",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "IFHighValue",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "OldCustomizeInvoiceItemCode",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "OldCustomizeInvoiceItemId",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "OriginPackUnit",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "OriginProductName",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "OriginSpecification",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "PackUnit",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "ProductName",
                table: "CustomizeInvoiceRedDetail");

            migrationBuilder.DropColumn(
                name: "Specification",
                table: "CustomizeInvoiceRedDetail");
        }
    }
}
