﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Utilities
{
    /// <summary>
    /// 帮助类
    /// </summary>
    public class Utility
    {
        public static CreditPo DeepCopyCredit(CreditPo oldCredit, string note = "")
        {
            if (oldCredit == null)
                return null;
            // 复制原 Credit 对象
            var newCredit = new CreditPo
            {
                AbatedStatus = oldCredit.AbatedStatus,
                BillDate = oldCredit.BillDate,
                CompanyId = oldCredit.CompanyId,
                CompanyName = oldCredit.CompanyName,
                NameCode = oldCredit.NameCode,
                Mark = oldCredit.Mark,
                CreditType = CreditTypeEnum.lossrecognition,
                CustomerId = oldCredit.CustomerId,
                CustomerName = oldCredit.CustomerName,
                LatestUniCode = oldCredit.LatestUniCode,
                BillCode = oldCredit.BillCode,
                FinishDate = oldCredit.FinishDate,
                InvoiceStatus = InvoiceStatusEnum.noninvoice,
                Note = note,
                ServiceId = oldCredit.ServiceId,
                ServiceName = oldCredit.ServiceName,
                RelateCode = oldCredit.RelateCode,
                BusinessDeptFullPath = oldCredit.BusinessDeptFullPath,
                BusinessDeptFullName = oldCredit.BusinessDeptFullName,
                BusinessDeptId = oldCredit.BusinessDeptId,
                IsLongTerm = oldCredit.IsLongTerm,

                OrderNo = oldCredit.OrderNo,
                OriginOrderNo = oldCredit.OriginOrderNo,
                GroupId = oldCredit.GroupId,
                SaleSystemId = oldCredit.SaleSystemId,
                SaleSystemName = oldCredit.SaleSystemName,
                SaleSource = oldCredit.SaleSource,
                HospitalId = oldCredit.HospitalId,
                HospitalName = oldCredit.HospitalName,
                SaleType = oldCredit.SaleType,
                ShipmentCode = oldCredit.ShipmentCode,
                DeptName = oldCredit.DeptName,
                ProjectCode = oldCredit.ProjectCode,
                ProjectName = oldCredit.ProjectName,
                ProjectId = oldCredit.ProjectId,
                CustomerOrderCode = oldCredit.CustomerOrderCode,
                SunPurchaseRelatecode = oldCredit.SunPurchaseRelatecode,
                ServiceConfirmRevenuePlanModeEnum = oldCredit.ServiceConfirmRevenuePlanModeEnum,
                AgentName = oldCredit.AgentName,
                ProducerName = oldCredit.ProducerName,
                CreditSaleSubType = oldCredit.CreditSaleSubType,
                Id = Guid.NewGuid(),
                CreatedBy = oldCredit.CreatedBy,
                CreatedTime = DateTime.Now,
                IsNoNeedInvoice = IsNoNeedInvoiceEnum.Need,
                CreditDetails = new List<CreditDetailPo>(),
            };
            return newCredit;
        }

        /// <summary>
        /// 应收明细金额
        /// </summary>
        /// <param name="oldCreditDetail"></param>
        /// <param name="newCredit"></param>
        /// <param name="amount">明细金额</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public static CreditDetailPo DeepCopyCreditDetail(CreditDetailPo oldCreditDetail, CreditPo newCredit, decimal? amount = null, decimal? quantity = null)
        {
            if (oldCreditDetail == null || newCredit == null)
                throw new ApplicationException("深拷贝必须传应收和原始应收明细");
            // 复制原 CreditDetail 对象
            var newCreditDetail = new CreditDetailPo
            {
                Credit = newCredit,
                SaleDetailId = oldCreditDetail.SaleDetailId,
                ServiceId = oldCreditDetail.ServiceId,
                Specification = oldCreditDetail.Specification,
                PackSpec = oldCreditDetail.PackSpec,
                PriceSource = oldCreditDetail.PriceSource,
                AgentId = oldCreditDetail.AgentId,
                Amount = amount.HasValue ? amount : oldCreditDetail.Amount,
                BatchId = oldCreditDetail.BatchId,
                CreatedBy = oldCreditDetail.CreatedBy,
                CreatedTime = oldCreditDetail.CreatedTime,
                CustomerId = oldCreditDetail.CustomerId,
                CustomerName = oldCreditDetail.CustomerName,
                IFHighValue = oldCreditDetail.IFHighValue,
                InvoiceAmount = 0,
                NoInvoiceAmount = amount.HasValue ? amount : oldCreditDetail.NoInvoiceAmount,
                OrderNo = oldCreditDetail.OrderNo,
                OriginalId = Guid.NewGuid(),
                OriginalPrice = oldCreditDetail.OriginalPrice,
                OriginDetailId = Guid.NewGuid().ToString(),
                PackUnit = oldCreditDetail.PackUnit,
                Price = oldCreditDetail.Price,
                ProductId = oldCreditDetail.ProductId,
                ProductName = oldCreditDetail.ProductName,
                ProductNo = oldCreditDetail.ProductNo,
                ProjectId = oldCreditDetail.ProjectId,
                Quantity = quantity.HasValue ? quantity.Value : oldCreditDetail.Quantity,
                RelateCode = oldCreditDetail.RelateCode,
                TaxRate = oldCreditDetail.TaxRate,
                UpdatedBy = oldCreditDetail.UpdatedBy,
                UpdatedTime = oldCreditDetail.UpdatedTime,
                Id = Guid.NewGuid(),
            };
            return newCreditDetail;
        }

        /// <summary>
        /// 深拷贝应付付款计划
        /// </summary>
        /// <param name="oldDebtDetail"></param>
        /// <param name="targetDebt"></param>
        /// <param name="amount"></param>
        /// <param name="status"></param>
        /// <param name="creditId"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public static DebtDetailPo DeepCopyDebtDetail(DebtDetailPo oldDebtDetail, DebtPo targetDebt, decimal? amount = null, DebtDetailStatusEnum? status = null, Guid? creditId = null)
        {
            if (oldDebtDetail == null || targetDebt == null)
                throw new ApplicationException("深拷贝必须传目标应付和原始应付明细");
            var newDebtDetail = new DebtDetailPo
            {
                Id = Guid.NewGuid(),
                Debt = targetDebt,
                Settletype = oldDebtDetail.Settletype,
                SpdDiscount = oldDebtDetail.SpdDiscount,
                Status = status.HasValue ? status.Value : oldDebtDetail.Status,
                AuditStatus = oldDebtDetail.AuditStatus,
                AccountingPeriodDate = oldDebtDetail.AccountingPeriodDate,
                AccountPeriodType = oldDebtDetail.AccountPeriodType,
                AttachFileIds = oldDebtDetail.AttachFileIds,
                BackPayTime = oldDebtDetail.BackPayTime,
                Code = oldDebtDetail.Code,
                CostDiscount = oldDebtDetail.CostDiscount,
                CreatedBy = oldDebtDetail.CreatedBy,
                CreatedTime = DateTimeOffset.UtcNow,
                Discount = oldDebtDetail.Discount,
                DistributionDiscount = oldDebtDetail.DistributionDiscount,
                DraftBillExpireDate = oldDebtDetail.DraftBillExpireDate,
                FinanceDiscount = oldDebtDetail.FinanceDiscount,
                IsInvoiceReceipt = oldDebtDetail.IsInvoiceReceipt,
                OARequestId = oldDebtDetail.OARequestId,
                OrderNo = oldDebtDetail.OrderNo,
                OriginValue = oldDebtDetail.OriginValue,
                ProbablyPayTime = oldDebtDetail.ProbablyPayTime,
                UpdatedBy = oldDebtDetail.UpdatedBy,
                Value = amount.HasValue ? amount.Value : oldDebtDetail.Value,
                PurchaseCode = oldDebtDetail.PurchaseCode,
                ReceiveCode = oldDebtDetail.ReceiveCode,
                RecognizeReceiveCode = oldDebtDetail.RecognizeReceiveCode,
                Remark = oldDebtDetail.Remark,
                TaxDiscount = oldDebtDetail.TaxDiscount,
                UpdatedTime = DateTimeOffset.UtcNow,
                CreditId = creditId.HasValue ? creditId.Value : oldDebtDetail.CreditId,
            };
            return newDebtDetail;
        }

        public static List<int> GetCanNotSubmitStatus()
        {
            return new List<int>() { (int)InventoryStatus.InventoryStocktaking, (int)InventoryStatus.WaintInventoryStocktaking,(int)InventoryStatus.InventoryStocktakingFinished };
        }
    }
}
