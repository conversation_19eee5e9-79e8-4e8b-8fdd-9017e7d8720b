﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addAdvanceRecordFiled : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "RateOfYear",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalDiscounts",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RateOfYear",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "TotalDiscounts",
                table: "AdvanceFundBusinessCheckDetail");
        }
    }
}
