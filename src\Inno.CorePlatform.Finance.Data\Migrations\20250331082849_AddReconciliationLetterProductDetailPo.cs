﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddReconciliationLetterProductDetailPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReconciliationLetterProductDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReconciliationLetterItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "日期"),
                    BusinessType = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "业务类型"),
                    ShipmentCode = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "随货单号"),
                    BillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "单号"),
                    Salesman = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "业务员"),
                    ProductNo = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "货号"),
                    Specification = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "规格"),
                    BatchNo = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "批号"),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "单价"),
                    Quantity = table.Column<int>(type: "int", nullable: true, comment: "数量"),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "金额"),
                    InvoiceTime = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "开票时间"),
                    ReceivableCode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "应收单号"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "备注"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReconciliationLetterProductDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReconciliationLetterProductDetail_ReconciliationLetterItem_ReconciliationLetterItemId",
                        column: x => x.ReconciliationLetterItemId,
                        principalTable: "ReconciliationLetterItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "财务对账函明细表");

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationLetterProductDetail_ReconciliationLetterItemId",
                table: "ReconciliationLetterProductDetail",
                column: "ReconciliationLetterItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReconciliationLetterProductDetail");
        }
    }
}
