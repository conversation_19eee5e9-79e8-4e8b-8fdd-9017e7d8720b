﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 对账表
    /// </summary>
    [Table("ReconciliationItem")]
    public class ReconciliationItemPo : BasePo
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        [Column(TypeName = "date")]
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        [Comment("系统月度")] 
        public string? SysMonth { get; set; } 
    }
}
