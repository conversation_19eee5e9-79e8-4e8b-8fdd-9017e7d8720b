﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddSunPurchaseInvoiceDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SunPurchaseInvoiceDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SXH = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "顺序号(必填)"),
                    SFWPSFP = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "是否无配送发票(必填)"),
                    WPSFPSM = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "无配送发票说明(选填)"),
                    SFCH = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "是否冲红(必填)"),
                    HCTBDM = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "耗材统编代码(必填)"),
                    HCXFDM = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "耗材细分代码(选填)"),
                    QYBDDM = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "企业本地代码(选填)"),
                    GGXHSM = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "规格型号说明(选填)"),
                    GLMXBH = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "关联明细编号(选填)"),
                    XSDDH = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "销售订单号(选填)"),
                    SCPH = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "生产批号(必填)"),
                    SCRQ = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "生产日期(必填)"),
                    YXRQ = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "有效日期(必填)"),
                    SPSL = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "商品数量(必填)"),
                    WSDJ = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "无税单价(必填)"),
                    HSDJ = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "含税单价(必填)"),
                    SL = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "税率(必填)"),
                    SE = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "税额(必填)"),
                    HSJE = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "含税金额(必填)"),
                    PFJ = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "批发价(必填)"),
                    LSJ = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "零售价(必填)"),
                    ZCZH = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "注册证号(必填)"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SunPurchaseInvoiceDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SunPurchaseInvoiceDetail_Invoice_InvoiceId",
                        column: x => x.InvoiceId,
                        principalTable: "Invoice",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "阳采发票明细表");

            migrationBuilder.CreateIndex(
                name: "IX_SunPurchaseInvoiceDetail_InvoiceId",
                table: "SunPurchaseInvoiceDetail",
                column: "InvoiceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SunPurchaseInvoiceDetail");
        }
    }
}
