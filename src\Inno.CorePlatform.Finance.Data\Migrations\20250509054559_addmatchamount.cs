﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addmatchamount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "MatchedAmount",
                table: "MergeInputBillSubmitDetail",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "NoTaxAmount",
                table: "MergeInputBillSubmitDetail",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                table: "MergeInputBillSubmitDetail",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                table: "MergeInputBillDetail",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MatchedAmount",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "NoTaxAmount",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "MergeInputBillDetail");
        }
    }
}
