using Dapr.Client;
using Google.Protobuf.WellKnownTypes;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.EPPlus;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.Enums;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json;
using Npoi.Mapper;
using OfficeOpenXml;
using StackExchange.Redis;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using static Inno.CorePlatform.Finance.Application.QueryServices.Inputs.BatchDownLoadInvoiceInput;
using static NPOI.HSSF.Util.HSSFColor;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class RecognizeReceiveAppService : IRecognizeReceiveAppService
    {
        private readonly IRecognizeReceiveItemRepository _itemRepository;
        private readonly IRecognizeReceiveDetailRepository _detailRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICodeGenClient _codeGenClient;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ISellRecognizeApiClient _sellRecognizeApiClient;
        private readonly PortInterfaces.Clients.ISellApiClient _sellApiClient;
        private readonly ISubLogService _logger;
        private readonly IBaseAllQueryService<RecognizeReceiveDetailPo> _receiveDetailQueryService;
        private readonly IBaseAllQueryService<RecognizeReceiveItemPo> _receiveItemQueryService;
        private readonly IBaseAllQueryService<InvoiceCreditPo> _invoiceCreditQueryService;
        private readonly IBaseAllQueryService<CreditPo> _creditQueryService;
        private readonly IBaseAllQueryService<AbatementPo> _abatementQueryService;
        private readonly FinanceDbContext _db;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        private readonly ISPDApiClient _sPDApiClient;
        private readonly PortInterfaces.Clients.IICApiClient _iICApiClient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly DaprClient _daprClient;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IConfiguration _configuration;
        private readonly ICoordinateClient _coordinateclient;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly ICompanyDateService _companyDateService;
        private readonly IEDAFailureMsgClient _edaFailureMsgClient;
        private readonly IKingdeeFinanceClient _kingdeeFinanceClient;

        public RecognizeReceiveAppService(IRecognizeReceiveItemRepository itemRepository,
            IRecognizeReceiveDetailRepository detailRepository,
            IUnitOfWork unitOfWork,
            ICodeGenClient codeGenClient,
            PortInterfaces.Clients.IBDSApiClient bDSApiClient,
            IKingdeeApiClient kingdeeApiClient,
            ISellRecognizeApiClient sellRecognizeApiClient,
            PortInterfaces.Clients.ISellApiClient sellApiClient,
            ISPDApiClient sPDApiClient,
            PortInterfaces.Clients.IICApiClient iICApiClient,
            FinanceDbContext db,
            DaprClient daprClient,
            ISubLogService logger,
            IBaseAllQueryService<RecognizeReceiveDetailPo> receiveDetailQueryService,
            IBaseAllQueryService<AbatementPo> abatementQueryService,
            IBaseAllQueryService<RecognizeReceiveItemPo> receiveItemQueryService,
            IBaseAllQueryService<InvoiceCreditPo> invoiceCreditQueryService,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
            IAppServiceContextAccessor appServiceContextAccessor,
            IBaseAllQueryService<CreditPo> creditRepostory,
            IFileGatewayClient fileGatewayClient,
            IConfiguration configuration,
            ICoordinateClient coordinateclient,
            ILogisticsApiClient logisticsApiClient,
            IEDAFailureMsgClient edaFailureMsgClient,
            ICompanyDateService companyDateService,
            IKingdeeFinanceClient kingdeeFinanceClient)
        {
            _itemRepository = itemRepository;
            _detailRepository = detailRepository;
            _unitOfWork = unitOfWork;
            _bDSApiClient = bDSApiClient;
            _codeGenClient = codeGenClient;
            _kingdeeApiClient = kingdeeApiClient;
            _sellRecognizeApiClient = sellRecognizeApiClient;
            _sellApiClient = sellApiClient;
            _logger = logger;
            _receiveDetailQueryService = receiveDetailQueryService;
            _invoiceCreditQueryService = invoiceCreditQueryService;
            _creditQueryService = creditRepostory;
            _abatementQueryService = abatementQueryService;
            _receiveItemQueryService = receiveItemQueryService;
            _inventoryQueryService = inventoryQueryService;
            _sPDApiClient = sPDApiClient;
            _iICApiClient = iICApiClient;
            _db = db;
            _daprClient = daprClient;
            _appServiceContextAccessor = appServiceContextAccessor;
            _fileGatewayClient = fileGatewayClient;
            _configuration = configuration;
            _coordinateclient = coordinateclient;
            _logisticsApiClient = logisticsApiClient;
            _companyDateService = companyDateService;
            _edaFailureMsgClient = edaFailureMsgClient;
            _kingdeeFinanceClient = kingdeeFinanceClient;
        }

        /// <summary>
        /// 单头操作
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        /// <exception cref="AppServiceException"></exception>
        public async Task<int> AddItemAsync(ReceivedItemAddInput input)
        {
            try
            {
                var companyNameCodes = input.RecognizeReceiveInfos.Select(p => p.orgNumber).Distinct().ToList();
                if (companyNameCodes.Count > 1)
                {
                    throw new ApplicationException($"操作失败，原因：数据明细中存在多个公司的数据");
                }

                var receivingtypes = new List<string>
                {
                    "负数应收",
                    "销售回款",
                    "预收款",
                    //"退采购付款",
                    "销售现金折扣",
                    "应付",
                    "收货款（保证金转）"
                };
                if (input.RecognizeReceiveInfos.Exists(p => !receivingtypes.Contains(p.receivingtype)))
                {
                    throw new ApplicationException($"操作失败，原因：数据中存在不支持款的数据类型，如【赔款】【押金】【未知】【其它】类型需要先指定认款类型后操作！");
                }

                if (string.IsNullOrEmpty(input.CompanyId))
                {
                    var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput
                    {
                        nameCodeEq = companyNameCodes.First()
                    });
                    if (company.Count == 0)
                    {
                        throw new ApplicationException($"操作失败，原因：{companyNameCodes.First()}找不到公司信息！");
                    }
                    input.CompanyId = company.First().companyId;
                    input.CompanyName = company.First().companyName;
                }

                #region 查询客户信息

                var customerNames = input.RecognizeReceiveInfos.Select(x => x.payerName).ToList();
                BDSBaseInput custInput = new();
                custInput.names = customerNames;
                var custResult = await _bDSApiClient.GetCustomerByNames(custInput);

                #endregion

                foreach (var item in input.RecognizeReceiveInfos)
                {
                    //var isExist = await _itemRepository.IsExist(item.billno);
                    //if (isExist)
                    //{
                    //    throw new ApplicationException($"操作失败，原因：收款单号[{item.billno}],存在未完成认款单，不允许创建");
                    //}

                    if (input.Classify == RecognizeReceiveClassifyEnum.Temp && item.receivingtype == "负数应收")
                    {
                        throw new ApplicationException($"操作失败，原因：【金蝶】收款单号[{item.billno}],负数应收不能认为暂收款");
                    }

                    if (string.IsNullOrEmpty(item.payeedate))
                    {
                        throw new ApplicationException($"操作失败，原因：【金蝶】收款单号[{item.billno}],收款日期为空");
                    }

                    if (string.IsNullOrEmpty(item.payerNumber))
                    {
                        throw new ApplicationException($"操作失败，原因：【金蝶】收款单客户id为空，创建认款单失败！");
                    }

                    if ((item.settletype == "商业承兑汇票" || item.settletype == "银行承兑汇票") &&
                        string.IsNullOrEmpty(item.draftbillexpiredate))
                    {
                        throw new ApplicationException($"操作失败，原因：商业承兑汇票，银行承兑汇票必须要有到期日");
                    }

                    var entity = new RecognizeReceiveItem
                    {
                        Status = 0,
                        BillDate = DateTime.Now,
                        ReceiveCode = item.billno,
                        CompanyId = input.CompanyId ?? "",
                        CompanyName = item.orgName,
                        CustomerNme = item.payerName,
                        CustomerId = item.payerNumber,
                        BusinessDepId = input.BusinessDeptId,
                        BusinessDeptFullName = input.BusinessDeptFullName,
                        BusinessDeptFullPath = input.BusinessDeptFullPath,
                        CreatedTime = DateTime.Now,
                        CreatedBy = input.CreatedBy ?? "none",
                        ReceiveValue = item.actrecamt,
                        Type = item.receivingtype,
                        ReceiveDate = DateTime.Parse(item.payeedate),
                        Classify = input.Classify,
                        Settletype = item.settletype, //结算方式
                        DraftBillExpireDate = string.IsNullOrEmpty(item.draftbillexpiredate)
                            ? null
                            : DateTime.Parse(item.draftbillexpiredate), //到期日
                        BankName = item.bankName,
                        BankNum = item.bankNum
                    };
                    // 应付收款类型根据供应商名称查找客户id
                    if (item.receivingtype == "应付")
                    {
                        var single = custResult.FirstOrDefault(x => x.Name == item.payerName);
                        if (single != null)
                        {
                            entity.CustomerId = single.Id;
                        }
                    }

                    //生成单号
                    var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(
                        new CompetenceCenter.BDSCenter.BDSBaseInput
                        { ids = new List<string> { entity.CompanyId.ToString() } });
                    var companyInfo = companyInfoOutput?.FirstOrDefault();
                    if (companyInfo == null)
                    {
                        entity.Code = Guid.NewGuid().ToString();
                        //      throw new AppServiceException("公司信息不存在");
                    }
                    else
                    {
                        if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
                        {
                            companyInfo.sysMonth = DateTime.Now.ToString("yyyy-MM");
                        }

                        var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = input.BusinessArea ?? "FXBD",
                            BillType = "REC",
                            SysMonth = companyInfo.sysMonth,
                            DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        if (outPut.Status)
                        {
                            entity.Code = outPut.Codes.First();
                            var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                            DateTime.TryParse(sysMonth, out DateTime billDate);
                            entity.BillDate = billDate;
                        }
                        else
                        {
                            throw new ApplicationException($"生成Code失败，{outPut.Msg}");
                        }

                        if (string.IsNullOrEmpty(outPut.Codes[0]))
                        {
                            throw new AppServiceException("单号生成异常，请重试！");
                        }
                    }

                    await _itemRepository.AddAsync(entity);
                }

                var res = await _unitOfWork.CommitAsync();
                return res;
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        #region 仅用作暂收款，货款认款逻辑修改，暂不支持

        /// <summary>
        /// 单个添加明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<int> CreatedDetailAsync(RecognizeReceiveDetailInput input)
        {
            try
            {
                if (!input.Value.HasValue || input.Value.Value <= 0)
                {
                    throw new ApplicationException($"操作失败，原因：认款金额不能为空或者认款金额必须大于0");
                }

                var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
                if (item == null)
                {
                    throw new ApplicationException($"未找到认款单据");
                }

                if (item.Status != 0)
                {
                    throw new ApplicationException($"该状态不能添加明细");
                }

                var companyId = Guid.Parse(item.CompanyId);
                //货款
                if (input.ClassifyType == RecognizeReceiveClassifyEnum.Goods)
                {
                    #region 页面已无此功能

                    //var exist = await _detailRepository.IsExist(input.Code, input.RecognizeReceiveItemId);
                    //if (exist)
                    //{
                    //    throw new ApplicationException($"发票号/订单号/初始应收{input.Code}已存在，请勿重复添加");
                    //}
                    //var list = await _db.RecognizeReceiveDetails.Where(t => t.RecognizeReceiveItemId == input.RecognizeReceiveItemId).ToListAsync();
                    //// 认款类型
                    //var rkType = list.Count > 0 ? list.FirstOrDefault().Type : input.Type;
                    //if (rkType != input.Type)
                    //{
                    //    throw new ApplicationException($"同一个认款单中，认款类型，只能有一种！");
                    //}
                    //if (item.Classify == RecognizeReceiveClassifyEnum.Goods && !string.IsNullOrEmpty(item.RelateCode))
                    //{
                    //    // 暂收款转货款金额校验
                    //    var receives = await _db.RecognizeReceiveItems.Where(x => item.ReceiveCode == x.ReceiveCode && x.Status != RecognizeReceiveItemStatusEnum.Canceled).Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking().ToListAsync();
                    //    var existValue = receives.Where(x => x.CustomerId == item.CustomerId && x.RelateCode == item.RelateCode).Sum(x => x.RecognizeReceiveDetails.Sum(d => d.Value));
                    //    // 暂收款转货款
                    //    if (item.ReceiveValue - existValue < input.Value)
                    //    {
                    //        throw new ApplicationException($"认款金额{input.Value}大于认款单剩余可认款金额{item.ReceiveValue - existValue}");
                    //    }
                    //}
                    //string? customerId = "";
                    //string? customerName = "";
                    //string? hospitalId = null; //终端客户id
                    //string? hospitalName = "";//终端客户
                    //Guid? serviceId = null;
                    //string? serviceName = "";
                    //if (input.Type == (int)RecognizeTypeEnums.Credit)
                    //{
                    //    var credit = await _creditQueryService.FirstOrDefaultAsync(p => p.BillCode == input.Code && p.CreditType == CreditTypeEnum.origin && p.CompanyId == companyId);
                    //    if (credit == null)
                    //    {
                    //        throw new ApplicationException($"应收单号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var abatments = await _db.Abatements.Where(p => (p.DebtBillCode == credit.BillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
                    //    var abatmentValue = abatments.Where(t => t.DebtBillCode == credit.BillCode || t.CreditBillCode == credit.BillCode).Sum(t => t.Value);
                    //    if (Math.Abs(credit.Value) - abatmentValue < input.Value.Value)
                    //    {
                    //        throw new ApplicationException($"超出初始应收{input.Code}余额，余额为{Math.Abs(credit.Value) - abatmentValue}请确认后重新操作！");
                    //    }
                    //    customerId = credit.CustomerId.ToString();
                    //    customerName = credit.CustomerName;
                    //    hospitalId = credit.HospitalId;
                    //    hospitalName = credit.HospitalName;
                    //    serviceId = credit.ServiceId;
                    //    serviceName = credit.ServiceName;
                    //}
                    //else if (input.Type == (int)RecognizeTypeEnums.Invoice)
                    //{
                    //    var invoiceCredits = await _invoiceCreditQueryService.GetAllListAsync(p => p.InvoiceNo == input.Code && p.Credit.CompanyId == companyId, new List<string> { "Credit" });
                    //    if (invoiceCredits == null || !invoiceCredits.Any())
                    //    {
                    //        throw new ApplicationException($"发票号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == input.Code);
                    //    if (invoice == null)
                    //    {
                    //        throw new ApplicationException($"未找到发票号：{input.Code}数据，请确认后重新操作！");
                    //    }
                    //    var recognizeReceiveAmount = await GetRecognizeReceiveAmount(invoice.InvoiceNo, 2);
                    //    // 初始化发票则加入计算
                    //    if (invoice.IsInit.HasValue && invoice.IsInit.Value)
                    //    {
                    //        recognizeReceiveAmount += Math.Abs(invoice.ReceiveAmount ?? 0);
                    //    }
                    //    if (invoice.InvoiceAmount - Math.Abs(invoice.RedAmount ?? 0) - recognizeReceiveAmount < input.Value)
                    //    {
                    //        throw new ApplicationException($"操作失败：发票号{invoice.InvoiceNo}超出可认款金额");
                    //    }
                    //    customerId = invoiceCredits.FirstOrDefault().Credit?.CustomerId.ToString(); ;
                    //    customerName = invoiceCredits.FirstOrDefault().Credit.CustomerName;
                    //    serviceId = invoiceCredits.FirstOrDefault().Credit?.ServiceId;
                    //    serviceName = invoiceCredits.FirstOrDefault().Credit?.ServiceName;
                    //    hospitalId = invoice.HospitalId;//终端客户id 
                    //    hospitalName = invoice.HospitalName;//终端客户名
                    //}
                    //else if (input.Type == (int)RecognizeTypeEnums.Orderno)
                    //{
                    //    var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.Code);
                    //    if (saleOut == null)
                    //    {
                    //        throw new ApplicationException($"订单号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                    //    {
                    //        id = saleOut.CustomerId.ToString()
                    //    });
                    //    var recognizeReceiveAmount = await GetRecognizeReceiveAmount(input.Code, 1);
                    //    if (input.TotalAmount - recognizeReceiveAmount < input.Value)
                    //    {
                    //        throw new ApplicationException($"操作失败：订单号{input.Code}超出可认款金额");
                    //    }
                    //    customerId = saleOut.CustomerId.ToString();
                    //    customerName = saleOut.CustomerName;
                    //    hospitalId = saleOut.HospitalId;//终端客户id
                    //    hospitalName = saleOut.HospitalName;//终端客户名
                    //}
                    //// 校验为不是第三方回款客户的id是否一致
                    //if (!input.IsReturnCustomer && customerId != input.CustomerId.ToString())
                    //{
                    //    throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                    //}
                    //var otherQuery = _receiveDetailQueryService.GetIQueryable(p => p.RecognizeReceiveItem.ReceiveCode == item.ReceiveCode &&
                    //                                                               p.RecognizeReceiveItemId != item.Id &&
                    //                                                               p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled, new List<string>() { "RecognizeReceiveItem" });
                    //var otherDetails = await otherQuery.ToListAsync();
                    //item.UpdatedTime = DateTime.Now;
                    //item.Value = item.RecognizeReceiveDetails.Sum(t => t.Value) + input.Value.Value;
                    //if (item.Type == "负数应收")
                    //{
                    //    if (Math.Abs(item.ReceiveValue) < item.Value + otherDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(t => t.Value))//总认款金额不能超过收款单的总金额
                    //    {
                    //        throw new ApplicationException($"认款金额不能大于收款金额");
                    //    }
                    //}
                    //else
                    //{
                    //    if (string.IsNullOrEmpty(item.RelateCode))
                    //    {
                    //        if (item.ReceiveValue < item.Value + otherDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(t => t.Value))//总认款金额不能超过收款单的总金额
                    //        {
                    //            throw new ApplicationException($"认款金额不能大于收款金额");
                    //        }
                    //    }
                    //    else
                    //    {
                    //        if (item.ReceiveValue < item.Value)
                    //        {
                    //            throw new ApplicationException($"认款金额不能大于收款金额");
                    //        }
                    //    }

                    //}

                    //var entity = input.Adapt<RecognizeReceiveDetailPo>();
                    //entity.RecognizeDate = DateTime.UtcNow.AddHours(8);
                    //entity.CustomerId = customerId;
                    //entity.CustomerNme = customerName;
                    //entity.ServiceId = serviceId;
                    //entity.ServiceName = serviceName;
                    //entity.Code = entity.Code.Trim();
                    //entity.HospitalId = hospitalId;
                    //entity.HospitalName = hospitalName;
                    //await _db.RecognizeReceiveDetails.AddAsync(entity);

                    #endregion

                    throw new ApplicationException($"请从批量创建弹窗中创建明细");
                }
                //暂收款
                else if (input.ClassifyType == RecognizeReceiveClassifyEnum.Temp)
                {
                    if (!input.CustomerId.HasValue)
                    {
                        throw new ApplicationException($"客户必填");
                    }

                    if (input.Value < 0)
                    {
                        throw new ApplicationException($"不允许负数");
                    }

                    var surplusAmount = item.ReceiveValue;
                    var others = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveTempDetails)
                        .Where(x => x.ReceiveCode == item.ReceiveCode &&
                                    x.Status != RecognizeReceiveItemStatusEnum.Canceled).AsNoTracking().ToListAsync();
                    foreach (var other in others)
                    {
                        surplusAmount -= other.RecognizeReceiveTempDetails.Sum(x =>
                            (x.Value - (x.CancelValue.HasValue ? x.CancelValue.Value : 0)));
                    }

                    if (surplusAmount < input.Value)
                    {
                        throw new ApplicationException($"超出剩余可认款金额{surplusAmount}");
                    }

                    var detail = item.RecognizeReceiveTempDetails
                        .Where(t => t.CustomerId == input.CustomerId.Value.ToString()).FirstOrDefault();
                    if (detail == null)
                    {
                        var entity = input.Adapt<RecognizeReceiveTempDetail>();
                        entity.Status = RecognizeReceiveDetailEnum.Normal;
                        entity.CancelValue = 0;
                        entity.RecognizeDate = DateTime.UtcNow.AddHours(8);
                        entity.BusinessDeptId = item.BusinessDepId;
                        entity.BusinessDeptFullPath = item.BusinessDeptFullPath;
                        entity.BusinessDeptFullName = item.BusinessDeptFullName;
                        entity.CustomerId = input.CustomerId.HasValue ? input.CustomerId.ToString() : string.Empty;
                        entity.CustomerName = input.CustomerName;
                        item.RecognizeReceiveTempDetails.Add(entity);
                    }
                    else
                    {
                        // 存在相同客户进行累加金额
                        detail.RecognizeDate = DateTime.UtcNow.AddHours(8);
                        detail.Value += input.Value.HasValue ? input.Value.Value : 0;
                    }

                    item.UpdatedTime = DateTime.Now;
                    item.Value = item.RecognizeReceiveTempDetails.Sum(t => t.Value);
                    if (item.Value > item.ReceiveValue)
                    {
                        throw new ApplicationException($"认款金额不能大于收款金额");
                    }
                    else
                    {
                        await _itemRepository.UpdateAsync(item);
                    }
                }

                var res = await _unitOfWork.CommitAsync();
                await UpdateRecognizeReceiveItemProjectNameByCode(input.RecognizeReceiveItemId); ////更新项目名称和项目代码
                return res;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 单个修改明细
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<int> UpdateDetailAsync(Guid id, RecognizeReceiveDetailInput input)
        {
            try
            {
                if (!input.Value.HasValue || input.Value.Value <= 0)
                {
                    throw new ApplicationException($"操作失败，原因：认款金额不能为空或者认款金额必须大于0");
                }

                var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
                var companyId = Guid.Parse(item.CompanyId);
                if (item == null)
                {
                    throw new ApplicationException($"未找到认款单据");
                }

                if (item.Status == 0)
                {
                    throw new ApplicationException($"该状态不能修改明细");
                }

                var list = await _db.RecognizeReceiveDetails
                    .Where(t => t.RecognizeReceiveItemId == input.RecognizeReceiveItemId && t.Code != input.Code)
                    .AsNoTracking().ToListAsync();
                // 当前已存在的认款金额
                var currentValue = _db.RecognizeReceiveDetails
                    .Where(t => t.RecognizeReceiveItemId == input.RecognizeReceiveItemId && t.Code == input.Code)
                    .AsNoTracking().Sum(x => x.Value);
                // 认款类型
                var rkType = list.Count > 0 ? list.FirstOrDefault().Type : input.Type;
                if (rkType != input.Type)
                {
                    throw new ApplicationException($"同一个认款单中，认款类型，只能有一种！");
                }

                //货款
                if (input.ClassifyType == RecognizeReceiveClassifyEnum.Goods)
                {
                    #region 页面已无此功能

                    //string? customerId = "";
                    //string? customerName = "";
                    //Guid? serviceId = null;
                    //string? serviceName = "";
                    //if (input.Type == (int)RecognizeTypeEnums.Credit)
                    //{
                    //    var credit = await _creditQueryService.FirstOrDefaultAsync(p => p.BillCode == input.Code && p.CreditType == CreditTypeEnum.origin && p.CompanyId == companyId);
                    //    if (credit == null)
                    //    {
                    //        throw new ApplicationException($"应收单号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var abatments = await _db.Abatements.Where(p => (p.DebtBillCode == credit.BillCode)).Select(p => p.Adapt<AbatmentDTO>()).AsNoTracking().ToListAsync();
                    //    var abatmentValue = abatments.Where(t => t.DebtBillCode == credit.BillCode || t.CreditBillCode == credit.BillCode).Sum(t => t.Value);
                    //    if (Math.Abs(credit.Value) - abatmentValue < input.Value.Value)
                    //    {
                    //        throw new ApplicationException($"超出初始应收{input.Code}余额，余额为{Math.Abs(credit.Value) - abatmentValue}请确认后重新操作！");
                    //    }
                    //    customerId = credit.CustomerId.ToString();
                    //    customerName = credit.CustomerName;
                    //}
                    //else if (input.Type == (int)RecognizeTypeEnums.Invoice)
                    //{
                    //    var invoiceCredit = await _invoiceCreditQueryService.FirstOrDefaultAsync(p => p.InvoiceNo == input.Code, new List<string> { "Credit" });
                    //    if (invoiceCredit == null)
                    //    {
                    //        throw new ApplicationException($"发票号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == input.Code);
                    //    if (invoice == null)
                    //    {
                    //        throw new ApplicationException($"未找到发票号：{input.Code}数据，请确认后重新操作！");
                    //    }
                    //    var recognizeReceiveAmount = await GetRecognizeReceiveAmount(invoice.InvoiceNo, 2);
                    //    // 初始化发票则加入计算
                    //    if (invoice.IsInit.HasValue && invoice.IsInit.Value)
                    //    {
                    //        recognizeReceiveAmount += Math.Abs(invoice.ReceiveAmount ?? 0);
                    //    }
                    //    if (invoice.InvoiceAmount - Math.Abs(invoice.RedAmount ?? 0) - recognizeReceiveAmount + currentValue < input.Value)
                    //    {
                    //        throw new ApplicationException($"操作失败：发票号{invoice.InvoiceNo}超出可认款金额");
                    //    }
                    //    //var responseData = await VerifyInvoice(new RecognizeReceiveDetailsOfInvoiceInput
                    //    //{
                    //    //    ReceiveInvoices = new List<ReceiveInvoice> {
                    //    //      new ReceiveInvoice{  Amount=input.Value, InvoiceNo=input.Code }
                    //    //    },
                    //    //    RecognizeReceiveItemId = input.RecognizeReceiveItemId
                    //    //});
                    //    //if (responseData.Code != CodeStatusEnum.Success)
                    //    //{
                    //    //    throw new ApplicationException(responseData.Message);
                    //    //}

                    //    customerId = invoiceCredit.Credit?.CustomerId.ToString(); ;
                    //    customerName = invoiceCredit.Credit.CustomerName;
                    //    serviceId = invoiceCredit.Credit?.ServiceId;
                    //    serviceName = invoiceCredit.Credit?.ServiceName;
                    //}
                    //else if (input.Type == (int)RecognizeTypeEnums.Orderno)
                    //{
                    //    var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.Code);
                    //    if (saleOut == null)
                    //    {
                    //        throw new ApplicationException($"订单号：{input.Code}有误，请确认后重新操作！");
                    //    }
                    //    var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                    //    {
                    //        id = saleOut.CustomerId.ToString()
                    //    });
                    //    var recognizeReceiveAmount = await GetRecognizeReceiveAmount(input.Code, 1);
                    //    if (input.TotalAmount - recognizeReceiveAmount + currentValue < input.Value)
                    //    {
                    //        throw new ApplicationException($"操作失败：订单号{input.Code}超出可认款金额");
                    //    }
                    //    //if (customer.customerType1.Equals("0101") || customer.customerType1.Equals("0102") || customer.customerType1.Equals("0103") || customer.customerType1.Equals("0104"))
                    //    //{
                    //    //    throw new ApplicationException($"认款到订单的不能是医疗机构！");
                    //    //}
                    //    customerId = saleOut.CustomerId.ToString();
                    //    customerName = saleOut.CustomerName;
                    //}
                    //// 校验为不是第三方回款客户的id是否一致
                    //if (!input.IsReturnCustomer && customerId != input.CustomerId.ToString())
                    //{
                    //    throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                    //}
                    //var detail = item.RecognizeReceiveDetails.Where(t => t.Id == id).FirstOrDefault();
                    //if (detail != null)
                    //{
                    //    input.Adapt(detail);
                    //    detail.ServiceId = serviceId;
                    //    detail.ServiceName = serviceName;
                    //    detail.CustomerId = customerId;
                    //    detail.CustomerNme = customerName;
                    //    detail.Code = detail.Code.Trim();
                    //}
                    //else
                    //{
                    //    throw new ApplicationException($"认款明细不存在");
                    //}
                    //var otherQuery = _receiveDetailQueryService.GetIQueryable(p => p.RecognizeReceiveItem.ReceiveCode == item.ReceiveCode &&
                    //                                                               p.RecognizeReceiveItemId != item.Id &&
                    //                                                               p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled, new List<string>() { "RecognizeReceiveItem" });
                    //var otherDetails = await otherQuery.ToListAsync();
                    //item.UpdatedTime = DateTime.Now;
                    //detail.Value = input.Value.Value;
                    //item.Value = item.RecognizeReceiveDetails.Sum(t => t.Value);
                    //if (item.Type == "负数应收")
                    //{
                    //    if (Math.Abs(item.ReceiveValue) < item.Value + otherDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(t => t.Value))//总认款金额不能超过收款单的总金额
                    //    {
                    //        throw new ApplicationException($"认款金额不能大于收款金额");
                    //    }
                    //}
                    //else
                    //{
                    //    if (string.IsNullOrEmpty(item.RelateCode))
                    //    {
                    //        if (item.ReceiveValue < item.Value + otherDetails.Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel).Sum(t => t.Value))
                    //        {
                    //            throw new ApplicationException($"认款金额不能大于收款金额");
                    //        }
                    //    }
                    //    else
                    //    {
                    //        if (item.ReceiveValue < item.Value)
                    //        {
                    //            throw new ApplicationException($"认款金额不能大于收款金额");
                    //        }
                    //    }
                    //}
                    //await _itemRepository.UpdateAsync(item);

                    #endregion

                    throw new ApplicationException($"页面已无此功能，请勿非法操作");
                }
                //暂收款
                else if (input.ClassifyType == RecognizeReceiveClassifyEnum.Temp)
                {
                    if (input.Value < 0)
                    {
                        throw new ApplicationException($"不允许负数");
                    }

                    if (item.RecognizeReceiveTempDetails == null || !item.RecognizeReceiveTempDetails.Any())
                    {
                        throw new ApplicationException($"没有认款明细");
                    }

                    var detail = item.RecognizeReceiveTempDetails.Where(t => t.Id == id).FirstOrDefault();
                    if (detail != null)
                    {
                        var originValue = detail.Value;
                        var totalValue = item.RecognizeReceiveTempDetails.Sum(x => x.Value);
                        input.Adapt(detail);
                        if (totalValue - originValue + detail.Value > item.ReceiveValue)
                        {
                            throw new ApplicationException($"认款金额不能大于收款金额");
                        }
                    }
                    else
                    {
                        throw new ApplicationException($"认款明细不存在");
                    }

                    await _itemRepository.UpdateAsync(item);
                }

                var res = await _unitOfWork.CommitAsync();
                //更新项目名称和项目代码
                await UpdateRecognizeReceiveItemProjectNameByCode(input.RecognizeReceiveItemId);
                return res;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"更新失败：{ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 获取已认款金额（返回集合）
        /// </summary>
        /// <param name="codes">发票号/订单号集合</param>
        /// <param name="type">1：发票号；2：订单号；3：初始应收</param>
        /// <returns></returns>
        public async Task<List<RecognizeReceiveAmountDto>> GetRecognizeReceiveAmount(List<string?>? codes, int type)
        {
            // 返回明细集合
            var retList = new List<RecognizeReceiveAmountDto>();
            if (codes == null)
            {
                return retList;
            }

            var hashCodes = codes.ToHashSet();

            #region #95422-重新定义可认款金额计算方式

            if (type == 1)
            {
                //查询对应应收
                var invoiceCredits = await _db.InvoiceCredits.Include(x => x.Credit).Where(x =>
                    hashCodes.Contains(x.InvoiceNo) && x.Credit.Value > 0 &&
                    x.Credit.AbatedStatus != AbatedStatusEnum.Abated).AsNoTracking().ToListAsync();
                //获取应收id
                var creditIds = invoiceCredits.Select(x => x.CreditId).ToHashSet();
                //获取应收（排除负数应收）
                var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
                var creditCodes = credits.Select(c => c.BillCode).ToList();
                //获取冲销表的总额
                var abas = new List<AbatementPo>();
                //获取应收定损金额
                var loss = new List<LossRecognitionDetailPo>();
                if (creditCodes != null && creditCodes.Any())
                {
                    var abas1 = await _db.Abatements.Where(x => creditCodes.ToHashSet().Contains(x.DebtBillCode)).AsNoTracking().ToListAsync();
                    var abas2 = await _db.Abatements.Where(x => creditCodes.ToHashSet().Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
                    abas.AddRange(abas1);
                    abas.AddRange(abas2);
                    abas = abas.Distinct().ToList();
                    loss = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => x.LossRecognitionItem.Status != StatusEnum.Refuse && creditCodes.ToHashSet().Contains(x.BillCode)).AsNoTracking().ToListAsync();
                }

                //查询认款明细对应应收记录
                var rrdcsbyCredit = await _db.RecognizeReceiveDetailCredits
                    .Where(x => creditCodes != null && creditCodes.ToHashSet().Any(p => p == x.CreditCode))
                    .ToListAsync();
                //查询认款明细对应发票记录
                var rrdcsbyInvoice = await _db.RecognizeReceiveDetailCredits
                    .Where(x => hashCodes.Any(p => p == x.InvoiceNo)).ToListAsync();
                //循环精准填充已认款金额
                foreach (var code in codes)
                {
                    //box
                    var single = new RecognizeReceiveAmountDto();
                    single.Code = code;
                    //当前发票已认款金额
                    single.Amount = rrdcsbyInvoice.Where(x => x.InvoiceNo == code).Sum(x => x.CurrentValue);
                    //发票下应收可认款金额之和
                    decimal? creditSurplusTotalValue = 0M;
                    //查询当前发票对应应收id集合
                    var currentInvoiceCreditIds =
                        invoiceCredits.Where(x => x.InvoiceNo == code).Select(x => x.CreditId).ToList();
                    foreach (var currentInvoiceCreditId in currentInvoiceCreditIds)
                    {
                        //应收冲销金额
                        var cAbaValue = 0M;
                        //应收认款金额（冲销表重复记录）
                        var rAbaValue = 0M;
                        //应收定损金额
                        var lossValue = 0M;
                        var currentCredit = credits.FirstOrDefault(x => x.Id == currentInvoiceCreditId);
                        if (currentCredit != null)
                        {
                            cAbaValue = abas.Where(x =>
                                    x.DebtBillCode == currentCredit.BillCode ||
                                    x.CreditBillCode == currentCredit.BillCode)
                                .Sum(x => x.Value);
                            rAbaValue = abas.Where(x =>
                                    x.DebtBillCode == currentCredit.BillCode &&
                                    (x.CreditType == "receive" || x.CreditType == "credit") && x.DebtType == "credit")
                                .Sum(x => x.Value);
                            lossValue = loss.Where(x => x.BillCode == currentCredit.BillCode)
                                .Sum(x => x.BadAmount ?? x.LeftAmount);
                        }

                        //应收认款金额
                        var ccValue = rrdcsbyCredit.Where(x => x.CreditId == currentInvoiceCreditId)
                            .Sum(x => x.CurrentValue);
                        //应收金额
                        var crValue = credits.Where(x => x.Id == currentInvoiceCreditId).Sum(x => x.Value);
                        //当前发票对应应收占用金额
                        var currentCreditValue = invoiceCredits
                            .Where(x => x.CreditId == currentInvoiceCreditId && x.InvoiceNo == code)
                            .Sum(x => x.CreditAmount);
                        //减去发票对应应收的认款金额
                        var myselfCurrentValue = rrdcsbyInvoice
                            .Where(x => x.InvoiceNo == code && x.CreditId == currentInvoiceCreditId)
                            .Sum(x => x.CurrentValue);
                        //对比取小
                        creditSurplusTotalValue +=
                            currentCreditValue - myselfCurrentValue >
                            crValue - ccValue - (cAbaValue - rAbaValue) - lossValue
                                ? crValue - ccValue - (cAbaValue - rAbaValue) - lossValue
                                : currentCreditValue - myselfCurrentValue;
                    }

                    //应收金额-记录对应应收认款金额之和
                    single.CreditSurplusTotalValue = creditSurplusTotalValue;
                    //发票可认款金额 = 【发票金额-初始化金额-红冲金额-新表对应发票使用金额】 与 【应收金额-记录对应应收用认款金额之和】对比，取小
                    retList.Add(single);
                }
            }
            else if (type == 2)
            {
                //查询订单对应应收
                var credits = await _db.Credits.Where(x => hashCodes.Contains(x.OrderNo) || hashCodes.Contains(x.RelateCode)).AsNoTracking().ToListAsync();
                var creidtCodes = credits.Select(c => c.BillCode).ToHashSet();
                //获取冲销表的总额
                var abas = new List<AbatementPo>();
                //获取应收定损金额
                var loss = new List<LossRecognitionDetailPo>();
                if (creidtCodes != null && creidtCodes.Any())
                {
                    abas = await _db.Abatements.Where(x => creidtCodes.Contains(x.DebtBillCode) || creidtCodes.Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
                    loss = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => x.LossRecognitionItem.Status != StatusEnum.Refuse && creidtCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                }

                //查询订单对应应收Id
                var creditIds = credits.Select(x => x.Id).ToHashSet();
                //查询认款明细对应应收记录
                var rrdcsbyCredit = await _db.RecognizeReceiveDetailCredits
                    .Where(x => creditIds.Any(p => p == x.CreditId)).ToListAsync();
                //查询认款明细对应订单记录
                var rrdcsbyOrder = await _db.RecognizeReceiveDetailCredits.Where(x => hashCodes.Contains(x.OrderNo))
                    .ToListAsync();
                foreach (var code in codes)
                {
                    //box
                    var single = new RecognizeReceiveAmountDto();
                    single.Code = code;
                    //当前订单已认款金额
                    single.Amount = rrdcsbyOrder.Where(x => x.OrderNo == code).Sum(x => x.CurrentValue);
                    //当前订单对应应收（排除负数应收）
                    var currentCredits = credits.Where(x => x.Value > 0 && (x.OrderNo == code || x.RelateCode == code))
                        .ToList();
                    if (currentCredits == null || !currentCredits.Any())
                    {
                        //未生成应收的订单
                        single.CreditSurplusTotalValue = null;
                        retList.Add(single);
                        continue;
                    }

                    //当前订单对应应收id
                    var currentOrderCreditIds = currentCredits.Select(x => x.Id).ToList();
                    //查询应收存在认款金额记录之和
                    var currentCreditRecognizeReceiveValue = rrdcsbyCredit
                        .Where(x => currentOrderCreditIds.Any(p => p == x.CreditId)).Sum(x => x.CurrentValue);
                    //应收冲销金额
                    var cAbaValue = 0M;
                    //应收认款金额（冲销表重复记录）
                    var rAbaValue = 0M;
                    //应收定损金额
                    var lossValue = 0M;
                    var currentCreditCodes = currentCredits.Select(x => x.BillCode).ToHashSet();
                    if (currentCreditCodes != null && currentCreditCodes.Any())
                    {
                        cAbaValue = abas.Where(x => currentCreditCodes.Contains(x.DebtBillCode) || currentCreditCodes.Contains(x.CreditBillCode)).Sum(x => x.Value);
                        rAbaValue = abas.Where(x => currentCreditCodes.Contains(x.DebtBillCode) && (x.CreditType == "receive" || x.CreditType == "credit") && x.DebtType == "credit").Sum(x => x.Value);
                        lossValue = loss.Where(x => currentCreditCodes.Contains(x.BillCode)).Sum(x => x.BadAmount ?? x.LeftAmount);
                    }

                    //查询当前发票对应应收金额之和
                    var currentCreditValue = currentCredits.Sum(x => x.Value);
                    //应收金额-记录对应应收认款金额之和
                    single.CreditSurplusTotalValue = currentCreditValue - currentCreditRecognizeReceiveValue -
                                                     (cAbaValue - rAbaValue) - lossValue;
                    //订单可认款金额 = 订单总金额-记录对应应收认款总金额（未生成应收的订单预存一条无应收单号、id的数据,后续无需对比）和应收金额-记录对应应收用认款金额之和对比，取小
                    retList.Add(single);
                }
            }
            else if (type == 3)
            {
                //查询订单对应应收
                var credits = await _db.Credits.Where(x => hashCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                //获取冲销表的总额
                var abas = new List<AbatementPo>();
                //获取应收定损金额
                var loss = new List<LossRecognitionDetailPo>();
                if (codes != null && codes.Any())
                {
                    abas = await _db.Abatements
                        .Where(x => hashCodes.Contains(x.DebtBillCode) || hashCodes.Contains(x.CreditBillCode))
                        .AsNoTracking().ToListAsync();
                    loss = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x =>
                            x.LossRecognitionItem.Status != StatusEnum.Refuse && hashCodes.Contains(x.BillCode))
                        .AsNoTracking().ToListAsync();
                }

                //查询应收Id
                var creditIds = credits.Select(x => x.Id).ToHashSet();
                //查询认款明细对应应收记录
                var rrdcsbyCredit = await _db.RecognizeReceiveDetailCredits
                    .Where(x => creditIds.Any(p => p == x.CreditId)).ToListAsync();
                foreach (var code in codes)
                {
                    //box
                    var single = new RecognizeReceiveAmountDto();
                    single.Code = code;
                    //当前应收已认款金额
                    single.Amount = rrdcsbyCredit.Where(x => x.CreditCode == code).Sum(x => x.CurrentValue);
                    //获取当前应收
                    var credit = credits.FirstOrDefault(x => x.BillCode == code);
                    if (credit == null)
                    {
                        //未找到应收
                        single.CreditSurplusTotalValue = 0;
                        retList.Add(single);
                        continue;
                    }

                    //应收冲销金额
                    var cAbaValue = abas
                        .Where(x => x.DebtBillCode == credit.BillCode || x.CreditBillCode == credit.BillCode)
                        .Sum(x => x.Value);
                    //应收认款金额（冲销表重复记录）
                    var rAbaValue = abas.Where(x =>
                        x.DebtBillCode == credit.BillCode && (x.CreditType == "receive" || x.CreditType == "credit") &&
                        x.DebtType == "credit").Sum(x => x.Value);
                    //应收定损金额
                    var lossValue = loss.Where(x => x.BillCode == credit.BillCode)
                        .Sum(x => x.BadAmount ?? x.LeftAmount);
                    single.CreditCode = credit.BillCode;
                    single.CreditId = credit.Id;
                    single.CreditSurplusTotalValue = credit.Value - single.Amount - (cAbaValue - rAbaValue) - lossValue;
                    retList.Add(single);
                }
            }

            #endregion

            return retList;
        }

        /// <summary>
        /// 校验明细数据合法性
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData> VerifySingleData([FromBody] RecognizeReceiveDetailsInput input)
        {
            var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
            if (item == null)
            {
                return new BaseResponseData()
                {
                    Message = "未找到认款单据",
                    Code = CodeStatusEnum.ParamFailed
                };
            }

            if (item.Status == -1)
            {
                return new BaseResponseData()
                {
                    Message = "该状态不能添加明细",
                    Code = CodeStatusEnum.ParamFailed
                };
            }

            //var single = input.List.FirstOrDefault();
            if (input.List.Count == 0)
            {
                return new BaseResponseData()
                {
                    Message = "数据不存在",
                    Code = CodeStatusEnum.NotFound
                };
            }

            var codes = input.List.Select(x => x.BusinessId).ToList();
            var exists = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem).Where(x =>
                codes.Contains(x.Code) && x.RecognizeReceiveItemId == input.RecognizeReceiveItemId).ToListAsync();
            if (exists != null && exists.Any())
            {
                string existCodes = string.Join(",", exists.Select(x => x.Code).ToList());
                return new BaseResponseData()
                {
                    Message = $"不能重复关联，code集合为{existCodes}",
                    Code = CodeStatusEnum.NotFound
                };
            }

            //货款
            if (input.ClassifyType == RecognizeReceiveClassifyEnum.Goods)
            {
                // 表单总认款金额
                decimal totalAmount = 0;
                var list = await _db.RecognizeReceiveDetails
                    .Where(t => t.RecognizeReceiveItemId == input.RecognizeReceiveItemId).ToListAsync();
                foreach (var detail in input.List)
                {
                    var model = list.FirstOrDefault(t => t.Code == detail.BusinessId);
                    // 认款类型
                    var rkType = list.Count > 0 ? list.FirstOrDefault().Type : input.List.FirstOrDefault().Type;
                    if (rkType != detail.Type)
                    {
                        return new BaseResponseData()
                        {
                            Message = $"同一个认款单中，认款类型，只能有一种！",
                            Code = CodeStatusEnum.ParamFailed
                        };
                    }

                    if (detail.Amount <= 0)
                    {
                        return new BaseResponseData()
                        {
                            Message = $"认款金额必须大于0！",
                            Code = CodeStatusEnum.ParamFailed
                        };
                    }

                    if (detail.Type == (int)RecognizeTypeEnums.Credit)
                    {
                        var credit =
                            await _creditQueryService.FirstOrDefaultAsync(p => p.BillCode == detail.BusinessId);
                        if (credit == null)
                        {
                            return new BaseResponseData()
                            {
                                Message = $"应收单号：{detail.BusinessId}有误，请确认后重新操作！",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }
                    }
                    else if (detail.Type == (int)RecognizeTypeEnums.Invoice)
                    {
                        //#122798 【小】收款认领到发票，发票配送状态控制，必须为【已完成】或【无需配送】
                        var logInput = new InvoiceShipmentInput()
                        {
                            InvoiceNo = detail.BusinessId
                        };
                        var logRet = await _logisticsApiClient.getInvoiceShipmentExport(logInput);
                        if (logRet.Code == CodeStatusEnum.Success)
                        {
                            if (logRet.Data != null && logRet.Data.Any())
                            {
                                if (logRet.Data[0].StatusDesc != "已完成" && logRet.Data[0].StatusDesc != "无需配送")
                                {
                                    return new BaseResponseData()
                                    {
                                        Message =
                                            $"发票号：{detail.BusinessId}配送状态不允许认款，当前配送状态为{logRet.Data[0].StatusDesc}！",
                                        Code = CodeStatusEnum.ParamFailed
                                    };
                                }
                            }
                            else
                            {
                                return new BaseResponseData()
                                {
                                    Message = $"发票号：{detail.BusinessId}未获取到配送状态！",
                                    Code = CodeStatusEnum.ParamFailed
                                };
                            }
                        }
                        else
                        {
                            return new BaseResponseData()
                            {
                                Message = $"发票号：{detail.BusinessId}获取配送状态失败：{logRet.Message}！",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }

                        var recognizeReceiveAmounts = await GetRecognizeReceiveAmount(codes, 1);
                        var invoiceCredit =
                            await _invoiceCreditQueryService.FirstOrDefaultAsync(p => p.InvoiceNo == detail.BusinessId,
                                new List<string> { "Credit" });
                        if (invoiceCredit == null)
                        {
                            return new BaseResponseData()
                            {
                                Message = $"发票号：{detail.BusinessId}有误，请确认后重新操作！",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }

                        var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == detail.BusinessId);
                        if (invoice == null)
                        {
                            return new BaseResponseData()
                            {
                                Message = $"未找到发票号：{detail.BusinessId}数据，请确认后重新操作！",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }

                        var recognizeReceiveAmount = recognizeReceiveAmounts
                            .FirstOrDefault(x => x.Code == invoice.InvoiceNo)?.Amount;
                        // 初始化发票则加入计算
                        if (invoice.IsInit.HasValue && invoice.IsInit.Value)
                        {
                            recognizeReceiveAmount += Math.Abs(invoice.ReceiveAmount ?? 0);
                        }

                        if (invoice.InvoiceAmount - Math.Abs(invoice.RedAmount ?? 0) - recognizeReceiveAmount <
                            detail.Amount)
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败：发票号{invoice.InvoiceNo}超出可认款金额");
                        }
                    }
                    else if (detail.Type == (int)RecognizeTypeEnums.Orderno)
                    {
                        if (detail.CanAmount < detail.Amount)
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败：订单号{detail.BusinessId}超出可认款金额");
                        }
                    }

                    if (model != null)
                    {
                        //存在数据则取变化金额
                        totalAmount += (detail.Amount.HasValue ? detail.Amount.Value : 0) - model.Value;
                    }
                    else
                    {
                        totalAmount += detail.Amount.HasValue ? detail.Amount.Value : 0;
                    }
                }


                var otherQuery = _receiveDetailQueryService.GetIQueryable(p =>
                        p.RecognizeReceiveItem.ReceiveCode == item.ReceiveCode &&
                        p.RecognizeReceiveItemId != item.Id &&
                        p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled,
                    new List<string>() { "RecognizeReceiveItem" });
                var otherDetails = await otherQuery.ToListAsync();
                item.UpdatedTime = DateTime.Now;
                item.Value = item.RecognizeReceiveDetails.Sum(t => t.Value) + totalAmount;
                if (item.Type == "负数应收")
                {
                    if (Math.Abs(item.ReceiveValue) < item.Value + otherDetails
                            .Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel)
                            .Sum(t => t.Value)) //总认款金额不能超过收款单的总金额
                    {
                        return new BaseResponseData()
                        {
                            Message = $"认款金额不能大于收款金额",
                            Code = CodeStatusEnum.ParamFailed
                        };
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(item.RelateCode))
                    {
                        if (item.ReceiveValue < item.Value + otherDetails
                                .Where(x => x.Status != RecognizeReceiveDetailEnum.Cancel)
                                .Sum(t => t.Value)) //总认款金额不能超过收款单的总金额
                        {
                            return new BaseResponseData()
                            {
                                Message = $"认款金额不能大于收款金额",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }
                    }
                    else
                    {
                        if (item.ReceiveValue < item.Value)
                        {
                            return new BaseResponseData()
                            {
                                Message = $"认款金额不能大于收款金额",
                                Code = CodeStatusEnum.ParamFailed
                            };
                        }
                    }
                }
            }

            return new BaseResponseData()
            {
                Message = $"校验通过",
                Code = CodeStatusEnum.Success
            };
        }

        /// <summary>
        /// 批量保存认款单明细数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SaveBatchDetailAsync([FromBody] RecognizeReceiveDetailsInput input)
        {
            try
            {
                var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
                if (item == null)
                {
                    return BaseResponseData<int>.Failed(500, $"未找到认款单据");
                }

                if (item.Status != 0)
                {
                    return BaseResponseData<int>.Failed(500, $"该状态不能添加明细");
                }

                if (input.List == null || !input.List.Any())
                {
                    return BaseResponseData<int>.Failed(500, $"请勾选需要添加的明细数据");
                }

                var check = await VerifySingleData(input);
                if (check.Code != CodeStatusEnum.Success)
                {
                    return BaseResponseData<int>.Failed(500, check.Message ??= "校验失败");
                }

                // 暂收款转货款金额校验
                var receives = await _db.RecognizeReceiveItems
                    .Where(x => item.ReceiveCode == x.ReceiveCode &&
                                x.Status != RecognizeReceiveItemStatusEnum.Canceled)
                    .Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking()
                    .ToListAsync();
                var existValue = receives.Where(x => x.CustomerId == item.CustomerId && x.RelateCode == item.RelateCode)
                    .Sum(x => x.RecognizeReceiveDetails.Sum(d => d.Value));
                // #117480 【小】负数应收认款时，需要判断负数应收和认款应收的业务单元是否一致
                if (item.Type == "负数应收")
                {
                    var negativeCredit = await _db.Credits.FirstOrDefaultAsync(x => x.BillCode == item.ReceiveCode);
                    if (negativeCredit == null)
                    {
                        return BaseResponseData<int>.Failed(500, $"负数应收不存在或已被删除");
                    }

                    if (negativeCredit.ServiceId.HasValue)
                    {
                        // 获取认款明细对应所有应收的业务单元是否匹配
                        var allRecognizeReceiveDetailCredits = input.List
                            .Where(x => x.CreditInfo != null && x.CreditInfo.Any()).SelectMany(item => item.CreditInfo)
                            .ToList();
                        if (allRecognizeReceiveDetailCredits.Any())
                        {
                            var hasDifferentServiceIdList = allRecognizeReceiveDetailCredits
                                .Where(x => x.ServiceId != negativeCredit.ServiceId).ToList();
                            if (hasDifferentServiceIdList != null && hasDifferentServiceIdList.Any())
                            {
                                return BaseResponseData<int>.Failed(500,
                                    $"负数应收业务单元与选择的应收{string.Join(",", hasDifferentServiceIdList.Select(x => x.BillCode).ToList())}的业务单元不同");
                            }
                        }
                        else
                        {
                            //获取认款明细
                            if (input.List[0].Type == (int)RecognizeTypeEnums.Credit)
                            {
                                var allCreditCodes = input.List.Select(item => item.BusinessId).ToList();
                                var allCredits = await _db.Credits.Where(x => allCreditCodes.Contains(x.BillCode))
                                    .AsNoTracking().ToListAsync();
                                if (allCredits != null && allCredits.Any())
                                {
                                    var hasDifferentServiceIdList = allCredits
                                        .Where(x => x.ServiceId != negativeCredit.ServiceId).ToList();
                                    if (hasDifferentServiceIdList != null && hasDifferentServiceIdList.Any())
                                    {
                                        return BaseResponseData<int>.Failed(500,
                                            $"负数应收业务单元与选择的应收{string.Join(",", hasDifferentServiceIdList.Select(x => x.BillCode).ToList())}的业务单元不同");
                                    }
                                }
                            }
                            //else if (input.List[0].Type == (int)RecognizeTypeEnums.Orderno)
                            //{
                            //    return BaseResponseData<int>.Failed(500, $"负数应收无法用于抵扣预收订单预收款");
                            //}
                            else if (input.List[0].Type == (int)RecognizeTypeEnums.Invoice)
                            {
                                return BaseResponseData<int>.Failed(500, $"未获取到发票关联的应收数据");
                            }
                        }
                    }
                }

                //货款
                if (input.ClassifyType == RecognizeReceiveClassifyEnum.Goods)
                {
                    //获取认款明细
                    var details = await _db.RecognizeReceiveDetails
                        .Where(t => t.RecognizeReceiveItemId == input.RecognizeReceiveItemId).ToListAsync();
                    var codes = input.List.Select(x => x.BusinessId).ToHashSet();
                    //获取应收信息
                    var credits = await _db.Credits.Where(x => codes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                    var invoiceCredits = new List<InvoiceCreditPo>();
                    var creditOrders = new List<CreditPo>();
                    if (input.List[0].Type == (int)RecognizeTypeEnums.Invoice)
                    {
                        invoiceCredits = await _db.InvoiceCredits.Include(x => x.Credit)
                            .Where(x => codes.Contains(x.InvoiceNo) && x.Credit.Value > 0 &&
                                        x.Credit.AbatedStatus != AbatedStatusEnum.Abated).AsNoTracking().ToListAsync();
                        var creditInvoices = invoiceCredits.Select(x => x.Credit).ToList();
                        var verifyCreditsData = new List<DTOs.Recognize.CreditInfo>();
                        foreach (var rrd in input.List)
                        {
                            if (rrd.CreditInfo == null || !rrd.CreditInfo.Any())
                            {
                                return BaseResponseData<int>.Failed(500, "请至少勾选一条当前选中行下的应收数据");
                            }

                            verifyCreditsData.AddRange(rrd.CreditInfo);
                        }

                        var creditCodes = verifyCreditsData.Select(x => x.BillCode).ToList();
                        //获取发票对应应收可认款金额
                        var recognizeReceiveAmounts = await GetRecognizeReceiveAmount(creditCodes, 3);
                        if (recognizeReceiveAmounts != null && recognizeReceiveAmounts.Any())
                        {
                            string errMsg = string.Empty;
                            foreach (var rra in recognizeReceiveAmounts)
                            {
                                var currentCreditRecognizeValue = verifyCreditsData
                                    .Where(x => x.BillCode == rra.CreditCode).Sum(x => x.CurrentValue);
                                if (rra.CreditSurplusTotalValue < currentCreditRecognizeValue)
                                {
                                    errMsg += string.Format($"[{rra.CreditCode}]");
                                }
                            }

                            if (!string.IsNullOrEmpty(errMsg))
                            {
                                return BaseResponseData<int>.Failed(500, $"添加失败，原因：已勾选对应应收单号：{errMsg}可认款金额不足");
                            }
                        }
                    }

                    if (input.List[0].Type == (int)RecognizeTypeEnums.Orderno)
                    {
                        creditOrders = await _db.Credits
                            .Where(x => codes.Contains(x.OrderNo) || codes.Contains(x.RelateCode)).AsNoTracking()
                            .ToListAsync();
                    }

                    foreach (var detail in input.List)
                    {
                        if (!detail.Amount.HasValue)
                        {
                            return BaseResponseData<int>.Failed(500, "请填写认款金额");
                        }

                        var model = details.FirstOrDefault(t => t.Code == detail.BusinessId);

                        if (item.Classify == RecognizeReceiveClassifyEnum.Goods &&
                            !string.IsNullOrEmpty(item.RelateCode))
                        {
                            // 暂收款转货款
                            if (item.ReceiveValue - existValue < detail.Amount)
                            {
                                return BaseResponseData<int>.Failed(500,
                                    $"认款金额{detail.Amount}大于认款单剩余可认款金额{item.ReceiveValue - existValue}");
                            }
                        }

                        if (detail.Type == (int)RecognizeTypeEnums.Credit)
                        {
                            var single = credits.FirstOrDefault(p => p.BillCode == detail.BusinessId);
                            if (model != null)
                            {
                                //存在修改
                                model.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                model.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                model.Note = detail.remark;
                                model.IsSkip = detail.IsSkip.HasValue ? detail.IsSkip.Value : false;
                                model.UpdatedTime = DateTime.Now;
                                model.ServiceId = single.ServiceId;
                                model.ServiceName = single.ServiceName;
                                model.UpdatedBy = input.CreatedBy;
                                _db.RecognizeReceiveDetails.Update(model);

                                //覆盖
                                var existCreditInfos = await _db.RecognizeReceiveDetailCredits
                                    .Where(x => x.RecognizeReceiveDetailId == model.Id).ToListAsync();
                                if (existCreditInfos != null && existCreditInfos.Any())
                                {
                                    _db.RecognizeReceiveDetailCredits.RemoveRange(existCreditInfos);
                                }

                                //初始应收需要预留一条数据
                                var rrcd = new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                {
                                    RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                    RecognizeReceiveDetailId = model.Id,
                                    CreditCode = single.BillCode,
                                    CreditId = single.Id,
                                    CurrentValue = detail.Amount.HasValue ? detail.Amount.Value : 0,
                                    CreatedBy = input.CreatedBy ??= "none"
                                };
                                _db.RecognizeReceiveDetailCredits.Add(rrcd);
                            }
                            else
                            {
                                //不存在新增
                                var entity = detail.Adapt<RecognizeReceiveDetailPo>();
                                entity.Id = Guid.NewGuid();
                                entity.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                                entity.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                entity.Type = input.Type.HasValue ? input.Type.Value : 0;
                                entity.CreatedBy = input.CreatedBy;
                                entity.Code = detail.BusinessId;
                                entity.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                entity.CustomerId = single != null ? single.CustomerId.ToString() : string.Empty;
                                entity.CustomerNme = single != null ? single.CustomerName.ToString() : string.Empty;
                                entity.ServiceId = single.ServiceId;
                                entity.ServiceName = single.ServiceName;
                                entity.HospitalId = detail.HospitalId; //终端客户id
                                entity.HospitalName = detail.HospitalName; //终端客户
                                entity.Note = detail.remark;
                                entity.Status = RecognizeReceiveDetailEnum.Normal;
                                entity.CreatedTime = DateTime.Now;
                                await _db.RecognizeReceiveDetails.AddAsync(entity);
                                //初始应收需要预留一条数据
                                var rrcd = new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                {
                                    RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                    RecognizeReceiveDetailId = entity.Id,
                                    CreditCode = single.BillCode,
                                    CreditId = single.Id,
                                    CurrentValue = detail.Amount.HasValue ? detail.Amount.Value : 0,
                                    CreatedBy = input.CreatedBy ??= "none"
                                };
                                _db.RecognizeReceiveDetailCredits.Add(rrcd);
                            }
                        }
                        else if (detail.Type == (int)RecognizeTypeEnums.Invoice)
                        {
                            var currentInvoiceCredits =
                                invoiceCredits.Where(x => x.InvoiceNo == detail.BusinessId).ToList();
                            if (currentInvoiceCredits.Count > 0 &&
                                (detail.CreditInfo == null || !detail.CreditInfo.Any()))
                            {
                                return BaseResponseData<int>.Failed(500, "请至少勾选一条当前选中行下的应收数据");
                            }

                            if (model != null)
                            {
                                //存在修改
                                model.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                model.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                model.IsSkip = detail.IsSkip.HasValue ? detail.IsSkip.Value : false;
                                model.Note = detail.remark;
                                model.UpdatedTime = DateTime.Now;
                                model.UpdatedBy = input.CreatedBy;
                                _db.RecognizeReceiveDetails.Update(model);

                                //对应应收表关系覆盖
                                if (detail.CreditInfo != null && detail.CreditInfo.Any())
                                {
                                    var existCreditInfos = await _db.RecognizeReceiveDetailCredits
                                        .Where(x => x.RecognizeReceiveDetailId == model.Id).ToListAsync();
                                    if (existCreditInfos != null && existCreditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.RemoveRange(existCreditInfos);
                                    }

                                    var creditInfos = new List<Finance.Data.Models.RecognizeReceiveDetailCreditPo>();
                                    foreach (var creditInfo in detail.CreditInfo)
                                    {
                                        if (creditInfo.CurrentValue == 0)
                                        {
                                            continue;
                                        }

                                        creditInfos.Add(new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                        {
                                            RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                            RecognizeReceiveDetailId = model.Id,
                                            InvoiceNo = detail.BusinessId,
                                            InvoiceId = detail.Id,
                                            CreditCode = !string.IsNullOrEmpty(creditInfo.BillCode)
                                                ? creditInfo.BillCode
                                                : string.Empty,
                                            CreditId = creditInfo.CreditId.HasValue
                                                ? creditInfo.CreditId.Value
                                                : Guid.Empty,
                                            CurrentValue = creditInfo.CurrentValue,
                                            CreatedBy = input.CreatedBy ??= "none"
                                        });
                                    }

                                    if (creditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.AddRange(creditInfos);
                                    }
                                }
                            }
                            else
                            {
                                var single = await _invoiceCreditQueryService.FirstOrDefaultAsync(
                                    p => p.InvoiceNo == detail.BusinessId, new List<string> { "Credit" });
                                //不存在新增
                                var entity = detail.Adapt<RecognizeReceiveDetailPo>();
                                entity.Id = Guid.NewGuid();
                                entity.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                                entity.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                entity.Type = input.Type.HasValue ? input.Type.Value : 0;
                                entity.CreatedBy = input.CreatedBy;
                                entity.Code = detail.BusinessId;
                                entity.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                entity.CustomerId = single.Credit?.CustomerId.ToString();
                                entity.CustomerNme = single.Credit?.CustomerName.ToString();
                                entity.ServiceId = single.Credit?.ServiceId;
                                entity.ServiceName = single.Credit?.ServiceName;
                                entity.HospitalId = detail.HospitalId; //终端客户id
                                entity.HospitalName = detail.HospitalName; //终端客户
                                entity.Note = detail.remark;
                                entity.Status = RecognizeReceiveDetailEnum.Normal;
                                entity.CreatedTime = DateTime.Now;
                                await _db.RecognizeReceiveDetails.AddAsync(entity);

                                //对应应收表关系新增
                                if (detail.CreditInfo != null && detail.CreditInfo.Any())
                                {
                                    var creditInfos = new List<Finance.Data.Models.RecognizeReceiveDetailCreditPo>();
                                    foreach (var creditInfo in detail.CreditInfo)
                                    {
                                        if (creditInfo.CurrentValue == 0)
                                        {
                                            continue;
                                        }

                                        creditInfos.Add(new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                        {
                                            RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                            RecognizeReceiveDetailId = entity.Id,
                                            InvoiceNo = detail.BusinessId,
                                            InvoiceId = detail.Id,
                                            CreditCode = !string.IsNullOrEmpty(creditInfo.BillCode)
                                                ? creditInfo.BillCode
                                                : string.Empty,
                                            CreditId = creditInfo.CreditId.HasValue
                                                ? creditInfo.CreditId.Value
                                                : Guid.Empty,
                                            CurrentValue = creditInfo.CurrentValue,
                                            CreatedBy = input.CreatedBy ??= "none"
                                        });
                                    }

                                    if (creditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.AddRange(creditInfos);
                                    }
                                }
                                else
                                {
                                    //预收款订单（未生成应收订单）需要预留一条数据
                                    var rrcd = new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                    {
                                        RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                        RecognizeReceiveDetailId = entity.Id,
                                        OrderId = detail.Id,
                                        OrderNo = detail.BusinessId,
                                        CreditCode = string.Empty,
                                        CreditId = null,
                                        CurrentValue = detail.Amount.HasValue ? detail.Amount.Value : 0,
                                        CreatedBy = input.CreatedBy ??= "none"
                                    };
                                    _db.RecognizeReceiveDetailCredits.Add(rrcd);
                                }
                            }
                        }
                        else if (detail.Type == (int)RecognizeTypeEnums.Orderno)
                        {
                            var currentCreditOrders = creditOrders.Where(x =>
                                x.OrderNo == detail.BusinessId || x.RelateCode == detail.BusinessId).ToList();
                            if (currentCreditOrders.Count > 0 &&
                                (detail.CreditInfo == null || !detail.CreditInfo.Any()))
                            {
                                return BaseResponseData<int>.Failed(500, "请至少勾选一条当前选中行下的应收数据");
                            }

                            if (model != null)
                            {
                                //存在修改
                                model.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                model.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                model.IsSkip = detail.IsSkip.HasValue ? detail.IsSkip.Value : false;
                                model.Note = detail.remark;
                                model.UpdatedTime = DateTime.Now;
                                model.Status = RecognizeReceiveDetailEnum.Normal;
                                model.UpdatedBy = input.CreatedBy;
                                _db.RecognizeReceiveDetails.Update(model);

                                //对应应收表关系覆盖
                                if (detail.CreditInfo != null && detail.CreditInfo.Any())
                                {
                                    var existCreditInfos = await _db.RecognizeReceiveDetailCredits
                                        .Where(x => x.RecognizeReceiveDetailId == model.Id).ToListAsync();
                                    if (existCreditInfos != null && existCreditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.RemoveRange(existCreditInfos);
                                    }

                                    var creditInfos = new List<Finance.Data.Models.RecognizeReceiveDetailCreditPo>();
                                    foreach (var creditInfo in detail.CreditInfo)
                                    {
                                        if (creditInfo.CurrentValue == 0)
                                        {
                                            continue;
                                        }

                                        creditInfos.Add(new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                        {
                                            RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                            RecognizeReceiveDetailId = model.Id,
                                            OrderId = detail.Id,
                                            OrderNo = detail.BusinessId,
                                            CreditCode = !string.IsNullOrEmpty(creditInfo.BillCode)
                                                ? creditInfo.BillCode
                                                : string.Empty,
                                            CreditId = creditInfo.CreditId.HasValue
                                                ? creditInfo.CreditId.Value
                                                : Guid.Empty,
                                            CurrentValue = creditInfo.CurrentValue,
                                            CreatedBy = input.CreatedBy ??= "none"
                                        });
                                    }

                                    if (creditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.AddRange(creditInfos);
                                    }
                                }
                                else
                                {
                                    //预收款订单（未生成应收订单）需要预留一条数据
                                    var rrcd = new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                    {
                                        RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                        RecognizeReceiveDetailId = model.Id,
                                        OrderId = detail.Id,
                                        OrderNo = detail.BusinessId,
                                        CreditCode = string.Empty,
                                        CreditId = null,
                                        CurrentValue = detail.Amount.HasValue ? detail.Amount.Value : 0,
                                        CreatedBy = input.CreatedBy ??= "none"
                                    };
                                    _db.RecognizeReceiveDetailCredits.Add(rrcd);
                                }
                            }
                            else
                            {
                                //不存在新增
                                var entity = detail.Adapt<RecognizeReceiveDetailPo>();
                                entity.Id = Guid.NewGuid();
                                entity.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                                entity.Value = detail.Amount.HasValue ? detail.Amount.Value : 0;
                                entity.Type = input.Type.HasValue ? input.Type.Value : 0;
                                entity.CreatedBy = input.CreatedBy;
                                entity.Code = detail.BusinessId;
                                entity.RecognizeDate = DateTime.UtcNow.AddHours(8);
                                entity.CustomerId = detail.CustomerId.HasValue
                                    ? detail.CustomerId.ToString()
                                    : string.Empty;
                                entity.CustomerNme = !string.IsNullOrEmpty(detail.CustomerName)
                                    ? detail.CustomerName.ToString()
                                    : string.Empty;
                                entity.HospitalId = detail.HospitalId; //终端客户id
                                entity.HospitalName = detail.HospitalName; //终端客户
                                entity.Note = detail.remark;
                                entity.Status = RecognizeReceiveDetailEnum.Normal;
                                entity.CreatedTime = DateTime.Now;
                                await _db.RecognizeReceiveDetails.AddAsync(entity);

                                //对应应收表关系新增
                                if (detail.CreditInfo != null && detail.CreditInfo.Any())
                                {
                                    var creditInfos = new List<Finance.Data.Models.RecognizeReceiveDetailCreditPo>();
                                    foreach (var creditInfo in detail.CreditInfo)
                                    {
                                        if (creditInfo.CurrentValue == 0)
                                        {
                                            continue;
                                        }

                                        creditInfos.Add(new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                        {
                                            RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                            RecognizeReceiveDetailId = entity.Id,
                                            OrderId = detail.Id,
                                            OrderNo = detail.BusinessId,
                                            CreditCode = !string.IsNullOrEmpty(creditInfo.BillCode)
                                                ? creditInfo.BillCode
                                                : string.Empty,
                                            CreditId = creditInfo.CreditId.HasValue
                                                ? creditInfo.CreditId.Value
                                                : Guid.Empty,
                                            CurrentValue = creditInfo.CurrentValue,
                                            CreatedBy = input.CreatedBy ??= "none"
                                        });
                                    }

                                    if (creditInfos.Any())
                                    {
                                        _db.RecognizeReceiveDetailCredits.AddRange(creditInfos);
                                    }
                                }
                                else
                                {
                                    //预收款订单（未生成应收订单）需要预留一条数据
                                    var rrcd = new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                    {
                                        RecognizeReceiveItemId = input.RecognizeReceiveItemId,
                                        RecognizeReceiveDetailId = entity.Id,
                                        OrderId = detail.Id,
                                        OrderNo = detail.BusinessId,
                                        CreditCode = string.Empty,
                                        CreditId = null,
                                        CurrentValue = detail.Amount.HasValue ? detail.Amount.Value : 0,
                                        CreatedBy = input.CreatedBy ??= "none"
                                    };
                                    _db.RecognizeReceiveDetailCredits.Add(rrcd);
                                }
                            }
                        }
                    }
                }

                var res = await _unitOfWork.CommitAsync();
                //更新项目名称和项目代码
                await UpdateRecognizeReceiveItemProjectNameByCode(input.RecognizeReceiveItemId);
                return BaseResponseData<int>.Success("操作成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 根据认款单Id更新项目名称和项目编码
        /// </summary>
        /// <param name="RecognizeReceiveItemId">认款单id</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        private async Task<int> UpdateRecognizeReceiveItemProjectNameByCode(Guid RecognizeReceiveItemId)
        {
            try
            {
                var item = await _db.RecognizeReceiveItems.Where(p => p.Id == RecognizeReceiveItemId)
                    .FirstOrDefaultAsync();
                if (item == null)
                {
                    throw new ApplicationException($"未找到认款单据");
                }

                var query = _db.RecognizeReceiveDetails.Where(t => t.RecognizeReceiveItemId == RecognizeReceiveItemId)
                    .Select(t => t.Adapt<RecognizeReceiveDetailOutput>()).AsNoTracking();
                var listDetails = await query.ToListAsync();
                var listCredits = new List<CreditPo>();
                foreach (var itemDetails in listDetails) //货款
                {
                    if (itemDetails.Type == (int)RecognizeTypeEnums.Invoice) //发票
                    {
                        var credits1 = await (from ic in _db.InvoiceCredits
                                              join c in _db.Credits
                                                  on ic.CreditId equals c.Id
                                              where itemDetails.Code == ic.InvoiceNo
                                              select c).Distinct().ToListAsync();
                        listCredits.AddRange(credits1);
                    }
                    else if (itemDetails.Type == (int)RecognizeTypeEnums.Orderno) //订单
                    {
                        //var credits2 = _db.Credits.Where(p => p.OrderNo == itemDetails.Code).AsNoTracking();
                        PageQueryForFinancesInput queryInput = new PageQueryForFinancesInput()
                        {
                            UserId = _appServiceContextAccessor.Get().UserId,
                            BillCode = itemDetails.Code,
                            StartTime = DateTime.UtcNow.AddYears(-1),
                            EndTime = DateTime.UtcNow.AddDays(1),
                            PageSize = int.MaxValue,
                            CompanyId = Guid.Parse(item.CompanyId),
                            BusinessDeptId = item.BusinessDepId
                        };
                        var output = await _sellApiClient.PageQueryForFinancesAsync(queryInput);
                        foreach (var itemPut in output.List)
                        {
                            if (itemPut.ProjectInfos != null && itemPut.ProjectInfos.Count != 0)
                            {
                                listCredits.Add(new CreditPo
                                {
                                    ProjectCode = string.Join(',',
                                        itemPut.ProjectInfos.Select(p => p.ProjectCode).Distinct().ToArray()),
                                    ProjectName = string.Join(',',
                                        itemPut.ProjectInfos.Select(p => p.ProjectName).Distinct().ToArray())
                                });
                            }
                        }
                    }
                    else if (itemDetails.Type == (int)RecognizeTypeEnums.Credit) //应收
                    {
                        var credits3 = _db.Credits.Where(p => p.BillCode == itemDetails.Code).AsNoTracking();
                        listCredits.AddRange(credits3);
                    }
                }

                var listTempDetails = await _db.RecognizeReceiveTempDetails
                    .Where(t => t.RecognizeReceiveItemId == RecognizeReceiveItemId).AsNoTracking().ToListAsync();
                foreach (var itemTempDetail in listTempDetails) //暂收款
                {
                    listCredits.Add(new CreditPo
                    {
                        ProjectCode = itemTempDetail.ProjectCode,
                        ProjectId = itemTempDetail.ProjectId,
                        ProjectName = itemTempDetail.ProjectName
                    });
                }

                item.ProjectName = ""; //先置空
                item.ProjectCode = "";
                if (listCredits != null && listCredits.Count > 0)
                {
                    item.ProjectName = string.Join(',', listCredits.Select(p => p.ProjectName).Distinct().ToArray());
                    item.ProjectCode = string.Join(',', listCredits.Select(p => p.ProjectCode).Distinct().ToArray());
                }

                var res = await _unitOfWork.CommitAsync();
                return res;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("UpdateRecognizeReceiveItemProjectNameByCode", $"更新失败{ex.Message}",
                    "根据认款单Id更新项目名称和项目编码", LogLevelEnum.Error);
                throw new ApplicationException($"更新失败，{ex.Message}");
            }
        }

        /// <summary>
        /// 导入发票（#77023 改为导入数据）
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<int> Import(IFormFile file, Guid id, Guid customerId, bool isReturnCustomer,
            string? currentUser = null)
        {
            try
            {
                throw new ApplicationException($"请勿非法操作");

                #region 页面已隐藏入口

                //var model = await _db.RecognizeReceiveItems.FirstOrDefaultAsync(x => x.Id == id);
                //if (model == null) { throw new ApplicationException($"未找到认款单据"); }
                //if (model.Status == RecognizeReceiveItemStatusEnum.Canceled)
                //{
                //    throw new ApplicationException($"该状态不能导入");
                //}
                //model.UpdatedTime = DateTime.Now;

                //Stream stream = file.OpenReadStream();
                //using (ExcelPackage package = new ExcelPackage(stream))
                //{
                //    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                //    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];

                //    //获取表格的列数和行数
                //    int rowCount = worksheet.Dimension.Rows;
                //    if (rowCount <= 1)
                //    {
                //        throw new ApplicationException($"模板内不存在要导入数据");
                //    }
                //    int colCount = worksheet.Dimension.Columns;
                //    var list = new List<RecognizeReceiveDetailPo>();
                //    for (int row = 2; row <= rowCount; row++)
                //    {
                //        if (worksheet.Cells[row, 1].Value == null)
                //        {
                //            break;
                //        }

                //        string code = worksheet.Cells[row, 1].Value.ToString();
                //        code = code.TrimStart().TrimEnd();
                //        //判断类型
                //        if (worksheet.Cells[row, 1].Value == null)
                //        {
                //            throw new ApplicationException($"模板内存在类型为空的数据");
                //        }

                //        // 具体的获取数据                        
                //        var entity = new RecognizeReceiveDetailPo
                //        {
                //            RecognizeReceiveItemId = id,
                //            RecognizeDate = DateTime.Now,
                //            Code = code,
                //            Value = Convert.ToDecimal(worksheet.Cells[row, 2].Value.ToString()),
                //            Note = worksheet.Cells[row, 4].Value != null ? worksheet.Cells[row, 4].Value.ToString() : "",
                //            IsSkip = (worksheet.Cells[row, 5].Value != null && worksheet.Cells[row, 5].Value.ToString() == "是") ? true : false,
                //            Classify = worksheet.Cells[row, 6].Value != null ? EnumHelper.GetEnumByValue<RecognizeReceiveDetailClassifyEnum>(worksheet.Cells[row, 6].Value.ToString()) : RecognizeReceiveDetailClassifyEnum.Goods,
                //            CreatedBy = currentUser ?? "none",
                //            CreatedTime = DateTime.Now,
                //            Type = (int)EnumHelper.GetEnumByValue<RecognizeTypeEnums>(worksheet.Cells[row, 3].Value.ToString()),
                //            //CustomerId = single.Credit?.CustomerId.ToString(),
                //            //CustomerNme = single.Credit?.CustomerName.ToString(),
                //            //ServiceId = single.Credit?.ServiceId,
                //            //ServiceName = single.Credit?.ServiceName,
                //            //HospitalId = single.Credit?.HospitalId,//终端客户id
                //            //HospitalName = single.Credit?.HospitalName
                //        };
                //        entity.RecognizeDate = DateTime.Now;

                //        //entity.Value = Math.Abs(entity.Value);
                //        // 金额为0或为负数不可导入
                //        if (entity.Value <= 0)
                //        {
                //            throw new ApplicationException($"导入明细金额错误，只能导入大于0的金额数据，请检查");
                //        }
                //        list.Add(entity);
                //    }

                //    if (list == null || !list.Any())
                //    {
                //        throw new ApplicationException($"模板内不存在要导入数据");
                //    }

                //    // 导入类型校验
                //    var types = list.Select(x => x.Type).Distinct().ToList();
                //    if (types.Count() > 1)
                //    {
                //        throw new ApplicationException($"导入明细内有不同认款类型数据，请检查");
                //    }
                //    var type = list[0].Type;
                //    var codes = list.Select(x => x.Code).ToList();
                //    if (codes.Count() > codes.Distinct().Count())
                //    {
                //        // 导入明细有重复数据
                //        throw new ApplicationException($"导入明细内有重复数据，请检查");
                //    }
                //    codes = codes.Distinct().ToList();
                //    var hasCodes = codes.ToHashSet();
                //    // 查询明细
                //    var existsTotalAmount = 0M; //已存在明细总金额（不含覆盖）
                //    var details = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem).Where(x => x.RecognizeReceiveItem.Id == id).ToListAsync();
                //    if (details != null && details.Any())
                //    {
                //        if (details[0].Type != type)
                //        {
                //            throw new ApplicationException($"只能导入一种认款类型的数据，已有明细类型与导入明细不一致！");
                //        }
                //        var exists = details.Where(x => hasCodes.Contains(x.Code)).ToList();
                //        if (exists != null && exists.Any())
                //        {
                //            //string existCodes = string.Join(",", exists.Select(x => x.Code).ToList());
                //            //throw new ApplicationException($"已存在，code集合为{existCodes}");

                //            // 改为覆盖导入
                //            _db.RecognizeReceiveDetails.RemoveRange(exists);
                //        }
                //        if (model.Classify == RecognizeReceiveClassifyEnum.Goods && !string.IsNullOrEmpty(model.RelateCode))
                //        {
                //            // 暂收款转货款金额校验
                //            var receives = await _db.RecognizeReceiveItems.Where(x => model.ReceiveCode == x.ReceiveCode && x.Status != RecognizeReceiveItemStatusEnum.Canceled).Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking().ToListAsync();
                //            var existValue = receives.Where(x => x.CustomerId == model.CustomerId && x.RelateCode == model.RelateCode).Sum(x => x.RecognizeReceiveDetails.Sum(d => d.Value));
                //            existsTotalAmount = existValue;
                //        }
                //        else
                //        {
                //            existsTotalAmount = details.Where(x => !hasCodes.Contains(x.Code)).Sum(x => x.Value);
                //        }
                //    }

                //    // 校验收款单明细金额不能大于收款单总金额
                //    var totalAmount = list.Sum(x => x.Value);
                //    if (totalAmount + existsTotalAmount > Math.Abs(model.ReceiveValue))
                //    {
                //        throw new ApplicationException($"导入明细总金额+已存在明细金额不能大于收款单总金额！");
                //    }

                //    // optimize 批量获取可认款金额
                //    var recognizeReceiveAmounts = await GetRecognizeReceiveAmount(codes, type);

                //    // 发票集合
                //    var invoices = new List<InvoicePo>();
                //    var invoiceCredits = new List<InvoiceCreditPo>();
                //    // 应收集合
                //    var credits = new List<CreditPo>();
                //    // 订单集合
                //    var orders = new List<PageQueryForFinancesOutput>();
                //    if (type == (int)RecognizeTypeEnums.Invoice)
                //    {
                //        invoices = await _db.Invoices.Where(x => hasCodes.Contains(x.InvoiceNo)).AsNoTracking().ToListAsync();
                //        invoiceCredits = await _invoiceCreditQueryService.GetAllListAsync(p => !string.IsNullOrEmpty(p.InvoiceNo) && (codes != null && codes.ToHashSet().Contains(p.InvoiceNo)), new List<string> { "Credit" });
                //    }
                //    else if (type == (int)RecognizeTypeEnums.Orderno)
                //    {
                //        credits = await _creditQueryService.GetAllListAsync(x => !string.IsNullOrEmpty(x.OrderNo) && codes.AsQueryable().Contains(x.OrderNo) && x.AbatedStatus != AbatedStatusEnum.Abated && x.Value > 0);
                //        var dto = new PageQueryForFinancesInput();
                //        dto.UserId = _appServiceContextAccessor.Get().UserId;
                //        dto.PageSize = int.MaxValue;
                //        dto.PageNum = 1;
                //        var recognize = await _db.RecognizeReceiveItems.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);
                //        if (recognize != null)
                //        {
                //            //if (!isReturnCustomer)
                //            //{
                //            //    // 第三方回款客户只允许导入当前客户
                //            //    if (recognize.CustomerId != customerId.ToString())
                //            //    {
                //            //        throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                //            //    }
                //            //}
                //            dto.CompanyId = Guid.Parse(recognize.CompanyId);
                //            dto.BusinessDeptId = recognize.BusinessDepId;
                //        }
                //        dto.CustomerId = customerId;
                //        // 默认值
                //        dto.StartTime = DateTime.UtcNow.AddYears(-1);
                //        dto.EndTime = DateTime.UtcNow.AddDays(1);
                //        var saleOut = await _sellApiClient.PageQueryForFinancesAsync(dto);
                //        if (saleOut.Total > 0)
                //        {
                //            orders = saleOut.List;
                //        }
                //    }
                //    else if (type == (int)RecognizeTypeEnums.Credit)
                //    {
                //        credits = await _creditQueryService.GetAllListAsync(x => !string.IsNullOrEmpty(x.BillCode) && hasCodes.Contains(x.BillCode) && x.AbatedStatus != AbatedStatusEnum.Abated && x.Value > 0);
                //        // 是否第三方回款客户校验
                //        var customerIds = credits.Select(x => x.CustomerId).Distinct().ToList();
                //        if (customerIds != null && customerIds.Any())
                //        {
                //            var currentCreditCustomerId = customerIds[0].ToString();
                //            if (!isReturnCustomer)
                //            {
                //                // 第三方回款客户只允许导入当前客户
                //                if (customerIds.Count() > 1)
                //                {
                //                    throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                //                }
                //                if (currentCreditCustomerId != customerId.ToString())
                //                {
                //                    throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                //                }
                //            }
                //        }
                //        foreach (var credit in credits)
                //        {
                //            if (credit.CreditType != CreditTypeEnum.origin)
                //            {
                //                throw new ApplicationException($"初始应收{credit.BillCode}不存在或已被删除");
                //            }
                //        }
                //    }
                //    else
                //    {
                //        throw new ApplicationException($"认款类型错误！");
                //    }
                //    // 重新组装部分参数
                //    foreach (var item in list)
                //    {
                //        if (item.Type == (int)RecognizeTypeEnums.Invoice)
                //        {
                //            var single = invoiceCredits.FirstOrDefault(x => x.InvoiceNo == item.Code);
                //            if (single == null)
                //            {
                //                throw new ApplicationException($"发票{item.Code}不存在或已被删除");
                //            }
                //            if (!isReturnCustomer && single.Credit?.CustomerId != customerId)
                //            {
                //                throw new ApplicationException($"该客户非第三方回款客户，不允许操作其他客户数据！");
                //            }
                //            var invoice = invoices.FirstOrDefault(x => x.InvoiceNo == item.Code);
                //            if (invoice == null)
                //            {
                //                throw new ApplicationException($"{item.Code}发票不存在");
                //            }
                //            //if (invoice.CompanyId != customerId)
                //            //{
                //            //    throw new ApplicationException($"{item.Code}发票对应公司不匹配");
                //            //}
                //            //var recognizeReceiveAmount = await GetRecognizeReceiveAmount(item.Code, 2);
                //            var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.Code);
                //            var recognizeReceiveAmount = rra != null ? rra.Amount : 0;

                //            // 减去将要覆盖的金额
                //            if (details != null && details.Any())
                //            {
                //                var currentValue = details.Where(x => x.Code == item.Code).Sum(x => x.Value);
                //                recognizeReceiveAmount -= currentValue;
                //            }
                //            // 初始化发票则加入计算
                //            if (invoice.IsInit.HasValue && invoice.IsInit.Value)
                //            {
                //                recognizeReceiveAmount += Math.Abs(invoice.ReceiveAmount ?? 0);
                //            }
                //            if (invoice.InvoiceAmount - Math.Abs(invoice.RedAmount ?? 0) - recognizeReceiveAmount < item.Value)
                //            {
                //                throw new ApplicationException($"操作失败：发票号{invoice.InvoiceNo}超出可认款金额");
                //            }
                //            item.CustomerId = single.Credit?.CustomerId.ToString();
                //            item.CustomerNme = single.Credit?.CustomerName;
                //            item.ServiceId = single.Credit?.ServiceId;
                //            item.ServiceName = single.Credit?.ServiceName;
                //            item.HospitalId = single.Credit?.HospitalId;//终端客户id
                //            item.HospitalName = single.Credit?.HospitalName;
                //        }
                //        else if (item.Type == (int)RecognizeTypeEnums.Credit)
                //        {
                //            var single = credits.FirstOrDefault(x => x.BillCode == item.Code);
                //            if (single == null)
                //            {
                //                throw new ApplicationException($"{item.Code}应收不存在");
                //            }
                //            //if (single.CompanyId != customerId)
                //            //{
                //            //    throw new ApplicationException($"{item.Code}应收对应公司不匹配");
                //            //}
                //            item.CustomerId = single.CustomerId.ToString();
                //            item.CustomerNme = single.CustomerName;
                //            item.ServiceId = single.ServiceId;
                //            item.ServiceName = single.ServiceName;
                //            item.HospitalId = single.HospitalId;//终端客户id
                //            item.HospitalName = single.HospitalName;
                //        }
                //        else if (item.Type == (int)RecognizeTypeEnums.Orderno)
                //        {
                //            var order = new PageQueryForFinancesOutput();
                //            var single = await _sellApiClient.GetTempSaleByCodeAsync(item.Code);
                //            if (orders != null && orders.Any())
                //            {
                //                order = orders.FirstOrDefault(x => x.BillCode == item.Code);
                //                if (order == null)
                //                {
                //                    throw new ApplicationException($"订单号{item.Code}不存在或已被删除");
                //                }
                //            }
                //            var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.Code);
                //            decimal? canAmount = 0M;
                //            if (rra != null)
                //            {
                //                if (rra.CreditSurplusTotalValue.HasValue && rra.CreditSurplusTotalValue < order.TotalAmount - rra.Amount)
                //                {
                //                    canAmount = rra.CreditSurplusTotalValue;
                //                }
                //                else
                //                {
                //                    canAmount = order.TotalAmount - rra.Amount;
                //                }
                //            }
                //            // 减去将要覆盖的金额
                //            if (details != null && details.Any())
                //            {
                //                var currentValue = details.Where(x => x.Code == item.Code).Sum(x => x.Value);
                //                canAmount += currentValue;
                //            }
                //            if (canAmount < item.Value)
                //            {
                //                throw new ApplicationException($"操作失败：订单号{item.Code}超出可认款金额");
                //            }
                //            item.CustomerId = single.CustomerId.ToString();
                //            item.CustomerNme = single.CustomerName;
                //            item.ServiceId = single.ServiceId;
                //            item.ServiceName = single.ServiceName;
                //            item.HospitalId = single.HospitalId;//终端客户id
                //            item.HospitalName = single.HospitalName;
                //        }
                //        else
                //        {
                //            continue;
                //        }
                //    }
                //    _db.RecognizeReceiveDetails.AddRange(list);
                //    _db.RecognizeReceiveItems.Update(model);
                //    var res = await _unitOfWork.CommitAsync();
                //    await UpdateRecognizeReceiveItemProjectNameByCode(id);//更新项目名称和项目代码
                //    return res;
                //}

                #endregion
            }
            catch (Exception ex)
            {
                _logger.LogAzure("Import", $"导入失败{ex.Message}", "导入失败", LogLevelEnum.Error);
                throw new ApplicationException($"导入失败，{ex.Message}");
            }
        }

        /// <summary>
        /// 认款明细导入
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="recognizeReceiveItemId"></param>
        /// <param name="isReturnCustomer"></param>
        /// <param name="userName"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<RecognizeDetailExcelOutput>> ExportDetails(Guid fileId,
            Guid recognizeReceiveItemId, bool isReturnCustomer, string userName, Guid? userId)
        {
            var ret = BaseResponseData<RecognizeDetailExcelOutput>.Success("操作成功！");
            var model = await _db.RecognizeReceiveItems.FirstOrDefaultAsync(x => x.Id == recognizeReceiveItemId);
            if (model == null)
            {
                ret = BaseResponseData<RecognizeDetailExcelOutput>.Failed(500, "导入失败！未找到认款单据");
                return ret;
            }

            if (model.Status != RecognizeReceiveItemStatusEnum.WaitSubmit)
            {
                ret = BaseResponseData<RecognizeDetailExcelOutput>.Failed(500, "导入失败！该状态不能导入");
                return ret;
            }

            model.UpdatedTime = DateTime.Now;
            try
            {
                var stream = await _fileGatewayClient.GetTempFileStreamAsync(fileId);
                var excelDetailList = new List<RecognizeReceviceDetailExcelModel>();

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    stream.Close();
                    var worksheet = package.Workbook.Worksheets[0];
                    worksheet.TrimLastEmptyRows();
                    int rows = worksheet.Dimension.End.Row;
                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        var errText = string.Empty;

                        #region 必填项

                        string code = worksheet.Cells[index, 1].Text.TrimStart().TrimEnd(); //单号
                        if (string.IsNullOrEmpty(code))
                        {
                            break;
                        }

                        string value = worksheet.Cells[index, 3].Text.TrimStart().TrimEnd(); //金额
                        string type = worksheet.Cells[index, 4].Text.TrimStart().TrimEnd(); //认款类型

                        #endregion

                        #region 非必填

                        string creditCode = worksheet.Cells[index, 2].Text.TrimStart().TrimEnd(); //应收单号
                        string remark = worksheet.Cells[index, 5].Text.TrimStart().TrimEnd(); //备注
                        string isSkip = worksheet.Cells[index, 6].Text.TrimStart().TrimEnd(); //是否跳号
                        string classify = worksheet.Cells[index, 7].Text.TrimStart().TrimEnd(); //细分类型

                        #endregion

                        if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(value) || string.IsNullOrEmpty(type))
                        {
                            errText = "请输入完整的数据";
                        }
                        //else if (excelDetailList.Where(x => x.Code == code && x.CreditCode == creditCode).Count() > 1)
                        //{
                        //    errText = "存在重复的单号";
                        //}
                        else if (Convert.ToDecimal(value) <= 0)
                        {
                            errText = "只能导入大于0的金额数据";
                        }

                        excelDetailList.Add(new RecognizeReceviceDetailExcelModel()
                        {
                            Code = code,
                            CreditCode = creditCode,
                            Value = Convert.ToDecimal(value),
                            Type = (int)EnumHelper.GetEnumByValue<RecognizeTypeEnums>(type),
                            Remark = remark,
                            IsSkip = !string.IsNullOrEmpty(isSkip) && isSkip == "是" ? true : false,
                            Classify = !string.IsNullOrEmpty(classify)
                                ? EnumHelper.GetEnumByValue<RecognizeReceiveDetailClassifyEnum>(classify)
                                : RecognizeReceiveDetailClassifyEnum.Goods,
                            ErrMsg = errText //错误信息
                        });
                    }

                    var codes = excelDetailList.Select(x => x.Code).Distinct().ToList();
                    // 导入类型校验
                    var types = excelDetailList.Select(x => x.Type).Distinct().ToList();
                    if (types.Count() > 1)
                    {
                        excelDetailList.ForEach(x => x.ErrMsg = "只能导入一种类型的数据");
                    }

                    var existsTotalAmount = 0M; //已存在的明细总金额（不含覆盖）
                    //与已存在的明细类型匹配
                    var details = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem)
                        .Where(x => x.RecognizeReceiveItem.Id == recognizeReceiveItemId).ToListAsync();
                    if (details != null && details.Any())
                    {
                        //if (details[0].Type != types[0])
                        //{
                        //    excelDetailList.ForEach(x => x.ErrMsg = "导入类型与已存在的明细类型不一致");
                        //}
                        //if (model.Classify == RecognizeReceiveClassifyEnum.Goods && !string.IsNullOrEmpty(model.RelateCode))
                        //{
                        //    // 暂收款转货款金额校验
                        //    var receives = await _db.RecognizeReceiveItems.Where(x => model.ReceiveCode == x.ReceiveCode && x.Status != RecognizeReceiveItemStatusEnum.Canceled).Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking().ToListAsync();
                        //    var existValue = receives.Where(x => x.CustomerId == model.CustomerId && x.RelateCode == model.RelateCode).Sum(x => x.RecognizeReceiveDetails.Sum(d => d.Value));
                        //    existsTotalAmount = existValue;
                        //}
                        //else
                        //{
                        //    existsTotalAmount = details.Where(x => !codes.Contains(x.Code)).Sum(x => x.Value);
                        //}

                        //覆盖
                        _db.RecognizeReceiveDetails.RemoveRange(details);
                        await _unitOfWork.CommitAsync();
                    }

                    // 校验收款单明细金额不能大于收款单总金额
                    var totalAmount = excelDetailList.Sum(x => x.Value);
                    if (totalAmount + existsTotalAmount > Math.Abs(model.ReceiveValue))
                    {
                        excelDetailList.ForEach(x => x.ErrMsg = "导入明细总金额不能大于收款单总金额！");
                    }

                    codes = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).Select(x => x.Code).Distinct()
                        .ToList();
                    if (codes != null && codes.Any())
                    {
                        if (codes != null && codes.Any())
                        {
                            var type = types[0];

                            #region 可认款金额

                            //批量获取可认款金额
                            var recognizeReceiveAmounts = await GetRecognizeReceiveAmount(codes, type);
                            //批量获取应收可认款金额
                            var creditCodes = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg))
                                .Select(x => x.CreditCode).Distinct().ToList();
                            // 按需分配
                            var recognizeReceiveAmountByCreditsByUser = await GetRecognizeReceiveAmount(creditCodes, 3);
                            // 自动分配
                            var recognizeReceiveAmountByCreditsByAuto = new List<RecognizeReceiveAmountDto>();

                            #endregion

                            #region 容器

                            // 认款明细集合
                            var recognizeReceiveDetails = new List<RecognizeReceiveDetailPo>();
                            // 认款明细应收集合
                            var recognizeReceiveDetailCredits =
                                new List<Finance.Data.Models.RecognizeReceiveDetailCreditPo>();
                            // 发票集合
                            var invoices = new List<InvoicePo>();
                            var invoiceCredits = new List<InvoiceCreditPo>();
                            // 应收集合
                            var credits = new List<CreditPo>();
                            // 订单集合
                            var orders = new List<PageQueryForFinancesOutput>();
                            if (type == (int)RecognizeTypeEnums.Invoice)
                            {
                                invoices = await _db.Invoices.Where(x => codes.ToHashSet().Contains(x.InvoiceNo))
                                    .AsNoTracking().ToListAsync();
                                invoiceCredits = await _invoiceCreditQueryService.GetAllListAsync(
                                    x => !string.IsNullOrEmpty(x.InvoiceNo) && codes.ToHashSet().Contains(x.InvoiceNo),
                                    new List<string> { "Credit" });
                                var currentCreditCodes = invoiceCredits.Select(x => x.Credit.BillCode).ToList();
                                recognizeReceiveAmountByCreditsByAuto =
                                    await GetRecognizeReceiveAmount(currentCreditCodes, 3);
                            }
                            else if (type == (int)RecognizeTypeEnums.Orderno)
                            {
                                credits = await _creditQueryService.GetAllListAsync(x => !string.IsNullOrEmpty(x.OrderNo) && codes.ToHashSet().Contains(x.OrderNo) && x.AbatedStatus != AbatedStatusEnum.Abated && x.Value > 0);
                                var dto = new PageQueryForFinancesInput();
                                dto.UserId = _appServiceContextAccessor.Get().UserId;
                                dto.PageSize = int.MaxValue;
                                dto.PageNum = 1;
                                dto.CompanyId = !string.IsNullOrEmpty(model.CompanyId)
                                    ? Guid.Parse(model.CompanyId)
                                    : null;
                                dto.BusinessDeptId = model.BusinessDepId;
                                dto.CustomerId = !string.IsNullOrEmpty(model.CustomerId)
                                    ? Guid.Parse(model.CustomerId)
                                    : null;
                                // 默认值
                                dto.StartTime = DateTime.UtcNow.AddYears(-1);
                                dto.EndTime = DateTime.UtcNow.AddDays(1);
                                var saleOut = await _sellApiClient.PageQueryForFinancesAsync(dto);
                                if (saleOut.Total > 0)
                                {
                                    orders = saleOut.List;
                                }

                                recognizeReceiveAmountByCreditsByAuto =
                                    await GetRecognizeReceiveAmount(credits.Select(x => x.BillCode).ToList(), 3);
                            }
                            else if (type == (int)RecognizeTypeEnums.Credit)
                            {
                                credits = await _creditQueryService.GetAllListAsync(x => codes.ToHashSet().Contains(x.BillCode) && x.AbatedStatus != AbatedStatusEnum.Abated && x.Value > 0);
                                recognizeReceiveAmountByCreditsByUser = await GetRecognizeReceiveAmount(credits.Select(x => x.BillCode).ToList(), 3);
                                recognizeReceiveAmountByCreditsByAuto = await GetRecognizeReceiveAmount(credits.Select(x => x.BillCode).ToList(), 3);
                            }

                            //应收可分配集合
                            var creditSurplusBox = recognizeReceiveAmountByCreditsByAuto.GroupBy(x => x.CreditCode)
                                .Select(x => new CreditSurplusBoxDto
                                {
                                    Code = x.Key,
                                    CreditSurplusTotalValue = x.FirstOrDefault()?.CreditSurplusTotalValue
                                }).ToList();

                            var purityList = excelDetailList.Where(x => string.IsNullOrEmpty(x.ErrMsg)).ToList();
                            if (type == (int)RecognizeTypeEnums.Orderno)
                            {
                                foreach (var item in purityList)
                                {
                                    var currentCredit = credits.FirstOrDefault(x =>
                                        (x.OrderNo == item.Code || x.RelateCode == item.Code) &&
                                        x.BillCode == item.CreditCode);
                                    if (currentCredit == null && !string.IsNullOrEmpty(item.CreditCode))
                                    {
                                        item.ErrMsg = $"订单{item.Code}对应应收关系错误";
                                    }
                                }
                            }

                            #endregion

                            #region 遍历

                            //根据认款明细code分组
                            foreach (var code in codes)
                            {
                                var currentList = purityList
                                    .Where(x => x.Code == code && string.IsNullOrEmpty(x.ErrMsg)).ToList();
                                if (currentList == null || !currentList.Any())
                                {
                                    continue;
                                }

                                //发票
                                if (type == (int)RecognizeTypeEnums.Invoice)
                                {
                                    var single = invoiceCredits.FirstOrDefault(x => x.InvoiceNo == code);
                                    if (single == null)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"发票{code}不存在或已被删除");
                                        continue;
                                    }

                                    if (!isReturnCustomer && !string.IsNullOrEmpty(model.CustomerId) &&
                                        single.Credit?.CustomerId != Guid.Parse(model.CustomerId))
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"发票{code}客户非第三方回款客户，不允许操作其他客户数据！");
                                        continue;
                                    }

                                    var invoice = invoices.FirstOrDefault(x => x.InvoiceNo == code);
                                    if (invoice == null)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"发票{code}不存在或已被删除");
                                        continue;
                                    }

                                    //发票可认款金额等于发票金额-初始化金额-红冲金额-新表对应发票使用金额和应收金额-记录对应应收用认款金额之和对比，取小
                                    var invoiceValue = invoice.InvoiceAmount - Math.Abs(invoice.RedAmount ??= 0);
                                    decimal? canAmount = invoiceValue;
                                    // 初始化发票则加入计算
                                    if (invoice.IsInit.HasValue && invoice.IsInit.Value)
                                    {
                                        invoiceValue -= Math.Abs(invoice.ReceiveAmount ?? 0);
                                    }

                                    var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == invoice.InvoiceNo);
                                    if (rra != null)
                                    {
                                        canAmount = invoiceValue - rra.Amount > rra.CreditSurplusTotalValue
                                            ? rra.CreditSurplusTotalValue
                                            : invoiceValue - rra.Amount;
                                    }

                                    // 加上将要覆盖的金额
                                    if (details != null && details.Any())
                                    {
                                        var currentValue = details.Where(x => x.Code == code).Sum(x => x.Value);
                                        canAmount += currentValue;
                                    }

                                    //当前发票剩余分配金额
                                    var importValue = currentList.Where(x => x.Code == code).Sum(x => x.Value);
                                    decimal? surplusAmount = canAmount > importValue ? importValue : canAmount;
                                    //当前认款明细单号认款金额
                                    var currentRecognizeReceiveValue = currentList.Sum(x => x.Value);
                                    if (canAmount < currentRecognizeReceiveValue)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"发票{code}超出可认款金额");
                                        continue;
                                    }

                                    //写入认款明细
                                    var recognizeReceiveDetailId = Guid.NewGuid();
                                    var recognizeReceiveDetail = new RecognizeReceiveDetailPo
                                    {
                                        Id = recognizeReceiveDetailId,
                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                        Code = code,
                                        Type = type,
                                        RecognizeDate = DateTime.Now,
                                        Value = surplusAmount.HasValue ? surplusAmount.Value : 0,
                                        IsSkip = currentList[0].IsSkip,
                                        Note = currentList[0].Remark,
                                        CreatedTime = DateTime.Now,
                                        CreatedBy = userName ??= "none",
                                        CustomerId = single.Credit?.CustomerId.ToString(),
                                        CustomerNme = single.Credit?.CustomerName,
                                        ServiceId = single.Credit?.ServiceId,
                                        ServiceName = single.Credit?.ServiceName,
                                        HospitalId = single.Credit?.HospitalId,
                                        HospitalName = single.Credit?.HospitalName,
                                        Status = RecognizeReceiveDetailEnum.Normal
                                    };
                                    foreach (var item in currentList)
                                    {
                                        if (!string.IsNullOrEmpty(item.CreditCode))
                                        {
                                            if (surplusAmount <= 0)
                                            {
                                                //剩余分配金额为0
                                                continue;
                                            }

                                            //应收单号不为空，按需分配应收单金额
                                            var currentRecognizeReceiveCredit =
                                                currentList.FirstOrDefault(x => x.CreditCode == item.CreditCode);
                                            if (currentRecognizeReceiveCredit == null)
                                            {
                                                continue;
                                            }

                                            //获取当前应收单号认款金额
                                            var currentCreditRecognizeReceiveModel =
                                                recognizeReceiveAmountByCreditsByUser.FirstOrDefault(x =>
                                                    x.Code == item.CreditCode);
                                            if (currentCreditRecognizeReceiveModel == null)
                                            {
                                                currentRecognizeReceiveCredit.ErrMsg = "应收单号认款金额分配不足，跳过";
                                                continue;
                                            }

                                            if (currentRecognizeReceiveCredit.Value > currentCreditRecognizeReceiveModel
                                                    .CreditSurplusTotalValue)
                                            {
                                                currentRecognizeReceiveCredit.ErrMsg = "应收单号可认款金额不足";
                                                continue;
                                            }

                                            if (surplusAmount > currentRecognizeReceiveCredit.Value)
                                            {
                                                //分配金额充足
                                                recognizeReceiveDetailCredits.Add(
                                                    new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                    {
                                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                                        RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                        InvoiceNo = code,
                                                        InvoiceId = invoice.Id,
                                                        CreditCode = currentCreditRecognizeReceiveModel.CreditCode ??=
                                                            "",
                                                        CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                        CurrentValue = currentRecognizeReceiveCredit.Value,
                                                        CreatedBy = userName ??= "none"
                                                    });
                                                surplusAmount -= currentRecognizeReceiveCredit.Value;
                                            }
                                            else
                                            {
                                                //分配数量不足
                                                recognizeReceiveDetailCredits.Add(
                                                    new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                    {
                                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                                        RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                        InvoiceNo = code,
                                                        InvoiceId = invoice.Id,
                                                        CreditCode = currentCreditRecognizeReceiveModel.CreditCode ??=
                                                            "",
                                                        CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                        CurrentValue = surplusAmount,
                                                        CreatedBy = userName ??= "none"
                                                    });
                                                surplusAmount = 0;
                                            }
                                        }
                                        else
                                        {
                                            //应收单号为空，自动分配应收单金额
                                            if (surplusAmount <= 0)
                                            {
                                                //剩余分配金额为0
                                                continue;
                                            }

                                            var currentInvoiceCrditCodes = invoiceCredits
                                                .Where(x => x.InvoiceNo == code).Select(x => x.Credit?.BillCode)
                                                .ToList();
                                            foreach (var creditCode in currentInvoiceCrditCodes)
                                            {
                                                if (surplusAmount <= 0)
                                                {
                                                    //剩余分配金额为0
                                                    continue;
                                                }

                                                //获取当前应收单号认款金额
                                                var currentCreditRecognizeReceiveModel =
                                                    recognizeReceiveAmountByCreditsByAuto.FirstOrDefault(x =>
                                                        x.Code == creditCode);
                                                if (currentCreditRecognizeReceiveModel == null)
                                                {
                                                    currentList.ForEach(x => x.ErrMsg = $"应收单号认款金额分配不足，跳过");
                                                    continue;
                                                }

                                                //获取应收剩余可分配金额
                                                decimal? surplusCreditAmount = currentCreditRecognizeReceiveModel
                                                    .CreditSurplusTotalValue;
                                                var singleSurplusBox =
                                                    creditSurplusBox.FirstOrDefault(x => x.Code == creditCode);
                                                if (singleSurplusBox != null)
                                                {
                                                    surplusCreditAmount = singleSurplusBox.CreditSurplusTotalValue;
                                                }

                                                //获取发票在应收中占用金额
                                                var singleInvoiceCredit = invoiceCredits.FirstOrDefault(x =>
                                                    x.InvoiceNo == code && x.CreditId ==
                                                    currentCreditRecognizeReceiveModel.CreditId);
                                                if (singleInvoiceCredit != null)
                                                {
                                                    surplusCreditAmount =
                                                        surplusCreditAmount > singleInvoiceCredit.CreditAmount
                                                            ? singleInvoiceCredit.CreditAmount
                                                            : surplusCreditAmount;
                                                }

                                                if (surplusCreditAmount <= 0)
                                                {
                                                    continue;
                                                }

                                                if (surplusAmount > surplusCreditAmount)
                                                {
                                                    //分配金额充足
                                                    recognizeReceiveDetailCredits.Add(
                                                        new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                        {
                                                            RecognizeReceiveItemId = recognizeReceiveItemId,
                                                            RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                            InvoiceNo = code,
                                                            InvoiceId = invoice.Id,
                                                            CreditCode =
                                                                currentCreditRecognizeReceiveModel.CreditCode ??= "",
                                                            CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                            CurrentValue = surplusCreditAmount,
                                                            CreatedBy = userName ??= "none"
                                                        });
                                                    surplusAmount -= surplusCreditAmount;
                                                    //分配完金额
                                                    creditSurplusBox.Where(x => x.Code == creditCode).ForEach(x =>
                                                    {
                                                        x.CreditSurplusTotalValue -=
                                                            currentCreditRecognizeReceiveModel
                                                                .CreditSurplusTotalValue;
                                                    });
                                                }
                                                else
                                                {
                                                    //分配数量不足
                                                    recognizeReceiveDetailCredits.Add(
                                                        new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                        {
                                                            RecognizeReceiveItemId = recognizeReceiveItemId,
                                                            RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                            InvoiceNo = code,
                                                            InvoiceId = invoice.Id,
                                                            CreditCode =
                                                                currentCreditRecognizeReceiveModel.CreditCode ??= "",
                                                            CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                            CurrentValue = surplusAmount,
                                                            CreatedBy = userName ??= "none"
                                                        });
                                                    //分配完金额
                                                    creditSurplusBox.Where(x => x.Code == creditCode).ForEach(x =>
                                                    {
                                                        x.CreditSurplusTotalValue -= surplusAmount;
                                                    });
                                                    surplusAmount = 0;
                                                }
                                            }
                                        }
                                    }

                                    //更新实际最终认款金额
                                    var finishValue = recognizeReceiveDetailCredits
                                        .Where(x => x.RecognizeReceiveDetailId == recognizeReceiveDetailId)
                                        .Sum(x => x.CurrentValue);
                                    recognizeReceiveDetail.Value = finishValue.HasValue ? finishValue.Value : 0;
                                    recognizeReceiveDetails.Add(recognizeReceiveDetail);
                                }
                                //订单
                                else if (type == (int)RecognizeTypeEnums.Orderno)
                                {
                                    if (orders == null || !orders.Any())
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"订单{code}不存在或已被删除");
                                        continue;
                                    }

                                    var order = orders.FirstOrDefault(x => x.BillCode == code);
                                    if (order == null)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"订单{code}不存在或已被删除");
                                        continue;
                                    }

                                    if (!isReturnCustomer && !string.IsNullOrEmpty(order.CustomerId) &&
                                        order.CustomerId.ToLower() != model.CustomerId.ToLower())
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"订单{code}客户非第三方回款客户，不允许操作其他客户数据！");
                                        continue;
                                    }

                                    var rra = recognizeReceiveAmounts.FirstOrDefault(x =>
                                        x.Code == order.BillCode && x.CreditSurplusTotalValue > 0);
                                    //订单可认款金额等于订单总金额-记录对应应收认款总金额（未生成应收的订单预存一条无应收单号、id的数据）和应收金额-记录对应应收用认款金额之和对比，取小
                                    decimal? canAmount = order.Amount;
                                    if (rra != null)
                                    {
                                        if (rra.CreditSurplusTotalValue.HasValue)
                                        {
                                            canAmount = order.Amount - rra.Amount > rra.CreditSurplusTotalValue
                                                ? rra.CreditSurplusTotalValue
                                                : order.Amount - rra.Amount;
                                        }
                                        else
                                        {
                                            canAmount = order.Amount - rra.Amount;
                                        }
                                    }

                                    // 加上将要覆盖的金额
                                    if (details != null && details.Any())
                                    {
                                        var currentValue = details.Where(x => x.Code == code).Sum(x => x.Value);
                                        canAmount += currentValue;
                                    }

                                    //当前发票剩余分配金额
                                    var importValue = currentList.Where(x => x.Code == code).Sum(x => x.Value);
                                    decimal? surplusAmount = canAmount > importValue ? importValue : canAmount;
                                    //当前认款明细单号认款金额
                                    var currentRecognizeReceiveValue = currentList.Sum(x => x.Value);
                                    if (canAmount < currentRecognizeReceiveValue)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"订单{code}超出可认款金额");
                                        continue;
                                    }

                                    //写入认款明细
                                    var recognizeReceiveDetailId = Guid.NewGuid();
                                    var recoginzeReceiveDetail = new RecognizeReceiveDetailPo
                                    {
                                        Id = recognizeReceiveDetailId,
                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                        Code = code,
                                        Type = type,
                                        RecognizeDate = DateTime.Now,
                                        Value = surplusAmount.HasValue ? surplusAmount.Value : 0,
                                        IsSkip = currentList[0].IsSkip,
                                        Note = currentList[0].Remark,
                                        CreatedTime = DateTime.Now,
                                        CreatedBy = userName ??= "none",
                                        CustomerId = order.CustomerId,
                                        CustomerNme = order.CustomerName,
                                        ServiceId = order.ServiceId,
                                        ServiceName = order.ServiceName,
                                        HospitalId = order.HospitalId,
                                        HospitalName = order.HospitalName,
                                        Status = RecognizeReceiveDetailEnum.Normal
                                    };
                                    foreach (var item in currentList)
                                    {
                                        if (!string.IsNullOrEmpty(item.CreditCode))
                                        {
                                            if (surplusAmount <= 0)
                                            {
                                                //剩余分配金额为0
                                                continue;
                                            }

                                            //应收单号不为空，按需分配应收单金额
                                            var currentRecognizeReceiveCredit =
                                                currentList.FirstOrDefault(x => x.CreditCode == item.CreditCode);
                                            if (currentRecognizeReceiveCredit == null)
                                            {
                                                continue;
                                            }

                                            //获取当前应收单号认款金额
                                            var currentCreditRecognizeReceiveModel =
                                                recognizeReceiveAmountByCreditsByUser.FirstOrDefault(x =>
                                                    x.Code == item.CreditCode);
                                            if (currentCreditRecognizeReceiveModel == null)
                                            {
                                                currentRecognizeReceiveCredit.ErrMsg = "应收单号认款金额分配不足，跳过";
                                                continue;
                                            }

                                            if (currentRecognizeReceiveCredit.Value > currentCreditRecognizeReceiveModel
                                                    .CreditSurplusTotalValue)
                                            {
                                                currentRecognizeReceiveCredit.ErrMsg = "应收单号可认款金额不足";
                                                continue;
                                            }

                                            if (surplusAmount > currentRecognizeReceiveCredit.Value)
                                            {
                                                //分配金额充足
                                                recognizeReceiveDetailCredits.Add(
                                                    new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                    {
                                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                                        RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                        OrderNo = code,
                                                        OrderId = order.Id,
                                                        CreditCode = currentCreditRecognizeReceiveModel.CreditCode ??=
                                                            "",
                                                        CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                        CurrentValue = currentRecognizeReceiveCredit.Value,
                                                        CreatedBy = userName ??= "none"
                                                    });
                                                surplusAmount -= currentRecognizeReceiveCredit.Value;
                                            }
                                            else
                                            {
                                                //分配数量不足
                                                recognizeReceiveDetailCredits.Add(
                                                    new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                    {
                                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                                        RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                        OrderNo = code,
                                                        OrderId = order.Id,
                                                        CreditCode = currentCreditRecognizeReceiveModel.CreditCode ??=
                                                            "",
                                                        CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                        CurrentValue = surplusAmount,
                                                        CreatedBy = userName ??= "none"
                                                    });
                                                surplusAmount = 0;
                                            }
                                        }
                                        else
                                        {
                                            //应收单号为空，自动分配应收单金额
                                            if (surplusAmount <= 0)
                                            {
                                                //剩余分配金额为0
                                                continue;
                                            }

                                            var currentOrderCrditCodes = credits
                                                .Where(x => x.OrderNo == code || x.RelateCode == code)
                                                .Select(x => x.BillCode).ToList();
                                            foreach (var creditCode in currentOrderCrditCodes)
                                            {
                                                if (surplusAmount <= 0)
                                                {
                                                    //剩余分配金额为0
                                                    continue;
                                                }

                                                //获取当前应收单号认款金额
                                                var currentCreditRecognizeReceiveModel =
                                                    recognizeReceiveAmountByCreditsByAuto.FirstOrDefault(x =>
                                                        x.Code == creditCode);
                                                if (currentCreditRecognizeReceiveModel == null)
                                                {
                                                    currentList.ForEach(x => x.ErrMsg = $"应收单号认款金额分配不足，跳过");
                                                    continue;
                                                }
                                                if (currentCreditRecognizeReceiveModel.CreditSurplusTotalValue == 0)
                                                {
                                                    continue;
                                                }
                                                if (surplusAmount > currentCreditRecognizeReceiveModel.CreditSurplusTotalValue)
                                                {
                                                    //分配金额充足
                                                    recognizeReceiveDetailCredits.Add(
                                                        new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                        {
                                                            RecognizeReceiveItemId = recognizeReceiveItemId,
                                                            RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                            OrderNo = code,
                                                            OrderId = order.Id,
                                                            CreditCode =
                                                                currentCreditRecognizeReceiveModel.CreditCode ??= "",
                                                            CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                            CurrentValue = currentCreditRecognizeReceiveModel
                                                                .CreditSurplusTotalValue,
                                                            CreatedBy = userName ??= "none"
                                                        });
                                                    surplusAmount -= currentCreditRecognizeReceiveModel
                                                        .CreditSurplusTotalValue;
                                                }
                                                else
                                                {
                                                    //分配数量不足
                                                    recognizeReceiveDetailCredits.Add(
                                                        new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                        {
                                                            RecognizeReceiveItemId = recognizeReceiveItemId,
                                                            RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                            OrderNo = code,
                                                            OrderId = order.Id,
                                                            CreditCode =
                                                                currentCreditRecognizeReceiveModel.CreditCode ??= "",
                                                            CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                            CurrentValue = surplusAmount,
                                                            CreatedBy = userName ??= "none"
                                                        });
                                                    surplusAmount = 0;
                                                }
                                            }
                                        }
                                    }

                                    //更新实际最终认款金额
                                    var finishValue = recognizeReceiveDetailCredits
                                        .Where(x => x.RecognizeReceiveDetailId == recognizeReceiveDetailId)
                                        .Sum(x => x.CurrentValue);
                                    recoginzeReceiveDetail.Value = finishValue.HasValue ? finishValue.Value : 0;
                                    recognizeReceiveDetails.Add(recoginzeReceiveDetail);
                                }
                                //初始应收
                                else if (type == (int)RecognizeTypeEnums.Credit)
                                {
                                    var credit = credits.FirstOrDefault(x => x.BillCode == code);
                                    if (credit == null)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"初始应收{code}不存在或已被删除");
                                        continue;
                                    }

                                    if (credit.CreditType != CreditTypeEnum.origin)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"初始应收{code}不存在或应收类型错误");
                                        continue;
                                    }

                                    if (!isReturnCustomer && !string.IsNullOrEmpty(model.CustomerId) &&
                                        credit.CustomerId != Guid.Parse(model.CustomerId))
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"初始应收{code}客户非第三方回款客户，不允许操作其他客户数据！");
                                        continue;
                                    }

                                    var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == credit.BillCode);
                                    //初始应收可认款金额等于应收金额-应收已认款金额
                                    decimal? canAmount = credit.Value;
                                    if (rra != null)
                                    {
                                        canAmount = rra.CreditSurplusTotalValue;
                                    }

                                    // 加上将要覆盖的金额
                                    if (details != null && details.Any())
                                    {
                                        var currentValue = details.Where(x => x.Code == code).Sum(x => x.Value);
                                        canAmount += currentValue;
                                    }

                                    //当前发票剩余分配金额
                                    var importValue = currentList.Where(x => x.Code == code).Sum(x => x.Value);
                                    decimal? surplusAmount = canAmount > importValue ? importValue : canAmount;
                                    //当前认款明细单号认款金额
                                    var currentRecognizeReceiveValue = currentList.Sum(x => x.Value);
                                    if (canAmount < currentRecognizeReceiveValue)
                                    {
                                        currentList.ForEach(x => x.ErrMsg = $"初始应收{code}超出可认款金额");
                                        continue;
                                    }

                                    //写入认款明细
                                    var recognizeReceiveDetailId = Guid.NewGuid();
                                    var recognizeReceiveDetail = new RecognizeReceiveDetailPo
                                    {
                                        Id = recognizeReceiveDetailId,
                                        RecognizeReceiveItemId = recognizeReceiveItemId,
                                        Code = code,
                                        Type = type,
                                        RecognizeDate = DateTime.Now,
                                        Value = surplusAmount.HasValue ? surplusAmount.Value : 0,
                                        IsSkip = currentList[0].IsSkip,
                                        Note = currentList[0].Remark,
                                        CreatedTime = DateTime.Now,
                                        CreatedBy = userName ??= "none",
                                        CustomerId = credit?.CustomerId.ToString(),
                                        CustomerNme = credit?.CustomerName,
                                        ServiceId = credit?.ServiceId,
                                        ServiceName = credit?.ServiceName,
                                        HospitalId = credit?.HospitalId,
                                        HospitalName = credit?.HospitalName,
                                        Status = RecognizeReceiveDetailEnum.Normal
                                    };
                                    foreach (var item in currentList)
                                    {
                                        //应收单号为空，自动分配应收单金额
                                        if (surplusAmount <= 0)
                                        {
                                            //剩余分配金额为0
                                            continue;
                                        }

                                        //获取当前应收单号认款金额
                                        var currentCreditRecognizeReceiveModel =
                                            recognizeReceiveAmountByCreditsByUser.FirstOrDefault(x =>
                                                x.Code == item.Code);
                                        if (currentCreditRecognizeReceiveModel == null)
                                        {
                                            currentList.ForEach(x => x.ErrMsg = $"应收单号认款金额分配不足，跳过");
                                            continue;
                                        }

                                        if (surplusAmount > currentCreditRecognizeReceiveModel.CreditSurplusTotalValue)
                                        {
                                            //分配金额充足
                                            recognizeReceiveDetailCredits.Add(
                                                new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                                {
                                                    RecognizeReceiveItemId = recognizeReceiveItemId,
                                                    RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                    CreditCode = code,
                                                    CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                    CurrentValue = currentCreditRecognizeReceiveModel
                                                        .CreditSurplusTotalValue,
                                                    CreatedBy = userName ??= "none"
                                                });
                                            surplusAmount -= currentCreditRecognizeReceiveModel.CreditSurplusTotalValue;
                                        }
                                        else
                                        {
                                            //分配数量不足
                                            recognizeReceiveDetailCredits.Add(new Finance.Data.Models.RecognizeReceiveDetailCreditPo
                                            {
                                                RecognizeReceiveItemId = recognizeReceiveItemId,
                                                RecognizeReceiveDetailId = recognizeReceiveDetailId,
                                                CreditCode = code,
                                                CreditId = currentCreditRecognizeReceiveModel.CreditId,
                                                CurrentValue = surplusAmount,
                                                CreatedBy = userName ??= "none"
                                            });
                                            surplusAmount = 0;
                                        }
                                    }

                                    //更新实际最终认款金额
                                    var finishValue = recognizeReceiveDetailCredits
                                        .Where(x => x.RecognizeReceiveDetailId == recognizeReceiveDetailId)
                                        .Sum(x => x.CurrentValue);
                                    recognizeReceiveDetail.Value = finishValue.HasValue ? finishValue.Value : 0;
                                    recognizeReceiveDetails.Add(recognizeReceiveDetail);
                                }
                            }

                            #endregion

                            #region 执行存储

                            if (recognizeReceiveDetails.Any())
                            {
                                _db.RecognizeReceiveDetails.AddRange(recognizeReceiveDetails);
                                _db.RecognizeReceiveDetailCredits.AddRange(recognizeReceiveDetailCredits);
                                _db.RecognizeReceiveItems.Update(model);
                                var res = await _unitOfWork.CommitAsync();
                                await UpdateRecognizeReceiveItemProjectNameByCode(recognizeReceiveItemId); //更新项目名称和项目代码
                            }

                            #endregion
                        }
                    }

                    for (int index = worksheet.Dimension.Start.Row + 1; index <= rows; index++)
                    {
                        string code = worksheet.Cells[index, 1].Text.TrimStart().TrimEnd(); //单号
                        string creditCode = worksheet.Cells[index, 2].Text.TrimStart().TrimEnd(); //应收单号
                        var errMsgModel =
                            excelDetailList.FirstOrDefault(x => x.Code == code && x.CreditCode == creditCode);
                        if (errMsgModel != null)
                        {
                            if (!string.IsNullOrEmpty(errMsgModel.ErrMsg)) //出错信息
                            {
                                worksheet.Cells[index, 8].Value = errMsgModel.ErrMsg;
                            }
                        }
                    }

                    //校验失败导出错误信息
                    var failCount = excelDetailList.Where(x => !string.IsNullOrEmpty(x.ErrMsg)).Count();
                    if (failCount > 0)
                    {
                        worksheet.Column(8).Style.Font.Color.SetColor(Color.Red);
                        //生成错误报告文件
                        MemoryStream msFailReport = new MemoryStream(package.GetAsByteArray());
                        ret = BaseResponseData<RecognizeDetailExcelOutput>.Success("导入失败！");
                        ret.Data = new RecognizeDetailExcelOutput()
                        {
                            FailReportFileId =
                                await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "认款明细导入错误数据.xlsx"),
                            FailNumber = failCount,
                            SuccessNumber = excelDetailList.Count() - failCount,
                            Total = excelDetailList.Count(),
                        };
                        return ret;
                    }
                    else
                    {
                        ret = BaseResponseData<RecognizeDetailExcelOutput>.Success("导入完成！");
                        ret.Data = new RecognizeDetailExcelOutput()
                        {
                            FailNumber = 0,
                            SuccessNumber = excelDetailList.Count(),
                            Total = excelDetailList.Count(),
                        };
                    }
                }

                return ret;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<RecognizeDetailExcelOutput>.Failed(500, "导入失败！错误信息：" + ex.Message);
                return ret;
            }
        }

        public async Task<int> DeleteItemAsync(Guid id, string createdBy)
        {
            var query = _receiveItemQueryService.GetIQueryable(p => p.Id == id);
            var item = await query.FirstOrDefaultAsync();
            if (item == null)
            {
                throw new ApplicationException($"未找到认款单据");
            }

            if (item.Status != RecognizeReceiveItemStatusEnum.WaitSubmit)
            {
                throw new ApplicationException($"该状态不能删除");
            }

            if (item.CreatedBy != createdBy)
            {
                throw new ApplicationException($"操作失败，原因：您无权限删除该条数据");
            }

            // #79452 暂收款转货款生成的记录不能删除
            if (item.Classify == RecognizeReceiveClassifyEnum.Goods && !string.IsNullOrEmpty(item.RelateCode) && item.Status != RecognizeReceiveItemStatusEnum.WaitSubmit)
            {
                throw new ApplicationException($"暂收款转货款的单据无法删除");
            }

            await _itemRepository.DeleteAsync(id);
            var res = await _unitOfWork.CommitAsync();

            _logger.LogAzure("DeleteItemAsync", $"{createdBy}删除认款单{item.Code}", "删除认款单");
            return res;
        }

        public async Task<int> DeleteDetailAsync(RecognizeReceiveDetailDelDto dto)
        {
            var item = await _itemRepository.GetWithNoTrackAsync(dto.RecognizeReceiveItemId);
            if (item == null)
            {
                throw new ApplicationException($"未找到认款单据");
            }

            if (item.Status != 0)
            {
                throw new ApplicationException($"该状态不能修改明细");
            }

            item.UpdatedTime = DateTime.Now;
            item.Value = item.RecognizeReceiveDetails.Where(t => !dto.Ids.Contains(t.Id)).Sum(t => t.Value);
            await _itemRepository.UpdateAsync(item);
            await _detailRepository.DeteteManyAsync(dto.Ids);
            var res = await _unitOfWork.CommitAsync();
            await UpdateRecognizeReceiveItemProjectNameByCode(dto.RecognizeReceiveItemId); //更新项目名称和项目代码
            _logger.LogAzure("DeleteDetailAsync", $"{dto.CreatedBy}删除认款单明细{item.Code}，明细id集合{string.Join(",", dto.Ids)}", "删除认款单明细");
            return res;
        }

        /// <summary>
        /// 提交到金蝶
        /// </summary>
        /// <param name="id"></param>
        /// <param name="createdBy"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<object> SubmitToKd(Guid id, string createdBy)
        {
            var entity = await _itemRepository.GetWithNoTrackAsync(id);
            if (entity == null || entity.Status != 0)
            {
                throw new ApplicationException("操作失败，原因：没有找到认款单或该状态不能提交");
            }

            var sysMonth = await _bDSApiClient.GetSystemMonth(entity.CompanyId);
            await CheckSysMonth(Guid.Parse(entity.CompanyId), sysMonth);
            var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == entity.Id)
                .AsNoTracking().ToListAsync();
            if (entity.RecognizeReceiveDetails != null && entity.RecognizeReceiveDetails.Any())
            {
                foreach (var rrd in entity.RecognizeReceiveDetails)
                {
                    var currentRrdcs = rrdcs.Where(x => x.RecognizeReceiveDetailId == rrd.Id).ToList();
                    if (currentRrdcs == null || currentRrdcs.Count == 0)
                    {
                        throw new ApplicationException("操作失败，原因：当前认款单有明细未勾选应收单，无法提交");
                    }

                    if (rrd.Value == 0)
                    {
                        throw new ApplicationException("操作失败，原因：当前认款单有明细认款金额为0，无法提交");
                    }
                }
            }

            var isNullOrEmptyByCredits = rrdcs.Where(x => !string.IsNullOrEmpty(x.CreditCode)).ToList();
            var creditBillCodes = isNullOrEmptyByCredits.Select(p => p.CreditCode).ToList();
            if (creditBillCodes.Count > 0)
            {
                var lossRecognitionDetails = await _db.LossRecognitionDetails.Where(p => creditBillCodes.ToHashSet().Contains(p.BillCode)).AsNoTracking().ToListAsync();
                if (lossRecognitionDetails != null && lossRecognitionDetails.Count > 0)
                {
                    var isLoss = await _db.LossRecognitionItem.Where(p =>
                        lossRecognitionDetails.Select(p => p.LossRecognitionItemId).Contains(p.Id) &&
                        p.Status != StatusEnum.Complate && p.Status != StatusEnum.Refuse).ToListAsync();
                    if (isLoss != null && isLoss.Count > 0)
                    {
                        throw new ApplicationException("操作失败，原因：当前认款单有明细存在损失确认申请单中，无法提交。应收单号：" + string.Join(',',
                            lossRecognitionDetails.Select(p => p.BillCode).Distinct().ToArray()));
                    }
                }
            }

            if (entity.Classify == RecognizeReceiveClassifyEnum.Goods)
            {
                return await GoodsSubmit(createdBy, entity);
            }
            else
            {
                var temp = await _receiveItemQueryService.FirstOrDefaultAsync(p => p.Code == entity.ReceiveCode);
                if (temp != null && temp.Status != RecognizeReceiveItemStatusEnum.Completed)
                {
                    throw new ApplicationException($"操作失败，原因：暂收款不是已完成状态，该货款不能提交");
                }

                return await TempSubmit(createdBy, entity);
            }
        }

        private async Task<string> CheckSysMonth(Guid companyId, string sysMonth)
        {
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
            {
                var inventory =
                    await _inventoryQueryService.FirstOrDefaultAsync(t =>
                        t.SysMonth == sysMonth && t.CompanyId == companyId);
                if (inventory != null)
                {
                    if (inventory.Status == 2)
                    {
                        throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                    }

                    if (inventory.Status == 99)
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }
                }
            }
            else
            {
                DateTime.TryParse(sysMonth, out DateTime billDate);
                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                {
                    throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                }
            }

            return sysMonth;
        }

        /// <summary>
        /// 暂收款提交
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        private async Task<object> TempSubmit(string createdBy, RecognizeReceiveItem entity)
        {
            try
            {
                if (entity.RecognizeReceiveTempDetails == null || !entity.RecognizeReceiveTempDetails.Any())
                {
                    throw new ApplicationException("没有认款明细");
                }

                //生成单号
                var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(
                    new CompetenceCenter.BDSCenter.BDSBaseInput
                    { ids = new List<string> { entity.CompanyId.ToString() } });
                var companyInfo = companyInfoOutput?.FirstOrDefault();
                if (companyInfo == null)
                {
                    throw new AppServiceException("公司信息不存在");
                }

                DateTime actualDate = await _companyDateService.GetActualDateAsync(entity.CompanyId);

                var kdInput = new BatchSaveAcceptanceInput
                {
                    billno = entity.Code,
                    jfzx_amountfield = entity.RecognizeReceiveTempDetails.Sum(p => p.Value),
                    jfzx_gatheringamount = entity.ReceiveValue,
                    jfzx_gatheringdate = entity.ReceiveDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    jfzx_gatheringnum = entity.ReceiveCode,
                    jfzx_gatheringorg = companyInfo.nameCode,
                    jfzx_payer = entity.CustomerId,
                    jfzx_bizorg = entity.BusinessDepId.ToString(),
                    billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                    acceptanceEntrys = new List<AcceptanceEntrysItemInput> { }
                };

                foreach (var detail in entity.RecognizeReceiveTempDetails)
                {
                    kdInput.acceptanceEntrys.Add(new AcceptanceEntrysItemInput
                    {
                        jfzx_payee = detail.CreatedBy,
                        jfzx_projectnos = detail.ProjectCode ?? "",
                        jfzx_subscriptiontype = "D",
                        jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                        jfzx_collectiontype =
                            detail.CollectionType.HasValue ? detail.CollectionType.ToString() : "100", //销售回款,
                        jfzx_subscriptionamount = detail.Value,
                        jfzx_subscriptiondate = actualDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        jfzx_skipornot = false,
                        jfzx_remaker = detail.Note ?? "无",
                        jfzx_subscriptionnum = "",
                        jfzx_customer = string.IsNullOrEmpty(detail.CustomerId)
                            ? string.Empty
                            : detail.CustomerId.ToUpper().ToString()
                    });
                }

                var result =
                    await _kingdeeApiClient.PushBatchSaveAcceptances(new List<BatchSaveAcceptanceInput> { kdInput });

                if (result.Code != CodeStatusEnum.Success)
                {
                    _logger.LogAzure("TempSubmit", result.Message, "暂收款提交");
                    throw new ApplicationException(result.Message);
                }
                else
                {
                    // 更新状态
                    entity.Status = (int)RecognizeReceiveItemStatusEnum.Completed;
                }

                entity.UpdatedTime = DateTime.Now;
                entity.UpdatedBy = createdBy;
                await _itemRepository.UpdateAsync(entity);
                await _unitOfWork.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("TempSubmit", ex.Message, "暂收款提交", LogLevelEnum.Error);
                //_logger.LogInformation($"{ex.Message}");
                throw new ApplicationException($"{ex.Message}");
            }
        }

        /// <summary>
        /// 货款提交
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        private async Task<object> GoodsSubmit(string createdBy, RecognizeReceiveItem entity)
        {
            if (entity.RecognizeReceiveDetails == null || !entity.RecognizeReceiveDetails.Any())
            {
                throw new ApplicationException("没有认款明细");
            }
            var receiveDetails_orders = entity.RecognizeReceiveDetails.Where(t => t.Type == (int)RecognizeTypeEnums.Orderno).ToList();
            var receiveDetails_credits = entity.RecognizeReceiveDetails.Where(t => t.Type == (int)RecognizeTypeEnums.Credit).ToList();
            var receiveDetails_invoice = entity.RecognizeReceiveDetails.Where(t => t.Type == (int)RecognizeTypeEnums.Invoice).ToList();
            bool needCancelSale = true;
            try
            {
                if (entity.Type == "负数应收" && receiveDetails_invoice != null && receiveDetails_invoice.Any())
                {
                    if (receiveDetails_invoice.Count(p => p.Value < 0) > 0)
                    {
                        throw new ApplicationException("负数应收，不允许认款到负数发票");
                    }
                }

                if (receiveDetails_credits != null && receiveDetails_credits.Any()) //有初始应收
                {
                    var creditNos = receiveDetails_credits.Select(t => t.Code).ToList();
                    var credits = await _creditQueryService.GetAllListAsync(p => creditNos.Contains(p.BillCode));
                    var abatements = await _abatementQueryService.GetAllListAsync(p =>
                        p.CreditType == "credit" && creditNos.Contains(p.CreditBillCode));

                    foreach (var detail_credit in receiveDetails_credits)
                    {
                        var credit = credits.FirstOrDefault(p => p.BillCode == detail_credit.Code);
                        if (credit != null)
                        {
                            var abatementsAmount = abatements.Where(p => p.CreditBillCode == detail_credit.Code)
                                ?.Sum(p => p.Value);
                            var remainAmount = credit.Value - abatementsAmount;
                            if (remainAmount - detail_credit.Value < 0)
                            {
                                throw new ApplicationException($"操作失败，原因：应收单号【{detail_credit.Code}】余额不够");
                            }
                        }
                        else
                        {
                            throw new ApplicationException($"操作失败，原因：应收单号【{detail_credit.Code}】不存在应收");
                        }
                    }
                }

                var salesDetails = new List<SellsDetailOutputDto>();
                if (receiveDetails_orders != null && receiveDetails_orders.Any())
                {
                    var saleCodes = receiveDetails_orders.Select(p => p.Code).Distinct().ToList();
                    var saleRes = await _sellRecognizeApiClient.GetSalesDetailByCodes(saleCodes);
                    if (saleRes.Code == CodeStatusEnum.Success)
                    {
                        salesDetails = saleRes.Data.Data;
                        if (salesDetails.Where(p => string.IsNullOrEmpty(p.ProjectCode)).Count() > 0)
                        {
                            throw new ApplicationException($"单号：{string.Join(",", saleCodes)}，销售明细中存在项目信息为空的数据");
                        }
                    }
                    else
                    {
                        throw new ApplicationException("取销售订单明细计算项目金额时出错：" + saleRes.Message);
                    }

                    if (salesDetails.Select(p => p.BillCode).Distinct().Count() != entity.RecognizeReceiveDetails
                            .Where(p => p.Type == 2).Distinct().Count())
                    {
                        throw new ApplicationException("取到的订单个数与认款的订单个数不一致");
                    }

                    _logger.LogAzure("GoodsSubmit", $"生成认款推送销售:{entity.Code}", "生成认款推送销售");
                    var saleOut = await CreateSellRecognizeReceive(entity);//先推给销售
                    if (saleOut.Code != CodeStatusEnum.Success)
                    {
                        _logger.LogAzure("GoodsSubmit", $"生成认款明细失败:{saleOut.Message?.Replace("收款单位", "公司")}",
                            "生成认款推送销售失败");

                        throw new ApplicationException($"生成认款明细失败:{saleOut.Message?.Replace("收款单位", "公司")}");
                    }
                }

                #region 提交金蝶

                if (string.IsNullOrEmpty(entity.RelateCode))
                {
                    BaseResponseData<int> kdResult = await PushKindeeGoods(entity, salesDetails);
                    if (kdResult != null && kdResult.Code != CodeStatusEnum.Success)
                    {
                        if (receiveDetails_orders != null && receiveDetails_orders.Any()) //认款到订单，推送给销售
                        {
                            var orderNos = receiveDetails_orders.Select(x => x.Code).Distinct().ToList();
                            await Cancel(new CancelReceiptInput { RecognizeCode = entity.Code, SaleCodes = orderNos }, false);
                            needCancelSale = false;
                        }

                        _logger.LogAzure("GoodsSubmit", kdResult.Message, "生成认款推送金蝶失败");
                        throw new ApplicationException(kdResult.Message);
                    }
                }
                else
                {
                    BaseResponseData<int> kdResult = await SubmitChangeGoods(entity, salesDetails);
                    if (kdResult.Code != CodeStatusEnum.Success)
                    {
                        if (receiveDetails_orders != null && receiveDetails_orders.Any()) //有订单，推送给销售
                        {
                            var orderNos = receiveDetails_orders.Select(x => x.Code).ToList();
                            await Cancel(new CancelReceiptInput { RecognizeCode = entity.Code, SaleCodes = orderNos },
                                false);
                        }

                        _logger.LogAzure("GoodsSubmit-SubmitChangeGoods", kdResult.Message, "生成认款推送金蝶失败");

                        throw new ApplicationException(kdResult.Message);
                    }
                }

                #endregion

                //认发票
                if (receiveDetails_invoice != null && receiveDetails_invoice.Any())
                {
                    //推送spd
                    await PushBusiness(new RecognizeReceiveApproveInput { Code = entity.Code });
                }

                //完成认订单
                if (receiveDetails_orders != null && receiveDetails_orders.Any()) //全部是认款到订单
                {
                    _logger.LogAzure("GoodsSubmit", $"认款完成推送销售:{entity.Code}", "认款完成推送销售");
                    var saleRet = await _sellRecognizeApiClient.Finished(entity.Code);
                    //销售接口异常直接报错到SA不处理
                    if (saleRet.Code != CodeStatusEnum.Success)
                    {
                        _logger.LogAzure("GoodsSubmit", $"认款完成销售执行:{entity.Code}", "认款完成销售执行-失败", LogLevelEnum.Error);
                        await _edaFailureMsgClient.SendFailureMsg(new FailureMsgInput
                        {
                            AppId = Constant.AppCenter.Sell_APPID,
                            Topic = Constant.AppCenter.Sell_RecognizeFinished,
                            MsgBody = $"/api/Receipt/finished?recognizeCode={entity.Code}",
                            FailReason = saleRet.Message,
                            CallBackMethodRoute = $"/api/Receipt/finished?recognizeCode={entity.Code}",
                            ExceptionMessage = saleRet.Message
                        });


                    }
                }


                entity.Status = (int)RecognizeReceiveItemStatusEnum.Completed;
                entity.UpdatedTime = DateTime.Now;
                entity.UpdatedBy = createdBy;
                await _itemRepository.UpdateAsync(entity);
                await _unitOfWork.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                if (receiveDetails_orders != null && receiveDetails_orders.Any() && needCancelSale) //有订单，推送给销售
                {
                    var orderNos = receiveDetails_orders.Select(x => x.Code).ToList();
                    await Cancel(new CancelReceiptInput { RecognizeCode = entity.Code, SaleCodes = orderNos });
                }

                _logger.LogAzure("GoodsSubmit", $"{ex.Message}", "认款完成报错", Enums.LogLevelEnum.Error);
                //_logger.LogInformation($"{ex.Message}");
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 保存认款单-提交至金蝶
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="salesDetails"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> PushKindeeGoods(RecognizeReceiveItem entity, List<SellsDetailOutputDto> salesDetails)
        {
            var recognizeReceiveDetailCredits = await _db.RecognizeReceiveDetailCredits
                .Where(x => x.RecognizeReceiveItemId == entity.Id).AsNoTracking().ToListAsync();

            //生成单号
            var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            { ids = new List<string> { entity.CompanyId.ToString() } });
            var companyInfo = companyInfoOutput?.FirstOrDefault();
            if (companyInfo == null)
            {
                throw new AppServiceException("公司信息不存在");
            }
            var actualDate = await _companyDateService.GetActualDateAsync(entity.CompanyId);

            var kdInput = new BatchSaveAcceptanceInput
            {
                billno = entity.Code,
                jfzx_amountfield = entity.RecognizeReceiveDetails.Sum(p => p.Value),
                jfzx_gatheringamount = entity.ReceiveValue,
                jfzx_gatheringdate = entity.ReceiveDate.ToString("yyyy-MM-dd HH:mm:ss"),
                jfzx_gatheringnum = entity.ReceiveCode,
                jfzx_gatheringorg = companyInfo.nameCode,
                jfzx_payer = entity.CustomerId,
                jfzx_bizorg = entity.BusinessDepId.ToString(),
                billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                acceptanceEntrys = new List<AcceptanceEntrysItemInput> { }
            };
            if (entity.Type == "应付")
            {
                kdInput.billtype = "ap_finarbill";
            }


            //结算明细
            var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
            foreach (var detail in entity.RecognizeReceiveDetails)
            {
                var recognizeReceiveDetailCredit = recognizeReceiveDetailCredits
                    .Where(x => x.RecognizeReceiveDetailId == detail.Id && !string.IsNullOrEmpty(x.CreditCode)).ToList();
                var creditBillCodes = recognizeReceiveDetailCredit.Select(x => x.CreditCode).ToList();
                var credits = await _db.Credits.Where(x => creditBillCodes.ToHashSet().Contains(x.BillCode))
                    .AsNoTracking().ToListAsync();
                if (detail.Type == 2) //认款到订单的，需要按订单明细的项目号拆分金额
                {
                    if (recognizeReceiveDetailCredit != null && recognizeReceiveDetailCredit.Any())
                    {
                        //#120234 认款--认款单单头的项目不再按照订单项目的权重来给金蝶传认款明细的项目，取应收的项目
                        var orderNos = salesDetails.Where(p => p.BillCode == detail.Code.Trim()).Select(x => x.BillCode)
                            .ToList();
                        var allcurrentCredits = new List<CreditPo>();
                        var currentCreditByOrderNos = credits.Where(x => orderNos.Contains(x.OrderNo)).ToList();
                        allcurrentCredits.AddRange(currentCreditByOrderNos);
                        var currentCreditByRelateCodes = credits.Where(x => orderNos.Contains(x.RelateCode)).ToList();
                        allcurrentCredits.AddRange(currentCreditByRelateCodes);
                        allcurrentCredits = allcurrentCredits.DistinctBy(x => x.BillCode).ToList();
                        //判断应收是否为同一个项目
                        var projects = allcurrentCredits.GroupBy(x => x.ProjectCode).Select(x => new
                        {
                            ProjectCode = x.Key,
                            Credits = x.ToList(),
                        }).ToList();
                        foreach (var p in projects)
                        {
                            var currentCreditCodes = p.Credits.Select(x => x.BillCode).ToHashSet();
                            var currentRrdcs = recognizeReceiveDetailCredit.Where(x => currentCreditCodes.Contains(x.CreditCode)).ToList();
                            var currentSumValue = currentRrdcs.Sum(x => x.CurrentValue);
                            var kdDetail = new AcceptanceEntrysItemInput
                            {
                                jfzx_payee = detail.CreatedBy,
                                jfzx_remaker = detail.Note ?? "无",
                                jfzx_skipornot = detail.IsSkip,
                                jfzx_subscriptionamount = currentSumValue.HasValue ? currentSumValue.Value : 0,
                                jfzx_subscriptiondate = actualDate.ToString("yyyy-MM-dd HH:mm:ss"),
                                jfzx_subscriptiontype = "B",//2=B
                                jfzx_subscriptionnum = detail.Code,
                                jfzx_projectnos = p.ProjectCode,
                                jfzx_collectiontype = "100",//销售回款,
                                jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                                jfzx_customer = string.IsNullOrEmpty(detail.CustomerId) ? string.Empty : detail.CustomerId.ToUpper()
                            };
                            if (kdDetail.jfzx_subscriptionamount > 0)
                            {
                                kdInput.acceptanceEntrys.Add(kdDetail);
                            }
                        }

                        //foreach (var rrdc in recognizeReceiveDetailCredit)
                        //{
                        //    if (!string.IsNullOrEmpty(rrdc.CreditCode))
                        //    {
                        //        var credit = allcurrentCredits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                        //        if (credit == null)
                        //        {
                        //            continue;
                        //        }
                        //        var kdDetail = new AcceptanceEntrysItemInput
                        //        {
                        //            jfzx_payee = detail.CreatedBy,
                        //            jfzx_remaker = detail.Note ?? "无",
                        //            jfzx_skipornot = detail.IsSkip,
                        //            jfzx_subscriptionamount = rrdc.CurrentValue.HasValue ? rrdc.CurrentValue.Value : 0,
                        //            jfzx_subscriptiondate = detail.RecognizeDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        //            jfzx_subscriptiontype = "B",//2=B
                        //            jfzx_subscriptionnum = detail.Code,
                        //            jfzx_projectnos = credit.ProjectCode,
                        //            jfzx_collectiontype = "100",//销售回款,
                        //            jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                        //            jfzx_customer = string.IsNullOrEmpty(detail.CustomerId) ? string.Empty : detail.CustomerId.ToUpper()
                        //        };
                        //        if (kdDetail.jfzx_subscriptionamount > 0)
                        //        {
                        //            kdInput.acceptanceEntrys.Add(kdDetail);
                        //        }
                        //    }
                        //    else
                        //    {
                        //        var group = salesDetails.Where(p => p.BillCode == detail.Code.Trim()).GroupBy(p => p.ProjectCode);
                        //        var saleTotalAmount = salesDetails.Where(p => p.BillCode == detail.Code).Sum(q => q.Quantity * q.Price);
                        //        var index = 0;
                        //        var amountCount = 0.00m;
                        //        foreach (var g in group)
                        //        {
                        //            var kdDetail = new AcceptanceEntrysItemInput
                        //            {
                        //                jfzx_payee = detail.CreatedBy,
                        //                jfzx_remaker = detail.Note ?? "无",
                        //                jfzx_skipornot = detail.IsSkip,
                        //                //jfzx_subscriptionamount = detail.Value,
                        //                jfzx_subscriptiondate = detail.RecognizeDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        //                jfzx_subscriptiontype = "B",//2=B
                        //                jfzx_subscriptionnum = detail.Code,
                        //                jfzx_projectnos = g.Key,
                        //                jfzx_collectiontype = "100",//销售回款,
                        //                jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                        //                jfzx_customer = string.IsNullOrEmpty(detail.CustomerId) ? string.Empty : detail.CustomerId.ToUpper()
                        //            };
                        //            var thisAmount = g.Sum(q => q.Quantity * q.Price);
                        //            if (index == group.Count() - 1)//最后一条数据用减法，避免尾差
                        //            {
                        //                kdDetail.jfzx_subscriptionamount = detail.Value - amountCount;
                        //            }
                        //            else
                        //            {
                        //                var amount = Math.Round(thisAmount / saleTotalAmount * detail.Value, 2);
                        //                amountCount += amount;
                        //                kdDetail.jfzx_subscriptionamount = amount;
                        //            }
                        //            if (kdDetail.jfzx_subscriptionamount > 0)
                        //            {
                        //                kdInput.acceptanceEntrys.Add(kdDetail);
                        //            }
                        //            index++;
                        //        }
                        //        break;
                        //    }
                        //}
                    }
                    else
                    {

                        var group = salesDetails.Where(p => p.BillCode == detail.Code.Trim()).GroupBy(g => g.BillCode);
                        var saleTotalAmount = salesDetails.Where(p => p.BillCode == detail.Code).Sum(q => q.Quantity * q.Price);
                        var index = 0;
                        var amountCount = 0.00m;
                        foreach (var g in group)
                        {
                            var kdDetail = new AcceptanceEntrysItemInput
                            {
                                jfzx_payee = detail.CreatedBy,
                                jfzx_remaker = detail.Note ?? "无",
                                jfzx_skipornot = detail.IsSkip,
                                //jfzx_subscriptionamount = detail.Value,
                                jfzx_subscriptiondate = detail.RecognizeDate.ToString("yyyy-MM-dd HH:mm:ss"),
                                jfzx_subscriptiontype = "B",//2=B
                                jfzx_subscriptionnum = detail.Code,
                                jfzx_projectnos = g.FirstOrDefault().ProjectCode,
                                jfzx_collectiontype = "100",//销售回款,
                                jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                                jfzx_customer = string.IsNullOrEmpty(detail.CustomerId) ? string.Empty : detail.CustomerId.ToUpper()
                            };
                            var thisAmount = g.Sum(x => x.Quantity * x.Price);
                            if (index == group.Count() - 1)//最后一条数据用减法，避免尾差
                            {
                                kdDetail.jfzx_subscriptionamount = detail.Value - amountCount;
                            }
                            else
                            {
                                var amount = Math.Round(thisAmount / saleTotalAmount * detail.Value, 2);
                                amountCount += amount;
                                kdDetail.jfzx_subscriptionamount = amount;
                            }
                            if (kdDetail.jfzx_subscriptionamount > 0)
                            {
                                kdInput.acceptanceEntrys.Add(kdDetail);
                            }
                            index++;
                        }
                    }
                }
                else
                {
                    kdInput.acceptanceEntrys.Add(new AcceptanceEntrysItemInput
                    {
                        jfzx_payee = detail.CreatedBy,
                        jfzx_remaker = detail.Note ?? "无",
                        jfzx_skipornot = detail.IsSkip,
                        jfzx_subscriptionamount = detail.Value,
                        jfzx_subscriptiondate = actualDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        jfzx_subscriptiontype = detail.Type == 1 ? "A" : "C", //1=A，3=C
                        jfzx_subscriptionnum = detail.Code,
                        jfzx_collectiontype = "100", //销售回款,
                        jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                        jfzx_customer = string.IsNullOrEmpty(detail.CustomerId)
                            ? string.Empty
                            : detail.CustomerId.ToUpper().ToString()
                    });
                }

                var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                foreach (var rrdc in recognizeReceiveDetailCredit)
                {
                    if (string.IsNullOrEmpty(rrdc.CreditCode))
                    {
                        //预收的不传结算明细
                        continue;
                    }

                    var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                    var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                    settlementEntrie.receivableNumber = rrdc.CreditCode;
                    settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                    settlementEntrie.orderNumber = rrdc.OrderNo;
                    settlementEntrie.settleAmount = rrdc.CurrentValue;
                    settlementEntrie.projectsNumber = project != null ? project.ProjectCode : string.Empty;
                    settlementEntrie.revenueConfirm = false;
                    if (currentCredit != null)
                    {
                        settlementEntrie.projectsNumber = currentCredit.ProjectCode;
                        settlementEntrie.orderNumber = string.IsNullOrEmpty(settlementEntrie.orderNumber)
                            ? currentCredit.OrderNo
                            : settlementEntrie.orderNumber;
                    }

                    if (currentCredit != null && currentCredit.IsSureIncome.HasValue && currentCredit.IsSureIncome == 1)
                    {
                        settlementEntrie.revenueConfirm = true;
                    }

                    settlementEntries.Add(settlementEntrie);
                }
            }

            kdInput.settlementEntries = settlementEntries;
            var kdResult =
                await _kingdeeApiClient.PushBatchSaveAcceptances(new List<BatchSaveAcceptanceInput> { kdInput });
            return kdResult;
        }

        /// <summary>
        /// 收款调整单保存-提交至金蝶
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="salesDetails"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> SubmitChangeGoods(RecognizeReceiveItem entity,
            List<SellsDetailOutputDto> salesDetails)
        {
            var recognizeReceiveDetailCredits = await _db.RecognizeReceiveDetailCredits
                .Where(x => x.RecognizeReceiveItemId == entity.Id).AsNoTracking().ToListAsync();
            var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            { ids = new List<string> { entity.CompanyId.ToString() } });

            var actualDate = await _companyDateService.GetActualDateAsync(entity.CompanyId);
            var kdInput = new SavePaymentModificationInput
            {
                billno = entity.Code,
                jfzx_sourceorder = entity.RelateCode,
                jfzx_billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                jfzx_accbillno = entity.ReceiveCode,
                org = companyInfoOutput.First().nameCode,
                jfzx_adjustmentdate = actualDate.ToString("yyyy-MM-dd"), //entity.ReceiveDate,
                entryentity = new List<PaymentModificationEntryModel> { }
            };
            if (entity.Type == "应付")
            {
                kdInput.jfzx_billtype = "ap_finarbill";
            }

            //结算明细
            var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
            foreach (var detail in entity.RecognizeReceiveDetails)
            {
                if (detail.Type == 2) //认款到订单的，需要按订单明细的项目号拆分金额
                {
                    var group = salesDetails.Where(p => p.BillCode == detail.Code).GroupBy(p => p.ProjectCode);
                    var saleTotalAmount = salesDetails.Where(p => p.BillCode == detail.Code)
                        .Sum(q => q.Quantity * q.Price);
                    var index = 0;
                    var amountCount = 0.00m;
                    foreach (var g in group)
                    {
                        var kdDetail = new PaymentModificationEntryModel
                        {
                            jfzx_payee = detail.CreatedBy,
                            jfzx_subscriptiontype = "B", //2=B
                            jfzx_modnumber = detail.Code,
                            jfzx_remarks = detail.Note ?? "无",
                            jfzx_bizorg = entity.BusinessDepId.ToString(),
                            jfzx_project = g.Key,
                            jfzx_customer = string.IsNullOrEmpty(detail.CustomerId)
                                ? string.Empty
                                : detail.CustomerId.ToUpper(),
                            jfzx_receivingtype_number = "100", //销售回款,  
                            jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                        };
                        var thisAmount = g.Sum(q => q.Quantity * q.Price);
                        if (index == group.Count() - 1) //最后一条数据用减法，避免尾差
                        {
                            kdDetail.jfzx_adjustmentamounts = detail.Value - amountCount;
                        }
                        else
                        {
                            var amount = Math.Round(thisAmount / saleTotalAmount * detail.Value, 2);
                            amountCount += amount;
                            kdDetail.jfzx_adjustmentamounts = amount;
                        }

                        kdInput.entryentity.Add(kdDetail);
                        index++;
                    }
                }
                else
                {
                    kdInput.entryentity.Add(new PaymentModificationEntryModel
                    {
                        jfzx_payee = detail.CreatedBy,
                        jfzx_subscriptiontype = detail.Type == 1 ? "A" : "C", //1=A，3=C
                        jfzx_modnumber = detail.Code,
                        jfzx_remarks = detail.Note ?? "无",
                        jfzx_bizorg = entity.BusinessDepId.ToString(),
                        jfzx_adjustmentamounts = detail.Value,
                        jfzx_project = "",
                        jfzx_customer = string.IsNullOrEmpty(detail.CustomerId)
                            ? string.Empty
                            : detail.CustomerId.ToUpper(),
                        jfzx_receivingtype_number = "100", //销售回款, 
                        jfzx_goodspaymenttype = detail.Classify.HasValue ? ((int)detail.Classify).ToString() : "2",
                    });
                }

                var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                var recognizeReceiveDetailCredit = recognizeReceiveDetailCredits
                    .Where(x => x.RecognizeReceiveDetailId == detail.Id).ToList();
                var creditBillCodes = recognizeReceiveDetailCredit.Select(x => x.CreditCode).ToList();
                var credits = await _db.Credits.Where(x => creditBillCodes.ToHashSet().Contains(x.BillCode))
                    .AsNoTracking().ToListAsync();
                foreach (var rrdc in recognizeReceiveDetailCredit)
                {
                    if (string.IsNullOrEmpty(rrdc.CreditCode))
                    {
                        //预收的不传结算明细
                        continue;
                    }

                    var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                    var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                    settlementEntrie.receivableNumber = rrdc.CreditCode;
                    settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                    settlementEntrie.orderNumber = rrdc.OrderNo;
                    settlementEntrie.settleAmount = rrdc.CurrentValue;
                    settlementEntrie.projectsNumber = project != null ? project.ProjectCode : string.Empty;
                    settlementEntrie.revenueConfirm = false;
                    if (currentCredit != null)
                    {
                        settlementEntrie.projectsNumber = currentCredit.ProjectCode;
                        settlementEntrie.orderNumber = string.IsNullOrEmpty(settlementEntrie.orderNumber)
                            ? currentCredit.OrderNo
                            : settlementEntrie.orderNumber;
                    }

                    if (currentCredit != null && currentCredit.IsSureIncome.HasValue && currentCredit.IsSureIncome == 1)
                    {
                        settlementEntrie.revenueConfirm = true;
                    }

                    settlementEntries.Add(settlementEntrie);
                }
            }

            kdInput.settlementEntries = settlementEntries;
            var kdResult = await _kingdeeApiClient.SavePaymentModification(kdInput);
            return kdResult;
        }

        private async Task<BaseResponseData<bool?>> CreateSellRecognizeReceive(RecognizeReceiveItem entity)
        {
            var ret = BaseResponseData<bool?>.Success("操作成功！");
            var sellInput = entity.RecognizeReceiveDetails.Where(t => t.Type == (int)RecognizeTypeEnums.Orderno)
                .Select(t => new DTOs.Sell.SellRecognizeReceiveInput
                {
                    billCode = t.Code,
                    amount = t.Value,
                    receiptCode = entity.ReceiveCode,
                    companyId = entity.CompanyId,
                    customerId = t.CustomerId,
                    userName = t.CreatedBy ?? "none",
                    recognizeCode = entity.Code
                }).ToList();
            if (sellInput.Any())
            {
                var jsonStr = JsonConvert.SerializeObject(sellInput);
                ret = await _sellRecognizeApiClient.CreateSellRecognizeReceive(sellInput);
            }

            return ret;
        }

        /// <summary>
        /// 取消销售认款明细
        /// </summary>
        /// <param name="input">入参（针对明细撤销已做更改）</param>
        /// <param name="isCallBack">是否更改状态，默认true</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<int> Cancel(CancelReceiptInput input, bool? isCallBack = true)
        {
            try
            {
                if (input == null || string.IsNullOrEmpty(input.RecognizeCode))
                {
                    return -1;
                }
                var entity = await _itemRepository.GetWithNoTrackAsyncByReceiveCode(input.RecognizeCode);
                if (entity == null)
                {
                    return -1;
                }

                if (entity.RecognizeReceiveDetails != null && entity.RecognizeReceiveDetails.Any())
                {
                    var orders = entity.RecognizeReceiveDetails.Where(t => t.Type == (int)RecognizeTypeEnums.Orderno)
                        .ToList();
                    if (orders != null && orders.Any())
                    {
                        if (input.SaleCodes == null || !input.SaleCodes.Any())
                        {
                            input.SaleCodes = orders.Select(t => t.Code).ToList();
                        }

                        var saleInput = new List<CancelReceiptInput>
                        {
                            input
                        };
                        var saleOut = await _sellRecognizeApiClient.CancelSellRecognizeReceive(saleInput);
                        if (saleOut.Code != CodeStatusEnum.Success)
                        {
                            throw new ApplicationException($"撤销销售认款明细失败：{saleOut.Message}");
                        }
                    }
                }

                if (isCallBack == true)
                {
                    entity.Status = (int)RecognizeReceiveItemStatusEnum.WaitSubmit;
                    await _itemRepository.UpdateAsync(entity);
                    var res = await _unitOfWork.CommitAsync();
                    return res;
                }

                return 1;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"认款审核驳回，回调失败：{ex.Message}");
            }
        }

        public async Task<int> Approve(RecognizeReceiveApproveInput input)
        {
            try
            {
                var entity = await _itemRepository.GetWithNoTrackAsyncByReceiveCode(input.Code);
                if (entity == null)
                {
                    return -1;
                }

                if (entity.Status == (int)RecognizeReceiveItemStatusEnum.Completed)
                {
                    return 0;
                }

                entity.Status = (int)RecognizeReceiveItemStatusEnum.Completed;
                await _itemRepository.UpdateAsync(entity);
                var res = await _unitOfWork.CommitAsync();
                return 1;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"认款审核通过，回调失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 推送SPD商务平台收款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<SPDResponse> PushBusiness(RecognizeReceiveApproveInput input)
        {
            try
            {
                var entity = await _itemRepository.GetWithNoTrackAsyncByReceiveCode(input.Code);
                if (entity == null)
                {
                    // 无明细不推
                    return new SPDResponse
                    {
                        code = -1,
                        msg = "未找到收款单数据"
                    };
                }

                var details = await _db.RecognizeReceiveDetails.Where(x => x.RecognizeReceiveItemId == entity.Id)
                    .AsNoTracking().ToListAsync();
                if (details == null || !details.Any())
                {
                    // 无明细不推
                    return new SPDResponse
                    {
                        code = 0,
                        msg = "无认款明细数据不推送!"
                    };
                }

                var ret1 = await PushSPDBusinessMethod(input, entity);
                var ret2 = await PushSPDMethod(input, entity);
                string msg = "";
                if (ret1.code != 0)
                {
                    msg = "推送至SPD商务平台失败：" + ret1.msg;
                }

                if (ret2.Code != CodeStatusEnum.Success)
                {
                    msg += ",推送至SPD失败：" + ret2.Message;
                }

                if (!string.IsNullOrEmpty(msg))
                {
                    return new SPDResponse
                    {
                        code = -1,
                        msg = msg
                    };
                }
                else
                {
                    return new SPDResponse
                    {
                        code = 0,
                        msg = "操作成功"
                    };
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"认款审核通过，推送SPD商务平台收款数据失败：{ex.Message}");
            }
        }

        private async Task<BaseResponseData<string>> PushSPDMethod(RecognizeReceiveApproveInput input,
            RecognizeReceiveItem entity)
        {
            var spdRet = BaseResponseData<string>.Success("操作成功");

            #region 推送SPD入参

            var spdInput = new RecognizeReceivePushSPDInput();
            spdInput.CompanyId = Guid.Parse(entity.CompanyId);
            spdInput.CustomerId = Guid.Parse(entity.CustomerId);
            // 认款到SPD发票
            var detailsBySPDInvoice = await (from rri in _db.RecognizeReceiveItems
                                             join rrd in _db.RecognizeReceiveDetails on rri.Id equals rrd.RecognizeReceiveItemId
                                             join ic in _db.InvoiceCredits on rrd.Code equals ic.InvoiceNo
                                             join c in _db.Credits on ic.CreditId equals c.Id
                                             where
                                                 rri.CompanyName == "上海建发鹭益科技有限公司"
                                                 && rrd.CustomerNme == "上海交通大学医学院附属新华医院"
                                                 && rrd.Type == 1 && rri.Code == input.Code
                                                 && c.SaleSource == SaleSourceEnum.Spd
                                             select new BillDetailToSPD
                                             {
                                                 invoiceNum = rrd.Code,
                                                 invoiceAmount = rrd.Value.ToString(),
                                                 remitTime = rri.ReceiveDate
                                             }).Distinct().ToListAsync();
            if (detailsBySPDInvoice.Any())
            {
                var jsonStr = "";
                try
                {
                    spdInput.list = detailsBySPDInvoice;
                    // 推送收款单发票数据至SPD
                    jsonStr = JsonConvert.SerializeObject(spdInput);
                    spdRet = await _iICApiClient.PushRecognizeReceive(spdInput);
                    if (spdRet.Code != CodeStatusEnum.Success)
                    {
                        // 写入重试队列
                        await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                        {
                            AppId = "finance-webapi",
                            MsgBody = jsonStr,
                            Topic = "finance-finance-pushRecognizeReceive",
                            FailReason = spdRet.Message,
                            ExceptionMessage = JsonConvert.SerializeObject(spdRet.Message),
                            CallBackMethodRoute = "/api/SellSub/PushRecognizeReceive" //重试的回调方法路由 
                        });
                    }
                }
                catch (Exception ex)
                {
                    spdRet = BaseResponseData<string>.Failed(500, ex.Message);
                    // 写入重试队列
                    await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    {
                        AppId = "finance-webapi",
                        MsgBody = jsonStr,
                        Topic = "finance-finance-pushRecognizeReceive",
                        FailReason = ex.Message,
                        ExceptionMessage = JsonConvert.SerializeObject(ex),
                        CallBackMethodRoute = "/api/SellSub/PushRecognizeReceive" //重试的回调方法路由 
                    });
                }
            }

            #endregion


            return spdRet;
        }

        private async Task<SPDResponse> PushSPDBusinessMethod(RecognizeReceiveApproveInput input,
            RecognizeReceiveItem entity)
        {
            var ret = new SPDResponse { code = 0, msg = "操作成功" };
            try
            {
                #region 推送商务平台入参

                var pushInput = new RecognizeReceivePushBusinessInput();
                pushInput.confirmCode = entity.Code;
                pushInput.relateCode = entity.ReceiveCode;
                pushInput.amount = Convert.ToDouble(entity.ReceiveValue);
                pushInput.receiveTime = entity.ReceiveDate;
                pushInput.Remark = "核心平台推送";

                // 认款到发票
                var list = await (from rri in _db.RecognizeReceiveItems
                                  join rrd in _db.RecognizeReceiveDetails on rri.Id equals rrd.RecognizeReceiveItemId
                                  join ic in _db.InvoiceCredits on rrd.Code equals ic.InvoiceNo
                                  join c in _db.Credits on ic.CreditId equals c.Id
                                  where c.SaleSource == SaleSourceEnum.Spd && rrd.Type == 1 && rri.Code == input.Code
                                  select new BillReceiveInvoiceRel
                                  {
                                      invoiceNum = rrd.Code,
                                      receiveInvoiceAmount = Convert.ToDouble(rrd.Value)
                                  }).Distinct().AsNoTracking().ToListAsync();
                if (list != null && list.Any())
                {
                    var groupedItems = list.GroupBy(item => item.invoiceNum) // 按invoiceNum分组
                        .Select(group => new BillReceiveInvoiceRel
                        {
                            invoiceNum = group.Key,
                            receiveInvoiceAmount = group.Sum(item => item.receiveInvoiceAmount)
                        }).ToList();
                    pushInput.billReceiveInvoiceRelList = groupedItems;
                    ret = await _sPDApiClient.synReceiveAndInvoice(pushInput);
                }

                #endregion
            }
            catch (Exception ex)
            {
                ret.msg = ex.Message;
            }

            return ret;
        }

        #region 附件

        /// <summary>
        /// 上传附件
        /// </summary>
        public async Task<BaseResponseData<int>> AttachFileIds(RecognizeItemAttachFileInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");

            var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
            item.AttachFileIds = item.AttachFileIds + "," + input.AttachFileIds;
            await _itemRepository.UpdateAsync(item);
            ret.Data = await _unitOfWork.CommitAsync();
            if (ret.Data <= 0)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败！请再次操作，谢谢！");
            }

            return ret;
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(RecognizeItemAttachFileInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var item = await _itemRepository.GetWithNoTrackAsync(input.RecognizeReceiveItemId);
            if (item.Status != 0)
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败！草稿状态才能删除附件");
                return ret;
            }

            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(item.AttachFileIds))
            {
                foreach (var fildId in item.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }

            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            item.AttachFileIds = newAttachFileIds;
            await _itemRepository.UpdateAsync(item);
            var retTemp = await _unitOfWork.CommitAsync();
            if (retTemp <= 0)
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败！请再次操作，谢谢！");
            }

            ret.Data = newAttachFileIds;
            return ret;
        }

        /// <summary>
        /// 撤销认款
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceive(Guid itemId, string userName)
        {
            try
            {
                var item = await _itemRepository.GetWithNoTrackAsync(itemId);
                if (item == null || item.Status != 99)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单不存在或该状态无法撤销认款");
                }

                if (item.CreatedBy != userName)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：非创建人无法撤销认款");
                }

                if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
                {
                    var goods = await _receiveItemQueryService.FirstOrDefaultAsync(p => p.RelateCode == item.Code);
                    if (goods != null)
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：该暂收款下面存在货款无法撤销认款");
                    }
                }

                // 获取系统月度
                var sysMonth = await _bDSApiClient.GetSystemMonth(item.CompanyId);
                sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
                // 获取认款单业务日期月度
                //var currentMonth = item.BillDate.ToString("yyyy-MM");
                //if (currentMonth != sysMonth)
                //{
                //    return BaseResponseData<int>.Failed(500, "操作失败，原因：不能跨月撤销");
                //}
                var companyId = Guid.Parse(item.CompanyId);
                var query = _db.InventoryItem.Where(p => p.CompanyId == companyId && p.Status == 2);
                var itemPo = await query.FirstOrDefaultAsync();
                if (itemPo != null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：盘点期间不能提交撤销");
                }

                await CheckSysMonth(companyId, sysMonth);

                var details = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveDetailCredits).Where(p => p.RecognizeReceiveItemId == itemId).AsNoTracking().ToListAsync();
                if (details != null)
                {
                    if (details.Count() == details.Count(p => p.Type == 2)) //撤销认款到订单
                    {
                        return await CancelReceiveOrder(item, details);
                    }
                    else if (details.Count() == details.Count(p => p.Type == 1)) //撤销认款到发票
                    {
                        return await CancelReceiveInvoice(item, details);
                    }
                    else if (details.Count() == details.Count(p => p.Type == 3)) //撤销认款到初始应收
                    {
                        return await CancelReceiveCredit(item, details);
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：不支持认款到此类型的撤销操作！");
                    }
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单明细不存！");
                }
            }
            catch (Exception e)
            {
                _logger.LogAzure("CancelReceive", $"{itemId}撤销认款异常:{e.Message}详情{e.StackTrace}", "撤销认款");
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + e.Message);
            }
        }

        /// <summary>
        /// 验证订单是否可以撤销认款
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> VerifyCancelReceiveByOrderNo(string orderNo)
        {
            try
            {
                //根据订单号获取所有存在该订单认款已完成的数据
                var rris = await _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(x =>
                        x.Code == orderNo &&
                        (x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Completed ||
                         x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.PartCanceled))
                    .Select(x => x.RecognizeReceiveItem).AsNoTracking().ToListAsync();
                if (rris == null || !rris.Any())
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败，原因：订单{orderNo}未关联认款单或没有完成的认款");
                }

                //校验是否存在盘点期间的认款
                var companyIds = rris.Select(x => Guid.Parse(x.CompanyId)).ToList();
                var inventoryItems = await _db.InventoryItem
                    .Where(x => companyIds.Any(p => p == x.CompanyId) && x.Status == 2).AsNoTracking().ToListAsync();
                foreach (var rri in rris)
                {
                    var companyId = Guid.Parse(rri.CompanyId);
                    var inventoryItem = inventoryItems.FirstOrDefault(x => x.CompanyId == companyId);
                    if (inventoryItem != null)
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：订单{orderNo}关联的认款单{rri.Code}正在盘点，不能提交撤销");
                    }

                    #region CheckSysMonth

                    //获取系统月度
                    var sysMonth = await _bDSApiClient.GetSystemMonth(rri.CompanyId);
                    sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
                    var currentMonth = DateTime.Now.ToString("yyyy-MM");
                    if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
                    {
                        var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t =>
                            t.SysMonth == sysMonth && t.CompanyId == companyId);
                        if (inventory != null)
                        {
                            if (inventory.Status == 2)
                            {
                                return BaseResponseData<int>.Failed(500,
                                    $"操作失败，原因：订单{orderNo}关联的认款单{rri.Code}正在盘点，不能提交撤销");
                            }

                            if (inventory.Status == 99)
                            {
                                DateTime.TryParse(sysMonth, out DateTime billDate);
                                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                                {
                                    return BaseResponseData<int>.Failed(500,
                                        $"操作失败，原因：订单{orderNo}关联的认款单{rri.Code}公司已完成本月度盘点，不允许操作，请下个月操作");
                                }
                            }
                        }
                    }
                    else
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            return BaseResponseData<int>.Failed(500,
                                $"操作失败，原因：订单{orderNo}关联的认款单{rri.Code}公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }

                    #endregion
                }

                //查询上述范围认款单是否存在其它订单认款
                var rriIds = rris.Select(x => x.Id).Distinct().ToList();
                var rrds = await _db.RecognizeReceiveDetails.Where(x => rriIds.Any(p => p == x.RecognizeReceiveItemId))
                    .AsNoTracking().ToListAsync();
                var orderNos = rrds.Select(x => x.Code).ToList();
                //调用销售接口查询
                if (orderNos != null && orderNos.Count > 0)
                {
                    return BaseResponseData<int>.Success("校验成功");
                    //var saleAdvancePeriodInfo = await _sellApiClient.GetSaleAdvancePeriodInfo(new SaleAdvancePeriodInfoInput() { BillCodes = orderNos });
                    //if (saleAdvancePeriodInfo == null || saleAdvancePeriodInfo.Count == 0)
                    //{
                    //    return BaseResponseData<int>.Failed(500, $"操作失败，原因：没有获取到销售订单预收账期信息!");
                    //}
                    //var hasAdvancePeriodList = saleAdvancePeriodInfo.Where(x => x.HasAdvancePeriod).ToList();
                    //if (hasAdvancePeriodList.Count() > 1)
                    //{
                    //    return BaseResponseData<int>.Failed(500, $"操作失败，原因：对应认款单下存在多个预收订单，无法撤销!");
                    //}
                    //if (hasAdvancePeriodList.Count() == 1 && hasAdvancePeriodList[0].BillCode == orderNo)
                    //{
                    //    return BaseResponseData<int>.Success("校验成功");
                    //}
                    //if (hasAdvancePeriodList.Count() == 1 && hasAdvancePeriodList[0].BillCode != orderNo)
                    //{
                    //    return BaseResponseData<int>.Success("操作失败，原因：对应认款单下存在一个预收订单但不是需要撤销的订单，无法撤销!");
                    //}
                    //if (hasAdvancePeriodList == null || !hasAdvancePeriodList.Any())
                    //{
                    //    return BaseResponseData<int>.Success("校验成功");
                    //}
                    //return BaseResponseData<int>.Success("操作失败，原因：校验失败");
                }

                return BaseResponseData<int>.Success("操作失败，原因：未找到收款单对应的订单!");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 根据订单号撤销认款
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveByOrderNo(string orderNo)
        {
            try
            {
                var check = await VerifyCancelReceiveByOrderNo(orderNo);
                if (check.Code != CodeStatusEnum.Success)
                {
                    //await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    //{
                    //    AppId = "finance-webapi",
                    //    MsgBody = orderNo,
                    //    Topic = "sale-fin-cancelReceiveByOrderNo",
                    //    FailReason = check.Message,
                    //    ExceptionMessage = JsonConvert.SerializeObject(check),
                    //    CallBackMethodRoute = "/api/SellSub/CancelReceiveByOrderNo?orderNo=" + orderNo  //重试的回调方法路由 
                    //});
                    return check;
                }

                //金蝶入参
                var kdInput = new List<QuashAcceptancesRequestVo>();
                var orderNos = new List<string> { orderNo };
                var saleAdvancePeriodInfo =
                    await _sellApiClient.GetSaleAdvancePeriodInfo(new SaleAdvancePeriodInfoInput()
                    { BillCodes = orderNos });
                if (saleAdvancePeriodInfo == null || saleAdvancePeriodInfo.Count == 0)
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败，原因：没有获取到销售订单预收账期信息!");
                }

                foreach (var saleItem in saleAdvancePeriodInfo)
                {
                    if (saleItem.SaleType != SaleTypeEnum.ServiceFee && saleItem.HasAdvancePeriod &&
                        (saleItem.Status != SaleStatusEnum.Rejected && saleItem.Status != SaleStatusEnum.Receivable))
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：预收款销售订单已流转至仓库出库，请先驳回出库单据才能撤销此认款!");
                    }
                }

                //根据订单号获取所有存在该订单认款已完成的数据
                var rris = await _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(x =>
                        x.Code == orderNo &&
                        (x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Completed ||
                         x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.PartCanceled))
                    .Select(x => x.RecognizeReceiveItem).AsNoTracking().ToListAsync();
                var rriIds = rris.Select(x => x.Id).Distinct().ToList();
                var rrds = await _db.RecognizeReceiveDetails.Where(x => rriIds.Any(p => p == x.RecognizeReceiveItemId))
                    .ToListAsync();
                var rrdcs = await _db.RecognizeReceiveDetailCredits
                    .Where(x => rriIds.Any(p => p == x.RecognizeReceiveItemId)).ToListAsync();

                var credits = await _db.Credits.Where(p => orderNos.ToHashSet().Contains(p.OrderNo) && p.AutoType != "98").AsNoTracking().ToListAsync();
                var credits2 = await _db.Credits.Where(p => orderNos.ToHashSet().Contains(p.RelateCode) && p.AutoType != "98").AsNoTracking().ToListAsync();
                credits.AddRange(credits2);
                credits = credits.DistinctBy(p => p.BillCode).ToList();
                if (credits.Any())
                {
                    foreach (var credit in credits)
                    {
                        credit.AbatedStatus = AbatedStatusEnum.NonAbate;
                    }

                    _db.Credits.UpdateRange(credits);
                }

                var creditNos = credits.Select(p => p.BillCode).Distinct().ToList();
                if (creditNos != null && creditNos.Count > 0)
                {
                    //损失确认申请--在途的损失确认单中的应收不允许撤销认款
                    var lossHasValue = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => creditNos.ToHashSet().Contains(x.BillCode) && x.LossRecognitionItem.Status != StatusEnum.Complate && x.LossRecognitionItem.Status != StatusEnum.Refuse).AsNoTracking().ToListAsync();
                    if (lossHasValue != null && lossHasValue.Any())
                    {
                        var errCreditCodeStr = string.Join(",", lossHasValue.Select(x => x.BillCode).ToList());
                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：应收单“{errCreditCodeStr}”在正在进行损失确认，无法撤销认款");
                    }
                }

                #region 查找冲销记录

                var recognizeReceiveCodes = kdInput.Select(x => x.billNo).Distinct().ToList();
                var receiceCodes = rris.Where(x => !string.IsNullOrEmpty(x.ReceiveCode)).Select(x => x.ReceiveCode)
                    .Distinct().ToList();
                if (recognizeReceiveCodes.Any())
                {
                    //根据认款单号查询冲销记录
                    var abas = await _db.Abatements.Where(p => recognizeReceiveCodes.ToHashSet().Contains(p.RecognizeReceiveCode)).ToListAsync();
                    if (abas.Any())
                    {
                        if (creditNos != null && creditNos.Any())
                        {
                            var currentAbasByCreditCode = abas.Where(x => creditNos.ToHashSet().Contains(x.DebtBillCode)).ToList();
                            _db.Abatements.RemoveRange(currentAbasByCreditCode);
                        }
                    }
                    else
                    {
                        if (creditNos != null && creditNos.Any())
                        {
                            //没有记录收款单号，则根据认款单对应的收款单号+应收单号+金额去匹配
                            abas = await _db.Abatements.Where(p => receiceCodes.ToHashSet().Contains(p.CreditBillCode) && p.CreditType == "receive" && p.DebtType == "credit").ToListAsync();
                            if (abas.Any())
                            {
                                var currentAbasByCreditCode = new List<AbatementPo>();
                                foreach (var credit in credits)
                                {
                                    var many = abas
                                        .Where(x => x.DebtBillCode == credit.BillCode && x.Value == credit.Value)
                                        .OrderByDescending(x => x.CreatedTime).ToList();
                                    //如果收款+应收+金额也是多条，按照时间顺序匹配，先匹配时间近的
                                    if (many.Count > 0)
                                    {
                                        currentAbasByCreditCode.Add(many.First());
                                    }
                                }

                                _db.Abatements.RemoveRange(currentAbasByCreditCode);
                            }
                        }
                    }
                }

                #endregion

                #region 查询账期数据

                var creditIds = credits.Select(p => p.Id).Distinct().ToList();
                var allowAccountPeriodType = new List<int> { 0, 2 };
                var needDebtDetails = new List<DebtDetailPo>();
                var debtDetails = await _db.DebtDetails.Where(
                    x => x.OrderNo == orderNo &&
                    receiceCodes.ToHashSet().Contains(x.ReceiveCode) &&
                    allowAccountPeriodType.ToHashSet().Contains(x.AccountPeriodType) &&
                    x.Status != DebtDetailStatusEnum.Completed).AsNoTracking().ToListAsync();
                foreach (var debtDetail in debtDetails)
                {
                    if (string.IsNullOrEmpty(debtDetail.RecognizeReceiveCode) || recognizeReceiveCodes
                            .Where(x => x == debtDetail.RecognizeReceiveCode).Count() > 0)
                    {
                        needDebtDetails.Add(debtDetail);
                    }
                }

                var debtDetailIds = needDebtDetails.Select(p => p.Id).ToList();
                var paymentAutoItemCodes = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                    .Where(p => debtDetailIds.Contains(p.DebtDetilId)).Select(p => p.PaymentAutoItem.Code)
                    .ToListAsync();
                if (paymentAutoItemCodes.Any())
                {
                    return BaseResponseData<int>.Failed(500,
                        $"操作失败，原因：操作失败，原因：应收对应的应付明细，已经在批量付款单【{string.Join(",", paymentAutoItemCodes)}】中存在！");
                }

                #endregion

                var companyIdToDateMap = new Dictionary<string, DateTime>();
                foreach (var rri in rris)
                {
                    // 使用缓存中的实际日期
                    if (!companyIdToDateMap.TryGetValue(rri.CompanyId, out var actualDate))
                    {
                        actualDate = await _companyDateService.GetActualDateAsync(rri.CompanyId);
                        companyIdToDateMap[rri.CompanyId] = actualDate;
                    }
                    //认款单明细总数
                    var totalRrds = rrds.Where(x => x.RecognizeReceiveItemId == rri.Id).ToList();
                    //本次撤销的认款明细
                    var currentRrds = rrds.Where(x => x.RecognizeReceiveItemId == rri.Id && x.Code == orderNo).ToList();
                    var item = rri.Adapt<RecognizeReceiveItem>();
                    kdInput.Add(new QuashAcceptancesRequestVo
                    {
                        billNo = rri.Code,
                        subscriptionType = "B",
                        subscriptionNumList = new List<string> { orderNo },
                        ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    });

                    #region 处理账期数据

                    // 根据DebtId分组
                    var currentNeedDebtDetails =
                        needDebtDetails.Where(x => x.RecognizeReceiveCode == item.Code).ToList();
                    var groupByDebtIdDetails = currentNeedDebtDetails.GroupBy(x => new { x.DebtId })
                        .Select(group => new
                        {
                            DebtId = group.Key.DebtId,
                            Details = group.ToList()
                        })
                        .ToList();
                    // 查询该id下是否为空收款单号的其它应付明细一起合并
                    var debtIds = groupByDebtIdDetails.Select(x => x.DebtId).ToList();
                    //没有认款的
                    var otherDebtDetails = await _db.DebtDetails.Where(x =>
                        x.DebtId.HasValue &&
                        debtIds.Contains(x.DebtId.Value) &&
                        !debtDetailIds.Contains(x.Id) &&
                        allowAccountPeriodType.Contains(x.AccountPeriodType) &&
                        (x.AccountPeriodType == 2
                            ? string.IsNullOrEmpty(x.ReceiveCode)
                            : !x.ProbablyPayTime.HasValue) &&
                        x.CreditId.HasValue &&
                        x.Status != DebtDetailStatusEnum.Completed).ToListAsync();

                    var otherDebtDetailIds = otherDebtDetails.Select(p => p.Id);
                    var paymentAutoDetailIds = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                        .Where(p => otherDebtDetailIds.Contains(p.DebtDetilId) &&
                                    p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed)
                        .Select(p => p.DebtDetilId).ToListAsync();
                    otherDebtDetails = otherDebtDetails.Where(p => !paymentAutoDetailIds.Contains(p.Id)).ToList();

                    foreach (var debt in groupByDebtIdDetails)
                    {
                        var others = otherDebtDetails.Where(x => x.DebtId.Value == debt.DebtId).ToList();

                        // 根据AccountPeriodType分组
                        var groupByAccountPeriodTypeDebtDetails = debt.Details
                            .GroupBy(x => new { x.AccountPeriodType, x.CreditId, x.CostDiscount }).Select(p => new
                            {
                                AccountPeriodType = p.Key.AccountPeriodType,
                                CreditId = p.Key.CreditId,
                                CostDiscount = p.Key.CostDiscount,
                                Details = p.ToList()
                            }).ToList();

                        // 单个类型
                        foreach (var groupByAccountPeriodType in groupByAccountPeriodTypeDebtDetails)
                        {
                            if (groupByAccountPeriodType.Details != null && groupByAccountPeriodType.Details.Any())
                            {
                                var tempAddDetails = others.Where(p =>
                                    p.AccountPeriodType == groupByAccountPeriodType.AccountPeriodType &&
                                    p.CostDiscount == groupByAccountPeriodType.CostDiscount &&
                                    p.CreditId == groupByAccountPeriodType.CreditId).ToList();

                                groupByAccountPeriodType.Details.AddRange(tempAddDetails);
                                // 1.有销售账期（1），也有回款账期(1),只需要清空收款单号，和（回款账期）预计付款日期
                                if (groupByAccountPeriodType.Details.Count == 1)
                                {
                                    groupByAccountPeriodType.Details[0].ReceiveCode = null;
                                    groupByAccountPeriodType.Details[0].RecognizeReceiveCode = null;
                                    if (groupByAccountPeriodType.Details[0].AccountPeriodType == 0)
                                    {
                                        groupByAccountPeriodType.Details[0].BackPayTime = null;
                                        groupByAccountPeriodType.Details[0].ProbablyPayTime = null;
                                        groupByAccountPeriodType.Details[0].Settletype = null;
                                        groupByAccountPeriodType.Details[0].DraftBillExpireDate = null;
                                    }

                                    _db.DebtDetails.Update(groupByAccountPeriodType.Details[0]);
                                }
                                else
                                {
                                    // 深拷贝对象，方便合并
                                    var single = JsonConvert.DeserializeObject<DebtDetailPo>(
                                        JsonConvert.SerializeObject(groupByAccountPeriodType.Details
                                            .OrderByDescending(p => p.CostDiscount).FirstOrDefault()));
                                    if (single == null)
                                    {
                                        continue;
                                    }

                                    var debtDetailIds_temp = groupByAccountPeriodType.Details.Select(p => p.Id);
                                    var detailAuditPos = await _db.DebtDetailAudit
                                        .Where(p => debtDetailIds_temp.Contains(p.DebtDetailId.Value)).ToListAsync();
                                    _db.DebtDetailAudit.RemoveRange(detailAuditPos);
                                    // 删除明细
                                    _db.DebtDetails.RemoveRange(groupByAccountPeriodType.Details);
                                    // 已经是同一账期，执行 清行空收款单号且合并
                                    single.Value = groupByAccountPeriodType.Details.Sum(x => x.Value);
                                    if (single.CostDiscount.HasValue)
                                    {
                                        if (groupByAccountPeriodType.Details[0].CostDiscount == 0)
                                        {
                                            single.OriginValue = null;
                                        }
                                        else
                                        {
                                            single.OriginValue = single.Value / single.CostDiscount * 100;
                                        }
                                    }

                                    if (single.AccountPeriodType == 0)
                                    {
                                        single.BackPayTime = null;
                                        single.ProbablyPayTime = null;
                                        single.Settletype = null;
                                        single.DraftBillExpireDate = null;
                                    }

                                    single.ReceiveCode = null;
                                    single.RecognizeReceiveCode = null;
                                    _db.DebtDetails.Add(single);
                                }
                            }
                        }
                    }

                    #endregion

                    if (totalRrds.Count() ==
                        rrds.Where(x =>
                                x.RecognizeReceiveItemId == rri.Id && x.Status == RecognizeReceiveDetailEnum.Cancel)
                            .Count() + currentRrds.Count())
                    {
                        //完全撤销认款
                        item.Status = -1;
                    }
                    else
                    {
                        //部分撤销认款
                        item.Status = -2;
                    }

                    item.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                    item.UpdatedTime = DateTime.Now;
                    await _itemRepository.UpdateAsync(item);
                    //删除认款明细应收记录
                    var delRrdcs = rrdcs.Where(x => x.RecognizeReceiveItemId == item.Id && currentRrds.Select(x => x.Id).ToHashSet().Contains(x.RecognizeReceiveDetailId)).ToList();
                    if (delRrdcs != null && delRrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.RemoveRange(delRrdcs);
                    }

                    var updDetails = rrds.Where(x => x.RecognizeReceiveItemId == item.Id && x.Code == orderNo).ToList();
                    updDetails.ForEach(x =>
                    {
                        x.Status = RecognizeReceiveDetailEnum.Cancel;
                        x.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                        x.UpdatedTime = DateTime.Now;
                    });
                }

                if (kdInput.Any())
                {
                    var ret = await _kingdeeApiClient.CancelReceive(kdInput);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        var input = new CancelReceiveByOrderNoInput()
                        {
                            orderNo = orderNo
                        };
                        var jsonStr = JsonConvert.SerializeObject(input);
                        await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                        {
                            AppId = "finance-webapi",
                            MsgBody = jsonStr,
                            Topic = "sale-fin-cancelReceiveByOrderNo",
                            FailReason = ret.Message,
                            ExceptionMessage = JsonConvert.SerializeObject(ret),
                            CallBackMethodRoute = "/api/SellSub/CancelReceiveByOrderNo" //重试的回调方法路由 
                        });
                        return BaseResponseData<int>.Success($"【金蝶】操作失败:{ret.Message}");
                    }
                    else
                    {
                        await _unitOfWork.CommitAsync();
                        return BaseResponseData<int>.Success("操作成功");
                    }
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，无参数");
                }
            }
            catch (Exception e)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + e.Message);
            }
        }

        /// <summary>
        /// 撤销认款到订单
        /// </summary>
        /// <param name="item"></param>
        /// <param name="details"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CancelReceiveOrder(RecognizeReceiveItem item,
            List<RecognizeReceiveDetailPo>? details)
        {
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var orderNos = details.Select(p => p.Code).ToList();
            if (orderNos != null && orderNos.Count > 0)
            {
                var saleAdvancePeriodInfo =
                    await _sellApiClient.GetSaleAdvancePeriodInfo(new SaleAdvancePeriodInfoInput()
                    { BillCodes = orderNos });
                if (saleAdvancePeriodInfo == null || saleAdvancePeriodInfo.Count == 0)
                {
                    ret.Message = $"操作失败，原因：没有获取到销售订单预收账期信息!";
                    return ret;
                }

                foreach (var saleItem in saleAdvancePeriodInfo)
                {
                    if (saleItem.SaleType != SaleTypeEnum.ServiceFee && saleItem.HasAdvancePeriod &&
                        (saleItem.Status != SaleStatusEnum.Rejected && saleItem.Status != SaleStatusEnum.Receivable))
                    {
                        ret.Message = $"操作失败，原因：预收款销售订单已流转至仓库出库，请先驳回出库单据才能撤销此认款!";
                        return ret;
                    }
                }
            }

            if (orderNos == null || orderNos.Count == 0)
            {
                ret.Message = $"操作失败，原因：未获取到订单号!";
                return ret;
            }

            //var credits = await _db.Credits.Where(p => orderNos.Contains(p.OrderNo) || orderNos.Contains(p.RelateCode)).AsNoTracking().ToListAsync();
            var credits = await _db.Credits.Where(p => orderNos.ToHashSet().Contains(p.OrderNo) && p.AutoType != "98").AsNoTracking().ToListAsync();
            var credits2 = await _db.Credits.Where(p => orderNos.ToHashSet().Contains(p.RelateCode) && p.AutoType != "98").AsNoTracking().ToListAsync();
            credits.AddRange(credits2);
            credits = credits.DistinctBy(p => p.BillCode).ToList();

            var creditNos = credits.Select(p => p.BillCode).Distinct().ToList();
            if (creditNos != null && creditNos.Count > 0)
            {
                //损失确认申请--在途的损失确认单中的应收不允许撤销认款
                var lossHasValue = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => creditNos.ToHashSet().Contains(x.BillCode) && x.LossRecognitionItem.Status != StatusEnum.Complate && x.LossRecognitionItem.Status != StatusEnum.Refuse).AsNoTracking().ToListAsync();
                if (lossHasValue != null && lossHasValue.Any())
                {
                    var errCreditCodeStr = string.Join(",", lossHasValue.Select(x => x.BillCode).ToList());
                    ret.Message = $"应收单“{errCreditCodeStr}”在正在进行损失确认，无法撤销认款";
                    return ret;
                }
            }

            var creditIds = credits.Select(p => p.Id).Distinct().ToList();
            var needAbatements = await _db.Abatements.Where(p => p.RecognizeReceiveCode == item.Code).ToListAsync();
            if (needAbatements.Any())
            {
                needAbatements = creditNos != null && creditNos.Any()
                    ? needAbatements.Where(x => creditNos.ToHashSet().Contains(x.DebtBillCode)).ToList()
                    : needAbatements;
                var abatementSum = needAbatements.Sum(p => p.Value);
                var cancelSum = details.Sum(p => p.Value);

                if (abatementSum < cancelSum)
                {
                    var excludedCreditNos = creditNos?.Except(needAbatements.Select(o => o.DebtBillCode)).ToList();
                    var additionalAbatements = await _db.Abatements
                        .Where(p => string.IsNullOrEmpty(p.RecognizeReceiveCode) &&
                                    p.CreditBillCode == item.ReceiveCode &&
                                    (excludedCreditNos != null && excludedCreditNos.Any()
                                        ? excludedCreditNos.Contains(p.DebtBillCode)
                                        : true))
                        .AsNoTracking()
                        .ToListAsync();

                    foreach (var detail in details.SelectMany(d => d.RecognizeReceiveDetailCredits, (d, c) => new { Detail = d, Credit = c }))
                    {
                        var credit = credits.FirstOrDefault(c => c.BillCode == detail.Credit.CreditCode);
                        if (credit == null) continue;

                        var partAbatements2 = additionalAbatements
                            .Where(x => x.Value == detail.Credit.CurrentValue &&
                                        (x.CreditBillCode == detail.Credit.CreditCode ||
                                         x.DebtBillCode == detail.Credit.CreditCode))
                            .OrderByDescending(x => x.CreatedTime)
                            .FirstOrDefault();

                        if (partAbatements2 != null) needAbatements.Add(partAbatements2);
                    }
                }

                if (needAbatements != null && needAbatements.Any())
                {
                    _db.Abatements.RemoveRange(needAbatements);
                }
            }
            else
            {
                var abatements = await _db.Abatements
                    .Where(p => p.CreditBillCode == item.ReceiveCode &&
                                (creditNos != null && creditNos.Any()
                                    ? creditNos.ToHashSet().Contains(p.DebtBillCode)
                                    : true))
                    .AsNoTracking()
                    .ToListAsync();

                foreach (var detail in details.SelectMany(d => d.RecognizeReceiveDetailCredits, (d, c) => new { Detail = d, Credit = c }))
                {
                    var credit = credits.FirstOrDefault(c => c.BillCode == detail.Credit.CreditCode);
                    if (credit == null) continue;

                    var partAbatements2 = abatements
                        .Where(x => x.Value == detail.Credit.CurrentValue &&
                                    (x.CreditBillCode == detail.Credit.CreditCode ||
                                     x.DebtBillCode == detail.Credit.CreditCode))
                        .OrderByDescending(x => x.CreatedTime)
                        .FirstOrDefault();

                    if (partAbatements2 != null) needAbatements.Add(partAbatements2);
                }

                if (needAbatements != null && needAbatements.Any())
                {
                    _db.Abatements.RemoveRange(needAbatements);
                }
            }

            if (item.ReceiveValue < 0)
            {
                var receiveCredit = credits.Where(p => p.BillCode == item.ReceiveCode).FirstOrDefault();
                if (receiveCredit == null)
                {
                    receiveCredit = await _db.Credits.Where(p => p.BillCode == item.ReceiveCode).FirstOrDefaultAsync();
                    if (receiveCredit != null)
                    {
                        receiveCredit.AbatedStatus = AbatedStatusEnum.NonAbate;
                    }
                }
            }

            if (credits.Any())
            {
                foreach (var credit in credits)
                {
                    if (needAbatements != null && needAbatements.Any())
                    {
                        if (needAbatements.Count(p =>
                                p.CreditBillCode == credit.BillCode || p.DebtBillCode == credit.BillCode) > 0)
                        {
                            credit.AbatedStatus = AbatedStatusEnum.NonAbate;
                        }
                    }
                }

                _db.Credits.UpdateRange(credits);
                if (creditIds != null && creditIds.Any())
                {
                    var invoiceNos = await _db.InvoiceCredits.Where(p => creditIds.ToHashSet().Contains(p.CreditId.Value)).Select(p => p.InvoiceNo).Distinct().AsNoTracking().ToListAsync();
                    //减掉发票表的认款金额
                    if (invoiceNos != null && invoiceNos.Any())
                    {
                        var invoices = await _db.Invoices.Where(p => invoiceNos.Contains(p.InvoiceNo)).AsNoTracking()
                            .ToListAsync();
                        foreach (var detail in details)
                        {
                            var invoice = invoices.Where(p => p.InvoiceNo == detail.Code).FirstOrDefault();
                            if (invoice != null)
                            {
                                invoice.ReceiveAmount = invoice.ReceiveAmount - detail.Value > 0
                                    ? invoice.ReceiveAmount - detail.Value
                                    : 0;
                            }
                        }

                        if (invoices.Any())
                        {
                            _db.Invoices.UpdateRange(invoices);
                        }
                    }
                }
            }

            var allowAccountPeriodType = new List<int> { 0, 2 };
            var needDebtDetails = new List<DebtDetailPo>();
            var debtDetails = await _db.DebtDetails.Where(
                x => (orderNos.Any() ? orderNos.ToHashSet().Contains(x.OrderNo) : true) &&
                x.ReceiveCode == item.ReceiveCode &&
                allowAccountPeriodType.ToHashSet().Contains(x.AccountPeriodType) &&
                x.Status != DebtDetailStatusEnum.Completed).AsNoTracking().ToListAsync();
            foreach (var debtDetail in debtDetails)
            {
                if (string.IsNullOrEmpty(debtDetail.RecognizeReceiveCode) ||
                    debtDetail.RecognizeReceiveCode == item.Code)
                {
                    needDebtDetails.Add(debtDetail);
                }
            }

            var debtDetailIds = needDebtDetails.Select(p => p.Id).ToHashSet();

            var paymentAutoItemCodes = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                .Where(p => debtDetailIds.Contains(p.DebtDetilId)).Select(p => p.PaymentAutoItem.Code).ToListAsync();
            if (paymentAutoItemCodes.Any())
            {
                ret.Message = $"操作失败，原因：应收对应的应付明细，已经在批量付款单【{string.Join(",", paymentAutoItemCodes)}】中存在！";
                return ret;
            }

            await MergeDebtDetails(allowAccountPeriodType, needDebtDetails, debtDetailIds);
            if (item.RecognizeReceiveDetails != null && item.RecognizeReceiveDetails.Any())
            {
                var rrds = item.RecognizeReceiveDetails.ToList();
                if (rrds.Count() == rrds.Where(x => x.Status == (int)RecognizeReceiveDetailEnum.Cancel).Count() +
                    details.Count())
                {
                    //完全撤销认款
                    item.Status = -1;
                }
                else
                {
                    //部分撤销认款
                    item.Status = -2;
                }

                item.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                item.UpdatedTime = DateTime.Now;
                await _itemRepository.UpdateAsync(item);
                var updDetails = await _db.RecognizeReceiveDetails.Where(x => details.Select(p => p.Id).Contains(x.Id))
                    .ToListAsync();
                updDetails.ForEach(x =>
                {
                    x.Status = RecognizeReceiveDetailEnum.Cancel;
                    x.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                    x.UpdatedTime = DateTime.Now;
                });
            }

            //删除认款明细应收记录
            var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == item.Id && details.Select(x => x.Id).ToHashSet().Contains(x.RecognizeReceiveDetailId)).ToListAsync();
            if (rrdcs != null && rrdcs.Any())
            {
                _db.RecognizeReceiveDetailCredits.RemoveRange(rrdcs);
            }

            var actualDate = await _companyDateService.GetActualDateAsync(item.CompanyId);

            var kdInput = new List<QuashAcceptancesRequestVo>
            {
                new QuashAcceptancesRequestVo
                {
                    billNo = item.Code,
                    subscriptionType = "B",
                    subscriptionNumList = orderNos,
                    ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };
            //推送金蝶
            var kdRet = await _kingdeeApiClient.CancelReceive(kdInput);
            if (kdRet.Code == CodeStatusEnum.Success)
            {
                await _unitOfWork.CommitAsync();
                var sendPara = new List<CancelReceiptInput>()
                {
                    new CancelReceiptInput { RecognizeCode = item.Code, SaleCodes = orderNos }
                };
                _logger.LogAzure("CancelReceiveOrder", sendPara.ToJson(), "撤销认款到订单发送通知给销售");
                //推给销售发生撤销认款消息
                await _daprClient.PublishEventAsync(
                    "pubsub-default",
                    "finance-sale-CancelReceive",
                    sendPara);
                await _logger.LogAsync("CancelReceiveOrder", sendPara.ToJson(), "撤销认款到订单发送通知给销售-成功发送");
                return BaseResponseData<int>.Success("操作成功");
            }

            return kdRet;
        }

        /// <summary>
        /// 撤销认款到发票
        /// </summary>
        /// <param name="item"></param>
        /// <param name="details"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CancelReceiveInvoice(RecognizeReceiveItem item,
            List<RecognizeReceiveDetailPo>? details)
        {
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var invoiceNos = details.Select(p => p.Code).ToList();
            var invoiceCredits = await _db.InvoiceCredits.Include(p => p.Credit)
                .Where(p => invoiceNos.Contains(p.InvoiceNo)).Distinct().AsNoTracking().ToListAsync();
            var credits = await _db.InvoiceCredits.Include(p => p.Credit)
                .Where(p => invoiceNos.Contains(p.InvoiceNo) && p.Credit.AutoType != "98").Select(p => p.Credit)
                .Distinct().AsNoTracking().ToListAsync();
            if (credits == null || !credits.Any())
            {
                ret.Message = $"操作失败，原因：发票号【{string.Join(",", invoiceNos)}】没有找到对应的应收";
                return ret;
            }

            var lessCredit =
                _db.Credits.Where(p => p.BillCode == item.ReceiveCode).AsNoTracking().FirstOrDefault(); //负数应收
            if (lessCredit != null)
            {
                credits.Add(lessCredit);
            }

            credits = credits.DistinctBy(p => p.BillCode).ToList();
            var creditNos = credits.Select(p => p.BillCode).Distinct().ToList();
            //损失确认申请--在途的损失确认单中的应收不允许撤销认款
            var lossHasValue = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => creditNos.ToHashSet().Contains(x.BillCode) && x.LossRecognitionItem.Status != StatusEnum.Complate && x.LossRecognitionItem.Status != StatusEnum.Refuse).AsNoTracking().ToListAsync();
            if (lossHasValue != null && lossHasValue.Any())
            {
                var errCreditCodeStr = string.Join(",", lossHasValue.Select(x => x.BillCode).ToList());
                ret.Message = $"应收单“{errCreditCodeStr}”在正在进行损失确认，无法撤销认款";
                return ret;
            }
            var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == item.Id && details.Select(x => x.Id).ToHashSet().Contains(x.RecognizeReceiveDetailId)).ToListAsync();
            var needAbatements = new List<AbatementPo>();
            // 删除冲销记录 
            needAbatements = await _db.Abatements.Where(p => p.RecognizeReceiveCode == item.Code).ToListAsync();
            if (needAbatements.Any())
            {
                foreach (var detail in details)
                {
                    // 根据发票号查询应收
                    var creditsTemp = invoiceCredits.Where(x => x.InvoiceNo == detail.Code).DistinctBy(x => x.CreditId)
                        .ToList();
                    if (credits == null)
                    {
                        continue;
                    }

                    foreach (var subitem in creditsTemp)
                    {
                        //应收金额
                        var creditValue = rrdcs.Where(x => x.CreditId == subitem.CreditId).Sum(x => x.CurrentValue);
                        //收款单号+应收查询
                        var partAbatements = needAbatements
                            .Where(p => p.CreditBillCode == item.ReceiveCode &&
                                        p.DebtBillCode == subitem.Credit.BillCode).OrderByDescending(x => x.CreatedTime)
                            .ToList();
                        if (partAbatements.Any())
                        {
                            //如果没有认款单号会通过收款单号+应收+金额去找，优先减去收款单+应收+金额能匹配上的结算记录
                            var matchAba = partAbatements.Where(x => x.Value == creditValue)
                                .OrderByDescending(x => x.CreatedTime).FirstOrDefault();
                            if (matchAba != null)
                            {
                                matchAba.Value = 0;
                            }
                            else
                            {
                                //如果有多条，按照时间顺序匹配，先匹配时间近的
                                //金额对不上，则扣减相应的金额，金额不够依次扣除
                                foreach (var partAba in partAbatements)
                                {
                                    if (creditValue == 0)
                                    {
                                        break;
                                    }

                                    if (partAba.Value > creditValue)
                                    {
                                        partAba.Value -= creditValue.HasValue ? creditValue.Value : 0;
                                        creditValue = 0;
                                    }
                                    else
                                    {
                                        var receiveDetailCredit = detail.RecognizeReceiveDetailCredits.FirstOrDefault(i =>
                                            i.CreditId == subitem.CreditId);
                                        partAba.Value = receiveDetailCredit != null && receiveDetailCredit.CurrentValue.HasValue ? partAba.Value - receiveDetailCredit.CurrentValue.Value : 0;
                                        creditValue -= partAba.Value;
                                    }
                                }
                            }
                        }
                    }
                }

                var updAbatements = needAbatements.Where(x => x.Value > 0).ToList();
                var delAbatements = needAbatements.Where(x => x.Value == 0).ToList();
                if (updAbatements.Any())
                {
                    _db.Abatements.UpdateRange(updAbatements);
                }

                if (delAbatements.Any())
                {
                    _db.Abatements.RemoveRange(delAbatements);
                }
            }
            else
            {
                var abatements = await _db.Abatements
                    .Where(p => p.CreditBillCode == item.ReceiveCode && creditNos.Any(x => x == p.DebtBillCode))
                    .AsNoTracking().ToListAsync();
                if (abatements.Any())
                {
                    foreach (var detail in details)
                    {
                        // 根据应收单号以及认款金额判断订单冲销记录
                        var creditsTemp = invoiceCredits.Where(x => x.InvoiceNo == detail.Code).ToList();
                        if (credits == null)
                        {
                            continue;
                        }

                        foreach (var subitem in creditsTemp)
                        {
                            //应收金额
                            var creditValue = subitem.Credit.Value;
                            //收款单号+应收查询
                            var partAbatements = abatements.Where(p => p.DebtBillCode == subitem.Credit.BillCode)
                                .OrderByDescending(x => x.CreatedTime).ToList();
                            if (partAbatements.Any())
                            {
                                //如果没有认款单号会通过收款单号+应收+金额去找，优先减去收款单+应收+金额能匹配上的结算记录
                                var matchAba = partAbatements.Where(x => x.Value == creditValue)
                                    .OrderByDescending(x => x.CreatedTime).FirstOrDefault();
                                if (matchAba != null)
                                {
                                    matchAba.Value = 0;
                                }
                                else
                                {
                                    //如果有多条，按照时间顺序匹配，先匹配时间近的
                                    //金额对不上，则扣减相应的金额，金额不够依次扣除
                                    foreach (var partAba in partAbatements)
                                    {
                                        if (creditValue == 0)
                                        {
                                            break;
                                        }

                                        if (partAba.Value > creditValue)
                                        {
                                            partAba.Value -= creditValue;
                                        }
                                        else
                                        {
                                            var receiveDetailCredit = detail.RecognizeReceiveDetailCredits.FirstOrDefault(i =>
                                                i.CreditId == subitem.CreditId);
                                            partAba.Value = receiveDetailCredit != null && receiveDetailCredit.CurrentValue.HasValue ? partAba.Value - receiveDetailCredit.CurrentValue.Value : 0;
                                            creditValue -= partAba.Value;
                                        }
                                    }
                                }
                            }
                        }

                        var updAbatements = abatements.Where(x => x.Value > 0).ToList();
                        var delAbatements = abatements.Where(x => x.Value == 0).ToList();
                        if (updAbatements.Any())
                        {
                            _db.Abatements.UpdateRange(updAbatements);
                        }

                        if (delAbatements.Any())
                        {
                            _db.Abatements.RemoveRange(delAbatements);
                        }
                    }
                }
            }

            if (item.ReceiveValue < 0)
            {
                var receiveCredit = credits.Where(p => p.BillCode == item.ReceiveCode).FirstOrDefault();
                if (receiveCredit == null)
                {
                    receiveCredit = await _db.Credits.Where(p => p.BillCode == item.ReceiveCode).FirstOrDefaultAsync();
                    if (receiveCredit != null)
                    {
                        receiveCredit.AbatedStatus = AbatedStatusEnum.NonAbate;
                    }
                }
            }

            foreach (var credit in credits)
            {
                if (needAbatements.Count(p =>
                        p.CreditBillCode == credit.BillCode || p.DebtBillCode == credit.BillCode) > 0)
                {
                    credit.AbatedStatus = AbatedStatusEnum.NonAbate;
                }
            }

            if (credits.Any())
            {
                _db.Credits.UpdateRange(credits);
            }

            //减掉发票表的认款金额
            if (invoiceNos != null && invoiceNos.Any())
            {
                var invoices = await _db.Invoices.Where(p => invoiceNos.ToHashSet().Contains(p.InvoiceNo)).AsNoTracking().ToListAsync();
                foreach (var detail in details)
                {
                    var invoice = invoices.Where(p => p.InvoiceNo == detail.Code).FirstOrDefault();
                    if (invoice != null)
                    {
                        invoice.ReceiveAmount = invoice.ReceiveAmount - detail.Value > 0
                            ? invoice.ReceiveAmount - detail.Value
                            : 0;
                    }
                }

                if (invoices.Any())
                {
                    _db.Invoices.UpdateRange(invoices);
                }
            }

            // 删除应付明细相关数据且合并
            var orderNos = credits.Select(p => p.OrderNo).ToList();
            var allowAccountPeriodType = new List<int> { 0, 2 };
            var needDebtDetails = new List<DebtDetailPo>();
            var debtDetails = await _db.DebtDetails.Where(
                                                 x => (orderNos.Any() ? orderNos.ToHashSet().Contains(x.OrderNo) : true) &&
                                                 x.ReceiveCode == item.ReceiveCode &&
                                                 allowAccountPeriodType.Contains(x.AccountPeriodType) &&
                                                 x.Status != DebtDetailStatusEnum.Completed).AsNoTracking().ToListAsync();
            foreach (var debtDetail in debtDetails)
            {
                if (string.IsNullOrEmpty(debtDetail.RecognizeReceiveCode) ||
                    debtDetail.RecognizeReceiveCode == item.Code)
                {
                    needDebtDetails.Add(debtDetail);
                }
            }

            var debtDetailIds = needDebtDetails.Select(p => p.Id).ToHashSet();
            if (debtDetailIds != null && debtDetailIds.Any())
            {
                var paymentAutoItemCodes = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem).Where(p => debtDetailIds.ToHashSet().Contains(p.DebtDetilId)).Select(p => p.PaymentAutoItem.Code).ToListAsync();
                if (paymentAutoItemCodes.Any())
                {
                    ret.Message = $"操作失败，原因：应收对应的应付明细，已经在批量付款单【{string.Join(",", paymentAutoItemCodes)}】中存在！";
                    return ret;
                }
            }

            if (item.RecognizeReceiveDetails != null && item.RecognizeReceiveDetails.Any())
            {
                var rrds = item.RecognizeReceiveDetails
                    .ToList(); //await _db.RecognizeReceiveDetails.Where(x => x.RecognizeReceiveItemId == item.Id).AsNoTracking().ToListAsync();
                if (rrds.Count() == rrds.Where(x => x.Status == (int)RecognizeReceiveDetailEnum.Cancel).Count() +
                    details.Count())
                {
                    //完全撤销认款
                    await MergeDebtDetails(allowAccountPeriodType, needDebtDetails, debtDetailIds);
                    item.Status = -1;
                }
                else
                {
                    //部分撤销认款
                    await MergePartDebtDetails(allowAccountPeriodType, needDebtDetails, debtDetailIds, details, rrdcs,
                        credits);
                    item.Status = -2;
                }

                item.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                item.UpdatedTime = DateTime.Now;
                await _itemRepository.UpdateAsync(item);
                var updDetails = await _db.RecognizeReceiveDetails.Where(x => details.Select(p => p.Id).Contains(x.Id))
                    .ToListAsync();
                updDetails.ForEach(x =>
                {
                    x.Status = RecognizeReceiveDetailEnum.Cancel;
                    x.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                    x.UpdatedTime = DateTime.Now;
                });
            }

            //删除认款明细应收记录
            if (rrdcs != null && rrdcs.Any())
            {
                _db.RecognizeReceiveDetailCredits.RemoveRange(rrdcs);
            }

            var actualDate = await _companyDateService.GetActualDateAsync(item.CompanyId);

            var kdInput = new List<QuashAcceptancesRequestVo>
            {
                new QuashAcceptancesRequestVo
                {
                    billNo = item.Code,
                    subscriptionType = "A",
                    subscriptionNumList = invoiceNos,
                    ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };
            //推送金蝶
            var kdRet = await _kingdeeApiClient.CancelReceive(kdInput);
            if (kdRet.Code == CodeStatusEnum.Success)
            {
                await _unitOfWork.CommitAsync();
                var sendPara = new List<CancelReceiptInput>()
                {
                    new CancelReceiptInput { RecognizeCode = item.Code, SaleCodes = orderNos }
                };
                _logger.LogAzure("CancelReceiveInvoice", sendPara.ToJson(), "撤销认款到发票通知销售");
                //推给销售发生撤销认款消息
                await _daprClient.PublishEventAsync(
                    "pubsub-default",
                    "finance-sale-CancelReceive",
                    sendPara);
                await _logger.LogAsync("CancelReceiveInvoice", sendPara.ToJson(), "撤销认款到发票通知销售-成功");
                return BaseResponseData<int>.Success("操作成功");
            }

            return kdRet;
        }


        /// <summary>
        /// 撤销认款到初始应收
        /// </summary>
        /// <param name="item"></param>
        /// <param name="details"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CancelReceiveCredit(RecognizeReceiveItem item,
            List<RecognizeReceiveDetailPo>? details)
        {
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var creditNos = details.Select(p => p.Code).ToList();
            creditNos.Add(item.ReceiveCode); //当收款账号是负数应收时
            var creditList = await _db.Credits.Where(p => creditNos.Contains(p.BillCode)).ToListAsync();
            //损失确认申请--在途的损失确认单中的应收不允许撤销认款
            var lossHasValue = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => creditNos.ToHashSet().Contains(x.BillCode) && x.LossRecognitionItem.Status != StatusEnum.Complate && x.LossRecognitionItem.Status != StatusEnum.Refuse).AsNoTracking().ToListAsync();
            if (lossHasValue != null && lossHasValue.Any())
            {
                var errCreditCodeStr = string.Join(",", lossHasValue.Select(x => x.BillCode).ToList());
                ret.Message = $"应收单“{errCreditCodeStr}”在正在进行损失确认，无法撤销认款";
                return ret;
            }

            var needAbatements = new List<AbatementPo>();
            // 删除冲销记录 
            needAbatements = await _db.Abatements.Where(p => p.RecognizeReceiveCode == item.Code).ToListAsync();
            if (needAbatements.Any())
            {
                //认款单+应收找，找到删除冲销记录
                needAbatements = needAbatements.Where(x => creditNos != null && creditNos.Any() ? creditNos.ToHashSet().Contains(x.DebtBillCode) : true).ToList();
                _db.Abatements.RemoveRange(needAbatements);
            }
            else
            {
                //删除冲销集合
                var delAbas = new List<AbatementPo>();
                //没有认款单就用收款单+应收+金额找，如果有多条，按照时间顺序匹配，先匹配时间近的
                needAbatements = await _db.Abatements.Where(p => p.CreditBillCode == item.ReceiveCode && creditNos.ToHashSet().Contains(p.DebtBillCode)).AsNoTracking().OrderByDescending(x => x.CreatedTime).ToListAsync();
                if (needAbatements != null && needAbatements.Any())
                {
                    foreach (var partAba in needAbatements)
                    {
                        var credit = details.FirstOrDefault(x => x.Code == partAba.DebtBillCode);
                        if (credit != null && credit.Value == partAba.Value)
                        {
                            delAbas.Add(partAba);
                        }
                    }

                    if (delAbas.Any())
                    {
                        _db.Abatements.RemoveRange(delAbas);
                    }
                }
            }

            //修改应收的冲销状态
            foreach (var credit in creditList)
            {
                credit.AbatedStatus = 0;
            }

            _db.Credits.UpdateRange(creditList);
            var creditIds = creditList.Select(p => p.Id).ToList();
            var orderNos = creditList.Select(p => p.OrderNo).ToList();
            var allowAccountPeriodType = new List<int> { 0, 2 };
            var needDebtDetails = new List<DebtDetailPo>();
            var debtDetails = await _db.DebtDetails.Where(x => creditIds.Any(p => p == x.CreditId.Value) &&
                                                               x.ReceiveCode == item.ReceiveCode &&
                                                               allowAccountPeriodType.Any(p =>
                                                                   p == x.AccountPeriodType) &&
                                                               x.Status != DebtDetailStatusEnum.Completed)
                .AsNoTracking().ToListAsync();
            foreach (var debtDetail in debtDetails)
            {
                if (string.IsNullOrEmpty(debtDetail.RecognizeReceiveCode) ||
                    debtDetail.RecognizeReceiveCode == item.Code)
                {
                    needDebtDetails.Add(debtDetail);
                }
            }

            var debtDetailIds = needDebtDetails.Select(p => p.Id).ToHashSet();

            var paymentAutoItemCodes = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                .Where(p => debtDetailIds.Any(x => x == p.DebtDetilId)).Select(p => p.PaymentAutoItem.Code)
                .ToListAsync();
            if (paymentAutoItemCodes.Any())
            {
                ret.Message = $"操作失败，原因：应收对应的应付明细，已经在批量付款单【{string.Join(",", paymentAutoItemCodes)}】中存在！";
                return ret;
            }

            await MergeDebtDetails(allowAccountPeriodType, needDebtDetails, debtDetailIds);

            if (item.RecognizeReceiveDetails != null && item.RecognizeReceiveDetails.Any())
            {
                var rrds = item.RecognizeReceiveDetails
                    .ToList(); //await _db.RecognizeReceiveDetails.Where(x => x.RecognizeReceiveItemId == item.Id).AsNoTracking().ToListAsync();
                if (rrds.Count() == rrds.Where(x => x.Status == (int)RecognizeReceiveDetailEnum.Cancel).Count() +
                    details.Count())
                {
                    //完全撤销认款
                    item.Status = -1;
                }
                else
                {
                    //部分撤销认款
                    item.Status = -2;
                }

                item.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                item.UpdatedTime = DateTime.Now;
                await _itemRepository.UpdateAsync(item);
                var updDetails = await _db.RecognizeReceiveDetails.Where(x => details.Select(p => p.Id).Contains(x.Id))
                    .ToListAsync();
                updDetails.ForEach(x =>
                {
                    x.Status = RecognizeReceiveDetailEnum.Cancel;
                    x.UpdatedBy = _appServiceContextAccessor?.Get().UserName;
                    x.UpdatedTime = DateTime.Now;
                });
            }

            var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == item.Id && details.Select(x => x.Id).ToHashSet().Contains(x.RecognizeReceiveDetailId)).ToListAsync();
            //删除认款明细应收记录
            if (rrdcs != null && rrdcs.Any())
            {
                _db.RecognizeReceiveDetailCredits.RemoveRange(rrdcs);
            }

            var actualDate = await _companyDateService.GetActualDateAsync(item.CompanyId);

            var kdInput = new List<QuashAcceptancesRequestVo>
            {
                new QuashAcceptancesRequestVo
                {
                    billNo = item.Code,
                    subscriptionType = "C",
                    subscriptionNumList = creditNos,
                    ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };
            //推送金蝶
            var kdRet = await _kingdeeApiClient.CancelReceive(kdInput);
            if (kdRet.Code == CodeStatusEnum.Success)
            {
                await _unitOfWork.CommitAsync();
                var sendPara = new List<CancelReceiptInput>()
                {
                    new CancelReceiptInput { RecognizeCode = item.Code, SaleCodes = orderNos }
                };
                _logger.LogAzure("CancelReceiveCredit", sendPara.ToJson(), "撤销认款到初始应收通知销售");
                //推给销售发生撤销认款消息
                await _daprClient.PublishEventAsync(
                    "pubsub-default",
                    "finance-sale-CancelReceive", sendPara);
                await _logger.LogAsync("CancelReceiveCredit", sendPara.ToJson(), "撤销认款到初始应收通知销售-成功");
                return BaseResponseData<int>.Success("操作成功");
            }

            return kdRet;
        }

        private async Task MergeDebtDetails(List<int> allowAccountPeriodType, List<DebtDetailPo> needDebtDetails,
            HashSet<Guid>? debtDetailIds)
        {
            // 根据DebtId分组
            var groupByDebtIdDetails = needDebtDetails.GroupBy(x => new { x.DebtId })
                .Select(group => new
                {
                    group.Key.DebtId,
                    Details = group.ToList()
                })
                .ToList();
            // 查询该id下是否为空收款单号的其它应付明细一起合并
            var debtIds = groupByDebtIdDetails.Select(x => x.DebtId).ToList();
            //没有认款的
            var otherDebtDetails = await _db.DebtDetails.Where(x =>
                x.DebtId.HasValue &&
                debtIds.Any(p => p == x.DebtId.Value) &&
                !debtDetailIds.Any(p => p == x.Id) &&
                allowAccountPeriodType.Contains(x.AccountPeriodType) &&
                (x.AccountPeriodType == 2 ? string.IsNullOrEmpty(x.ReceiveCode) : !x.ProbablyPayTime.HasValue) &&
                x.CreditId.HasValue &&
                x.Status != DebtDetailStatusEnum.Completed).ToListAsync();
            var otherDebtDetailIds = otherDebtDetails.Select(p => p.Id);
            var paymentAutoDetailIds = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                .Where(p => otherDebtDetailIds.Contains(p.DebtDetilId) &&
                            p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed).Select(p => p.DebtDetilId)
                .ToListAsync();
            otherDebtDetails = otherDebtDetails.Where(p => !paymentAutoDetailIds.Contains(p.Id)).ToList();
            foreach (var debt in groupByDebtIdDetails)
            {
                var others = otherDebtDetails.Where(x => x.DebtId.Value == debt.DebtId).ToList();

                // 根据AccountPeriodType分组
                var groupByAccountPeriodTypeDebtDetails = debt.Details.GroupBy(x =>
                    new { x.AccountPeriodType, x.CreditId, x.CostDiscount, x.AccountPeriodDays }).Select(p => new
                    {
                        p.Key.CostDiscount,
                        p.Key.AccountPeriodType,
                        p.Key.CreditId,
                        p.Key.AccountPeriodDays,
                        Details = p.ToList()
                    }).ToList();

                // 单个类型
                foreach (var groupByAccountPeriodType in groupByAccountPeriodTypeDebtDetails)
                {
                    if (groupByAccountPeriodType.Details != null && groupByAccountPeriodType.Details.Any())
                    {
                        var tempAddDetails = others.Where(p =>
                            p.AccountPeriodType == groupByAccountPeriodType.AccountPeriodType &&
                            p.CostDiscount == groupByAccountPeriodType.CostDiscount &&
                            p.AccountPeriodDays == groupByAccountPeriodType.AccountPeriodDays &&
                            p.CreditId == groupByAccountPeriodType.CreditId).ToList();
                        groupByAccountPeriodType.Details.AddRange(tempAddDetails);
                        // 1.有销售账期（1），也有回款账期(1),只需要清空收款单号，和（回款账期）预计付款日期
                        if (groupByAccountPeriodType.Details.Count == 1)
                        {
                            groupByAccountPeriodType.Details[0].ReceiveCode = null;
                            groupByAccountPeriodType.Details[0].RecognizeReceiveCode = null;
                            if (groupByAccountPeriodType.Details[0].AccountPeriodType == 0)
                            {
                                groupByAccountPeriodType.Details[0].BackPayTime = null;
                                groupByAccountPeriodType.Details[0].ProbablyPayTime = null;
                                groupByAccountPeriodType.Details[0].Settletype = null;
                                groupByAccountPeriodType.Details[0].DraftBillExpireDate = null;
                            }

                            _db.DebtDetails.Update(groupByAccountPeriodType.Details[0]);
                        }
                        else
                        {
                            // 深拷贝对象，方便合并
                            var single = JsonConvert.DeserializeObject<DebtDetailPo>(
                                JsonConvert.SerializeObject(groupByAccountPeriodType.Details
                                    .OrderByDescending(p => p.CostDiscount).FirstOrDefault()));
                            if (single == null)
                            {
                                continue;
                            }

                            var debtDetailIds_temp = groupByAccountPeriodType.Details.Select(p => p.Id);
                            var detailAuditPos = await _db.DebtDetailAudit
                                .Where(p => debtDetailIds_temp.Contains(p.DebtDetailId.Value)).ToListAsync();
                            _db.DebtDetailAudit.RemoveRange(detailAuditPos);
                            // 删除明细
                            _db.DebtDetails.RemoveRange(groupByAccountPeriodType.Details);
                            // 已经是同一账期，执行 清行空收款单号且合并
                            single.Value = groupByAccountPeriodType.Details.Sum(x => x.Value);
                            if (single.CostDiscount.HasValue)
                            {
                                if (single.CostDiscount == 0)
                                {
                                    single.OriginValue = null;
                                }
                                else
                                {
                                    single.OriginValue = single.Value / single.CostDiscount * 100;
                                }
                            }

                            if (single.AccountPeriodType == 0)
                            {
                                single.BackPayTime = null;
                                single.ProbablyPayTime = null;
                                single.Settletype = null;
                                single.DraftBillExpireDate = null;
                            }

                            single.ReceiveCode = null;
                            single.RecognizeReceiveCode = null;
                            _db.DebtDetails.Add(single);
                        }
                    }
                }
            }
        }

        private async Task MergePartDebtDetails(List<int> allowAccountPeriodType, List<DebtDetailPo> needDebtDetails,
            HashSet<Guid>? debtDetailIds, List<RecognizeReceiveDetailPo>? details,
            List<RecognizeReceiveDetailCreditPo>? rrdcs, List<CreditPo?>? credits)
        {
            // 根据DebtId分组
            var groupByDebtIdDetails = needDebtDetails.GroupBy(x => new { x.DebtId })
                .Select(group => new
                {
                    group.Key.DebtId,
                    Details = group.ToList()
                })
                .ToList();
            // 查询该id下是否为空收款单号的其它应付明细一起合并
            var debtIds = groupByDebtIdDetails.Select(x => x.DebtId).ToList();
            var debtIdsHashSet = debtIds.ToHashSet();
            var completeDebts = await _db.Debts.Include(x => x.DebtDetails).Where(x => debtIdsHashSet.Any() && debtIdsHashSet.Contains(x.Id)).AsNoTracking().ToListAsync();
            //没有认款的
            var otherDebtDetails = await _db.DebtDetails.Where(x =>
                x.DebtId.HasValue &&
                debtIds.Any(p => p == x.DebtId.Value) &&
                !debtDetailIds.Any(p => p == x.Id) &&
                allowAccountPeriodType.Contains(x.AccountPeriodType) &&
                (x.AccountPeriodType == 2 ? string.IsNullOrEmpty(x.ReceiveCode) : !x.ProbablyPayTime.HasValue) &&
                x.CreditId.HasValue &&
                x.Status != DebtDetailStatusEnum.Completed).ToListAsync();
            var otherDebtDetailIds = otherDebtDetails.Select(p => p.Id);
            var paymentAutoDetailIds = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem)
                .Where(p => otherDebtDetailIds.Contains(p.DebtDetilId) &&
                            p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed).Select(p => p.DebtDetilId)
                .ToListAsync();
            otherDebtDetails = otherDebtDetails.Where(p => !paymentAutoDetailIds.Contains(p.Id)).ToList();
            foreach (var debt in groupByDebtIdDetails)
            {
                var others = otherDebtDetails.Where(x => x.DebtId.Value == debt.DebtId).ToList();

                // 根据AccountPeriodType分组
                var groupByAccountPeriodTypeDebtDetails = debt.Details.GroupBy(x =>
                    new { x.AccountPeriodType, x.CreditId, x.CostDiscount, x.AccountPeriodDays }).Select(p => new
                    {
                        p.Key.CostDiscount,
                        p.Key.AccountPeriodType,
                        p.Key.CreditId,
                        p.Key.AccountPeriodDays,
                        Details = p.ToList()
                    }).OrderBy(x => x.AccountPeriodType).ToList();

                // 计算每个应收需要撤销的金额
                var creditBoxs = new List<CreditSurplusBoxDto>();
                foreach (var groupByAccountPeriodType in groupByAccountPeriodTypeDebtDetails)
                {
                    if (groupByAccountPeriodType.Details != null && groupByAccountPeriodType.Details.Any())
                    {
                        // 撤销的金额
                        decimal? currentValue = 0M;
                        var currentCredit = credits.FirstOrDefault(x => x.Id == groupByAccountPeriodType.CreditId);
                        if (currentCredit != null && rrdcs != null && rrdcs.Any())
                        {
                            // 收付比
                            var currentDebt = completeDebts.FirstOrDefault(x => x.Id == debt.DebtId);
                            if (currentDebt == null)
                            {
                                continue;
                            }

                            var debtDetailTotalValue = currentDebt.DebtDetails
                                .Where(x => x.CreditId == currentCredit.Id).Sum(x => x.Value);
                            decimal rate = debtDetailTotalValue / currentCredit.Value;
                            currentValue = rrdcs.Where(x => x.CreditCode == currentCredit.BillCode)
                                .Sum(x => x.CurrentValue);
                            // 需要撤销的付款计划金额
                            decimal cancelValue = currentValue.HasValue ? currentValue.Value * rate : 0;
                            var single = creditBoxs.FirstOrDefault(x =>
                                x.Code == currentCredit.BillCode && x.CreditSurplusTotalValue == cancelValue);
                            if (single == null)
                            {
                                creditBoxs.Add(new CreditSurplusBoxDto
                                {
                                    Code = currentCredit.BillCode,
                                    CreditSurplusTotalValue = cancelValue
                                });
                            }
                        }
                    }
                }

                // 单个类型
                foreach (var groupByAccountPeriodType in groupByAccountPeriodTypeDebtDetails)
                {
                    if (groupByAccountPeriodType.Details != null && groupByAccountPeriodType.Details.Any())
                    {
                        var currentCredit = credits.FirstOrDefault(x => x.Id == groupByAccountPeriodType.CreditId);
                        if (currentCredit == null)
                        {
                            continue;
                        }

                        var box = creditBoxs.FirstOrDefault(x => x.Code == currentCredit.BillCode);
                        if (box == null)
                        {
                            continue;
                        }

                        var tempAddDetails = others.Where(p =>
                            p.AccountPeriodType == groupByAccountPeriodType.AccountPeriodType &&
                            p.CostDiscount == groupByAccountPeriodType.CostDiscount &&
                            p.AccountPeriodDays == groupByAccountPeriodType.AccountPeriodDays &&
                            p.CreditId == groupByAccountPeriodType.CreditId).ToList();
                        groupByAccountPeriodType.Details.AddRange(tempAddDetails);
                        //if (others.Any() && !tempAddDetails.Any())
                        //{
                        //    // 不符合合并条件，跳出
                        //    continue;
                        //}
                        // 1.有销售账期（1），也有回款账期(1),只需要清空收款单号，和（回款账期）预计付款日期
                        if (groupByAccountPeriodType.Details.Count == 1)
                        {
                            if (box.CreditSurplusTotalValue == 0)
                            {
                                continue;
                            }

                            // 付款计划金额只有一个且小于撤销金额
                            if (groupByAccountPeriodType.Details[0].Value < box.CreditSurplusTotalValue)
                            {
                                groupByAccountPeriodType.Details[0].ReceiveCode = null;
                                groupByAccountPeriodType.Details[0].RecognizeReceiveCode = null;
                                if (groupByAccountPeriodType.Details[0].AccountPeriodType == 0)
                                {
                                    groupByAccountPeriodType.Details[0].BackPayTime = null;
                                    groupByAccountPeriodType.Details[0].ProbablyPayTime = null;
                                    groupByAccountPeriodType.Details[0].Settletype = null;
                                    groupByAccountPeriodType.Details[0].DraftBillExpireDate = null;
                                }

                                _db.DebtDetails.Update(groupByAccountPeriodType.Details[0]);
                                box.CreditSurplusTotalValue -= groupByAccountPeriodType.Details[0].Value;
                            }
                            // 撤销金额刚好相等
                            else if (groupByAccountPeriodType.Details[0].Value == box.CreditSurplusTotalValue)
                            {
                                groupByAccountPeriodType.Details[0].ReceiveCode = null;
                                groupByAccountPeriodType.Details[0].RecognizeReceiveCode = null;
                                if (groupByAccountPeriodType.Details[0].AccountPeriodType == 0)
                                {
                                    groupByAccountPeriodType.Details[0].BackPayTime = null;
                                    groupByAccountPeriodType.Details[0].ProbablyPayTime = null;
                                    groupByAccountPeriodType.Details[0].Settletype = null;
                                    groupByAccountPeriodType.Details[0].DraftBillExpireDate = null;
                                }

                                _db.DebtDetails.Update(groupByAccountPeriodType.Details[0]);
                                box.CreditSurplusTotalValue = 0;
                            }
                            // 大于撤销的金额，则需要拆分
                            else
                            {
                                // 深拷贝对象，方便合并
                                var single = JsonConvert.DeserializeObject<DebtDetailPo>(
                                    JsonConvert.SerializeObject(groupByAccountPeriodType.Details
                                        .OrderByDescending(p => p.CostDiscount).FirstOrDefault()));
                                if (single == null)
                                {
                                    continue;
                                }

                                var debtDetailIds_temp = groupByAccountPeriodType.Details.Select(p => p.Id);
                                var detailAuditPos = await _db.DebtDetailAudit
                                    .Where(p => debtDetailIds_temp.Contains(p.DebtDetailId.Value)).ToListAsync();
                                _db.DebtDetailAudit.RemoveRange(detailAuditPos);
                                // 删除明细
                                _db.DebtDetails.RemoveRange(groupByAccountPeriodType.Details);
                                // 拆分新的付款计划数据
                                var addDebtDetails = new List<DebtDetailPo>();
                                // 被撤销的
                                single.Id = Guid.NewGuid();
                                single.Value -= box.CreditSurplusTotalValue.HasValue
                                    ? box.CreditSurplusTotalValue.Value
                                    : 0;
                                if (single.CostDiscount.HasValue)
                                {
                                    if (single.CostDiscount == 0)
                                    {
                                        single.OriginValue = null;
                                    }
                                    else
                                    {
                                        single.OriginValue = single.Value / single.CostDiscount * 100;
                                    }
                                }

                                _db.DebtDetails.Add(single);
                                //addDebtDetails.Add(single);
                                // 剩余的
                                // 深拷贝对象，方便合并
                                var single2 = JsonConvert.DeserializeObject<DebtDetailPo>(
                                    JsonConvert.SerializeObject(groupByAccountPeriodType.Details
                                        .OrderByDescending(p => p.CostDiscount).FirstOrDefault()));
                                if (single2 == null)
                                {
                                    continue;
                                }

                                single2.Id = Guid.NewGuid();
                                single2.Value = box.CreditSurplusTotalValue.HasValue
                                    ? box.CreditSurplusTotalValue.Value
                                    : 0;
                                if (single2.CostDiscount.HasValue)
                                {
                                    if (single2.CostDiscount == 0)
                                    {
                                        single2.OriginValue = null;
                                    }
                                    else
                                    {
                                        single2.OriginValue = single2.Value / single2.CostDiscount * 100;
                                    }
                                }

                                if (single2.AccountPeriodType == 0)
                                {
                                    single2.BackPayTime = null;
                                    single2.ProbablyPayTime = null;
                                    single2.Settletype = null;
                                    single2.DraftBillExpireDate = null;
                                }

                                single2.ReceiveCode = null;
                                single2.RecognizeReceiveCode = null;
                                // 手动清空可能引起跟踪问题的导航属性（如果有）
                                single2.Credit = null; // 假设 DebtDetailPo 有一个 CreditPo 导航属性
                                single2.CreditId = single.CreditId; // 如果有外键，也重置
                                addDebtDetails.Add(single2);
                                _db.DebtDetails.Add(single2);
                                box.CreditSurplusTotalValue = 0;
                            }
                        }
                        // 多条付款计划数据
                        else
                        {
                            // 找出其他（收款单号为空，未认款）的付款计划
                            var other = groupByAccountPeriodType.Details.FirstOrDefault(x =>
                                x.Value > 0 && string.IsNullOrEmpty(x.ReceiveCode));
                            var exist = groupByAccountPeriodType.Details
                                .Where(x => !string.IsNullOrEmpty(x.ReceiveCode)).ToList();
                            foreach (var item in exist)
                            {
                                if (item.Value < box.CreditSurplusTotalValue)
                                {
                                    if (other != null)
                                    {
                                        // 删除明细
                                        _db.DebtDetails.RemoveRange(item);
                                        // 深拷贝对象，方便合并
                                        var single2 =
                                            JsonConvert.DeserializeObject<DebtDetailPo>(
                                                JsonConvert.SerializeObject(other));
                                        if (single2 == null)
                                        {
                                            continue;
                                        }

                                        // 删除明细
                                        _db.DebtDetails.RemoveRange(other);
                                        single2.Id = Guid.NewGuid();
                                        single2.Value = other.Value + item.Value;
                                        if (single2.CostDiscount.HasValue)
                                        {
                                            if (single2.CostDiscount == 0)
                                            {
                                                single2.OriginValue = null;
                                            }
                                            else
                                            {
                                                single2.OriginValue = single2.Value / single2.CostDiscount * 100;
                                            }
                                        }

                                        if (single2.AccountPeriodType == 0)
                                        {
                                            single2.BackPayTime = null;
                                            single2.ProbablyPayTime = null;
                                            single2.Settletype = null;
                                            single2.DraftBillExpireDate = null;
                                        }

                                        single2.ReceiveCode = null;
                                        single2.RecognizeReceiveCode = null;
                                        // 手动清空可能引起跟踪问题的导航属性（如果有）
                                        single2.Credit = null; // 假设 DebtDetailPo 有一个 CreditPo 导航属性
                                        single2.CreditId = item.CreditId; // 如果有外键，也重置
                                        _db.DebtDetails.Add(single2);
                                    }
                                    else
                                    {
                                        // 付款计划金额只有一个且小于撤销金额
                                        item.ReceiveCode = null;
                                        item.RecognizeReceiveCode = null;
                                        if (item.AccountPeriodType == 0)
                                        {
                                            item.BackPayTime = null;
                                            item.ProbablyPayTime = null;
                                            item.Settletype = null;
                                            item.DraftBillExpireDate = null;
                                        }

                                        _db.DebtDetails.Update(item);
                                    }

                                    box.CreditSurplusTotalValue -= item.Value;
                                }
                                // 撤销金额刚好相等
                                else if (item.Value == box.CreditSurplusTotalValue)
                                {
                                    if (other != null)
                                    {
                                        // 删除明细
                                        _db.DebtDetails.RemoveRange(item);
                                        // 深拷贝对象，方便合并
                                        var single2 =
                                            JsonConvert.DeserializeObject<DebtDetailPo>(
                                                JsonConvert.SerializeObject(other));
                                        if (single2 == null)
                                        {
                                            continue;
                                        }

                                        // 删除明细
                                        _db.DebtDetails.RemoveRange(other);
                                        single2.Id = Guid.NewGuid();
                                        single2.Value = other.Value + (box.CreditSurplusTotalValue.HasValue
                                            ? box.CreditSurplusTotalValue.Value
                                            : 0);
                                        if (single2.CostDiscount.HasValue)
                                        {
                                            if (single2.CostDiscount == 0)
                                            {
                                                single2.OriginValue = null;
                                            }
                                            else
                                            {
                                                single2.OriginValue = single2.Value / single2.CostDiscount * 100;
                                            }
                                        }

                                        if (single2.AccountPeriodType == 0)
                                        {
                                            single2.BackPayTime = null;
                                            single2.ProbablyPayTime = null;
                                            single2.Settletype = null;
                                            single2.DraftBillExpireDate = null;
                                        }

                                        single2.ReceiveCode = null;
                                        single2.RecognizeReceiveCode = null;
                                        // 手动清空可能引起跟踪问题的导航属性（如果有）
                                        single2.Credit = null; // 假设 DebtDetailPo 有一个 CreditPo 导航属性
                                        single2.CreditId = item.CreditId; // 如果有外键，也重置
                                        _db.DebtDetails.Add(single2);
                                    }
                                    else
                                    {
                                        item.ReceiveCode = null;
                                        item.RecognizeReceiveCode = null;
                                        if (item.AccountPeriodType == 0)
                                        {
                                            item.BackPayTime = null;
                                            item.ProbablyPayTime = null;
                                            item.Settletype = null;
                                            item.DraftBillExpireDate = null;
                                        }

                                        _db.DebtDetails.Update(item);
                                        box.CreditSurplusTotalValue = 0;
                                    }
                                }
                                // 大于撤销的金额，则需要拆分
                                else
                                {
                                    // 深拷贝对象，方便合并
                                    var single =
                                        JsonConvert.DeserializeObject<DebtDetailPo>(JsonConvert.SerializeObject(item));
                                    if (single == null)
                                    {
                                        continue;
                                    }
                                    var detailAuditPos = await _db.DebtDetailAudit.Where(p => p.DebtDetailId.Value == item.Id).ToListAsync();
                                    _db.DebtDetailAudit.RemoveRange(detailAuditPos);
                                    // 删除明细
                                    _db.DebtDetails.RemoveRange(item);
                                    // 被撤销的
                                    single.Id = Guid.NewGuid();
                                    single.Value -= box.CreditSurplusTotalValue.HasValue
                                        ? box.CreditSurplusTotalValue.Value
                                        : 0;
                                    if (single.CostDiscount.HasValue)
                                    {
                                        if (single.CostDiscount == 0)
                                        {
                                            single.OriginValue = null;
                                        }
                                        else
                                        {
                                            single.OriginValue = single.Value / single.CostDiscount * 100;
                                        }
                                    }

                                    _db.DebtDetails.Add(single);
                                    // 剩余的合并进other
                                    if (other != null)
                                    {
                                        // 深拷贝对象，方便合并
                                        var single2 =
                                            JsonConvert.DeserializeObject<DebtDetailPo>(
                                                JsonConvert.SerializeObject(other));
                                        if (single2 == null)
                                        {
                                            continue;
                                        }

                                        // 删除明细
                                        _db.DebtDetails.RemoveRange(other);
                                        single2.Id = Guid.NewGuid();
                                        single2.Value = other.Value + (box.CreditSurplusTotalValue.HasValue
                                            ? box.CreditSurplusTotalValue.Value
                                            : 0);
                                        if (single2.CostDiscount.HasValue)
                                        {
                                            if (single2.CostDiscount == 0)
                                            {
                                                single2.OriginValue = null;
                                            }
                                            else
                                            {
                                                single2.OriginValue = single2.Value / single2.CostDiscount * 100;
                                            }
                                        }

                                        if (single2.AccountPeriodType == 0)
                                        {
                                            single2.BackPayTime = null;
                                            single2.ProbablyPayTime = null;
                                            single2.Settletype = null;
                                            single2.DraftBillExpireDate = null;
                                        }

                                        single2.ReceiveCode = null;
                                        single2.RecognizeReceiveCode = null;
                                        // 手动清空可能引起跟踪问题的导航属性（如果有）
                                        single2.Credit = null; // 假设 DebtDetailPo 有一个 CreditPo 导航属性
                                        single2.CreditId = single.CreditId; // 如果有外键，也重置
                                        _db.DebtDetails.Add(single2);
                                        box.CreditSurplusTotalValue = 0;
                                    }
                                    else
                                    {
                                        // 深拷贝对象，方便合并
                                        var single2 =
                                            JsonConvert.DeserializeObject<DebtDetailPo>(
                                                JsonConvert.SerializeObject(item));
                                        if (single2 == null)
                                        {
                                            continue;
                                        }

                                        single2.Id = Guid.NewGuid();
                                        single2.Value = box.CreditSurplusTotalValue.HasValue
                                            ? box.CreditSurplusTotalValue.Value
                                            : 0;
                                        if (single2.CostDiscount.HasValue)
                                        {
                                            if (single2.CostDiscount == 0)
                                            {
                                                single2.OriginValue = null;
                                            }
                                            else
                                            {
                                                single2.OriginValue = single2.Value / single2.CostDiscount * 100;
                                            }
                                        }

                                        if (single2.AccountPeriodType == 0)
                                        {
                                            single2.BackPayTime = null;
                                            single2.ProbablyPayTime = null;
                                            single2.Settletype = null;
                                            single2.DraftBillExpireDate = null;
                                        }

                                        single2.ReceiveCode = null;
                                        single2.RecognizeReceiveCode = null;
                                        // 手动清空可能引起跟踪问题的导航属性（如果有）
                                        single2.Credit = null; // 假设 DebtDetailPo 有一个 CreditPo 导航属性
                                        single2.CreditId = single.CreditId; // 如果有外键，也重置
                                        _db.DebtDetails.Add(single2);
                                        box.CreditSurplusTotalValue = 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 转货款
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> Transfer(Guid itemId, string userName)
        {
            try
            {
                string msg = "转货款成功！";
                var item = await _itemRepository.GetWithNoTrackAsync(itemId);
                if (item == null)
                {
                    return BaseResponseData<int>.Failed(500, "未找到认款单据");
                }

                var goodsItem = await _db.RecognizeReceiveItems.Include(p => p.RecognizeReceiveDetails).Where(p =>
                    p.RelateCode == item.Code && p.Status != RecognizeReceiveItemStatusEnum.Canceled).ToListAsync();
                var goodsAmount = goodsItem.Sum(p => p.RecognizeReceiveDetails.Sum(p => p.Value));
                var tempAmount = item.RecognizeReceiveTempDetails.Sum(p => p.Value);
                if (goodsAmount >= tempAmount)
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败：暂收款{item.Code},认款金额已用完。");
                }

                var customerIds = item.RecognizeReceiveTempDetails.Select(x => x.CustomerId).ToList();
                var list = new List<RecognizeReceiveItem>();
                foreach (var tempDetail in item.RecognizeReceiveTempDetails)
                {
                    // deep copy
                    var currentItems = goodsItem.Where(x => x.CustomerId == tempDetail.CustomerId).ToList();
                    if (currentItems.Count(p =>
                            p.Status != RecognizeReceiveItemStatusEnum.Completed &&
                            p.Status != RecognizeReceiveItemStatusEnum.Canceled) > 0)
                    {
                        msg = $"生成部分成功，公司{tempDetail.CustomerName}的暂收款{item.Code}生成失败,存在货款未完成的单据，";
                        continue;
                    }

                    var currentValue = 0M;
                    foreach (var ci in currentItems)
                    {
                        currentValue += ci.RecognizeReceiveDetails.Sum(x => x.Value);
                    }
                    var cancelValue = tempDetail.CancelValue.HasValue ? tempDetail.CancelValue.Value : 0;
                    if (tempDetail.Value <= cancelValue)
                    {
                        continue;
                    }
                    if (tempDetail.Value - currentValue - cancelValue <= 0)
                    {
                        continue;
                    }

                    if (tempDetail.Value - cancelValue <= 0)
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败：当前暂收款{item.Code},可转货款金额为0，无法转货款。");
                    }

                    var entity = JsonConvert.DeserializeObject<RecognizeReceiveItem>(JsonConvert.SerializeObject(item));
                    entity.Id = Guid.Empty;
                    entity.RelateCode = item.Code;
                    entity.CreatedTime = DateTime.Now;
                    entity.Classify = RecognizeReceiveClassifyEnum.Goods;
                    entity.RecognizeReceiveDetails = new List<RecognizeReceiveDetail>();
                    entity.RecognizeReceiveTempDetails = new List<RecognizeReceiveTempDetail>();
                    entity.Status = 0;
                    entity.Value = tempDetail.Value - cancelValue;
                    entity.ReceiveValue = tempDetail.Value - cancelValue;
                    entity.CustomerId = tempDetail.CustomerId ??= item.CustomerId;
                    entity.CustomerNme = tempDetail.CustomerName ??= item.CustomerNme;
                    entity.CreatedBy = userName;
                    //生成单号
                    var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(
                        new CompetenceCenter.BDSCenter.BDSBaseInput
                        { ids = new List<string> { item.CompanyId.ToString() } });
                    var companyInfo = companyInfoOutput?.FirstOrDefault();
                    if (companyInfo == null)
                    {
                        entity.Code = Guid.NewGuid().ToString();
                    }
                    else
                    {
                        if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
                        {
                            companyInfo.sysMonth = DateTime.Now.ToString("yyyy-MM");
                        }

                        var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = "FXBD",
                            BillType = "REC",
                            SysMonth = companyInfo.sysMonth,
                            DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        if (outPut.Status)
                        {
                            entity.Code = outPut.Codes.First();
                            var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                            DateTime.TryParse(sysMonth, out DateTime billDate);
                            entity.BillDate = billDate;
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, $"生成Code失败，{outPut.Msg}");
                        }

                        if (string.IsNullOrEmpty(outPut.Codes[0]))
                        {
                            return BaseResponseData<int>.Failed(500, "单号生成异常，请重试！");
                        }
                    }

                    list.Add(entity);
                }

                if (list.Any())
                {
                    var adds = list.Adapt<List<RecognizeReceiveItemPo>>();
                    await _db.RecognizeReceiveItems.AddRangeAsync(adds);
                    var res = await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Success(res, msg);
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "生成失败，转货款的明细均不符合条件");
                }
            }
            catch (Exception ex)
            {
                _logger.LogAzure("Transfer", $"转货款失败，{ex.Message}", "转货款失败", LogLevelEnum.Error);
                return BaseResponseData<int>.Failed(500, $"转货款失败，{ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 保存上游回款显示日期
        /// </summary>
        /// <param name="input">入参</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SaveBackDateTime(SaveBackTimeVo input)
        {
            if (input.Details == null || !input.Details.Any())
            {
                return BaseResponseData<int>.Failed(500, "未获取到明细");
            }

            var ids = input.Details.Select(x => x.Id).ToList();
            var details = await _db.RecognizeReceiveDetails.Where(x => ids.Contains(x.Id)).ToListAsync();
            foreach (var item in input.Details)
            {
                var detail = details.FirstOrDefault(x => x.Id == item.Id);
                if (detail != null)
                {
                    detail.BackDateTime = item.BackDateTime.HasValue ? item.BackDateTime.Value.AddHours(8) : null;
                    detail.UpdatedBy = input.UserName;
                    detail.UpdatedTime = System.DateTime.UtcNow;
                }
            }

            await _unitOfWork.CommitAsync();
            return BaseResponseData<int>.Success("保存上游回款显示时间成功");
        }

        /// <summary>
        /// 更新贴现日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<BaseResponseData<int>> UpdateDiscountDate(KingdeeUpdateDiscountDateInput input)
        {
            try
            {
                var recognizeItems = await _db.RecognizeReceiveItems.Where(p => p.ReceiveCode == input.ReceiveCode)
                    .ToListAsync();
                if (recognizeItems == null || recognizeItems.Count() == 0)
                {
                    return BaseResponseData<int>.Failed(500, "没有获取到认款单");
                }

                var discountDate = DateTimeHelper.LongToDateTime(input.DiscountDate.Value);
                foreach (var item in recognizeItems)
                {
                    item.DiscountDate = discountDate;
                }

                var detailCredits = await _db.RecognizeReceiveDetailCredits.Where(p =>
                    recognizeItems.Select(p => p.Id).ToList().Contains(p.RecognizeReceiveItemId)).ToListAsync();
                if (detailCredits == null && detailCredits.Count() == 0)
                {
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Failed(500, "更新认款单贴现日期成功，但没有获取到认款明细对应应收");
                }

                var detailCreditsIds = detailCredits.Where(z => z.CreditId.HasValue).Select(z => z.CreditId).ToList();
                var debtDetails = await _db.DebtDetails.Where(p => detailCreditsIds.Contains(p.CreditId)).ToListAsync();
                if (debtDetails == null && debtDetails.Count() == 0)
                {
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Failed(500, "更新认款单贴现日期成功，但没有获取到应付付款计划");
                }

                foreach (var item in debtDetails)
                {
                    if ((item.Settletype == "商业承兑汇票" || item.Settletype == "银行承兑汇票") &&
                        item.Status == DebtDetailStatusEnum.WaitExecute)
                    {
                        item.DraftBillExpireDate =
                            (discountDate < item.DraftBillExpireDate || item.DraftBillExpireDate == null)
                                ? discountDate
                                : item.DraftBillExpireDate;
                    }
                }

                await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"认款审核通过，回调失败：{ex.Message}");
            }

            return BaseResponseData<int>.Success("更新贴现日期成功");
        }

        /// <summary>
        /// 撤销认款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveDetail(PartCancelInput input)
        {
            try
            {
                var item = await _itemRepository.GetWithNoTrackAsync(input.Id);
                if (item == null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单不存在");
                }
                if (item.Status != 99 && item.Status != -2)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单状态无法撤销认款");
                }

                if (item.CreatedBy != input.UserName)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：非创建人无法撤销认款");
                }

                if (item.Classify == RecognizeReceiveClassifyEnum.Temp)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：暂收款不支持撤销认款明细");
                }

                if (input.DetailIds == null || input.DetailIds.Count == 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：请至少勾选一条需要撤销的明细");
                }

                // 获取系统月度
                var sysMonth = await _bDSApiClient.GetSystemMonth(item.CompanyId);
                sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
                var companyId = Guid.Parse(item.CompanyId);
                var query = _db.InventoryItem.Where(p => p.CompanyId == companyId && p.Status == 2);
                var itemPo = await query.FirstOrDefaultAsync();
                if (itemPo != null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：盘点期间不能提交撤销");
                }

                await CheckSysMonth(companyId, sysMonth);
                if (input.DetailIds == null || !input.DetailIds.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：请先勾选需要撤销的认款明细");
                }
                var details = await _db.RecognizeReceiveDetails.Include(i => i.RecognizeReceiveDetailCredits).Where(p => input.DetailIds.ToHashSet().Contains(p.Id) && p.RecognizeReceiveItemId == input.Id).AsNoTracking().ToListAsync(); //await _receiveDetailQueryService.GetAllListAsync(p => input.DetailIds.AsQueryable().Contains(p.Id) && p.RecognizeReceiveItemId == input.Id);

                if (details != null && details.Count > 0)
                {
                    if (details.Count() == details.Count(p => p.Type == 2)) //撤销认款到订单
                    {
                        return await CancelReceiveOrder(item, details);
                    }
                    else if (details.Count() == details.Count(p => p.Type == 1)) //撤销认款到发票
                    {
                        return await CancelReceiveInvoice(item, details);
                    }
                    else if (details.Count() == details.Count(p => p.Type == 3)) //撤销认款到初始应收
                    {
                        return await CancelReceiveCredit(item, details);
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：不支持认款到此类型的撤销操作！");
                    }
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单明细不存在！");
                }
            }
            catch (Exception e)
            {
                _logger.LogAzure("CancelReceiveDetail", $"{input.Id}撤销认款异常:{e.Message}详情{e.StackTrace}", "撤销认款到明细");
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + e.Message);
            }
        }

        /// <summary>
        /// 暂收款撤销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveTempDetail(PartCancelTempInput input)
        {
            try
            {
                var item = await _db.RecognizeReceiveItems.FirstAsync(x => x.Id == input.ItemId);
                if (item == null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单不存在");
                }

                if (item.Status != RecognizeReceiveItemStatusEnum.Completed &&
                    item.Status != RecognizeReceiveItemStatusEnum.PartCanceled)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单状态无法撤销认款");
                }

                if (item.CreatedBy != input.UserName)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：非创建人无法撤销认款");
                }

                if (item.Classify == RecognizeReceiveClassifyEnum.Goods)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：此功能仅支持暂收款撤销");
                }

                if (input.Details == null || !input.Details.Any())
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：请至少勾选一条需要撤销的明细");
                }

                var detailIds = input.Details.Select(x => x.Id).ToList();
                if (detailIds == null || detailIds.Count == 0)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：请至少勾选一条需要撤销的明细");
                }

                // 暂收款转货款数据
                var goods = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails)
                    .Where(x => x.RelateCode == item.Code).AsNoTracking().ToListAsync();
                // 暂收款明细
                var currentDetails = await _db.RecognizeReceiveTempDetails.Where(p => p.RecognizeReceiveItemId == input.ItemId).ToListAsync();
                var details = currentDetails.Where(x => detailIds.ToHashSet().Contains(x.Id)).ToList();
                // 金蝶入参
                var kdInput = new List<QuashAcceptancesRequestVo>();
                var customerModel = new List<AcceptancesCustomerModel>();
                //完全撤销的个数
                int cnt = 0;
                if (details != null)
                {
                    foreach (var detail in details)
                    {
                        var single = input.Details.FirstOrDefault(x => x.Id == detail.Id);
                        if (single == null)
                        {
                            continue;
                        }

                        //组装参数
                        if (single.CurrentValue.HasValue && single.CurrentValue.Value != 0)
                        {
                            customerModel.Add(new AcceptancesCustomerModel
                            {
                                customer = detail.CustomerId,
                                subscriptionAmount = single.CurrentValue
                            });
                        }
                        if (!detail.CancelValue.HasValue)
                        {
                            detail.CancelValue = 0;
                        }

                        if (!single.CurrentValue.HasValue)
                        {
                            single.CurrentValue = 0;
                        }

                        detail.CancelValue += single.CurrentValue;
                        //暂收款单扣除撤销的金额
                        item.Value -= single.CurrentValue.Value;
                        if (detail.CancelValue == detail.Value)
                        {
                            cnt += 1;
                            detail.Status = RecognizeReceiveDetailEnum.Cancel;
                        }
                        else if (detail.CancelValue < detail.Value)
                        {
                            detail.Status = RecognizeReceiveDetailEnum.PartCancel;
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：超出可撤销金额");
                        }

                        detail.UpdatedBy = input.UserName;
                        detail.UpdatedTime = DateTime.Now;

                        var singleGoods = goods.FirstOrDefault(x => x.CustomerId == detail.CustomerId);
                        if (singleGoods == null)
                        {
                            // 明细未转货款，跳出
                            continue;
                        }

                        if (singleGoods.Status == RecognizeReceiveItemStatusEnum.WaitSubmit)
                        {
                            // 待提交的暂收款转货款，将被直接删除
                            _db.RecognizeReceiveItems.Remove(singleGoods);
                        }
                        else
                        {
                            singleGoods.ReceiveValue -= single.CurrentValue.HasValue ? single.CurrentValue.Value : 0;
                            singleGoods.UpdatedBy = input.UserName;
                            singleGoods.UpdatedTime = DateTime.Now;
                            _db.RecognizeReceiveItems.Update(singleGoods);
                        }

                        var otherGoods = goods.Where(x =>
                                x.CustomerId != detail.CustomerId &&
                                x.Status == RecognizeReceiveItemStatusEnum.WaitSubmit)
                            .ToList();
                        // 其他待提交的暂收款转货款，将被直接删除
                        _db.RecognizeReceiveItems.RemoveRange(otherGoods);
                    }

                    if (cnt == currentDetails.Count())
                    {
                        item.Status = RecognizeReceiveItemStatusEnum.Canceled;
                    }
                    else
                    {
                        item.Status = RecognizeReceiveItemStatusEnum.PartCanceled;
                    }

                    item.UpdatedBy = input.UserName;
                    item.UpdatedTime = DateTime.Now;
                    _db.RecognizeReceiveItems.Update(item);

                    if (customerModel != null && customerModel.Any())
                    {
                        var actualDate = await _companyDateService.GetActualDateAsync(item.CompanyId);

                        var kdSingle = new QuashAcceptancesRequestVo
                        {
                            billNo = item.Code,
                            subscriptionType = "D",
                            customerModel = customerModel,
                            ActualDate = actualDate.ToString("yyyy-MM-dd HH:mm:ss")
                        };
                        kdInput.Add(kdSingle);
                    }
                    if (kdInput.Any())
                    {
                        //推送金蝶
                        var kdRet = await _kingdeeApiClient.CancelReceive(kdInput);
                        if (kdRet.Code == CodeStatusEnum.Success)
                        {
                            await _unitOfWork.CommitAsync();
                            _logger.LogAzure("CancelReceiveTempDetail", $"撤销暂收款{item.Code}", "撤销暂收款");
                            return BaseResponseData<int>.Success("操作成功");
                        }

                        return kdRet;
                    }
                    else
                    {
                        await _unitOfWork.CommitAsync();
                        _logger.LogAzure("CancelReceiveTempDetail", $"撤销暂收款{item.Code}-撤销金额为0", "撤销暂收款");
                        return BaseResponseData<int>.Success("操作成功");
                    }
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：认款单明细不存在！");
                }
            }
            catch (Exception e)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + e.Message);
            }
        }

        /// <summary>
        /// 导出认款明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailsTask(
            [FromBody] ExportDetailsInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName ??= string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_recognizeReceiveExportDetailsTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("认款明细导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 导出明细包含应收
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailCreditsTask(
            [FromBody] ExportDetailsInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName ??= string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_recognizeReceiveExportDetailCreditsTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("认款明细包含应收导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 更新收款认领明细中的销售单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> UpdateBillCodeInRecognizeReceiveDetail(SaleBillCodeChangeInput input)
        {
            try
            {
                if (input == null || input.ChangeList == null || !input.ChangeList.Any())
                {
                    return BaseResponseData<int>.Failed(400, "操作失败，原因：单号变更列表为空");
                }

                var updateCount = 0;
                var codes = input.ChangeList.Select(s => s.OriginalBillCode).ToHashSet();
                // 查找需要更新的认款明细记录
                // Type = 2 表示订单号类型的认款明细
                var detailsToUpdate = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem)
                    .Where(t => t.RecognizeReceiveItem.CompanyId.ToUpper() == input.CompanyId.ToUpper() && codes.Contains(t.Code) && t.Type == 2)
                    .ToListAsync();

                var paymentModificationRequests = new List<BatchUpdateOrderNumberInput>();
                var acceptanceRequests = new List<BatchUpdateOrderNumberInput>();

                foreach (var item in detailsToUpdate.Where(w => string.IsNullOrEmpty(w.RecognizeReceiveItem.RelateCode)))
                {
                    // 调整code 到 NewBillCode
                    var changeItem = input.ChangeList.FirstOrDefault(cl => cl.OriginalBillCode == item.Code);
                    if (changeItem != null)
                    {
                        // 调用金蝶接口
                        var batchUpdateInput = new BatchUpdateOrderNumberInput
                        {
                            BillNo = item.RecognizeReceiveItem.Code,
                            AcceptanceEntryList = new List<OrderNumberUpdateEntry>
                            {
                                new OrderNumberUpdateEntry
                                {
                                    OrderNumber = changeItem.NewBillCode,
                                    OriginalOrderNumber = item.Code
                                }
                            }
                        };
                        item.Code = changeItem.NewBillCode;
                        item.UpdatedTime = DateTimeOffset.Now;
                        acceptanceRequests.Add(batchUpdateInput);
                    }
                }

                foreach (var item in detailsToUpdate.Where(w => !string.IsNullOrEmpty(w.RecognizeReceiveItem.RelateCode)))
                {
                    // 调整code 到 NewBillCode
                    var changeItem = input.ChangeList.FirstOrDefault(cl => cl.OriginalBillCode == item.Code);
                    if (changeItem != null)
                    {
                        // 调用金蝶接口
                        var batchUpdateInput = new BatchUpdateOrderNumberInput
                        {
                            BillNo = item.RecognizeReceiveItem.Code,
                            AcceptanceEntryList = new List<OrderNumberUpdateEntry>
                            {
                                new OrderNumberUpdateEntry
                                {
                                    OrderNumber = changeItem.NewBillCode,
                                    OriginalOrderNumber = item.Code
                                }
                            }
                        };
                        item.Code = changeItem.NewBillCode;
                        item.UpdatedTime = DateTimeOffset.Now;
                        paymentModificationRequests.Add(batchUpdateInput);
                    }
                }
                // 更新数据库中的订单号
                foreach (var detail in detailsToUpdate)
                {
                    _db.RecognizeReceiveDetails.Update(detail);
                }

                // 同时更新 RecognizeReceiveDetailCredit 表中的 OrderNo 字段
                var detailIds = detailsToUpdate.Select(d => d.Id).ToList();
                var creditDetailsToUpdate = await _db.RecognizeReceiveDetailCredits
                    .Where(rdc => detailIds.Contains(rdc.RecognizeReceiveDetailId))
                    .ToListAsync();

                _logger.LogAzure("UpdateBillCodeInRecognizeReceiveDetail",
                    $"找到 {creditDetailsToUpdate.Count} 条 RecognizeReceiveDetailCredit 记录需要更新 OrderNo",
                    "更新认款明细应收信息中的订单号");

                foreach (var creditDetail in creditDetailsToUpdate)
                {
                    // 找到对应的认款明细
                    var relatedDetail = detailsToUpdate.FirstOrDefault(d => d.Id == creditDetail.RecognizeReceiveDetailId);
                    if (relatedDetail != null)
                    {
                        // 找到对应的变更信息
                        var changeItem = input.ChangeList.FirstOrDefault(cl => cl.NewBillCode == relatedDetail.Code);
                        if (changeItem != null)
                        {
                            var oldOrderNo = creditDetail.OrderNo;
                            // 更新 OrderNo 字段
                            creditDetail.OrderNo = changeItem.NewBillCode;
                            creditDetail.UpdatedTime = DateTimeOffset.Now;
                            _db.RecognizeReceiveDetailCredits.Update(creditDetail);

                            _logger.LogAzure("UpdateBillCodeInRecognizeReceiveDetail",
                                $"更新 RecognizeReceiveDetailCredit OrderNo: {oldOrderNo} -> {changeItem.NewBillCode}, CreditId: {creditDetail.CreditId}",
                                "更新认款明细应收信息订单号");
                        }
                    }
                }

                await _unitOfWork.CommitAsync();
                StringBuilder sb = new StringBuilder();
                if (paymentModificationRequests.Any())
                {
                    var paymentResult = await _kingdeeFinanceClient.BatchUpdatePaymentModificationAsync(paymentModificationRequests);
                    if (!paymentResult.IsSuccess)
                    {
                        sb.Append(paymentResult.ErrorMessage);
                    }
                }
                if (acceptanceRequests.Any())
                {
                    // 批量调用金蝶接口
                    var acceptanceResult = await _kingdeeFinanceClient.BatchUpdateAcceptanceAsync(acceptanceRequests);
                    if (!acceptanceResult.IsSuccess)
                    {
                        sb.Append(acceptanceResult.ErrorMessage);
                    }
                }
                if (sb.Length > 0)
                {
                    throw new ApplicationException(sb.ToString());
                }
                return BaseResponseData<int>.Success(updateCount, "操作成功");
            }
            catch (Exception ex)
            {
                // 记录异常信息
                _logger.LogAzure("UpdateBillCodeInRecognizeReceiveDetail", $"错误：{ex.Message}", "调用金蝶接口异常", Application.Enums.LogLevelEnum.Error);
                return BaseResponseData<int>.Failed(500, $"【金蝶】更新订单号出错，原因：{ex.Message}");
            }
        }

        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }

            return dict;
        }
    }
}