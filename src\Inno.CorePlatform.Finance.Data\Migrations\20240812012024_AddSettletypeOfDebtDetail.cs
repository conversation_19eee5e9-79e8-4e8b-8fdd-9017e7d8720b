﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddSettletypeOfDebtDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "ProbablyPayTime",
                table: "DebtDetail",
                type: "datetime2",
                nullable: true,
                comment: "预计付款日期",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldComment: "预付款日期");

            migrationBuilder.AddColumn<DateTime>(
                name: "DraftBillExpireDate",
                table: "DebtDetail",
                type: "datetime2",
                maxLength: 200,
                nullable: true,
                comment: "到期日");

            migrationBuilder.AddColumn<string>(
                name: "Settletype",
                table: "DebtDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "结算方式");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DraftBillExpireDate",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "Settletype",
                table: "DebtDetail");

            migrationBuilder.AlterColumn<DateTime>(
                name: "ProbablyPayTime",
                table: "DebtDetail",
                type: "datetime2",
                nullable: true,
                comment: "预付款日期",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true,
                oldComment: "预计付款日期");
        }
    }
}
