﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 对账-存货明细表
    /// </summary>
    [Table("ReconciliationStockDetail")]
    public class ReconciliationStockDetailPo : BasePo
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public int? BillType { get; set; }

        /// <summary>
        /// 单据类型文本
        /// </summary>
        public string? BillTypeStr { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string? SaleOrderNo { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }
        /// <summary>
        /// 收入不含税
        /// </summary> 
        [Column(TypeName = "decimal(21,10)")]
        public decimal? IncomeOfNoTax { get; set; }
        /// <summary>
        /// 成本不含税
        /// </summary>
        [Column(TypeName = "decimal(21,10)")]
        public decimal? CostOfNoTax { get; set; }
        /// <summary>
        /// 收入含税
        /// </summary>
        [Column(TypeName = "decimal(21,10)")]
        public decimal? Income { get; set; }
        /// <summary>
        /// 成本含税
        /// </summary>
        [Column(TypeName = "decimal(21,10)")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// 发生额
        /// </summary>
        [Column(TypeName = "decimal(18,10)")]
        public decimal? ChangeAmount { get; set; }
         
        /// <summary>
        /// 对账Id
        /// </summary>
        [Comment("对账Id")]
        public Guid? ReconciliationItemId { get; set; }
        [ForeignKey("ReconciliationItemId")]
        public virtual ReconciliationItemPo? ReconciliationItem { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        public int? Mark { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        [Column(TypeName = "decimal(18,10)")]
        public decimal? StandardUnitCost { get; set; }

    }
}
