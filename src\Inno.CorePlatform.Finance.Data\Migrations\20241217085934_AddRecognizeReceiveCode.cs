﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRecognizeReceiveCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RecognizeReceiveCode",
                table: "DebtDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "认款单号");

            migrationBuilder.AddColumn<string>(
                name: "RecognizeReceiveCode",
                table: "Abatement",
                type: "nvarchar(max)",
                nullable: true,
                comment: "认款单号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RecognizeReceiveCode",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "RecognizeReceiveCode",
                table: "Abatement");
        }
    }
}
