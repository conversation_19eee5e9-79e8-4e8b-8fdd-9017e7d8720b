﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 发票入账单明细
    /// </summary>
    [Table("InvoiceReceiptDetail")]
    public class InvoiceReceiptDetailPo : BasePo
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid InvoiceReceiptItemId { get; set; }

        [ForeignKey("InvoiceReceiptItemId")]
        public InvoiceReceiptItemPo InvoiceReceiptItem { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCheckCode { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 是否取消
        /// </summary>

        [Comment("是否取消")]
        public bool? IsCancel { get; set; }
      
        /// <summary>
        /// 备注
        /// </summary>
        [Comment("备注")]
        public string? Remark { get; set; }
    }
}
