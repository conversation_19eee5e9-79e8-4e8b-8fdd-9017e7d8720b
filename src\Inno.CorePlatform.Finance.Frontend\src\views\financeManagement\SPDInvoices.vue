<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>SPD发票清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL>
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button slot="reference" :disabled="!isSPDWaitSubmit" type="primary" @click="submit">提交至SPD</el-button>
              <el-button slot="reference" type="warning" @click="exportdata">导出数据</el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.SPDStatus" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                  <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                  <el-tab-pane :label="`待审核(${tabCount.waitAuditCount})`" name="1" lazy />
                  <el-tab-pane :label="`已驳回(${tabCount.rejectCount})`" name="88" lazy />
                  <el-tab-pane :label="`已完成(${tabCount.complateCount})`" name="99" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            :data="crud.data"
            stripe
            fit1
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column label="发票号" property="invoiceNo" fixed="left" show-overflow-tooltip sortable min-width="100">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票代码" property="invoiceCode" sortable min-width="110" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票类型" property="type" min-width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.type }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="invoiceTime" label="开票日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.invoiceTime === null
                ? ''
                : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="开票金额" property="invoiceAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="开票申请单号" property="code" min-width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" width="300" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="SPD状态" property="spdStatusStr" min-width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.spdStatusStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="statusStr" min-width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.statusStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.createdTime === null
                ? ''
                : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link
                  v-if="scope.row.code"
                  style="font-size: 12px"
                  type="primary"
                  @click="
                    downloadFile(scope.row.invoiceNo, scope.row.invoiceCode)
                  "
                >查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud" border hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`应收明细`" />
              </el-tabs>
            </template>
            <!-- <template #default>
              <template v-if="crud.selections">
                <inno-button-tooltip type="primary">同步三方单号</inno-button-tooltip>
              </template>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>-->
          </inno-crud-operation>
          <el-table
            ref="tableRef2"
            v-inno-loading="crud2.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crud2.data"
            stripe
            @selection-change="crud2.selectionChangeHandler"
            @row-click="crud2.rowClick"
          >
            <el-table-column type="selection" fixed="left" width="35" />
            <el-table-column label="发票号" property="invoiceNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="应收单号" property="billCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.billDate === null
                ? ''
                : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="关联单号" property="relateCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="销售子系统" property="saleSystemName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="开票金额" property="creditAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.creditAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="是否确认收入" property="isSureIncomeStr" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.isSureIncomeStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="部门" property="businessDeptFullName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="三方单号" property="shipmentCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.shipmentCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红字消耗单号" property="redReversalConsumNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.redReversalConsumNo }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud2.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud2" />
          </div>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable,ElLoading } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { parse } from 'qs';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ExportInvoices } from '@/api/financeapi';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
const functionUris = {
  export: 'metadata://fam/finance-InvoiceCredit/functions/excute-export'
};
//获取路由
const router = useRouter();
const route = useRoute();
const tableItem = ref<InstanceType<typeof ElTable>>();
const props = defineProps({
  __refresh: Boolean
});
const isSPDWaitSubmit = ref(false);
const crud = CRUD(
  {
    title: '发票清单',
    url: '/api/InvoiceQuery/GetInvoices',
    idField: 'id',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      searchKey: '',
      SPDStatus: -1,
      IsPassZero: -1,
      IsSimple: 1
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
          if(crud.data[0].spdStatusStr === '待提交'){
            isSPDWaitSubmit.value = true
          }
        }
        loadTableData();                                                                
      }
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableItem
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '应收明细',
    url: '/api/InvoiceQuery/GetSPDInvoiceCreditsByInvoiceNo',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list', 
      total: 'total'
    },
    tablekey: 'tableRef2',
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRef2
  }
);
onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();
  }
});
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  crud.toQuery();
});
const InvoiceTypelist = [
  {
    label: '电子普通发票',
    value: '电子普通发票'
  },
  {
    label: '电子专用发票',
    value: '电子专用发票'
  },
  {
    label: '纸质普通发票',
    value: '纸质普通发票'
  },
  {
    label: '纸质专用发票',
    value: '纸质专用发票'
  },
  {
    label: '增值税普通发票(卷票)',
    value: '增值税普通发票(卷票)'
  },
  {
    label: '数电票(增值税专用发票)',
    value: '数电票(增值税专用发票)'
  },
  {
    label: '数电票(普通发票)',
    value: '数电票(普通发票)'
  }
];
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  return [
    {
      key: 'invoiceNo',
      label: '发票号',
      show: true
    },
    {
      key: 'invoiceCode',
      label: '发票代码',
      show: true
    },
    {
      key: 'type',
      label: '发票类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: InvoiceTypelist,
      show: true
    },
    {
      key: 'billDateBeging',
      endDate: 'billDateEnd',
      label: '开票日期',
      type: 'daterange',
      show: true,
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ]
    },
    {
      key: 'customerId',
      label: '客户',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      placeholder: '客户搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`,
      valueK: 'businessUnitID',
      labelK: 'businessUnitName',
      props: { KeyWord: 'nameLike', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'code',
      label: '开票申请单号',
      show: true
    },
    {
      key: 'companyId',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      placeholder: '公司搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'shipmentCode',
      label: '三方单号',
      show: true
    }
  ];
});

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      var i = 0;
      crud.selections.forEach((item) => {
        if (item.spdStatusStr !== '待提交') {
          isSPDWaitSubmit.value = false;
        } else {
          i++;
        }
      });
      if (i === crud.selections.length) {
        isSPDWaitSubmit.value = true;
      }
      crud2.query = {
        invoiceNos: crud.selections.map(x=>x.invoiceNo),
        companyIds: crud.selections.map(x=>x.companyId),
        serviceIds: crud.selections.map(x=>x.serviceId)
      };
      crud2.toQuery();
    }
  },
  { deep: true }
);

let tabCount = ref({
  waitSubmitCount: 0,
  waitAuditCount: 0,
  rejectCount: 0,
  complateCount:0,
  allCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/InvoiceQuery/GetSPDTabCount',
    data: {
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
 
const exportdata = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => { 
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
    var exportquery = JSON.parse(JSON.stringify(crud.query));
    exportquery['page'] = 0;
    exportquery['limit'] = 0;
    ExportInvoices(exportquery);
    loading.close();
  });
};

const downloadFile = (invoiceNo, invoiceCode) => {
  request({
    url:
      '/api/CreditQuery/GetKDFilePath?invoiceNo=' +
      invoiceNo +
      '&invoiceCode=' +
      invoiceCode,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          FileViewer.show(
            res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
            0, // 默认打开的下标
            {} // FileViewer props
          );
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶附件，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};

const submit = () => { 
 const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  request({
    url: '/api/InvoiceQuery/PushSPD',
    data: {
      InvoiceNos: crud.selections.map(p=>p.invoiceNo)
    },
    method: 'post'
  })
    .then((res) => {
      if (res.data.code === 200) {
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: '提交成功!',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
       loading.close();
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
       loading.close();
    });

} 
</script>
