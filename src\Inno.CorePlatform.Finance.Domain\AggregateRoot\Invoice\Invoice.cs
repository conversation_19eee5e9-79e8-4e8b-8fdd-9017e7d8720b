﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice
{
    public class Invoice : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 发票号
        /// </summary>  
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>  
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>  
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary> 
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 税额
        /// </summary> 
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string Type { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary> 
        public string? NameCode { get; set; }

        /// <summary>
        /// 运营制作开票Code
        /// </summary>   
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary> 
        public string? Remark { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        ///  关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// SPD状态
        /// </summary>
        public SPDStatusEnum? SPDStatus { get; set; }

        /// <summary>
        /// 初始发票入账（1=是，反之是否）
        /// </summary>
        public int? InitInvoiceReceipt { get; set; }
        /// <summary>
        /// 终端医院Id
        /// </summary> 
        public Guid? HospitalId { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
        public string? CustomerName { get; set; }
        /// <summary>
        ///价格来源
        /// </summary>  
        [Comment("价格来源")]
        public PriceSourceEnum? PriceSource { get; set; }
    }
}
