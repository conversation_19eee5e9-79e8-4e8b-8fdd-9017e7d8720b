﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addLossRecognitionItemCreditBillCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "项目单号");

            migrationBuilder.AddColumn<string>(
                name: "CreditBillCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreditBillCode",
                table: "LossRecognitionDetail");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "项目单号");
        }
    }
}
