﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("LossRecognitionItem")]
    [Comment("损失确认申请单")]
    [Index("BillCode", IsUnique = true)]
    public class LossRecognitionItemPo : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(50)]
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        [MaxLength(100)]
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        [MaxLength(200)]
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        [MaxLength(200)]
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [MaxLength(200)]
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        [MaxLength(50)]
        public string NameCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [MaxLength(200)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 应收单合计金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum CreditType { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        [MaxLength(400)]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string? Remark { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }
        /// <summary>
        /// OA请求Id
        /// </summary>
        [MaxLength(50)]
        public string? OARequestId { get; set; }
        /// <summary>
        /// 明细数据
        /// </summary>
        public virtual List<LossRecognitionDetailPo> Details { get; set; } = new List<LossRecognitionDetailPo>();
    }
}
