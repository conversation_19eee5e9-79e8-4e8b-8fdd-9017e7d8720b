<template>
  <el-dialog
    v-model="createDialog"
    draggable
    :before-close="handleClose"
    :destroy-on-close="true"
    close-on-click-modal="false"
    modal-class="position-fixed"
    width="80%"
    hight="70%"
  >
    <template #title>
      <!-- 使用行内元素 span 包裹内容，确保不换行 -->
      <span>
        批量新增
        <!-- 新增的粗体文本 -->
        <span style="font-weight: bold; margin-left: 15px;" @click="copyToClipboard(props.canAmount)">认款单可认款金额：{{ props.canAmount }}</span>
      </span>
    </template>
    <div>
      <el-form
        ref="formRef"
        :model="form"
        label-position="right"
        :rules="formRules"
      >
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item
              v-if="classifyType === 1"
              label="认款类型"
              prop="type"
            >
              <el-select
                v-model="form.type"
                filterable
                placeholder="认款类型"
                @change = "typeChange()"
                style="width: 200px"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="classifyType === 1"
              label="业务编号"
              prop="code"
            >
              <el-input
                v-model="form.code"
                placeholder="订单/发票/应收单号"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="classifyType === 2"
              label="收款类型"
              prop="collectionType"
            >
              <el-select v-model="form.collectionType">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item v-if="classifyType === 1" label="起止时间">
              <el-date-picker
                v-model="availableTime"
                :default-time="defaultTime"
                type="daterange"
                range-separator="-"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handledateBlur"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item v-if="classifyType === 1" label="客户" prop="custom">
              <inno-remote-select
                v-model="form.customerId"
                :is-guid="1"
                :disabled="!isrc"
                placeholder="请选择客户"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                :url="`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
                @change="handleSelectChange"
              />
            </el-form-item>
          </el-col>
          <inno-button-tooltip type="primary" :loading="queryLoading" @click="query">
            查询
          </inno-button-tooltip>
        </el-row>

        <div style="margin-bottom: 10px">
          <el-table
            ref="tablePre"
            class="auto-layout-table"
            height="40vh"
            highlight-current-row
            :loading = "queryLoading"
            :data="classifyType === 1 ? crudPre.data : crudPro.data"
            row-key="businessId" 
            :expand-row-keys="expandedRows"
            border
            show-summary
            :summary-method="getSummaries"
            @selection-change="handleSelectionChange"
            @sort-change="crudPre.sortChange"
          >
            <el-table-column type="selection"/>
            
            <!-- 新增用于显示带展开折叠功能的子表格的列 -->
            <el-table-column type="expand">
              <template #default="{ row }">
                <div v-if="(form.type === '1' || form.type === '2')" style="padding-left: 47px">
                  <el-table
                    :data="row.creditInfo"
                    class="auto-layout-table"
                    stripe
                    border
                    fit
                    @expand="handleTableExpand(row)" 
                     @selection-change="(selection) => handlechildSelectionChange(row, selection)"
                  >
                    <el-table-column type="selection"/>
                    <el-table-column label="应收单号" property="billCode" width="220" show-overflow-tooltip>
                      <template #header="{ column }">
                        <inno-header-filter :config="{ key:'billCode' }" :crud="crudCurrent" @search="curdCurrentQuery(row)" @cancel="curdCurrentCancelBillCode(row)" :column="column" />
                      </template>
                      <template #default="scope">{{ scope.row.billCode }}</template>
                    </el-table-column>
                    <el-table-column label="应收类型" property="creditTypeStr" width="120" show-overflow-tooltip>
                      <template #header="{ column }">
                        <inno-header-filter :config="{ key:'creditTypeStr' }" :crud="crudCurrent" @search="curdCurrentQuery(row)" @cancel="curdCurrentCancelCreditTypeStr(row)" :column="column" />
                      </template>
                      <template #default="scope">{{ scope.row.creditTypeStr }}</template>
                    </el-table-column>
                    <el-table-column label="单据日期" property="billDateStr" width="110" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.billDateStr }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="订单号" property="orderNo" width="220" show-overflow-tooltip>
                      <template #header="{ column }">
                        <inno-header-filter :config="{ key:'orderNo' }" :crud="crudCurrent" @search="curdCurrentQuery(row)" @cancel="curdCurrentCancelOrderNo(row)" :column="column" />
                      </template>
                      <template #default="scope">{{ scope.row.orderNo }}</template>
                    </el-table-column>
                    <el-table-column label="项目" property="projectName" width="120" show-overflow-tooltip>
                      <template #header="{ column }">
                        <inno-header-filter :config="{ key:'projectName' }" :crud="crudCurrent" @search="curdCurrentQuery(row)" @cancel="curdCurrentCancelProjectName(row)" :column="column" />
                      </template>
                      <template #default="scope">{{ scope.row.projectName }}</template>
                    </el-table-column>
                    <el-table-column label="业务单元" property="serviceName" width="120" show-overflow-tooltip>
                      <template #header="{ column }">
                        <inno-header-filter :config="{ key:'serviceName' }" :crud="crudCurrent" @search="curdCurrentQuery(row)" @cancel="curdCurrentCancelServiceName(row)" :column="column" />
                      </template>
                      <template #default="scope">{{ scope.row.serviceName }}</template>
                    </el-table-column>
                    <el-table-column label="金额" property="value" width="120" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.value }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="本次认款金额" property="currentValue" width="100" show-overflow-tooltip>
                      <template #default="scope">
                        <el-input
                          v-model="scope.row.currentValue"
                          type="number"
                          @input="computeAmount(row)"
                        ></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="应收可认款金额" property="surplusValue" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.surplusValue }}</inno-button-copy>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 1"
              label="订单/发票/应收单号"
              property="businessId"
              min-width="100"
              width="250"
            >
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.businessId }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 2"
              label="项目编号"
              property="code"
              width="250"
            >
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.code }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 2"
              label="项目名称"
              property="name"
              width="250"
            >
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.name }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column v-if="false" label="认款类型" property="type">
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.type }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 1"
              label="客户"
              property="customerName"
              width="250"
            >
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.customerName }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 1"
              label="终端客户"
              property="hospitalName"
              width="250"
            >
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.hospitalName }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column
              v-if="classifyType === 1"
              label="是否跳号"
              property="isSkip"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.isSkip"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                  size="large"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="开票日期"
              property="invoiceTime"
              min-width="120"
              sortable="custom"
            >
              <template #default="scope">
                {{ dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column
              label="总金额"
              property="totalAmount"
              class-name="isSum"
            >
              <template #default="scope">
                <inno-numeral :value="scope.row.totalAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="本次认款金额" property="amount" width="100"
                  class-name="isSum">
              <template #default="scope">
                <el-input
                  v-model="scope.row.amount"
                  type="number"
                  :disabled="(form.type === '1' || form.type === '2') && scope.row.creditInfo !== null && scope.row.creditInfo.length > 0"
                  @input="verifySingle"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column :label="canAmountTitle" property="canAmount" width="150">
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.canAmount }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="备注" property="remark">
              <template #default="scope">
                <el-input v-model="scope.row.remark"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="细分类型" property="classify">
              <template #default="scope">
                <el-select v-model="scope.row.classify">
                  <el-option
                    v-for="item in classifyEnum"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ multipleSelection.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination
              :crud="classifyType === 1 ? crudPre : crudPro"
            ></inno-crud-pagination>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          :disabled="classifyType === 1 ? !isChecked : false"
          @click="save"
        >
          添加到明细
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ref,
  toRef,
  toRaw,
  watch,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  nextTick
} from 'vue';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import request from '@/utils/request';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { FormRules } from 'element-plus';
import { backendUrl, gatewayUrl } from '@/public-path';
import { InitData } from '@/api/financeapi';
import { Decimal } from 'decimal.js';
import _ from 'lodash';
const props = defineProps({
  showDialog: Boolean,
  itemId: { type: String, default: '' },
  cId: { type: String, default: '' },
  customerId: { type: String, default: '' },
  isReturnCustomer: { type: Boolean, default: false },
  classifyType: { type: Number, default: 1 },
  detailId: { type: String, default: '' },
  editdata: { type: Array, default: null },
  canAmount: { type: Number, default: 0 }
});
let createDialog = ref(false);
watch(
  () => props.showDialog,
  (newVulue) => {
    createDialog.value = newVulue;
    //crudPre.data = props.data;
    //拼装数据
    if (props.editdata != null && props.editdata.length > 0) {
      //initData();
      crudPre.data = [];
      multipleSelection = [];
    } else {
      multipleSelection = [];
      crudPre.data = [];
    }
    isChecked.value = false;
    form.customerId = props.customerId.toLowerCase();
  }
);
const isrc = ref(false);
watch(
  () => props.isReturnCustomer,
  (newVulue) => {
    isrc.value = newVulue;
  }
);
  const tablePre = ref<InstanceType<typeof ElTable>>();
  const tablePro = ref<InstanceType<typeof ElTable>>();
const formRef = ref();
const emits = defineEmits(['closeDialog', 'refreshIndex']);
const handleClose = () => {
  createDialog.value = false;
  emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
  // console.log('createDialog.value', createDialog.value);
};
const handleSelectChange = () => {
  // console.log(JSON.stringify(form.customerId));
  // if (form.custom !== undefined && form.custom !== null) {
  //   form.customerId = form.custom.id
  // } else {
  //   form.customerId = ''
  // }
};
const classifyEnum = ref([
  { id: 1, name: '租金' },
  { id: 2, name: '商品款' },
  { id: 3, name: '服务费' }
]);
// 初始化数据
const initData = () => {
  InitData(props.editdata)
    .then((res) => {
      crudPre.data = res.data.data.list;
    })
    .catch((err) => {
      console.log(err);
    });
};
const availableTime = ref([]);
const defaultTime = reactive([
  new Date(0, 0, 0, 0, 0, 0),
  new Date(0, 0, 0, 23, 59, 59)
]);
const handledateBlur = () => {
  if (availableTime.value != null && availableTime.value.length === 2) {
    form.startDate = availableTime.value[0];
    form.endDate = availableTime.value[1];
  } else {
    form.startDate = 0;
    form.endDate = 0;
  }
};
const getDetail = (id) => {
  request({
    url: '/api/RecognizeReceiveQuery/getdetailInfo',
    method: 'POST',
    params: { id }
  })
    .then((res) => {
      if (res.data.code === 200) {
        form.value = res.data.data;
        form.value.type = res.data.data.type.toString();
        if (res.data.data.collectionType != null)
          form.value.collectionType = res.data.data.collectionType.toString();
      } else {
        ElMessage.error(res.data.message);
      }
    })
    .catch((t) => {
      ElMessage.error(t.message);
    });
};
//勾选合计
//总金额
const checkTotal = ref(0);
//本次认款总和
const checkCurrentValueTotal = ref(0);
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        if (column.property === 'totalAmount') {
          sums[index] = rbstateFormat(checkTotal.value);
        }
        else if (column.property === 'amount') {
          sums[index] = rbstateFormat(checkCurrentValueTotal.value);
        }
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(2);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
// 将这个方法暴露出去,这样父组件就可以使用了哈
defineExpose({
  getDetail
});
//表单规则校验
const formRules = reactive<FormRules>({
  type: [
    {
      required: true,
      message: '请选择认款类型',
      trigger: 'change'
    }
  ]
});
//提交是否校验通过
const isChecked = ref(false);
//表单提交数据
let form = reactive({
  customerId: '',
  type: '',
  code: '',
  startDate: 0,
  endDate: 0,
  custom: {},
  collectionType: '100',
  tabData: []
});
//认款类型枚举
const typeList = [
  {
    id: '1',
    name: '发票'
  },
  {
    id: '2',
    name: '订单'
  },
  {
    id: '3',
    name: '初始应收'
  }
];
const crudPre = CRUD(
  {
    title: '应用',
    url: backendUrl + 'api/RecognizeReceive/GetListByType',
    method: 'post',
    tablekey: 'tablekeyStorage',
    query: {
      customerId: form.customerId,
      startDate: new Date(form.startDate).getTime(),
      endDate: new Date(form.endDate).getTime(),
      type: form.type,
      code: form.code,
      recognizeReceiveItemId: props.itemId,
      companyId: props.cId
    },
    idField: 'id',
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crudPre.data.length && crudPre.data.length > 0) {
          crudPre.singleSelection(crudPre.data[0]);
          checkTotal.value = crudPre.data[0].totalAmount;
          checkCurrentValueTotal.value = crudPre.data[0].amount;
          queryLoading.value = false;
        }else{
          queryLoading.value = false;
        }
      }
    }
  },
  {
    table: tablePre
  }
);
const crudPro = CRUD(
  {
    title: '应用',
    url: gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta',
    method: 'post',
    tablekey: 'tablekeyStorage',
    query: { status: 2 },
    idField: 'id',
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tablePro
  }
);
const options = [
  {
    value: '100',
    label: '销售回款'
  },
  {
    value: '101',
    label: '预收款'
  },
  {
    value: '102',
    label: '退采购付款'
  },
  {
    value: '104',
    label: '代收款'
  },
  {
    value: '108',
    label: '赔款'
  },
  {
    value: '109',
    label: '收回之前支付的押金'
  },
  {
    value: '110',
    label: '收回之前支付的投标保证金'
  },
  {
    value: '111',
    label: '收回之前支付的履约保证金'
  },
  {
    value: '115',
    label: '工会经费返还'
  },
  {
    value: '116',
    label: '政府补贴'
  },
  {
    value: '117',
    label: '即征即退'
  },
  {
    value: '118',
    label: '个税返还'
  },
  {
    value: '119',
    label: '税费返还'
  },
  {
    value: '120',
    label: '出口退税'
  },
  {
    value: '121',
    label: '所得税退税'
  },
  {
    value: '122',
    label: '保险理赔收入'
  },
  {
    value: '123',
    label: '收到保证金'
  },
  {
    value: '124',
    label: '收到招标押金'
  },
  {
    value: '127',
    label: '收回之前支付的海关保证金'
  },
  {
    value: '129',
    label: '收回之前支付的医院保证金'
  },
  {
    value: '999',
    label: '其他'
  }
];
//查询数据
const queryLoading = ref(false);
const query = () => {
  queryLoading.value = true;
  crudPre.data = [];
  if (props.classifyType === 1) {
    formRef.value.validate((valid, field) => {
      if (valid) {
        crudPre.query.customerId = form.customerId;
        crudPre.query.startDate = new Date(form.startDate).getTime();
        crudPre.query.endDate = new Date(form.endDate).getTime();
        crudPre.query.type = form.type;
        crudPre.query.code = form.code;
        crudPre.query.recognizeReceiveItemId = props.itemId;
        crudPre.query.companyId = props.cId;
        crudPre.toQuery();
        //queryLoading.value = false;
      } else {
        queryLoading.value = false;
      }
      //
    });
  } else {
    queryLoading.value = true;
    crudPro.query.status = 2;
    crudPro.toQuery();
    //queryLoading.value = false;
    // console.log(JSON.stringify(crudPro.data));
  }
};
const orderRef = ref();
let saveLoading = ref(false);
// let multipleSelection = ref([]);
let multipleSelection:any =[];
//获取多选
const handleSelectionChange = (val) => {
  multipleSelection = val;
  verifySingle();
  checkTotal.value = 0; //总金额
  checkCurrentValueTotal.value = 0; //本次认款总和
  val.map((item) => {
    checkTotal.value += item.totalAmount;
    checkCurrentValueTotal.value += Number(item.amount);
  });
  expandedRows.value.splice(0);
  val.map(el=>{
    expandedRows.value.push(el.businessId);
  })
};
//子表格获取多选
const handlechildSelectionChange = (parentRow,val) => {
  console.log('父级行数据：' + JSON.stringify(parentRow));
  console.log('勾选行数据' + JSON.stringify(val));
  if(val.length > 0){
    parentRow.seleteChild = val;
    parentRow.amount = val.reduce((accumulator:any, currentProduct:any) => {
        return new Decimal(accumulator).add(currentProduct.currentValue);
    }, 0);
    nextTick(() => {
      if (parentRow) {
        tablePre.value.toggleRowSelection(parentRow, true); // 选中该行
      }
    });
  }else{
    parentRow.amount = parentRow.creditInfo.reduce((accumulator:any, currentProduct:any) => {
        return new Decimal(accumulator).add(currentProduct.currentValue);
    }, 0);
    nextTick(() => {
      if (parentRow) {
        tablePre.value.toggleRowSelection(parentRow, false); // 选中该行
      }
    });
  }
}

//虚假的本地crud
const crudCurrent = ref({
  toQuery: () => {

   }, query: { 
    billCode: '',
    creditTypeStr: '',
    orderNo: '',
    projectName: '',
    serviceName: '',
    code: '',
    type: ''
} });
//已展开的行数
const expandedRows = ref([]);
const handleTableExpand = (row) => {
  expandedRows[row.id] = true; // 根据行数据的唯一标识（这里是id），记录该行已展开
};
//计算行当前认款金额
const computeAmount = (parentRow:any) => {
  if (parentRow) { 
    // let sum = 0;
    // // 遍历当前父行对应的子表格数据（假设子表格数据在creditInfo数组中）
    // parentRow.creditInfo.forEach((subRow) => {
    //   sum += parseFloat(subRow.currentValue) || 0;
    // });
    // // 将计算得到的总和赋值给父行的amount属性
    // parentRow.amount = sum;
    if(parentRow.seleteChild && parentRow.seleteChild.length>0){
      parentRow.amount = parentRow.seleteChild.reduce((accumulator:any, currentProduct:any) => {
        return new Decimal(accumulator).add(currentProduct.currentValue);
    }, 0);
    }else{
      parentRow.amount = parentRow.creditInfo.reduce((accumulator:any, currentProduct:any) => {
        return new Decimal(accumulator).add(currentProduct.currentValue);
    }, 0);
    }
    
    verifySingle();
  }
}
//本地筛选
const curdCurrentQuery = (parentRow) => {
  var creditInfos = parentRow.creditInfo;
  crudCurrent.value.query.code = parentRow.businessId;
  crudCurrent.value.query.type = form.type;
  console.log(JSON.stringify(crudCurrent.value.query))
  //查询
  request({
    url: '/api/RecognizeReceive/GetCreditInfos',
    method: 'post',
    data: crudCurrent.value.query
  })
  .then((res) => {
    if (res.data.code === 200) {
      parentRow.creditInfo = res.data.data;
      parentRow.amount = 0;
      res.data.data.forEach(item => {
        if (item.hasOwnProperty('currentValue')) {
          parentRow.amount += item.currentValue;
        }
      });
      return;
    } else {
      parentRow.creditInfo = creditInfos;
      return;
    }
  })
  .catch(() => {
    parentRow.creditInfo = creditInfos;
    return;
  });
}
//重置应收单号
const curdCurrentCancelBillCode = (parentRow) => {
  crudCurrent.value.query.billCode = '';
  curdCurrentQuery(parentRow);
}
//重置应收类型
const curdCurrentCancelCreditTypeStr = (parentRow) => {
  crudCurrent.value.query.creditTypeStr = '';
  curdCurrentQuery(parentRow);
}
//重置订单号
const curdCurrentCancelOrderNo = (parentRow) => {
  crudCurrent.value.query.orderNo = '';
  curdCurrentQuery(parentRow);
}
//重置项目名称
const curdCurrentCancelProjectName = (parentRow) => {
  crudCurrent.value.query.projectName = '';
  curdCurrentQuery(parentRow);
}
//重置业务单元
const curdCurrentCancelServiceName = (parentRow) => {
  crudCurrent.value.query.serviceName = '';
  curdCurrentQuery(parentRow);
}
//验证数据
const verifySingle = () => {
  checkCurrentValueTotal.value = 0;
  if (multipleSelection.length > 0) {
    multipleSelection.map((item) => {
      console.log('本次认款金额：' + item.amount + ',可认款金额：' + item.canAmount);
      if (item.amount > item.canAmount) {
        ElMessage({
          showClose: true,
          message: '收款金额应小于可认款金额',
          type: 'warning',
          duration: 3 * 1000
        });
        item.amount = 1;
        item.creditInfo.forEach((childRow,index) => {
          if (index === 0) {
            childRow.currentValue = 1;
          } else {
            childRow.currentValue = 0;
          }
        });
        isChecked.value = false;
        return;
      } else {
        checkCurrentValueTotal.value = Number(checkCurrentValueTotal.value) + Number(item.amount);
      }
    });
    //调用接口保存
    request({
      url: '/api/RecognizeReceive/VerifySingleData',
      method: 'post',
      data: {
        recognizeReceiveItemId: props.itemId,
        classifyType: props.classifyType,
        type: form.type,
        list: multipleSelection
      }
    })
      .then((res) => {
        if (res.data.code != 200) {
          isChecked.value = false;
          ElMessage({
            showClose: true,
            message: '所选行' + res.data.message,
            type: 'warning',
            duration: 3 * 1000
          });
          return;
        } else {
          if (multipleSelection.length > 0) {
            isChecked.value = true;
          }
        }
      })
      .catch(() => {
        ElMessage({
          showClose: true,
          message: '请正确输入信息',
          type: 'warning',
          duration: 3 * 1000
        });
        isChecked.value = false;
        return;
      });
  } else {
    isChecked.value = false;
  }
};
//保存
const save = () => {
  if (multipleSelection.length < 1) {
    ElMessage({
      showClose: true,
      message: '请至少选择一行数据',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var list = multipleSelection;
  list.forEach((item, index) => {
    index = Number(index) + 1;
    if (typeof item.amount === 'undefined' || item.amount == null) {
      ElMessage({
        showClose: true,
        message: '请填写勾选中第' + index + '行的本次认款金额',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    }
  });
  let seleteData:any = [];
  let cloneData = _.cloneDeep(multipleSelection);
  cloneData.map(el=>{
    el.creditInfo = el.seleteChild;
    seleteData.push(el);
  })
 
  saveLoading.value = true;
  //调用接口保存
  request({
    url: '/api/RecognizeReceive/SaveBatchDetailAsync',
    method: 'post',
    data: {
      recognizeReceiveItemId: props.itemId,
      classifyType: props.classifyType,
      type: form.type,
      list: seleteData
    }
  })
    .then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '保存成功',
          type: 'success',
          duration: 3 * 1000
        });

        saveLoading.value = false;
       /* createDialog.value = false;*/
        //emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
        emits('refreshIndex');
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        saveLoading.value = false;
      }
    })
    .finally(() => {
      saveLoading.value = false;
    });
};

const canAmountTitle = ref('可认款金额');
// 类型改变事件
const typeChange = () => {
  if (form.type === '1') {
    canAmountTitle.value = '发票可认款金额';
  } else if (form.type === '2') {
    canAmountTitle.value = '订单可认款金额';
  } else if (form.type === '3') {
    canAmountTitle.value = '初始应收可认款金额';
  }
  crudPre.toQuery();
}

//复制
const copyToClipboard = (content) => {
  navigator.clipboard.writeText(content).then(() => {
    // 可以添加复制成功提示，如使用Element UI的Message\
    ElMessage({
      showClose: true,
      message: '已复制到剪贴板: ' + content,
      type: 'success',
      duration: 3 * 1000
    });
  }).catch(err => {
    console.error('复制失败:', err);
    ElMessage({
      showClose: true,
      message: '复制失败',
      type: 'error',
      duration: 3 * 1000
    });
  });
}
</script>
