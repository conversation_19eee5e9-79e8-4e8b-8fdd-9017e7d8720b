﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class RecognizeReceiveDetailOfHospitalName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "HospitalId",
                table: "RecognizeReceiveDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "终端医院Id");

            migrationBuilder.AddColumn<string>(
                name: "HospitalName",
                table: "RecognizeReceiveDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "终端医院");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "RecognizeReceiveDetail");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "RecognizeReceiveDetail");
        }
    }
}
