﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AlterCustomizeInvoiceCredit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OriginSpecification",
                table: "CreditDetail");

            migrationBuilder.AlterColumn<decimal>(
                name: "Value",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,2)",
                nullable: false,
                comment: "应收金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "金额");

            migrationBuilder.AddColumn<decimal>(
                name: "CreditDetailAmount",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "应收明细金额");

            migrationBuilder.AddColumn<Guid>(
                name: "CreditDetailId",
                table: "CustomizeInvoiceCredit",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ProductId",
                table: "CustomizeInvoiceCredit",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProductNo",
                table: "CustomizeInvoiceCredit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Amount",
                table: "CreditDetail",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "NoInvoiceAmount",
                table: "CreditDetail",
                type: "decimal(18,4)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreditDetailAmount",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.DropColumn(
                name: "CreditDetailId",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.DropColumn(
                name: "ProductNo",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.DropColumn(
                name: "Amount",
                table: "CreditDetail");

            migrationBuilder.DropColumn(
                name: "NoInvoiceAmount",
                table: "CreditDetail");

            migrationBuilder.AlterColumn<decimal>(
                name: "Value",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,2)",
                nullable: false,
                comment: "金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "应收金额");

            migrationBuilder.AddColumn<string>(
                name: "OriginSpecification",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
