﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_CancelValue_To_RecognizeReceiveTempDetailPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "CancelValue",
                table: "RecognizeReceiveTempDetail",
                type: "decimal(18,2)",
                nullable: true);

            // 设置默认值
            migrationBuilder.Sql(@"
                -- 暂收款部分撤销金额初始化为0
                UPDATE RecognizeReceiveTempDetail SET CancelValue = 0 WHERE CancelValue is null;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CancelValue",
                table: "RecognizeReceiveTempDetail");
        }
    }
}
