﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class InvoiceProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CustomerId",
                table: "Invoice",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "Invoice",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "Invoice",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号");

            migrationBuilder.AddColumn<Guid>(
                name: "ProjectId",
                table: "Invoice",
                type: "uniqueidentifier",
                nullable: true,
                comment: "项目Id");

            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "Invoice",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目名称");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "ProjectId",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "Invoice");
        }
    }
}
