import request from '@/utils/request';

// 获取公司系统月度
export function getCompanySysMonth(id: string) {
    const url = `api/bff/GetSysmonth?companyId=${id}`;
    return request({
      url,
      method: 'get'
    });
}
// 获取应收列表
export function GetCreditList(data: any) {
    const url = `/api/LossRecognitionQuery/GetCreditList`;
    return request({
      url,
      method: 'post',
      data
    });
}
// 获取应付列表
export function GetDebtByCreditIds(data: any) {
    const url = `/api/LossRecognitionQuery/GetDebtByCreditIds`;
    return request({
      url,
      method: 'post',
      data
    });
}
// 创建接口
export function create(data: any) {
  return request({
    url: '/api/LossRecognition/Create',
    method: 'post',
    data
  });
}
// 提交
export function submit(id: string) {
  const url = `/api/LossRecognition/submit?id=${id}`;
  return request({
    url,
    method: 'put'
  });
}
// 对接获取详情接口
export function getItemById(id: any) {
  return request({
    url: `/api/LossRecognitionQuery/GetItemById?id=${id}`,
    method: 'post',
  });
}