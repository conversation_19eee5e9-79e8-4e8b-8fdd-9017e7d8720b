﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    public class AdvanceBusinessApply : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 垫资单号
        /// </summary>
        /// /// </summary>
        public string? Code { get; set; }
       
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string ProjectCode { get; set; }



        /// <summary>
        /// 老致新平台单号
        /// </summary>
        public string? OldZXCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        #region 公司信息
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 核准的资金占用总额度（万元）
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 已审批预计垫资额度（万元）
        /// </summary>
        public decimal? ExpectedAmount { get; set; }

        /// <summary>
        /// 截止上月末资金占用金额（万元）
        /// </summary>
        public decimal? TotalAmountOfMonth { get; set; }

        /// <summary>
        /// 尚未收回的应收金额（万元）
        /// </summary>
        public decimal? ReceivableAmountOfNon { get; set; }

        /// <summary>
        /// 已经逾期的应收金额（万元）
        /// </summary>
        public decimal? ReceivableAmountOfTimeout { get; set; }

        #endregion

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }

        public string? ServiceName { get; set; }

        /// <summary>
        /// 是否发票入账
        /// </summary>
        public bool IsInvoice { get; set; }

        /// <summary>
        /// 业务单元组别
        /// </summary>
        public string? ServiceGroup { get; set; }

        /// <summary>
        /// 供应商付款天数
        /// </summary>
        public int? ProvidePayDays { get; set; }

        #region 医院指标
        /// <summary>
        /// 医院Id
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 终端医院Id
        /// </summary>
        public Guid EndHospitalId { get; set; }

        /// <summary>
        /// 终端医院名称
        /// </summary>
        public string? EndHospitalName { get; set; }

        /// <summary>
        /// 医院年销售额(垫资销售额)万元
        /// </summary>
        public decimal? SalesVolume { get; set; }

        /// <summary>
        /// 医院回款天数（天）
        /// </summary>        
        public int? ReturnMoneyDays { get; set; }

        /// <summary>
        /// 是否核准医院
        /// </summary>
        public bool IsVerify { get; set; }

        /// <summary>
        /// 非核准医院申请原因
        /// </summary>
        public string? NonVerifyRemark { get; set; }

        /// <summary>
        /// 是否全流程接管医院
        /// </summary>
        public bool IsTakeOver { get; set; }

        /// <summary>
        /// 该医院尚未收回的垫资应收金额（万元）
        /// </summary>
        public decimal UnrecycledReceivableAmount { get; set; }

        /// <summary>
        /// 该医院已经逾期的垫资应收金额（万元）
        /// </summary>
        public decimal TimeOutReceivableAmount { get; set; }

        /// <summary>
        /// 上游在该医院垫资应收金额（万元）
        /// </summary>
        public decimal PreUnrecycledReceivableAmount { get; set; }

        /// <summary>
        /// 上游在该医院逾期垫资应收金额（万元）
        /// </summary>
        public decimal PreTimeOutReceivableAmount { get; set; }
        #endregion


        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        public decimal? SupplyChainDiscounts { get; set; }


        /// <summary>
        /// 终止时间
        /// </summary>
        public DateTime EndDateTime { get; set; }

        #region 风控指标
        /// <summary>
        /// 年化垫资利率（%）
        /// </summary>
        public decimal? RateOfYear { get; set; }

        /// <summary>
        /// 垫资比例（%）
        /// </summary>
        public decimal? Ratio { get; set; }

        /// <summary>
        /// 实际占用天数（天）
        /// </summary>
        public decimal? RealUseDays { get; set; }

        /// <summary>
        /// 实际供应链金融折扣（%）
        /// </summary>
        public decimal? RealSupplyChainDiscounts { get; set; }

        /// <summary>
        /// 合计折扣（%）
        /// </summary>
        public decimal? TotalDiscounts { get; set; }

        /// <summary>
        ///预计垫资金额（万元）
        /// </summary>
        public decimal? ExpectAmount { get; set; }

        /// <summary>
        ///预计垫资利息收入（万元）
        /// </summary>
        public decimal? ExpectInterestAmount { get; set; }
        #endregion

        /// <summary>
        /// 变更/终止原因 
        /// </summary>        
        public string? OperateNote { get; set; }
        public string StatusName { get; set; }

        /// <summary>
        /// 垫资申请说明
        /// </summary>
        public string? ApplyNote { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }
        /// <summary>
        ///1:按业务单元垫资、2:按供应商垫资
        /// </summary>
        public int? AdvanceModel { get; set; }
    }
}
