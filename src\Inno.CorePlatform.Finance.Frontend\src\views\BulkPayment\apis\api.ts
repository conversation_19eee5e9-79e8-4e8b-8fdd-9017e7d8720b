import request from '@/utils/request';

//经销入库导入明细
export function ImportDetail(data: any) {
    return request({
      url: '/api/BatchPayment/ExportBatchPaymentDetail',
      method: 'post',
      data
    });
  }
  //下载错误报告
export function GetTempFileCode(tempFileId: string) {
    return request({
      url: window.gatewayUrl + 'api/FileDownload/code?fileId=' + tempFileId,
      method: 'get'
    });
  }