﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 应收明细查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CreditDetailsController : BaseController
    {
        private readonly ICreditQueryService _creditQueryService;
        private readonly ICreditAppService _creditAppService;
        private readonly ILogger<CreditQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly ISellApiClient _sellApiClient;
        private readonly ICustomizeInvoiceAppService _customizeInvoiceAppService;
        /// <summary>
        /// 应收明细查询
        /// </summary>
        public CreditDetailsController(ICreditQueryService creditQueryService,
            ICreditAppService creditAppService,
            ILogger<CreditQueryController> logger,
            IPCApiClient pCApiClient,
            IBDSApiClient bDSApiClient,
            IInvoiceQueryService invoiceQueryService,
            ICustomizeInvoiceAppService customizeInvoiceAppService,
            IEasyCachingProvider easyCaching,
            IKingdeeApiClient kingdeeApiClient, ISubLogService subLog) : base(subLog)
        {
            _creditQueryService = creditQueryService;
            _creditAppService = creditAppService;
            _logger = logger;
            _pCApiClient = pCApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _invoiceQueryService = invoiceQueryService;
            _customizeInvoiceAppService= customizeInvoiceAppService;
            _bDSApiClient = bDSApiClient;
            _easyCaching= easyCaching;
        }

        /// <summary>
        /// 获取未开票应收单明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetCreditDetails")]
        public async Task<BaseResponseData<PageResponse<CreditDetailOutput>>> GetCreditDetailsAsync([FromBody] CreditDetailsInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetCreditDetailsAsync(query);
                return new BaseResponseData<PageResponse<CreditDetailOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<CreditDetailOutput>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 插入应收开票明细临时表
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddCreditInvoiceDetailTemps")]
        public async Task<BaseResponseData<string>> AddCreditInvoiceDetailTemps(List<CreditDetailOutput> inputs)
        {
            var cacheKey = $"AddCreditInvoiceDetailTemps_{Utility.GenerateMd5Hash(JsonConvert.SerializeObject(inputs))}";
            var cachedResult = await _easyCaching.GetAsync<string>(cacheKey);
            try
            {
                if (cachedResult != null && cachedResult.Value != null)
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：请勿重复操作，稍后5s后操作");
                }
                else
                {   // 设置缓存，有效期为5秒
                    await _easyCaching.SetAsync(cacheKey, "AddCreditInvoiceDetailTemps", TimeSpan.FromSeconds(5));
                    var ret = await _creditQueryService.AddCreditInvoiceDetailTemps(inputs, CurrentUser.UserName);
                    if (ret.Code!=CodeStatusEnum.Success)
                    { 
                        _easyCaching.Remove(cacheKey);
                    }
                    return ret;
                }
            }
            catch (Exception)
            {
                _easyCaching.Remove(cacheKey);
                throw;
            }
        }

        /// <summary>
        /// 根据用户名获取 CreditInvoiceDetailTemp 记录
        /// </summary> 
        /// <returns>符合条件的应收明细临时记录列表</returns>
        [HttpPost("GetCreditInvoiceDetailTempsByUserName")]
        public async Task<BaseResponseData<PageResponse<OriginDetailOutput>>> GetCreditInvoiceDetailTempsByUserNameAsync()
        {
            var list = await _creditQueryService.GetCreditInvoiceDetailTempsByUserNameAsync(CurrentUser.UserName);
            return new BaseResponseData<PageResponse<OriginDetailOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<OriginDetailOutput>() { List = list, Total = list.Count },
            };
        }

        /// <summary>
        /// 根据CreditDetailIds删除CreditInvoiceDetailTemp记录
        /// </summary>
        /// <param name="creditDetailIds">信用明细ID列表</param>
        /// <returns>操作结果（成功/失败）</returns>
        [HttpPost("DeleteCreditInvoiceDetailTempsByCreditDetailIds")]
        public async Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByCreditDetailIdsAsync(List<Guid> creditDetailIds)
        {
            var ret = await _creditQueryService.DeleteCreditInvoiceDetailTempsByCreditDetailIdsAsync(creditDetailIds);
            return ret;
        }
        /// <summary>
        /// 根据CreditDetailIds删除CreditInvoiceDetailTemp记录
        /// </summary>
        /// <param name="originDetailIds">信用明细ID列表</param>
        /// <returns>操作结果（成功/失败）</returns>
        [HttpPost("DeleteCreditInvoiceDetailTempsByOriginDetailIds")]
        public async Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByOriginDetailIdsAsync(List<string> originDetailIds)
        {
            var ret = await _creditQueryService.DeleteCreditInvoiceDetailTempsByOriginDetailIdsAsync(originDetailIds);
            return ret;
        }

        /// <summary>
        /// 保存开票明细（合并后）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveCustomizeInvoice")]
        public async Task<ResponseData<int>> SaveCustomizeInvoice([FromBody] SaveCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                input.Source = 1;
                var (count, msg) = await _customizeInvoiceAppService.SaveCustomizeInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count
                        },
                        Msg = msg
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}

