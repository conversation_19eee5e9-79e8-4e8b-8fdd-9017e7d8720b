﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class removeDebtIdOfDebtPaymentUseDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DebtPaymentUseDetail_Debt_DebtId",
                table: "DebtPaymentUseDetail");

            migrationBuilder.DropIndex(
                name: "IX_DebtPaymentUseDetail_DebtId",
                table: "DebtPaymentUseDetail");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_DebtPaymentUseDetail_DebtId",
                table: "DebtPaymentUseDetail",
                column: "DebtId");

            migrationBuilder.AddForeignKey(
                name: "FK_DebtPaymentUseDetail_Debt_DebtId",
                table: "DebtPaymentUseDetail",
                column: "DebtId",
                principalTable: "Debt",
                principalColumn: "Id");
        }
    }
}
