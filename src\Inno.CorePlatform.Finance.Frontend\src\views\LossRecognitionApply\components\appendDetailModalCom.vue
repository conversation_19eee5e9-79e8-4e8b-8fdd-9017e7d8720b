<template>
  <el-dialog v-model="model.controlModel.showAppendModal" title="添加应收" width="80%" @close="cancel" draggable>
    <span>
      <el-row :gutter="20">
        <el-col :span="6" :offset="0">
          <el-form-item label="应收单号">
            <el-input
              v-model="model.bindModel.detailModal.billCode"
              placeholder="输入应收单号查询"
              clearable
              @change="()=>{
                model.controlModel.productPagination.pageIndex = 1;
                queryProduct()
              }"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
          <el-form-item label="项目">
          <inno-remote-select
            v-model="model.bindModel.detailModal.projectId"
            :is-guid="1"
            default-first-option
            placeholder="请选择项目"
            @change="()=>{
              model.controlModel.productPagination.pageIndex = 1;
              queryProduct()
            }"
            :url="gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta'"
          />
        </el-form-item>
        </el-col>
        <!-- <el-col :span="6" :offset="0">
          <el-form-item label="客户">
            <inno-remote-select
                v-model="model.bindModel.detailModal.customerId"
                :is-guid="2"
                placeholder="请选择客户"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                :url="`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
                @change="queryProduct"
              />
          </el-form-item>
        </el-col> -->
        <el-col :span="6" :offset="0">
            <el-form-item label="业务单元">
              <inno-remote-select
                v-model="model.bindModel.detailModal.serviceId"
                isObject
                :is-guid="2"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                labelK="businessUnitName"
                valueK="businessUnitName"
                placeholder="请选择业务单元"
                :url="`${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`"
                @change="()=>{
                  model.controlModel.productPagination.pageIndex = 1;
                  queryProduct()
                }"
              />
            </el-form-item>
          </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" size="small" @click="queryProduct">查询</el-button>
        </el-col>
        </el-row>
      <el-table
        ref="refTable"
        v-inno-loading="model.controlModel.appendDetailTableLoading"
        max-height="350"
        :data="model?.dataSource.selectCreditDetails"
        style="width: 100%"
        border
        stripe
        @selection-change="handleSelectionChange"
        @row-click="clickRow"
      >
        <el-table-column type="selection" width="40" fixed="left" show-overflow-tooltip />
            <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip />
            <el-table-column label="应收单号" width="220" property="billCode" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
                <template #default="scope">
                  {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="项目名称" width="260" property="projectName" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                </template>
              </el-table-column>
              <!-- <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip></el-table-column> -->
              <el-table-column label="客户" width="220" property="customerName" show-overflow-tooltip></el-table-column>
              <el-table-column class-name="isSum" label="应收单金额" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="model.controlModel.productPagination.pageIndex"
        small
        style="margin-top: 10px; margin-bottom: 10xp"
        :page-sizes="[20, 40, 80, 100, 200, 500]"
        :page-size="model.controlModel.productPagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="model.controlModel.productPagination.total"
        background
        @size-change="
          (size) => {
            model.controlModel.productPagination.pageSize = size;
            queryProduct();
          }
        "
        @current-change="
          (currentPage) => {
            model.controlModel.productPagination.pageIndex = currentPage;
            queryProduct();
          }
        "
      >
        :pager-count="7">
      </el-pagination>
    </span>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="append"> 添加到明细 </el-button>
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { inject, ref } from 'vue';
import {
  LossRecognitionApplyVModel,
  IProductListItem,
  CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY
} from '../models/LossRecognitionApplyVModel';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
const model = inject<LossRecognitionApplyVModel>(CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY) as LossRecognitionApplyVModel;
const refTable = ref();
const gatewayUrl = window.gatewayUrl;
// 处理行选中
const handleSelectionChange = (items: Array<IProductListItem>) => {
  model.bindModel.detailModal.selectProducts = items;
};
// 处理行点击
const clickRow = (row: IProductListItem) => {
  let isSelected: boolean = !model.bindModel.detailModal.selectProducts.find((item) => {
    return item == row;
  });
  refTable.value!.toggleRowSelection(row, isSelected);
};
const append = () => {
  model.appendToDetails();
};
const queryProduct = () => {
  model.queryProduct();
};
const cancel = () => {
  model.bindModel.detailModal.billCode = '';
  model.bindModel.detailModal.projectId = '';
  model.bindModel.detailModal.serviceId  = '';
  model.controlModel.showAppendModal = false;
};
</script>
