﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    ///  迁移数据表
    /// </summary>
    [Table("MigrationRecords")]
    public class MigrationRecordPo : BasePo
    {
        /// <summary>
        /// 原始单号
        /// </summary> 
        [Comment("原始单号")]
        [MaxLength(200)]
        public string OriginBillCode { get; set; }


        /// <summary>
        /// 中间单号
        /// </summary> 
        [Comment("中间单号")]
        [MaxLength(200)]
        public string MiddleBillCode { get; set; }

        /// <summary>
        /// 新单号
        /// </summary> 
        [Comment("新单号")]
        [MaxLength(200)]
        public string NewBillCode { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        [Comment("数据类型")]
        public MigrationDataTypeEnum DataType { get; set; }


        /// <summary>
        /// 备注
        /// </summary> 
        [Comment("备注")]
        [MaxLength(1000)]
        public string? Remark { get; set; }
    }
}
