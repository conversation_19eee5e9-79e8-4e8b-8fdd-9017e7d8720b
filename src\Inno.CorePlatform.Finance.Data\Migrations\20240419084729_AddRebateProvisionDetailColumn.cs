﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRebateProvisionDetailColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BizOrg",
                table: "RebateProvisionDetail");

            migrationBuilder.AlterColumn<string>(
                name: "Redinvoice",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "厂家红票发票号",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "RebateType",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                comment: "返利类型 A:平移返利, B:指标返利, C:补偿返利	",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "RebateTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "返利不含税金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "RebateAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "返利金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Policydeadline",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "政策期限",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PeriodSummary",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "返利期间(摘要)",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "NextTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "下家不含税金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "NextRebateMethod",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                comment: "下家返利方式 A:发票, B:优惠劵",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "NextInvoiceOrCoupon",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                comment: "下家返利发票号/优惠券(针对已结算的)",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "NextAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "下家对应金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "客户",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "RebateProvisionDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "客户Id",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CouponDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                comment: "发票日期/优惠卷日期",
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "ConfirmationDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                comment: "确认函日期",
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "ConfirmTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                comment: "金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AgentName",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "供应商",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "AgentId",
                table: "RebateProvisionDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "供应商Id",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "ActualarrivalDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                comment: "实际收到日期",
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullName",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullPath",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptId",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RebateProvisionItemId",
                table: "RebateProvisionDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "返利计提");

            migrationBuilder.CreateIndex(
                name: "IX_RebateProvisionDetail_RebateProvisionItemId",
                table: "RebateProvisionDetail",
                column: "RebateProvisionItemId");

            migrationBuilder.AddForeignKey(
                name: "FK_RebateProvisionDetail_RebateProvisionItem_RebateProvisionItemId",
                table: "RebateProvisionDetail",
                column: "RebateProvisionItemId",
                principalTable: "RebateProvisionItem",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RebateProvisionDetail_RebateProvisionItem_RebateProvisionItemId",
                table: "RebateProvisionDetail");

            migrationBuilder.DropIndex(
                name: "IX_RebateProvisionDetail_RebateProvisionItemId",
                table: "RebateProvisionDetail");

            migrationBuilder.DropColumn(
                name: "BusinessDeptFullName",
                table: "RebateProvisionDetail");

            migrationBuilder.DropColumn(
                name: "BusinessDeptFullPath",
                table: "RebateProvisionDetail");

            migrationBuilder.DropColumn(
                name: "BusinessDeptId",
                table: "RebateProvisionDetail");

            migrationBuilder.DropColumn(
                name: "RebateProvisionItemId",
                table: "RebateProvisionDetail");

            migrationBuilder.AlterColumn<string>(
                name: "Redinvoice",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "厂家红票发票号");

            migrationBuilder.AlterColumn<int>(
                name: "RebateType",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "返利类型 A:平移返利, B:指标返利, C:补偿返利	");

            migrationBuilder.AlterColumn<decimal>(
                name: "RebateTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "返利不含税金额");

            migrationBuilder.AlterColumn<decimal>(
                name: "RebateAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "返利金额");

            migrationBuilder.AlterColumn<string>(
                name: "Policydeadline",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "政策期限");

            migrationBuilder.AlterColumn<string>(
                name: "PeriodSummary",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "返利期间(摘要)");

            migrationBuilder.AlterColumn<decimal>(
                name: "NextTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "下家不含税金额");

            migrationBuilder.AlterColumn<int>(
                name: "NextRebateMethod",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "下家返利方式 A:发票, B:优惠劵");

            migrationBuilder.AlterColumn<int>(
                name: "NextInvoiceOrCoupon",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "下家返利发票号/优惠券(针对已结算的)");

            migrationBuilder.AlterColumn<decimal>(
                name: "NextAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "下家对应金额");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerName",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "客户");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "RebateProvisionDetail",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true,
                oldComment: "客户Id");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CouponDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true,
                oldComment: "发票日期/优惠卷日期");

            migrationBuilder.AlterColumn<DateTime>(
                name: "ConfirmationDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true,
                oldComment: "确认函日期");

            migrationBuilder.AlterColumn<decimal>(
                name: "ConfirmTaxAmount",
                table: "RebateProvisionDetail",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true,
                oldComment: "金额");

            migrationBuilder.AlterColumn<string>(
                name: "AgentName",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "供应商");

            migrationBuilder.AlterColumn<Guid>(
                name: "AgentId",
                table: "RebateProvisionDetail",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true,
                oldComment: "供应商Id");

            migrationBuilder.AlterColumn<DateTime>(
                name: "ActualarrivalDate",
                table: "RebateProvisionDetail",
                type: "Date",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "Date",
                oldNullable: true,
                oldComment: "实际收到日期");

            migrationBuilder.AddColumn<int>(
                name: "BizOrg",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true);
        }
    }
}
