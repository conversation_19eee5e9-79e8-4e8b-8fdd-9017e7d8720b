﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits
{
    /// <summary>
    /// 应收
    /// </summary>
    public class Credit : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public int? AbatedStatus { get; set; }

        public string? BillCode { get; set; }

        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        public int? Mark { get; set; }
        public string? CreditType { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 客户统一社会信用代码
        /// </summary> 
        [Comment("客户统一社会信用代码")]
        public string? LatestUniCode { get; set; }
        public DateTime? FinishDate { get; set; }

        public int? InvoiceStatus { get; set; }

        public string? Note { get; set; }


        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }

        public decimal Value { get; set; }
        public decimal? NonAbatedValue { get; set; }
        public int? Auto { get; set; }

        public string? AutoType { get; set; }

        public string? AutoTypeName { get; set; }

        public string? RelateCode { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        public int? IsLongTerm { get; set; }
        public int? IsSureIncome { get; set; } = 0;
        /// <summary>
        /// 是否确认收入时间
        /// </summary>
        public DateTime? IsSureIncomeDate { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary> 
        public string? OriginOrderNo { get; set; }

        /// <summary>
        /// 是否无需开票,1=无需开票，0 or null=需要开票
        /// </summary> 
        public IsNoNeedInvoiceEnum? IsNoNeedInvoice { get; set; }

        /// <summary>
        /// 分组Id
        /// </summary>
        [Comment("分组Id")]
        public Guid? GroupId { get; set; }

        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }
        public SaleSourceEnum? SaleSource { get; set; }


        public string? HospitalId { get; set; }


        public string? HospitalName { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum? SaleType { get; set; }
        /// <summary>
        /// 运输单号
        /// </summary>
        public string? ShipmentCode { get; set; }
        /// <summary>
        /// 暂存科室/订货部门名称
        /// </summary>
        public string? DeptName { get; set; }



        /// <summary>
        /// 项目单号 
        /// </summary>  
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>  
        public Guid? ProjectId { get; set; }
        public string? CustomerOrderCode { get; set; }

        public string? CustomerPersonName { get; set; }
        public string? SunPurchaseRelatecode { get; set; }
        /// <summary>
        /// 收入确认模式 有值并且值=1=分期生成
        /// </summary>
        public ServiceConfirmRevenuePlanModeEnum? ServiceConfirmRevenuePlanModeEnum { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 销售应收子类型
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? ProbablyBackTime { get; set; }
        /// <summary>
        /// 采购成本
        /// </summary>
        public decimal? PurchaseCost { get; set; }

        /// <summary>
        /// 销售单号(调回)
        /// </summary>
        public string? SaleCode { get; set; }
        /// <summary>
        /// 红字消耗单号
        /// </summary> 
        public string? RedReversalConsumNo { get; set; }
        /// <summary>
        /// 红字消耗Id
        /// </summary> 
        public string? RedReversalConsumId { get; set; }
        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }
        /// <summary>
        /// 修订范围
        /// </summary>
        public ReviseRangeEnum? ReviseRange { get; set; }
        /// <summary>
        /// 是否内部交易
        /// </summary>
        public bool? IsInternalTransactions { get; set; }
    }
}
