﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddReconciliationLetterItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReconciliationLetterItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "单号"),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "单据日期"),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ArrearsAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Deadline = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReconciliationLetterTemplate = table.Column<int>(type: "int", nullable: false),
                    AttachFileIds = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "附件Ids"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConfirmAttachFileIds = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "询证函确认附件Ids"),
                    Status = table.Column<int>(type: "int", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReconciliationLetterItem", x => x.Id);
                },
                comment: "财务对账函表");

            migrationBuilder.CreateTable(
                name: "ReconciliationLetterDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReconciliationLetterItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Classify = table.Column<int>(type: "int", nullable: false),
                    BillDate = table.Column<DateTime>(type: "datetime2", nullable: false, comment: "明细单据日期（开票日期、应收日期）"),
                    BillCode = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "明细单据号（发票号、应收单号）"),
                    Value = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "明细金额（开票金额、应收金额）"),
                    ReceivedValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "已收明细金额（开票金额、应收金额）"),
                    NonReceivedValue = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "未收明细金额（开票金额、应收金额）"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReconciliationLetterDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReconciliationLetterDetail_ReconciliationLetterItem_ReconciliationLetterItemId",
                        column: x => x.ReconciliationLetterItemId,
                        principalTable: "ReconciliationLetterItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "财务对账函明细表");

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationLetterDetail_ReconciliationLetterItemId",
                table: "ReconciliationLetterDetail",
                column: "ReconciliationLetterItemId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationLetterItem_BillCode",
                table: "ReconciliationLetterItem",
                column: "BillCode",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReconciliationLetterDetail");

            migrationBuilder.DropTable(
                name: "ReconciliationLetterItem");
        }
    }
}
