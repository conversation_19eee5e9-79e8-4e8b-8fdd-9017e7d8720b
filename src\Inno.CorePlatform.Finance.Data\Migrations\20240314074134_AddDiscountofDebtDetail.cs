﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddDiscountofDebtDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "CostDiscount",
                table: "DebtDetail",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "厂家折扣");

            migrationBuilder.AddColumn<decimal>(
                name: "DistributionDiscount",
                table: "DebtDetail",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "基础折扣");

            migrationBuilder.AddColumn<decimal>(
                name: "FinanceDiscount",
                table: "DebtDetail",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "供应链金融折扣");

            migrationBuilder.AddColumn<decimal>(
                name: "SpdDiscount",
                table: "DebtDetail",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "SPD折扣");

            migrationBuilder.AddColumn<decimal>(
                name: "TaxDiscount",
                table: "DebtDetail",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "税率折扣");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CostDiscount",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "DistributionDiscount",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "FinanceDiscount",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "SpdDiscount",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "TaxDiscount",
                table: "DebtDetail");
        }
    }
}
