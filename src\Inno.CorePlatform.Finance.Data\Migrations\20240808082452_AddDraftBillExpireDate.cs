﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddDraftBillExpireDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "DraftBillExpireDate",
                table: "RecognizeReceiveItem",
                type: "datetime2",
                maxLength: 200,
                nullable: true,
                comment: "到期日");

            migrationBuilder.AddColumn<string>(
                name: "Settletype",
                table: "RecognizeReceiveItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "结算方式");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DraftBillExpireDate",
                table: "RecognizeReceiveItem");

            migrationBuilder.DropColumn(
                name: "Settletype",
                table: "RecognizeReceiveItem");
        }
    }
}
