﻿using Dapr;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    [Route("api/StageSurgerySub")]
    [ApiController]
    public class StageSurgerySubController : ControllerBase
    {
        private ILogger<StageSurgerySubController> _logger;
        private IServiceProvider _serviceProvider;
        private DaprClient _daprClient;
        private IEasyCachingProvider _easyCaching;
        public StageSurgerySubController(
         IServiceProvider serviceProvider,
         DaprClient daprClient,
         IEasyCachingProvider easyCaching,
         ILogger<StageSurgerySubController> logger)
        {
            this._easyCaching = easyCaching;
            this._serviceProvider = serviceProvider;
            this._logger = logger;
            this._daprClient = daprClient;
        }
        /// <summary>
        /// 跟台手术转暂存单据完成,发送给财务,生成财务凭证
        /// </summary>
        /// <returns></returns>
        [HttpPost("SurgeryFinish")]
        [Topic("pubsub-default", "sginy-finance-surgery-sync")]
        public async Task<ActionResult> SurgeryFinish(StageSurgeryInput input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var ret = BaseResponseData<int>.Success("操作成功");
            var cachekey = "sginy-finance-surgery-sync_" + input.stageSurgeryId;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    var _stageSurgeryAppService = _serviceProvider.GetService<IStageSurgeryAppService>();
                    if (_stageSurgeryAppService != null)
                    {
                        var eventBus = new EventBusDTO
                        {
                            BusinessId = Guid.Parse(input.stageSurgeryId),
                            BusinessType = "跟台手术转暂存单据完成",
                            RelateId = Guid.Parse(input.stageSurgeryId),
                        };
                        await _stageSurgeryAppService.CreateSubLog(SubLogSourceEnum.Stage, jsonStr, "admin", "跟台手术转暂存单据完成");
                        ret = await _stageSurgeryAppService.PullIn(eventBus);
                    }
                    _easyCaching.Remove(cachekey);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "sginy-finance-surgery-sync",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/StageSurgerySub/SurgeryFinish"  //重试的回调方法路由 
                });
                return Ok();
            }
        }
    }
    public class StageSurgeryInput
    {
        public string? stageSurgeryId { get; set; }
    }
}

