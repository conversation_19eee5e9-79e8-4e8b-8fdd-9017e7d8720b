﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills
{
    public class InputBill : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanName { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary> 
        public DateTime BillTime { get; set; }

        /// <summary>
        /// 票据类型 1,普票 2，专票
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }


        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }


        /// <summary>
        /// 金额
        /// </summary> 
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额=金额*税率
        /// </summary> 
        public decimal TaxAmount { get; set; }


        /// <summary>
        /// 总金额=金额+税额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态 1=临时发票，2=已提交，3=正在匹配，4=匹配完成，9=忽略
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 取消勾稽时间
        /// </summary>
        public DateTime? CancelReconciliationTime { get; set; }

        /// <summary>
        /// 是否已取消勾稽
        /// </summary>
        public bool? IsCancelledReconciliation { get; set; }

    }
}
