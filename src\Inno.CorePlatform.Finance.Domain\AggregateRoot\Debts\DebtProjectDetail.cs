﻿using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot
{
    public class DebtProjectDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public Guid DebtId { get; set; }

        public virtual Debt Debt { get; set; }

        public Guid ProjectId { get; set; }

        public string ProjectName { get; set; } = "";

        public string ProjectCode { get; set; } = "";

        public decimal Amount { get; set; }
    }
}
