﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Migrations;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{

    /// <summary>
    /// 认款
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class RecognizeReceiveController : ControllerBase
    {

        private readonly IRecognizeReceiveQueryService _recognizeReceiveQueryService;
        private readonly ILogger<RecognizeReceiveController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;

        public RecognizeReceiveController(ILogger<RecognizeReceiveController> logger,
            IRecognizeReceiveQueryService recognizeReceiveQueryService,
            IKingdeeApiClient kingdeeApiClient)
        {
            _recognizeReceiveQueryService = recognizeReceiveQueryService;
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
        }


        /// <summary>
        /// 获取认款清单
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("getlist")]
        public async Task<BaseResponseData<BasePagedData<RecognizeReceiveItemOutPut>>> GetListPages([FromBody] RecognizeReceiveItemInput input)
        {
            input.Source = 1; //外部webapi
            var result = await _recognizeReceiveQueryService.GetListPages(input);
            var res = new BaseResponseData<BasePagedData<RecognizeReceiveItemOutPut>>
            {
                Code = CodeStatusEnum.Success,
                Data = new BasePagedData<RecognizeReceiveItemOutPut>
                {
                    Total = result.Total,
                    List = result.List,
                }
            };
            return res;

        }
        /// <summary>
        /// 订单是否关联认款单;
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("IsExists")]
        public async Task<BaseResponseData<bool>> IsExists([FromBody] IsExistsInput input)
        {
            return await _recognizeReceiveQueryService.IsExists(input);

        }

        /// <summary>
        /// 获取收款单
        /// </summary>
        /// <param name="input">认款单Id</param>
        /// <returns></returns>
        [HttpPost("getReceiveBills")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveOutput>>> GetReceiveBills([FromBody] RecognizeReceiveInput input)
        {
            var companyNameCodes = new List<string>();
            if (input.customers != null && input.customers.Any())
            {
                foreach (var customerId in input.customers)
                {
                    input.casRecbillCustomer = new List<QueryCasRecbillCustomerModel> {
                        new QueryCasRecbillCustomerModel
                        {
                            customer = customerId
                        }
                    };
                }
            }

            if (input.company == null || !input.company.Any())
            {
                input.company = companyNameCodes;
            }
            input.type = new List<string> {
                 "100",//销售回款
                 "101",//预收款
                 "102",//退采购付款
                 "108",//赔款
                 "109",//收回之前支付的押金
                 "110",//收回之前支付的投标保证金
                 "111",//收回之前支付的履约保证金
                 "998",//未知
                 "999",//其他
                };
            if (input.classify == "-1") //选择全部
            {
                input.type.Add("129");//收回之前支付的医院保证金
                input.type.Add("127");//收回之前支付的海关保证金
                input.type.Add("124");//收到招标押金
                input.type.Add("123");//收到保证金
                input.type.Add("122");//保险理赔收入
                input.type.Add("121");//所得税退税
                input.type.Add("120");//出口退税
                input.type.Add("119");//税费返还
                input.type.Add("118");//个税返还
                input.type.Add("117");//即征即退
                input.type.Add("116");//政府补贴
                input.type.Add("115");//工会经费返还
                input.type.Add("104");//代收款
                input.type.Add("142");//销售现金折扣
            }
            else if (!string.IsNullOrEmpty(input.classify))
            {
                input.type = new List<string> { input.classify };
            }
            var result = await _recognizeReceiveQueryService.GetKdReceiveBills(input);
            if (!string.IsNullOrEmpty(input.code))
            {
                result = result.Where(p => p.billno.Contains(input.code)).ToList();
            }
            var resultPage = result.OrderByDescending(p => p.payeedate).Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
            var res = new BaseResponseData<PageResponse<RecognizeReceiveOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<RecognizeReceiveOutput>
                {
                    List = resultPage,
                    Total = result.Count
                },
            };
            return res;
        }
        /// <summary>
        /// 根据认款单号或者销售单号获取认款单详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("BatchQueryRecognizeReceiveByCode")]
        public async Task<BaseResponseData<List<RecognizeReceiveBatchQueryByCodeOutput>>> BatchQueryRecognizeReceiveByCode([FromBody] RecognizeReceiveBatchQueryByCodeInput input)
        {
            return await _recognizeReceiveQueryService.BatchQueryRecognizeReceiveByCode(input);
        }

        [HttpPost("ExportDetailsTask")]
        [SkipLogging]
        public async Task<ResponseData<RecognizeReceiveDetailExportOutput>> ExportDetailsTask([FromBody] ExportDetailsInput input)
        {
            try
            {
                input.Type = 1;
                var (list,count) = await _recognizeReceiveQueryService.GetExportDetails(input);
                return new ResponseData<RecognizeReceiveDetailExportOutput>
                {
                    Code = 200,
                    Data = new Data<RecognizeReceiveDetailExportOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        [HttpPost("ExportDetailCreditsTask")]
        [SkipLogging]
        public async Task<ResponseData<RecognizeReceiveDetailExportOutput>> ExportDetailCreditsTask([FromBody] ExportDetailsInput input)
        {
            try
            {
                input.Type = 2;
                var (list, count) = await _recognizeReceiveQueryService.GetExportDetails(input);
                return new ResponseData<RecognizeReceiveDetailExportOutput>
                {
                    Code = 200,
                    Data = new Data<RecognizeReceiveDetailExportOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
