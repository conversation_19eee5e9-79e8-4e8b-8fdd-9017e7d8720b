﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.Enums
{
    /// <summary>
    /// 业务类型枚举
    /// </summary>
    public enum BusinessType
    {
        /// <summary>
        /// 经销购货入库
        /// </summary>
        [Description("经销购货入库")]
        DistributionPurchase = 1,

        /// <summary>
        /// 寄售转购货
        /// </summary>
        [Description("寄售转购货")]
        ConsignmentToPurchase = 2,

        /// <summary>
        /// 服务费采购
        /// </summary>
        [Description("服务费应付")]
        ServiceFeeProcurement = 3,

        /// <summary>
        /// 经销调出
        /// </summary>
        [Description("经销调出")]
        DistributionTransfer = 4,

        /// <summary>
        /// 购货修订
        /// </summary>
        [Description("购货修订")]
        PurchaseRevision = 5,

        /// <summary>
        /// 换货转退货
        /// </summary>
        [Description("换货转退货")]
        ExchangeToReturn = 6,

        /// <summary>
        /// 损失确认
        /// </summary>
        [Description("损失确认应付")]
        LossRecognition = 7
    }
}
