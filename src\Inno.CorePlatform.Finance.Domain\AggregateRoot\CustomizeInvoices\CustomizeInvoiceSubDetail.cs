﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    public class CustomizeInvoiceSubDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        ///  原明细id
        /// </summary> 
        public Guid CustomizeInvoiceDetailId { get; set; }

        /// <summary>
        ///  原始货品名称（不可变）
        /// </summary>
        public string? OriginProductName { get; set; }
        /// <summary>
        ///  货品名称（开票名称）
        /// </summary> 
        public string ProductName { get; set; }

        /// <summary>
        ///  原明细id
        /// </summary> 
        public string OriginDetailId { get; set; }
        /// <summary>
        ///  数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }
        /// <summary>
        ///  单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }


        /// <summary>
        ///  应收单号
        /// </summary> 
        public string CreditBillCode { get; set; }

        /// <summary>
        ///  货号Id
        /// </summary>
        [Comment("货号Id")]
        public Guid? ProductId { get; set; }
        /// <summary>
        ///  货号
        /// </summary>  
        public string? ProductNo { get; set; }

        /// <summary>
        ///  计量单位
        /// </summary>  
        public string? PackUnit { get; set; }

        /// <summary>
        ///  原始计量单位
        /// </summary>
        [Comment("原始计量单位")] 
        public string? OriginPackUnit { get; set; }
        /// <summary>
        ///  规格型号
        /// </summary> 
        [MaxLength(200)]
        public string? Specification { get; set; }
        /// <summary>
        /// 原始规格型号
        /// </summary> 
        public string? OriginSpecification { get; set; }
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid? CustomizeInvoiceItemId { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高值
        /// </summary>
        public int? IFHighValue { get; set; }
    }
}
