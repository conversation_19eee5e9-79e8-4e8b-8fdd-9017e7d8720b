﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application
{
    public static class StringExtensions
    {
        public static HashSet<Guid> ToGuidHashSet(this IEnumerable<string> values)
        {
            return new HashSet<Guid>(
                values
                    .Select(value => Guid.TryParse(value, out var guid) ? (Guid?)guid : null)
                    .Where(guid => guid.HasValue)
                    .Select(guid => guid.Value)
            );
        }
    }
}
