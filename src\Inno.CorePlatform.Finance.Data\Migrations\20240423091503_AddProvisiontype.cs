﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddProvisiontype : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ProvisionType",
                table: "RebateProvisionItem",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "NextInvoiceOrCoupon",
                table: "RebateProvisionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "下家返利发票号/优惠券(针对已结算的)",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "下家返利发票号/优惠券(针对已结算的)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProvisionType",
                table: "RebateProvisionItem");

            migrationBuilder.AlterColumn<int>(
                name: "NextInvoiceOrCoupon",
                table: "RebateProvisionDetail",
                type: "int",
                nullable: true,
                comment: "下家返利发票号/优惠券(针对已结算的)",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "下家返利发票号/优惠券(针对已结算的)");
        }
    }
}
