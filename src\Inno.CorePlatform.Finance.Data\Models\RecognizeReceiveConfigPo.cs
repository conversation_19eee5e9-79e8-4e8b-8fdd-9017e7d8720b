﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{

    [Table("RecognizeReceiveConfig")]
    [Comment("认款配置表")]
    public class RecognizeReceiveConfigPo : BasePo
    {
        /// <summary>
        /// 客户Id
        /// </summary>
        [Comment("客户Id")]
        [MaxLength(50)]
        public Guid CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [Comment("客户名称")]
        [MaxLength(200)]
        public string CustomerName { get; set; }
    }
}
