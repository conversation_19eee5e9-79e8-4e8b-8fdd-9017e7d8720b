﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addReceivedNoInvoiceRecordCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ReceivedNoInvoiceRecordCode",
                table: "InventoryItem",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "已签收未开票盘点单号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReceivedNoInvoiceRecordCode",
                table: "InventoryItem");
        }
    }
}
