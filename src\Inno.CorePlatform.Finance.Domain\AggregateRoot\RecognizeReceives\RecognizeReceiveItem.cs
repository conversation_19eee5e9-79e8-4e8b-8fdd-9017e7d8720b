﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive
{
    public class RecognizeReceiveItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 认款单号
        /// </summary>      
        public string Code { get; set; }
        /// <summary>
        /// 单号日期
        /// </summary>     
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>  
        public string ReceiveCode { get; set; }
        /// <summary>
        /// 本次认款金额
        /// </summary>   
        public decimal Value { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>       
        public decimal ReceiveValue { get; set; }
        /// <summary>
        /// 收款单位Id
        /// </summary>    
        public string CompanyId { get; set; }
        /// <summary>
        /// 收款单位名称
        /// </summary>  
        public string CompanyName { get; set; }
        /// <summary>
        /// 付款单位Id
        /// </summary>   
        public string CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary> 
        public string CustomerNme { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public string? Type { get; set; }
        /// <summary>
        /// 收款时间
        /// </summary>
        public DateTime ReceiveDate { get; set; }
        /// <summary>
        /// 批量附件Id
        /// </summary>    
        public string? AttachFileIds { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>     
        public string BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>   
        public string BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int BusinessDepId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public RecognizeReceiveClassifyEnum Classify { get; set; }
        /// <summary>
        /// 认款明细(货款)
        /// </summary>
        public virtual List<RecognizeReceiveDetail>? RecognizeReceiveDetails { get; set; }
        /// <summary>
        /// 认款明细(暂收款)
        /// </summary>
        public virtual List<RecognizeReceiveTempDetail>? RecognizeReceiveTempDetails { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 结算方式 
        /// </summary>   
        public string? Settletype { get; set; }

        /// <summary>
        /// 到期日 
        /// </summary>   
        public DateTime? DraftBillExpireDate { get; set; }
        /// <summary>
        /// 银行账户
        /// </summary>
        public string? BankNum { get; set; }
        /// <summary>
        /// 银行类型(名称)
        /// </summary>
        public string? BankName { get; set; }
        /// <summary>
        /// 贴现日期
        /// </summary>
        public DateTime? DiscountDate { get; set; }
    }
}
