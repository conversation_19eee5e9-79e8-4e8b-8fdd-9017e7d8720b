<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>发票入账详情</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
    </div>
    <div class="app-page-body">
      <el-card class="zx-box-card" shadow="never" style="min-width: 1000px">
        <template #header>
          <div class="card-header">
            <span>发票入账详情</span>
          </div>
        </template>
        <el-form ref="formRef" :model="formData" label-position="right" label-width="120px" :rules="generateformRules">
          <!-- <el-descriptions label-width="100px" class="margin-top" :column="2" border>
            <el-descriptions-item label="部门:">{{ formData.businessDeptFullName }}</el-descriptions-item>
            <el-descriptions-item label="部门:">{{ formData.businessDeptFullName }}</el-descriptions-item>
          </el-descriptions>-->
          <el-row>
            <el-col :span="12">
              <el-form-item label="单据:">
                <inno-button-copy :link="false">{{ formData.billCode }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="单据日期">
                <inno-button-copy :link="false">
                  {{
                  formData.billDate === null
                  ? ''
                  : dateFormat(formData.billDate, 'YYYY-MM-DD')
                  }}  
                </inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门:">
                <inno-button-copy :link="false">{{ formData.businessDeptFullName }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="公司" prop="companys.id">
                <inno-button-copy :link="false">{{ formData.companyName }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务单元" prop="services.businessUnitID">
                <inno-button-copy :link="false">{{ formData.serviceName }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="客户" prop="customers.id">
                <inno-button-copy :link="false">{{ formData.customerName }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合约回款天数" prop="backAmountDays">
                <inno-button-copy :link="false">{{ formData.backAmountDays }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="销售账期天数" prop="saleAccountPeriodDays">
                <inno-button-copy :link="false">{{ formData.saleAccountPeriodDays }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际回款天数" prop="actualBackAmountDays">
                <inno-button-copy :link="false">{{ formData.actualBackAmountDays }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="备注" prop="remark">
                <inno-button-copy :link="false">{{ formData.remark }}</inno-button-copy>
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item label="附件" prop="attachment">
                <comfile :exceptFiles="formData.attachment" tablewidth="100vw" :isview="true" />
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <div style="margin-bottom: 10px;" ></div>
              <el-form-item label="发票" prop="invoices">
              <div style="height: 300px; overflow: auto;" >
                <el-table
                  id="tableDetail"
                  ref="tableRef"
                  class="auto-layout-table"
                  highlight-current-row
                  :data="formData.invoiceDetail"
                  
                  border
                >
                  <el-table-column label="应收单号" property="creditCode">
                    <template #default="scope">
                      <inno-button-copy :link="false">{{ scope.row.creditCode }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票号" property="invoiceNo">
                    <template #default="scope">
                      <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票代码" property="invoiceCode">
                    <template #default="scope">
                      <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票验证码" property="invoiceCheckCode">
                    <template #default="scope">
                      <inno-button-copy :link="false">{{ scope.row.invoiceCheckCode }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票时间" property="invoiceTime" width="150">
                    <template #default="scope">
                      {{
                      scope.row.billDate === null
                      ? ''
                      : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column label="开票金额" property="invoiceAmount" width="100">
                    <template #default="scope">
                      <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                    </template>
                  </el-table-column>
                  <el-table-column label="开票状态" property="isCancel" width="100">
                    <template #default="scope">
                      <inno-button-copy :link="false">{{ scope.row.isCancel === 1 ? '已取消' : '已开票' }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="备注" property="remark" show-overflow-tooltip>
                    <template #default="scope">{{ scope.row.remark }}</template>
                  </el-table-column>
                </el-table>
                </div>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="22">
            <el-form-item label>
              <div class="crud-opts">
                <inno-button-tooltip type @click="cancel">返回</inno-button-tooltip>
              </div>
            </el-form-item>
            </el-col>-->
          </el-row>
        </el-form>
      </el-card>
    </div>
    <div class="app-page-footer">
      <div class="flex-1" />
      <div>
        <!-- <el-button type="default" @click="cancel">返回</el-button> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import comfile from '@/component/com-files.vue';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import { ref, onMounted, reactive, watch } from 'vue';
import { ElTable, ElForm } from 'element-plus';

const tableInvoiceRef = ref<InstanceType<typeof ElTable>>();
const formRef = ref();
const tableRef = ref<InstanceType<typeof ElTable>>();

onMounted(() => {
  getData();
});
const route = useRoute();
watch(
  () => route.query.id,
  (n, o) => {
    if (n != null) {
      //查询数据
      getData();
    }
  },
  { deep: true }
);
//生成表单提交数据
let formData = reactive({
  id: '',
  billCode:'',
  billDate:'',
  companyId: '',
  serviceId: '',
  customerId: '',
  companyName: '',
  serviceName: '',
  customerName: '',
  businessDeptId: '',
  businessDeptFullPath: '',
  businessDeptFullName: '',
  backAmountDays: '',
  saleAccountPeriodDays: '',
  actualBackAmountDays: '',
  attachment: [],
  remark: '',
  invoiceDetail: [
    {
      creditCode: '',
      invoiceNo: '',
      invoiceCode: '',
      invoiceCheckCode: '',
      invoiceTime: '',
      invoiceAmount: '',
      isCancel: '',
      remark: ''
    }
  ]
});
//编辑时查询数据
const getData = () => {
  console.log('开始查询');
  request({
    url: '/api/InvoiceReceipts/GetItem',
    method: 'POST',
    data: {
      invoiceReceiptItemId: route.query.id
    }
  }).then((res) => {
    if (res.data && res.data.code === 200) {
      //拼装数据
      formData.billCode = res.data.data.billCode,
      formData.billDate = res.data.data.billDate,
      formData.businessDeptFullName = res.data.data.businessDeptFullName;
      formData.companyId = res.data.data.companyId;
      formData.companyName = res.data.data.companyName;
      formData.serviceId = res.data.data.serviceId;
      formData.serviceName = res.data.data.serviceName;
      formData.customerId = res.data.data.customerId;
      formData.customerName = res.data.data.customerName;
      formData.backAmountDays = res.data.data.backAmountDays;
      formData.saleAccountPeriodDays = res.data.data.saleAccountPeriodDays;
      formData.actualBackAmountDays = res.data.data.actualBackAmountDays;
      formData.remark = res.data.data.remark;
      formData.invoiceDetail = res.data.data.invoiceDetail;

      var list = res.data.data.attachments;
      list.forEach((item) => {
        formData.attachment.push({
          attachFileId: item.id,
          attachFileName: item.name,
          attachFileSize: item.length
        });
      });
      // formData.attachment = res.data.data.attachments
      console.log(JSON.stringify(formData));
    }
  });
};

const router = useRouter();
//取消
const cancel = () => {
  router.push({
    path: 'InvoiceReceipts'
  });
};
</script>
<style scoped></style>
