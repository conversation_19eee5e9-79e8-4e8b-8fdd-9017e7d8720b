﻿﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.MergeInputBills
{
    /// <summary>
    /// 合并进项发票关联应付
    /// </summary>
    public class MergeInputBillDebt : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        public virtual MergeInputBill MergeInputBill { get; set; }

        /// <summary>
        /// 应付单ID
        /// </summary>
        public Guid DebtId { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string DebtCode { get; set; }

        /// <summary>
        /// 关联应付金额
        /// </summary>
        public decimal DebtAmount { get; set; }
    }
}
