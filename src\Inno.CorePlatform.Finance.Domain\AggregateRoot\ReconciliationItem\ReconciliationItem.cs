﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive
{
    public class ReconciliationItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>

        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        public string? SysMonth { get; set; }

    }
}
