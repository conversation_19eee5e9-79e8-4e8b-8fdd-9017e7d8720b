﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 批量付款
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BulkPaymentController : ControllerBase
    {
        private readonly IBulkPaymentQueryService _bulkPaymentQueryService;
        private readonly IDebtQueryService _debtQueryService;
        public BulkPaymentController(IBulkPaymentQueryService bulkPaymentQueryService, IDebtQueryService debtQueryService)
        {
            this._bulkPaymentQueryService = bulkPaymentQueryService;
            _debtQueryService = debtQueryService;
        }
        /// <summary>
        /// 【采购】根据明细单号获取批量付款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<List<PaymentAutoDetailOfPurchaseOutput>> GetPaymentDetailsByCodes(PaymentAutoDetailOfPurchaseInput input)
        {
            return _bulkPaymentQueryService.GetPaymentDetailsByCodes(input);
        }

        /// <summary>
        /// 【采购】根据明细单号获取批量付款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("downloadAccountPeriod")]
        public async Task<BaseResponseData<PageResponse<DebtDetailBulkOutput>>> DownloadAccountPeriod([FromBody] DebtDetailBulkQuery query)
        {
            try
            {
                (var list, var count) = await _debtQueryService.GetDetailsByCompanies(query);
                var res = new BaseResponseData<PageResponse<DebtDetailBulkOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<DebtDetailBulkOutput>
                    {
                        List = list,
                        Total = count
                    },
                    Total = count
                };
                return res;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
