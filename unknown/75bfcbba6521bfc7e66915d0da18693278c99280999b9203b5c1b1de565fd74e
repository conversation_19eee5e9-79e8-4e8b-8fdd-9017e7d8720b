﻿using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 进项票
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InputBillController : ControllerBase
    {
        private readonly IInputBillQueryService _inputBillQueryService;
        public InputBillController(IInputBillQueryService inputBillQueryService)
        {
            this._inputBillQueryService = inputBillQueryService;
        }

        /// <summary>
        /// 【采购】根据明细单号获取进项票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<List<PurchaseInputBillOutput>> GetPurchaseInputBill(PurchaseInputBillInput input)
        {
            return _inputBillQueryService.GetPurchaseInputBill(input);
        }
    }
}
