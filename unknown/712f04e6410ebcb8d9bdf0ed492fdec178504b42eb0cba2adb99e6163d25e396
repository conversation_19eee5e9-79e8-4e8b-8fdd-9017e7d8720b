﻿using Amazon.Runtime.Internal.Util;
using EasyCaching.Core;
using EasyCaching.Core.DistributedLock;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.DTOs;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.Interfaces;
using Inno.CorePlatform.Finance.WebApi.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using System.Text.Json;


namespace Inno.CorePlatform.Finance.WebApi.Filters
{
    public class ParameterLoggingFilter : IAsyncActionFilter
    {
        private readonly ILogger<ParameterLoggingFilter> _logger;
        private readonly IEasyCachingProvider _cachingProvider;
        private readonly IServiceProvider _serviceProvider;

        public ParameterLoggingFilter(IEasyCachingProvider cachingProvider, ILogger<ParameterLoggingFilter> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            // 使用默认缓存实例
            _cachingProvider = cachingProvider;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if (!ShouldProcessLogging(context))
            {
                await next();
                return;
            }



            var controller = (BaseController)context.Controller;

            var lockKey = $"lock:{new CustomIdempotencyKeyGenerator().GenerateKey(context)}";
            if (!await LcokRequest(lockKey, BaseController.LockTimeoutSeconds)) return;
            try
            {
                await LogParameters(context, controller);

                var idempotentAttr = GetIdempotentAttribute(context);
                if (idempotentAttr == null)
                {
                    await next();
                    return;
                }

                // 幂等性处理流程
                var (keyGenerator, key) = await CreateKeyGeneratorAndKey(context, idempotentAttr);

                if (keyGenerator == null || key == null) return; // 错误已处理


                var cacheKey = $"idempotency:{key}";
                var stateKey = $"{cacheKey}:state";

                // 检查缓存结果
                if (await TryReturnCachedResult(cacheKey, context)) return;

                // 检查退避状态
                if (await CheckBackoffState(stateKey, context)) return;

                // 执行请求并处理结果
                var resultContext = await next();
                await ProcessIdempotentResult(resultContext, context, idempotentAttr, cacheKey, stateKey, key);
            }
            finally
            {
                await ReleaseLcok(lockKey);
            }

        }

        private bool ShouldProcessLogging(ActionExecutingContext context)
        {
            return context.Controller is BaseController controller
                   && controller.EnableParameterLogging
                   && !context.ActionDescriptor.EndpointMetadata.Any(m => m is SkipLoggingAttribute);
        }

        private IdempotentWithBackoffAttribute? GetIdempotentAttribute(ActionExecutingContext context)
        {
            return context.ActionDescriptor.EndpointMetadata
                .OfType<IdempotentWithBackoffAttribute>()
                .FirstOrDefault();
        }

        private async Task<(IIdempotencyKeyGenerator?, string?)> CreateKeyGeneratorAndKey(ActionExecutingContext context, IdempotentWithBackoffAttribute attr)
        {
            try
            {
                var keyGenerator = (IIdempotencyKeyGenerator)ActivatorUtilities.CreateInstance(_serviceProvider, attr.KeyGeneratorType);
                var key = keyGenerator.GenerateKey(context);
                return (keyGenerator, key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "httpContext[head]缺失幂等键Idempotency-Key,处理失败");
                context.Result = ex is ArgumentException
                    ? new BadRequestObjectResult(new ProblemDetails
                    {
                        Title = "请求参数错误",
                        Detail = ex.Message,
                        Status = StatusCodes.Status400BadRequest
                    })
                    : new StatusCodeResult(StatusCodes.Status500InternalServerError);
                return (null, null);
            }
        }

        private async Task<bool> LcokRequest(string cacheKey, int lockTimeoutSeconds)
        {
            if (await _cachingProvider.ExistsAsync(cacheKey))
            {
                return false;
            }
            await _cachingProvider.SetAsync(
                cacheKey,
                "防止瞬时请求多次",
                TimeSpan.FromSeconds(lockTimeoutSeconds));
            return true;
        }

        private async Task ReleaseLcok(string cacheKey)
        {
            if (await _cachingProvider.ExistsAsync(cacheKey))
            {
                await _cachingProvider.RemoveAsync(cacheKey);
            }
        }

        private async Task<bool> TryReturnCachedResult(string cacheKey, ActionExecutingContext context)
        {
            var cachedResult = await _cachingProvider.GetAsync<string>(cacheKey);
            if (!cachedResult.HasValue) return false;

            context.Result = JsonConvert.DeserializeObject<ObjectResult>(cachedResult.Value)!;
            _logger.LogInformation("返回幂等缓存结果: {CacheKey}", cacheKey);
            return true;
        }

        private async Task<bool> CheckBackoffState(string stateKey, ActionExecutingContext context)
        {
            var stateResult = await _cachingProvider.GetAsync<IdempotencyState>(stateKey);
            if (!stateResult.HasValue) return false;

            var state = stateResult.Value;
            if (state.NextRetryTime == null || DateTime.UtcNow >= state.NextRetryTime)
                return false;

            var retrySeconds = (int)(state.NextRetryTime.Value - DateTime.UtcNow).TotalSeconds;
            context.Result = JsonConvert.DeserializeObject<ObjectResult>(state.LastResult.Message);


            //context.Result = new ObjectResult(new
            //{
            //    Message = "操作进行中，请稍后重试",
            //    RetryAfterSeconds = retrySeconds,
            //    PreviousResult = state.LastResult
            //})
            //{ StatusCode = StatusCodes.Status429TooManyRequests };

            return true;
        }

        private async Task ProcessIdempotentResult(
            ActionExecutedContext resultContext,
            ActionExecutingContext originalContext,
            IdempotentWithBackoffAttribute attr,
            string cacheKey,
            string stateKey,
            string idempotencyKey)
        {
            // 处理异常情况
            if (resultContext.Exception != null)
            {
                await HandleFailureAsync(
                    stateKey,
                    attr,
                    failureCount: await GetCurrentFailureCount(stateKey) + 1,
                    error: new ErrorResult
                    {
                        Message = JsonConvert.SerializeObject(resultContext.Result),
                        ErrorCode = "SERVER_ERROR"
                    },
                    idempotencyKey);

                originalContext.Result = CreateTooManyRequestsResult(await GetCurrentFailureCount(stateKey), attr);
                resultContext.ExceptionHandled = true; // 标记异常已处理
                return;
            }

            // 处理业务逻辑失败
            if (resultContext.Result is ObjectResult objectResult
                && objectResult.Value is BaseResponseData response
                && response.Code != CodeStatusEnum.Success)
            {
                await HandleFailureAsync(
                    stateKey,
                    attr,
                    failureCount: await GetCurrentFailureCount(stateKey) + 1,
                    error: new ErrorResult
                    {
                        Message = JsonConvert.SerializeObject(objectResult),
                        ErrorCode = response.Code.ToString()
                    },
                    idempotencyKey);

                originalContext.Result = CreateTooManyRequestsResult(await GetCurrentFailureCount(stateKey), attr);
                return;
            }

            // 处理成功结果
            if (resultContext.Result is ObjectResult successResult)
            {
                await CacheSuccessfulResult(cacheKey, successResult, attr);
                await _cachingProvider.RemoveAsync(stateKey); // 清除失败状态
                _logger.LogInformation("幂等操作成功: {Key}", idempotencyKey);
            }
        }

        private async Task<int> GetCurrentFailureCount(string stateKey)
        {
            var stateResult = await _cachingProvider.GetAsync<IdempotencyState>(stateKey);
            return stateResult.HasValue ? stateResult.Value.FailureCount : 0;
        }

        private ObjectResult CreateTooManyRequestsResult(int failureCount, IdempotentWithBackoffAttribute attr)
        {
            var retryMinutes = CalculateRetryInterval(attr, failureCount);
            return new ObjectResult(new
            {
                Message = $"请等待后重试 (失败次数: {failureCount})",
                RetryAfterSeconds = (int)TimeSpan.FromMinutes(retryMinutes).TotalSeconds
            })
            { StatusCode = StatusCodes.Status429TooManyRequests };
        }

        private async Task CacheSuccessfulResult(string cacheKey, ObjectResult result, IdempotentWithBackoffAttribute attr)
        {
            var response = new CachedResponse
            {
                Value = result.Value,
                StatusCode = result.StatusCode ?? (int)CodeStatusEnum.Success,
                CreatedAt = DateTime.UtcNow
            };

            await _cachingProvider.SetAsync(
                cacheKey,
                JsonConvert.SerializeObject(response),
                TimeSpan.FromDays(attr.SuccessResultCacheDays));
        }

        private async Task HandleFailureAsync(
            string stateKey,
            IdempotentWithBackoffAttribute attr,
            int failureCount,
            ErrorResult error,
            string idempotencyKey)
        {
            var retryMinutes = CalculateRetryInterval(attr, failureCount);
            var newState = new IdempotencyState
            {
                FailureCount = failureCount,
                NextRetryTime = DateTime.UtcNow.AddMinutes(retryMinutes),
                LastResult = error
            };

            await _cachingProvider.SetAsync(
                stateKey,
                newState,
                TimeSpan.FromMinutes(retryMinutes * 2));

            _logger.LogWarning(
                "幂等操作失败 [Key:{Key}] 次数:{Count} 下次重试:{Time}",
                idempotencyKey, failureCount, newState.NextRetryTime);
        }

        private double CalculateRetryInterval(IdempotentWithBackoffAttribute attr, int failureCount)
        {
            // 指数退避算法: initial * (factor ^ (count-1))
            var interval = attr.InitialRetryMinutes * Math.Pow(attr.BackoffFactor, failureCount - 1);

            // 限制最大重试间隔
            return Math.Min(interval, attr.MaxRetryMinutes);
        }

        private async Task LogParameters(ActionExecutingContext context, BaseController controller)
        {
            var operationAttr = context.ActionDescriptor.EndpointMetadata
                .OfType<OperationLogAttribute>()
                .FirstOrDefault();

            var parameters = context.ActionArguments
                .Where(kvp => kvp.Value != null)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            await controller.LogParametersAsync(parameters, operationAttr?.OperationName);
        }

    }



    [AttributeUsage(AttributeTargets.Method)]
    public class SkipLoggingAttribute : Attribute { }

    /// <summary>
    /// 自定义日志参数特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class OperationLogAttribute : Attribute
    {
        public string OperationName { get; }

        public OperationLogAttribute(string operationName)
        {
            OperationName = operationName;
        }
    }

    /// <summary>
    /// 幂等特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = false)]
    public class IdempotentWithBackoffAttribute : Attribute
    {
        // 基础配置
        public int InitialRetryMinutes { get; set; } = 5;
        public int MaxRetryMinutes { get; set; } = 24 * 60; // 24小时
        public double BackoffFactor { get; set; } = 6;
        public int SuccessResultCacheDays { get; set; } = 365;

        // 自定义键生成器类型
        public Type KeyGeneratorType { get; set; } = typeof(DefaultIdempotencyKeyGenerator);

        // 锁超时时间（秒）
        public int LockTimeoutSeconds { get; set; } = 30;
    }

}
