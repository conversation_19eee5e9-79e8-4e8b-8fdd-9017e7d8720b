﻿using Dapr;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Adapter.Clients.Competence;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 订阅进项票Sub
    /// </summary>
    [ApiController]
    [Route("api/InputBillSub")]
    public class InputBillSubController : ControllerBase
    {
        private ILogger<InputBillSubController> _logger;
        private ISellAppService _sellAppService = null;
        public IServiceProvider _serviceProvider;
        private IKingdeeApiClient _kingdeeApiClient;
        private IInventoryExcuteApiClient _inventoryExcuteApiClient;
        private IPurchaseExcuteApiClient _purchaseExcuteApiClient;
        private DaprClient _daprClient;
        private IInputBillAppService _inputBillAppService;
        private IEasyCachingProvider _easyCaching;
        public InputBillSubController(
            ILogger<InputBillSubController> logger,
            DaprClient daprClient,
            IEasyCachingProvider easyCaching,
            IKingdeeApiClient kingdeeApiClient,
            IInventoryExcuteApiClient inventoryExcuteApiClient,
            IPurchaseExcuteApiClient purchaseExcuteApiClient,
            IInputBillAppService inputBillAppService,
            IServiceProvider serviceProvider)
        {
            this._easyCaching = easyCaching;
            this._logger = logger;
            this._serviceProvider = serviceProvider;
            this._daprClient = daprClient;
            _kingdeeApiClient = kingdeeApiClient;
            _inventoryExcuteApiClient = inventoryExcuteApiClient;
            _purchaseExcuteApiClient = purchaseExcuteApiClient;
            _inputBillAppService = inputBillAppService;
        }

        #region 提交进项票 - 回滚
        /// <summary>
        /// 金蝶回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("InputBillUnassign")]
        [Topic("pubsub-default", "fin-fin-inputBillUnassign")]
        public async Task<ActionResult> InputBillUnassign(InputBillUnassignInput input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "fin-fin-inputBillUnassign_" + input.invoiceno;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (!string.IsNullOrEmpty(input.invoiceno))
                    {
                        var kingRet = await _kingdeeApiClient.InputBillUnassign(input);
                        if (kingRet.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(kingRet.Message);
                        }
                        await _inputBillAppService.DeleteInputBillDebt(input.invoiceno);
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-inputBillUnassign",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/InputBillUnassign"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 入库回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateInvoiceInfoRevokeRK")]
        [Topic("pubsub-default", "fin-fin-updateInvoiceInfoRevokeRK")]
        public async Task<ActionResult> UpdateInvoiceInfoRevokeRK(List<string> invoiceNumbers)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(invoiceNumbers);
            var cachekey = "fin-fin-updateInvoiceInfoRevokeRK_" + invoiceNumbers.FirstOrDefault();
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (invoiceNumbers != null && invoiceNumbers.Any())
                    {
                        var kcRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(invoiceNumbers);
                        if (kcRet.code != 200)
                        {
                            throw new Exception(kcRet.message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateInvoiceInfoRevokeRK",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateInvoiceInfoRevokeRK"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 经销调出回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateInvoiceInfoRevokeDC")]
        [Topic("pubsub-default", "fin-fin-updateInvoiceInfoRevokeDC")]
        public async Task<ActionResult> UpdateInvoiceInfoRevokeDC(List<InventoryStoreOutUpdateDetail> inventoryStoreOutUpdateDetails)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(inventoryStoreOutUpdateDetails);
            var cachekey = "fin-fin-updateInvoiceInfoRevokeDC_" + inventoryStoreOutUpdateDetails.FirstOrDefault() != null ? inventoryStoreOutUpdateDetails[0].invoiceNumber : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (inventoryStoreOutUpdateDetails != null && inventoryStoreOutUpdateDetails.Any())
                    {
                        var kcRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(inventoryStoreOutUpdateDetails);
                        if (kcRet.code != 200)
                        {
                            throw new Exception(kcRet.message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateInvoiceInfoRevokeDC",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateInvoiceInfoRevokeDC"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 寄售转购货回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateInvoiceQuantity")]
        [Topic("pubsub-default", "fin-fin-updateInvoiceQuantity")]
        public async Task<ActionResult> UpdateInvoiceQuantity(List<UpdateInvoiceQuantityInput> updateQuantiyPuaInput)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(updateQuantiyPuaInput);
            var cachekey = "fin-fin-updateInvoiceQuantity_" + updateQuantiyPuaInput.FirstOrDefault() != null ? updateQuantiyPuaInput[0].InvoiceNumber.ToString() : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (updateQuantiyPuaInput != null && updateQuantiyPuaInput.Any())
                    {
                        var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                        if (purRet.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(purRet.Message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateInvoiceQuantity",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateInvoiceQuantity"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 购货修订回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdatePurchaseReviseInviceAmount")]
        [Topic("pubsub-default", "fin-fin-updatePurchaseReviseInviceAmount")]
        public async Task<ActionResult> UpdateInvoiceQuantity(List<UpdatePurchaseReviseInvoiceAmountInputDto> updatePurchaseReviseInviceAmountInput)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(updatePurchaseReviseInviceAmountInput);
            var cachekey = "fin-fin-updatePurchaseReviseInviceAmount_" + updatePurchaseReviseInviceAmountInput.FirstOrDefault() != null ? updatePurchaseReviseInviceAmountInput[0].purchaseOrderCode : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (updatePurchaseReviseInviceAmountInput != null && updatePurchaseReviseInviceAmountInput.Any())
                    {
                        var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(updatePurchaseReviseInviceAmountInput);
                        if (retpuchase.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(retpuchase.Message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updatePurchaseReviseInviceAmount",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdatePurchaseReviseInviceAmount"  //重试的回调方法路由 
                });
                return Ok();
            }
        }
        #endregion

        #region 取消勾稽 - 回滚
        /// <summary>
        /// 金蝶回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("SubInputBillToKingdee")]
        [Topic("pubsub-default", "fin-fin-subInputBillToKingdee")]
        public async Task<ActionResult> SubInputBillToKingdee(KingdeeInputBillSubmitDto input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "fin-fin-subInputBillToKingdee_" + input.invoiceno;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (!string.IsNullOrEmpty(input.invoiceno))
                    {
                        var kingRet = await _kingdeeApiClient.SubInputBillToKingdee(input);
                        if (kingRet.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(kingRet.Message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-subInputBillToKingdee",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/SubInputBillToKingdee"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 入库回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateStoreInDetail")]
        [Topic("pubsub-default", "fin-fin-updateStoreInDetail")]
        public async Task<ActionResult> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> inventoryStoreInUpdateDetails)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(inventoryStoreInUpdateDetails);
            var cachekey = "fin-fin-updateStoreInDetail_" + inventoryStoreInUpdateDetails.FirstOrDefault().invoiceNumber;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (inventoryStoreInUpdateDetails != null && inventoryStoreInUpdateDetails.Any())
                    {
                        var siRet = await _inventoryExcuteApiClient.UpdateStoreInDetail(inventoryStoreInUpdateDetails);
                        if (siRet.code != 200)
                        {
                            throw new Exception(siRet.message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateStoreInDetail",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateStoreInDetail"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 经销调出回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateStoreOutDetail")]
        [Topic("pubsub-default", "fin-fin-updateStoreOutDetail")]
        public async Task<ActionResult> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> inventoryStoreOutUpdateDetails)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(inventoryStoreOutUpdateDetails);
            var cachekey = "fin-fin-updateStoreOutDetail_" + inventoryStoreOutUpdateDetails.FirstOrDefault() != null ? inventoryStoreOutUpdateDetails[0].invoiceNumber : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (inventoryStoreOutUpdateDetails != null && inventoryStoreOutUpdateDetails.Any())
                    {
                        var soRet = await _inventoryExcuteApiClient.UpdateStoreOutDetail(inventoryStoreOutUpdateDetails);
                        if (soRet.code != 200)
                        {
                            throw new Exception(soRet.message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateStoreOutDetail",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateStoreOutDetail"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 寄售转购货回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdateInvoiceQuantityQx")]
        [Topic("pubsub-default", "fin-fin-updateInvoiceQuantityQx")]
        public async Task<ActionResult> UpdateInvoiceQuantityQx(List<UpdateInvoiceQuantityInput> updateQuantiyPuaInput)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(updateQuantiyPuaInput);
            var cachekey = "fin-fin-updateInvoiceQuantityQx_" + updateQuantiyPuaInput.FirstOrDefault() != null ? updateQuantiyPuaInput[0].InvoiceNumber.ToString() : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (updateQuantiyPuaInput != null && updateQuantiyPuaInput.Any())
                    {
                        var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                        if (purRet.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(purRet.Message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updateInvoiceQuantityQx",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdateInvoiceQuantityQx"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 购货修订回滚
        /// </summary> 
        /// <returns></returns>
        [HttpPost("UpdatePurchaseReviseInviceAmountQx")]
        [Topic("pubsub-default", "fin-fin-updatePurchaseReviseInviceAmountQx")]
        public async Task<ActionResult> UpdatePurchaseReviseInviceAmountQx(List<UpdatePurchaseReviseInvoiceAmountInputDto> updatePurchaseReviseInviceAmountInput)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(updatePurchaseReviseInviceAmountInput);
            var cachekey = "inputbill-cancel-rollbackgh_" + updatePurchaseReviseInviceAmountInput.FirstOrDefault() != null ? updatePurchaseReviseInviceAmountInput[0].purchaseOrderCode : string.Empty;
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (updatePurchaseReviseInviceAmountInput != null && updatePurchaseReviseInviceAmountInput.Any())
                    {
                        var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(updatePurchaseReviseInviceAmountInput);
                        if (retpuchase.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(retpuchase.Message);
                        }
                    }
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "fin-fin-updatePurchaseReviseInviceAmountQx",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/InputBillSub/UpdatePurchaseReviseInviceAmountQx"  //重试的回调方法路由 
                });
                return Ok();
            }
        }
        #endregion
    }
}
