Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.Adapter", "Inno.CorePlatform.Finance.Adapter\Inno.CorePlatform.Finance.Adapter.csproj", "{FFD1CF2D-5082-6123-B1A3-ACB339CED3E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.Application", "Inno.CorePlatform.Finance.Application\Inno.CorePlatform.Finance.Application.csproj", "{7022F973-FAD7-8569-42FA-3B9B2C5B34ED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.Backend", "Inno.CorePlatform.Finance.Backend\Inno.CorePlatform.Finance.Backend.csproj", "{03772C23-8891-AD60-FA85-DEA2F157C473}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.Data", "Inno.CorePlatform.Finance.Data\Inno.CorePlatform.Finance.Data.csproj", "{D4C0C2CF-A450-10CB-F8E3-F1FDE64D941A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.Domain", "Inno.CorePlatform.Finance.Domain\Inno.CorePlatform.Finance.Domain.csproj", "{6F438C98-1AC1-C912-1CC9-AAACD666A8A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Inno.CorePlatform.Finance.WebApi", "Inno.CorePlatform.Finance.WebApi\Inno.CorePlatform.Finance.WebApi.csproj", "{9074EF42-AC77-970A-2436-EDF4EB451CE8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FFD1CF2D-5082-6123-B1A3-ACB339CED3E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFD1CF2D-5082-6123-B1A3-ACB339CED3E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFD1CF2D-5082-6123-B1A3-ACB339CED3E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFD1CF2D-5082-6123-B1A3-ACB339CED3E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7022F973-FAD7-8569-42FA-3B9B2C5B34ED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7022F973-FAD7-8569-42FA-3B9B2C5B34ED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7022F973-FAD7-8569-42FA-3B9B2C5B34ED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7022F973-FAD7-8569-42FA-3B9B2C5B34ED}.Release|Any CPU.Build.0 = Release|Any CPU
		{03772C23-8891-AD60-FA85-DEA2F157C473}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03772C23-8891-AD60-FA85-DEA2F157C473}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03772C23-8891-AD60-FA85-DEA2F157C473}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03772C23-8891-AD60-FA85-DEA2F157C473}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4C0C2CF-A450-10CB-F8E3-F1FDE64D941A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4C0C2CF-A450-10CB-F8E3-F1FDE64D941A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4C0C2CF-A450-10CB-F8E3-F1FDE64D941A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4C0C2CF-A450-10CB-F8E3-F1FDE64D941A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F438C98-1AC1-C912-1CC9-AAACD666A8A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F438C98-1AC1-C912-1CC9-AAACD666A8A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F438C98-1AC1-C912-1CC9-AAACD666A8A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F438C98-1AC1-C912-1CC9-AAACD666A8A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9074EF42-AC77-970A-2436-EDF4EB451CE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9074EF42-AC77-970A-2436-EDF4EB451CE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9074EF42-AC77-970A-2436-EDF4EB451CE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9074EF42-AC77-970A-2436-EDF4EB451CE8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DA2C5D0D-A35C-471D-ADFB-41E4774B528D}
	EndGlobalSection
EndGlobal
