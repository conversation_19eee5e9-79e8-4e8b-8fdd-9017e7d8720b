using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Dapr;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 多发票指定应付控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ManyInvoiceController : ControllerBase
    {
        private readonly IKingdeeApiClient _kingdeeApiClient;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="kingdeeApiClient">金蝶API客户端</param>
        public ManyInvoiceController(IKingdeeApiClient kingdeeApiClient)
        {
            _kingdeeApiClient = kingdeeApiClient;
        }

        /// <summary>
        /// 多发票指定应付
        /// </summary>
        /// <param name="input">包含finentry数组、invoiceno和coreBillNo的请求对象</param>
        /// <returns>操作结果</returns>
        [HttpPost("ManyInvoiceSpecifyApFin")]
        [Topic("pubsub-default", "fin-fin-manyInvoiceSpecifyApFin")]
        public async Task<ActionResult<BaseResponseData<int>>> ManyInvoiceSpecifyApFin(ManyInvoiceSpecifyApFinInput input)
        {
            try
            {
                var result = await _kingdeeApiClient.ManyInvoiceSpecifyApFin(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return Ok(BaseResponseData<int>.Failed(500, $"多发票指定应付出错：{ex.Message}"));
            }
        }
    }
}
