﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("LossRecognitionDetail")]
    [Comment("损失确认明细表")]
    public class LossRecognitionDetailPo : BasePo
    {
        /// <summary>
        /// 损失确认Id
        /// </summary>
        public Guid LossRecognitionItemId { get; set; }
        /// <summary>
        ///  损失确认单
        /// </summary>
        [ForeignKey("LossRecognitionItemId")]
        public virtual LossRecognitionItemPo LossRecognitionItem { get; set; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public LossRecognitionDetailTypeEnum Classify { get; set; }

        /// <summary>
        /// 明细单据日期（应收日期、应付日期）
        /// </summary>
        [Comment("明细单据日期（应收日期、应付日期）")]
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 明细单据号（应收/应付单号）
        /// </summary>
        [Comment("明细单据号（应收/应付单号）")]
        [MaxLength(50)]
        public string BillCode { get; set; }

        /// <summary>
        /// 明细金额（应收金额、应付金额）
        /// </summary>
        [Comment("明细金额（应收金额、应付金额）")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        [Comment("已冲销金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        [Comment("余额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal LeftAmount { get; set; }

        /// <summary>
        /// 确认坏账金额
        /// </summary>
        [Comment("确认坏账金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BadAmount { get; set; }

        [Comment("终端医院Id")]
        [MaxLength(100)]
        public string? HospitalId { get; set; }

        [Comment("终端医院")]
        [MaxLength(100)]
        public string? HospitalName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>

        [Comment("项目单号")]
        [MaxLength(200)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>

        [Comment("项目名称")]
        [MaxLength(100)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [MaxLength(200)]
        public string? CustomerName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [MaxLength(200)]
        public string? AgentName { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        [MaxLength(200)]
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 已使用金额（已入票金额）
        /// </summary>
        [Comment("已使用金额（已入票金额）")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InvoicedAmount { get; set; }
    }
}
