<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>应收盘点管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud">
        <el-input slot="right" v-model="crud.query.searchKey" placeholder="模糊查询单号/公司" suffix-icon="el-icon-search" style="width: 160px; margin-right: 10px" @change="crud.toQuery"></el-input>
      </inno-crud-operation>
    </div>

    <div class="app-page-body" style="padding-top: 0px">
      <inno-query-operation :crud="crud" :query-list.sync="queryList" />

      <inno-split-pane split="horizontal" :default-percent="50">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`盘点主体信息`" />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip type="warning" @click="download">导出应收盘点</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRef"
              v-inno-loading="crud.loading"
              highlight-current-row
              :height="height - 2"
              stripe
              border
              :data="crud.data"
              :row-class-name="crud.tableRowClassName"
              @selection-change="crud.selectionChangeHandler"
              @row-click="crud.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="companyName" label="盘点公司" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="billDate" label="盘点日期" min-width="110">
                <template #default="scope">
                  {{
                  scope.row.billDate === null
                  ? ''
                  : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="code" label="盘点单号" min-width="110" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="操作人" property="createdByName" width="110" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.createdByName }}</template>
              </el-table-column>
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`盘点详细信息`" />
              </el-tabs>
            </template>
            <template #default>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRefDeJd"
              v-inno-loading="crudDeJd.loading"
              highlight-current-row
              :height="height - 2"
              stripe
              border
              show-summary
              :summary-method="getSummaries"
              :data="crudDeJd.data"
              :row-class-name="crudDeJd.tableRowClassName"
              @selection-change="crudDeJd.selectionChangeHandler"
              @row-click="crudDeJd.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="billCode" label="单据编号" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="customerName" label="客户单位" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.customerName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="serviceName" label="业务单元" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.credit.serviceName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column prop="typename" label="单据类型" min-width="110" />
              <el-table-column class-name="isSumChild" sortable prop="credit.value" label="金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.credit.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="abatedValue" label="冲销金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.abatedValue" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="sureIncomeAmount " label="确认收入金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.sureIncomeAmount " format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="invoiceAmount" label="开票金额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="value" label="余额(元)" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <!-- <el-table-column sortable prop="invoiceNo" label="发票号码" min-width="110">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="amount" label="发票金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.amount" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" sortable prop="invoiceAmount" label="开票金额" min-width="110">
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip prop="invoiceTime" label="开票日期" min-width="110">
                  <template #default="scope">
                    {{
                    scope.row.invoiceTime === null
                    ? ''
                    : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                    }}
                  </template>
              </el-table-column>-->
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crudDeJd.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudDeJd" />
          </div>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';

const route = useRoute();

const tableRef = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '应收盘点主体信息',
    url: '/api/InventoryQuery/GetCreditRecordList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      code: '',
      classify: 1 // 1:已签收未开票
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    },
    method: 'post',
    userNames: ['createdBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDeJd = CRUD(
  {
    title: '应收盘点详细信息',
    url: '/api/InventoryQuery/GetCreditRecordDetailList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDeJd
  }
);

const queryList = computed(() => [
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司搜索',
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'billDateBeging',
    endDate: 'billDateEnd',
    label: '盘点日期',
    type: 'daterange',
    formart: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  }
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
onMounted(() => {
  if (route.query && route.query.code) {
    crud.query.code = route.query.code;
    crud.toQuery();
  } else {
    crud.query.code = undefined;
    crud.data = [];
  }
  // crud.toQuery();
  tableDrag(tableRef);
  tableDrag(tableRefDeJd);
});
watch(
  () => crud.rowData.id,
  (n, o) => {
    if (n != null) {
      crudDeJd.query = {
        searchKey: '',
        itemId: crud.rowData.id
      };
      crudDeJd.toQuery();
    }
  },
  { deep: true }
);
watch(
  () => route.query.code,
  (n, o) => {
    if (n == undefined) {
      crud.query.code = '';
      crud.toQuery();
    }
  },
  { deep: true }
);
//下载
const download = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportConvertPaymentList(crud.query, '应收盘点');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportConvertPaymentList = async (data, filename) => {
  await request({
    url: '/api/InventoryQuery/CreditDownLoad',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum', 'isSumChild'].includes(column.className)) {
      var values = [];
      if (column.className == 'isSum') {
        values = data.map((item) => Number(item[column.property]));
      } else if (column.className == 'isSumChild') {
        // 子实体
        const parts = column.property.split('.');
        //const child = parts[0];
        const prop = parts[1];
        values = data.map((item) => Number(item.credit[prop]));
      }
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
</script>
