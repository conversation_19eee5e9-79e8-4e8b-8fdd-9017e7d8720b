﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class InvoiceRadInfo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsRedOff",
                table: "Invoice",
                type: "bit",
                nullable: true,
                comment: "是否红冲");

            migrationBuilder.AddColumn<decimal>(
                name: "ReceiveAmount",
                table: "Invoice",
                type: "decimal(18,2)",
                nullable: true,
                comment: "认款金额");

            migrationBuilder.AddColumn<decimal>(
                name: "RedAmount",
                table: "Invoice",
                type: "decimal(18,2)",
                nullable: true,
                comment: "红冲金额");

            migrationBuilder.AddColumn<string>(
                name: "RelateInvoiceNo",
                table: "Invoice",
                type: "nvarchar(max)",
                nullable: true,
                comment: "红/蓝票号");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsRedOff",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "ReceiveAmount",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "RedAmount",
                table: "Invoice");

            migrationBuilder.DropColumn(
                name: "RelateInvoiceNo",
                table: "Invoice");
        }
    }
}
