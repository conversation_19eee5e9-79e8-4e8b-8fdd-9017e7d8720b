<template>
  <el-dialog v-model="model.controlModel.showDebtByCreditModal" title="添加应付" width="80%" draggable>
    <span>
      <!-- <el-row :gutter="20">

        <el-col :span="8" :offset="0">
          <el-form-item label="应收单号">
            <el-input
              v-model="model.bindModel.detailModal.billCode"
              placeholder="输入应收单号查询"
              clearable
              @keyup.enter.native="queryProduct"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="0">
            <el-form-item label="业务单元">
              <inno-remote-select 
                v-model="model.bindModel.detailModal.billCode" 
                :url="'api/bff/GetServices'" :method="'post'"
                :labelK="'name'" :valueK="'id'" filterable
                placeholder="请选择业务单元" 
                :props="{ KeyWord: 'nameLike' }" 
                is-guid 
                default-first-option
                isObject 
                style="width: 100%" />
            </el-form-item>
          </el-col>
        <el-col :span="8" :offset="0">
          <el-button type="primary" size="small" @click="queryProduct">查询</el-button>
        </el-col>
      </el-row> -->
      <el-table ref="refTable"
                v-inno-loading="model.controlModel.appendDebtByCreditLoading"
                max-height="350"
                :data="model?.dataSource.selectDebtByCreditDetails"
                style="width: 100%"
                border
                stripe
                @selection-change="handleSelectionChange"
                @row-click="clickRow">
        <el-table-column type="selection" width="40" fixed="left" show-overflow-tooltip />
        <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip />
        <el-table-column label="应收单号" width="220" property="billCode" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="应付单号" width="220" property="billCode" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
          <template #default="scope">
            {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
          </template>
        </el-table-column>
        <el-table-column label="项目名称" width="260" property="projectName" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
          </template>
        </el-table-column>
        <!-- <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip></el-table-column> -->
        <el-table-column label="供应商" width="220" property="agentName" show-overflow-tooltip></el-table-column>
        <el-table-column class-name="isSum" label="应付单金额" property="value" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.value" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="model.controlModel.debtByCreditPagination.pageIndex"
        small
        style="margin-top: 10px; margin-bottom: 10xp"
        :page-sizes="[20, 40, 80, 100, 200, 500]"
        :page-size="model.controlModel.debtByCreditPagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="model.controlModel.debtByCreditPagination.total"
        background
        @size-change="
          (size) => {
            model.controlModel.debtByCreditPagination.pageSize = size;
            queryProduct();
          }
        "
        @current-change="
          (currentPage) => {
            model.controlModel.debtByCreditPagination.pageIndex = currentPage;
            queryProduct();
          }
        "
      >
        :pager-count="7">
      </el-pagination>
    </span>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="append"> 添加到明细 </el-button>
        <el-button @click="model.controlModel.showDebtByCreditModal = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { inject, ref } from 'vue';
import {
  AdvanceCapitalApplyVModel,
  CONST_ADVANCECAPITALAPPLY_INJECTIONKEY
} from '../models/AdvanceCapitalApplyVModel';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
const model = inject<AdvanceCapitalApplyVModel>(CONST_ADVANCECAPITALAPPLY_INJECTIONKEY) as AdvanceCapitalApplyVModel;
const refTable = ref();
const gatewayUrl = window.gatewayUrl;
// 处理行选中
const handleSelectionChange = (items: Array<any>) => {
  model.bindModel.detailModal.selectDebtByCredit = items;
};
// 处理行点击
const clickRow = (row: any) => {
  let isSelected: boolean = !model.bindModel.detailModal.selectDebtByCredit.find((item) => {
    return item == row;
  });
  refTable.value!.toggleRowSelection(row, isSelected);
};
const append = () => {
  model.appendDebtByCredit();
};
const queryProduct = () => {
  model.queryDebtByCreditList();
};
</script>
