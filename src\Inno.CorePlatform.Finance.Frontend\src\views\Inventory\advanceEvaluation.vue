<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>垫资运行评价</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud">
        <el-input slot="right" v-model="crud.query.searchKey" placeholder="模糊查询单号/公司" suffix-icon="el-icon-search" style="width: 160px; margin-right: 10px" @change="crud.toQuery"></el-input>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0px">
      <!-- 高级查询 -->
      <inno-query-operation :crud="crud" :query-list.sync="queryList" />
      <inno-split-pane :default-percent="100" split="horizontal" style="padding: 0">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`垫资申请单`" />
              </el-tabs>
            </template>
            <template #default>
              <!-- <inno-button-tooltip type="warning" icon="Download" @click="download">导出垫资申请单</inno-button-tooltip> -->
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRef"
              v-inno-loading="crud.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              border
              show-summary
              :summary-method="getSummaries"
              :data="crud.data"
              :row-class-name="crud.tableRowClassName"
              @selection-change="crud.selectionChangeHandler"
              @row-click="crud.singleSelection"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="code" label="单据编号" fixed width="220" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="billDate" label="单据日期" fixed width="120" :show-overflow-tooltip="true">
                <template #default="scope">
                  {{
                  scope.row.createdTime === null
                  ? ''
                  : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="statusName" label="状态" fixed :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="companyName" label="公司" fixed width="170" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="serviceName" label="业务单元" fixed :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="serviceName" label="业务类型" fixed :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="hospitalName" label="医院" fixed :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="isVerify" label="是否属于核准医院" width="120" :show-overflow-tooltip="true">
                <template #default="scope">{{ scope.row.isVerify ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column prop="isTakeOver" label="是否属于全流程接管医院" width="150" :show-overflow-tooltip="true">
                <template #default="scope">{{ scope.row.isTakeOver ? '是' : '否' }}</template>
              </el-table-column>
              <el-table-column prop="nonVerifyRemark" label="非核准医院申请原因" width="150" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="isInvoice" label="是否发票入账" width="150" :show-overflow-tooltip="true">
                <template #default="scope">{{ scope.row.isInvoice ? '是' : '否' }}</template>
              </el-table-column>

              <el-table-column prop="companyName" label="公司指标" :show-overflow-tooltip="true">
                <el-table-column class-name="isSum" prop="totalAmount" label="标准的资金占用总额度(元)" width="160" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="expectedAmount" label="已审批预计资金额度(元)" width="160" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="totalAmountOfMonth" label="截止上月末资金占用金额(元)" width="170" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="receivableAmountOfNon" label="尚未收回的应收金额(元)" width="160" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="receivableAmountOfTimeout" label="已经逾期的应收金额(元)" width="160" :show-overflow-tooltip="true"></el-table-column>
              </el-table-column>
              <el-table-column label="医院指标" :show-overflow-tooltip="true">
                <el-table-column class-name="isSum" prop="salesVolume" label="医院年销售额(垫资销售额)(元)" width="180" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="unrecycledReceivableAmount" label="尚未收回的垫资应收金额(元)" width="170" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="preUnrecycledReceivableAmount" label="该业务单元垫资应收金额(元)" width="170" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="timeOutReceivableAmount" label="已经逾期的垫资应收金额(元)" width="170" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column class-name="isSum" prop="preTimeOutReceivableAmount" label="该业务单元逾期的垫资应收金额(元)" width="210" :show-overflow-tooltip="true"></el-table-column>
              </el-table-column>
              <el-table-column label="垫资占用天数" :show-overflow-tooltip="true">
                <el-table-column prop="providePayDays" label="供应商付款天数" width="120" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="returnMoneyDays" label="医院回款天数" width="120" :show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="realUseDays" label="实际占用天数" width="120" :show-overflow-tooltip="true"></el-table-column>
              </el-table-column>
              <el-table-column class-name="isSum" prop="expectAmount" label="预计垫资金额(元)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column class-name="isSum" prop="rateOfYear" label="年化垫资利率(%)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column class-name="isSum" prop="expectInterestAmount" label="垫资利息收入(元)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="baseDiscount" label="基础折扣(%)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="SPDDiscount" label="SPD折扣(%)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="supplyChainDiscounts" label="供应链金融折扣(%)" width="130" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="ratio" label="垫资比例(%)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="realSupplyChainDiscounts" label="实际供应链金融折扣(%)" width="150" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="totalDiscounts" label="合计折扣(%)" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="updatedTime" label="修订日期" width="120">
                <template #default="scope">
                  {{
                  scope.row.createdTime === null
                  ? ''
                  : dateFormat(scope.row.updatedTime, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="创建时间" width="120">
                <template #default="scope">
                  {{
                  scope.row.createdTime === null
                  ? ''
                  : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="applyNote" label="备注" width="120" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column prop="createdByName" roperty="createdByName" label="制单人" width="120" :show-overflow-tooltip="true"></el-table-column>
            </el-table>
          </inno-table-container>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <!-- <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`汇总`" />
              </el-tabs>
            </template>
            <template #default>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <inno-table-container v-slot="{ height }">
            <el-table
              ref="tableRefDeJd"
              v-inno-loading="crudDeJd.loading"
              highlight-current-row
              :height="height - 2"
              
              stripe
              border
              show-summary
              :row-class-name="crudDeJd.tableRowClassName"
              :summary-method="getSummaries"
              :data="crudDeJd.data"
            >
              <el-table-column type="selection" fixed="left" width="55" />
              <el-table-column prop="overdueStatus" label="是否逾期" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.overdueStatus }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" prop="overdueInterest" label="逾期利息" :show-overflow-tooltip="true">
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.overdueInterest }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column class-name="isSum" prop="advanceReceiveInterest" label="提前回款利息" min-width="110" />
              <el-table-column class-name="isSum" prop="occupyFundBalance" label="资金占用余额" min-width="110"></el-table-column>
              <el-table-column class-name="isSum" prop="basicProfit" label="基础毛利" min-width="110"></el-table-column>
              <el-table-column class-name="isSum" prop="advanceInterest" label="垫资利息收入" min-width="110"></el-table-column>
              <el-table-column class-name="isSum" prop="totalProfit" label="合计毛利" min-width="110"></el-table-column>
            </el-table>
          </inno-table-container>
        </template> -->
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';

const route = useRoute();

const tableRef = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '应付盘点主体信息',
    url: '/api/AdvanceBusiness/GetAdvanceList',
    idField: 'id',
    userNames: ['createdBy'],
    query: {
      searchKey: '',
      code: ''
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    },
    method: 'post',
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDeJd = CRUD(
  {
    title: '汇总信息',
    url: '/api/AdvanceBusiness/GetAdvanceDetailGroup',
    method: 'post'
  },
  {
    table: tableRefDeJd
  }
);
// 避免传入默认的limit、page、sort参数
crudDeJd.getQueryParams = function () {
  // 清除参数无值的情况
  Object.keys(this.query).length !== 0 &&
    Object.keys(this.query).forEach((item) => {
      if (this.query[item] === null || this.query[item] === '')
        this.query[item] = undefined;
    });
  Object.keys(this.params).length !== 0 &&
    Object.keys(this.params).forEach((item) => {
      if (this.params[item] === null || this.params[item] === '')
        this.params[item] = undefined;
    });
  return {
    // ...this.query,
    ...this.params
  };
};

const queryList = computed(() => [
  {
    key: 'billDateS',
    endDate: 'billDateE',
    label: '单据日期',
    type: 'daterange',
    formart: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司搜索',
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'serviceId',
    label: '业务单元',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/meta`,
    placeholder: '业务单元搜索',
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: true
  },
  {
    key: 'customerId',
    label: '客户',
    type: 'remoteSelect',
    method: 'post',
    placeholder: '客户搜索',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: true
  },
  {
    key: 'code',
    label: '单号',
    method: 'post',
    type: 'remoteInput',
    placeholder: '单号搜索',
    show: true
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    labelK: 'name',
    valueK: 'value',
    dataList: [
      {
        name: '全部',
        value: -1
      },
      {
        name: '准备中',
        value: 1
      },
      {
        name: '运行中',
        value: 2
      },
      {
        name: '已完成',
        value: 3
      }
    ],
    show: true
  }
]);
const download = () => {
  request({
    url: `${backendUrl}api/AdvanceBusiness/Export`,
    method: 'post',
    data: {
      ...crud.query
    },
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  }).then((res) => {
    console.log(res.data);
    const filename = '垫资申请单';
    const xlsx = 'application/vnd.ms-excel';
    const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
    const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
    a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();
  });
};
onMounted(() => {
  if (route.query && route.query.code) {
    crud.query.code = route.query.code;
    crud.toQuery();
  }else{
    crud.query.code = undefined;
    crud.data = [];
  }
  // crud.toQuery();
  tableDrag(tableRef);
  tableDrag(tableRefDeJd);
});
watch(
  () => crud.rowData.id,
  (n, o) => {
    if (n != null) {
      crudDeJd.url = `/api/AdvanceBusiness/GetAdvanceDetailGroup?advanceBusinessApplyId=${crud.rowData.id}`;
      crudDeJd.toQuery();
    }
  },
  { deep: true }
);

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};
</script>
