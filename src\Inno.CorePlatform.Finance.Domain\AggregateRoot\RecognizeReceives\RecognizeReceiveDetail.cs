﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive
{
    public class RecognizeReceiveDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 发票号/订单号/应收单号
        /// </summary>    
        public string Code { get; set; }
        /// <summary>
        /// 认款类型(1:发票，2：订单，3：发票)
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 认款时间
        /// </summary>
        public DateTime RecognizeDate { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal Value { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool IsSkip { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 付款单位Id
        /// </summary> 
        public string? CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary> 
        public string? CustomerNme { get; set; }
        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary> 
        public string? ServiceName { get; set; }
        /// <summary>
        /// 终端客户id
        /// </summary>
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端客户
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 回款显示日期
        /// </summary> 
        public DateTime? BackDateTime { get; set; }
    }


    public class UpdateInvioceReceiveAmount
    { 
        /// <summary>
        /// 发票号/订单号/应收单号
        /// </summary>    
        public string Code { get; set; }
      
      
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal Value { get; set; }
    
    }
}
