﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class RecognizeReceiveDetailCredit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RecognizeReceiveDetailCredit",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RecognizeReceiveDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CurrentValue = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CreditId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecognizeReceiveDetailCredit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RecognizeReceiveDetailCredit_Credit_CreditId",
                        column: x => x.CreditId,
                        principalTable: "Credit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RecognizeReceiveDetailCredit_RecognizeReceiveDetail_RecognizeReceiveDetailId",
                        column: x => x.RecognizeReceiveDetailId,
                        principalTable: "RecognizeReceiveDetail",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "认款明细列表对应应收信息");

            migrationBuilder.CreateIndex(
                name: "IX_RecognizeReceiveDetailCredit_CreditId",
                table: "RecognizeReceiveDetailCredit",
                column: "CreditId");

            migrationBuilder.CreateIndex(
                name: "IX_RecognizeReceiveDetailCredit_RecognizeReceiveDetailId",
                table: "RecognizeReceiveDetailCredit",
                column: "RecognizeReceiveDetailId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RecognizeReceiveDetailCredit");
        }
    }
}
