﻿﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.MergeInputBills
{
    /// <summary>
    /// 合并进项发票
    /// </summary>
    public class MergeInputBill : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 合并发票号（格式为： 核算部门-公司简码-IN-年月-序列号，例如：ZXBD-SH-IN-2504-0001）
        /// </summary>
        public string MergeInvoiceNumber { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanName { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 合并时间
        /// </summary>
        public DateTime MergeTime { get; set; }

        /// <summary>
        /// 票据类型 1=普票，2=专票
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }

        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态 1=临时，2=已提交，3=正在匹配，4=匹配完成，9=忽略
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 取消勾稽时间
        /// </summary>
        public DateTime? CancelReconciliationTime { get; set; }

        /// <summary>
        /// 是否已取消勾稽
        /// </summary>
        public bool? IsCancelledReconciliation { get; set; }
    }
}
