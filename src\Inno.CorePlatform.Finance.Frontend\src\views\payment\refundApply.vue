<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>退款处理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="100" split="horizontal" style="padding: 0">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`待提交`" name="0" lazy />
                <el-tab-pane :label="`已提交`" name="1" lazy />
                <el-tab-pane :label="`审核不通过`" name="66" lazy />
                <el-tab-pane :label="`审核通过`" name="99" lazy />
                <el-tab-pane :label="`已冲销`" name="100" lazy />
                <el-tab-pane :label="`全部`" name="-1" lazy />
              </el-tabs>
            </template>
            <template #default>
              <!-- v-auth="functionUris.add" -->
              <el-button v-if="hasPermission(functionUris.add) && crud.rowData.billstatus === '待提交'" type="primary" style="margin-left: 15px" @click="submitOpt">提交</el-button>
              <el-button v-if="hasPermission(functionUris.add)" type="primary" style="margin-left: 15px" @click="createOpt">创建</el-button>
              <!-- v-auth="functionUris.add" -->
              <el-button v-if="hasPermission(functionUris.add)" type="primary" style="margin-left: 15px" :disabled="!crud.selections.length" v-show="crud.query.status==='0'||crud.query.status==='66'||crud.query.status===undefined" @click="editOpt(crud.rowData)">编辑</el-button>
              <!-- v-auth="functionUris.del" -->
              <el-button v-if="hasPermission(functionUris.del)" type="danger" style="margin-left: 15px" :disabled="!crud.selections.length" v-show="crud.query.status==='0'||crud.query.status==='66'||crud.query.status===undefined" @click="deleteOpt(crud.rowData)">删除</el-button>
              <el-button type="primary" style="margin-left: 15px" :disabled="!crud.selections.length" @click="auditProcessClick(crud.rowData)">审批过程</el-button>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
              </el-button>
            </template>
          </inno-crud-operation>

          <el-table ref="tableItem"
                    v-inno-loading="crud.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    :data="crud.data"
                    stripe
                    border
                    show-summary
                    :summary-method="getSummaries"
                    @sort-change="crud.sortChange"
                    @selection-change="crud.selectionChangeHandler"
                    @row-click="crud.singleSelection">
            <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.billno === crud.rowData.billno && scope.row.e_payeeaccbanknum === crud.rowData.e_payeeaccbanknum" />
              </template>
            </el-table-column>
            <el-table-column label="申请单号" property="billno" fixed="left" min-width="180" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billno }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单据日期" property="applydate" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.applydate.split(' ')[0] }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="orgStr" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orgStr }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="公司" property="payment" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.payment }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户/供应商" property="e_payee" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.e_payee }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="付款方式" min-width="90" property="e_settlementtype" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.e_settlementtype }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="收款账号" min-width="120" property="e_payeeaccbanknum" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.e_payeeaccbanknum }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="退款类型" min-width="120" property="paymenttype" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.paymenttype }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="退款金额" property="payeeamount" class-name="isSum" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  <inno-numeral :value="scope.row.payeeamount" format="0,0.00" />
                </inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="单据状态" property="billstatus" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billstatus }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="付款状态" property="paidstatus" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.paidstatus }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" property="createtime" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.createtime }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="creatorName" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creatorName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link v-if="scope.row.billno" style="font-size: 12px" type="primary" @click="downloadFile(scope.row.billno)">回执单</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <!-- <span style="margin-left: 20px">
              金额：
              <inno-numeral :value="selectionValue" format="0,0.00" />
            </span>-->
            <div class="flex-1" />

            <inno-crud-pagination :crud="crud" />
            <approveProcess ref="approveProcessRef" />

            <el-dialog v-model="createDialogVisible" title="退款申请单" width="800" hight="70%" destroy-on-close :close-on-click-modal="false" draggable>
              <el-form ref="submitFormRef" :inline="true" :model="formData" :rules="rules">
                <el-form-item label="退款形式" prop="refundParty" style="width: 44%">
                  <el-select v-model="formData.refundParty" placeholder="请选择退款形式" clearable @change="reSelectRefundParty" :disabled="isDisabled">
                    <el-option label="客商退我方" value="0" />
                    <el-option label="我方退客商" value="1" />
                  </el-select>
                </el-form-item>
                <el-form-item label="核算部门" prop="newDepart.id" style="width: 44%">
                  <inno-department-select v-model="formData.newDepart.id"
                                          v-model:name="formData.newDepart.name"
                                          v-model:path="formData.newDepart.path"
                                          v-model:fullName="formData.newDepart.fullName"
                                          v-model:item="formData.newDepart.item"
                                          :disabled="[1, 2].includes(props.createdType)"
                                          functionUri="metadata://fam"
                                          @change="businessDeptsChange"></inno-department-select>
                </el-form-item>
                <el-form-item label="公司名称" prop="companies.id" style="width: 44%">
                  <el-select v-model="formData.companies"
                             value-key="id"
                             filterable placeholder="请选择公司"
                             @change="companyChange">
                    <el-option v-for="item in CompanyList" :key="item.id" :label="item.name" :value="item"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="项目名称" prop="projects" :rules="[
      { required: formData.refundParty === '0' ? true : false, message: '请选择项目名称', trigger: 'change' }
    ]" style="width: 44%">
                  <el-select v-model="formData.projects"
                             value-key="id"
                             filterable placeholder="请选择项目">
                    <el-option v-for="item in ProjectList" :key="item.id" :label="item.name" :value="item"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="付款方式" v-if="formData.refundParty === '1'" prop="payType" style="width: 44%">
                  <el-select v-model="formData.payType" placeholder="请选择付款方式" clearable>
                    <el-option v-for="item in payClassifyLst" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="退款类型" v-if="formData.refundParty === '1'" prop="refundType" style="width: 44%">
                  <el-select v-model="formData.refundType" placeholder="请选择退款类型" clearable @change="reSelectCredit">
                    <!-- （1）负数应收——>【203】退销售回款；（与现在保持不变）
      （2）退返利款——>【240】退返利款；（核心、金蝶新增类型）
      （3）退预收款——>【204】退预收款；（核心新增类型）
      （4）退回收到的押金——>【228】 退回收到的押金；（核心新增类型）
      （5）退回收到的保证金——>【229】 退回收到的保证金；（核心新增类型） -->
                    <el-option label="负数应收" value="203" />
                    <el-option label="退返利款" value="240" />
                    <el-option label="退预收款" value="204" />
                    <el-option label="退回收到的押金" value="228" />
                    <el-option label="退回收到的保证金" value="229" />
                    <el-option label="供应商打错款" value="246" />
                    <!-- #113298，重新支付供应商货款由201变更为247 -->
                    <el-option label="重新支付供应商货款" value="247"/>
                    <!--<el-option label="其它" value="996" />-->
                  </el-select>
                </el-form-item>
                <el-form-item label="客商类型" prop="businessGuestsType" style="width: 44%">
                  <el-select v-model="formData.businessGuestsType" :disabled="true" placeholder="请选择客商类型" clearable>
                    <el-option label="供应商" value="0" />
                    <el-option label="客户" value="1" />
                  </el-select>
                </el-form-item>
                <el-form-item label="客商名称" prop="businessGuests.id" style="width: 44%">
                  <inno-remote-select v-model="formData.businessGuests"
                                      isObject
                                      :is-guid="2"
                                      placeholder="请选择客商名称"
                                      value-key="id"
                                      :url="formData.businessGuestsType === '0'?`${gatewayUrl}v1.0/bdsapi/api/agents/meta`:`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
                                      @change="customerChange" />
                </el-form-item>

                <el-form-item label="收款账号" v-if="formData.refundParty === '1'" style="width: 44%" prop="receivedAccount">
                  <el-select v-model="formData.receivedAccount" placeholder="请选择收款账号" clearable>
                    <el-option v-for="item in receivedAccountLst" :key="item.bankNo" :label="item.bank===null ? '没有获取到收款账号' :item.bank + '(' + item.bankNo + ')'" :value="item.bankNo"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="退款金额" prop="refundAmount" v-if="formData.refundParty === '1'" :style="formData .refundType === '996'||formData.refundType === '228'||formData.refundType === '247'||formData.refundType==='204'||formData.refundType==='229'||formData.refundType==='246'?'width: 44%':'width: 92%'">
                  <el-input v-model="formData.refundAmount" :disabled="formData.refundType === '203'||formData.refundType==='240'" maxlength="15" type="tel" />
                </el-form-item>
                <el-form-item label="收款单号" prop="receivablesNumber" style="width: 44%" v-if="(formData.refundType === '996'&& formData.refundParty === '1')||((formData.refundType === '228'||formData.refundType === '247')&&formData.refundParty === '1')||(formData.refundType==='204'&&formData.refundParty === '1')||(formData.refundType==='229'&&formData.refundParty === '1')||(formData.refundType==='246'&&formData.refundParty === '1')">
                  <!-- <el-input v-model="formData.receivablesNumber" clearable /> -->
                  <el-select v-model="formData.receivablesNumber" placeholder="请选择收款单号" clearable filterable>
                    <el-option v-for="item in receivablesNumberLst" :key="item.billno" :label="item.billno" :value="item.billno"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="formData.refundParty==='0'||formData.refundType === '203'||formData.refundType==='240'">
                  <el-button type="primary" @click="addCredit">新增一行</el-button>
                  <el-button type="primary" @click="removeCredit">减少最后一行</el-button>
                  <el-label v-show="formData.refundParty === '0'" style="color:red;margin-left:20px">注：需要先在收款认领-待认款记录中把收款类型改为退采购付款，才可以拉取到</el-label>
                </el-form-item>
                <div v-for="row in formData.credits" :key="row">
                  <el-form-item :label="row.creditNoLable" style="width: 44%">
                    <inno-remote-select v-model="row.minusNumber"
                                        v-model:item="selectCreditItem"
                                        :autoLoad="true"
                                        placeholder="请选择应收单"
                                        :url="'/api/RefundApplyQuery/getCredit'"
                                        :queryData="{
                        CustomerId: formData.businessGuests.id,
                        NameCode: formData.companies.extraInfo?.nameCode,
                        DeptId: formData.newDepart.id,
                        functionUri: 'metadata://fam'
                      }"
                                        async
                                        valueK="billCode"
                                        labelK="billCode"
                                        @blur="selectCredit(selectCreditItem.nonAbatedValue, row)" />
                  </el-form-item>
                  <el-form-item :label="row.creditAmountlabel" style="width: 44%">
                    <el-input v-model="row.refundMoney" maxlength="15" type="tel" @input="inputChange"/>
                  </el-form-item>
                </div>
                <div v-for="row in formData.receiveAndPayment" :key="row">
                  <el-form-item :label="row.receiveLable" style="width: 44%">
                    <inno-remote-select v-model="row.receiveCode"
                                        v-model:item="selectReceiveItem"
                                        :autoLoad="true"
                                        placeholder="请选择收款单"
                                        :url="'/api/RefundApplyQuery/getReceive?companyCode='+formData.companies.extraInfo?.nameCode+'&customerName='+formData.businessGuests.name"
                                        :filterData="(data) =>
                      {
                      return data.map(v => {
                      v.label = v.billno + '   金额   ' + v.namountclaimed
                      v.item=v.billno +  '   金额   ' + v.namountclaimed
                      return v
                      })
                      }"
                      async
                      valueK="item"
                      labelK="label"
                      />
                  </el-form-item>
                  <el-form-item :label="row.paymentLable" style="width: 44%">
                    <inno-remote-select v-model="row.paymentCode"
                                        v-model:item="selectPaymentItem"
                                        :autoLoad="true"
                                        placeholder="请选择冲销对象游离付款单"
                                        :url="'/api/RefundApplyQuery/getPaymnet?CompanyId='+formData.companies.id+'&AgentId='+formData.businessGuests.id+'&projectCode='+formData.projects.code"
                                        :filterData="(data) =>
                      {
                      return data.map(v => {
                      v.label = v.code + '   金额   ' + v.value
                      v.item = v.code + '   金额   ' + v.value
                      return v
                      })
                      }"
                      async
                      valueK="item"
                      labelK="label"
                      />
                  </el-form-item>
                </div>
                <el-form-item label="退款事由" prop="refundReason" style="width: 100%">
                  <el-input v-model="formData.refundReason" type="textarea" maxlength="200" style="width: 92%" rows="4" />
                </el-form-item>
                <el-form-item label="转账附言" prop="transferPostscript" v-if="formData.refundParty === '1'" style="width: 100%">
                  <el-input v-model="formData.transferPostscript" type="textarea" maxlength="200" style="width: 92%" rows="4" />
                </el-form-item>
              </el-form>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="cancelRefundApply">取消</el-button>
                  <el-button type="primary" @click="submitRefundApply">提交</el-button>
                  <el-button type="success" @click="saveRefundApply" v-show="formData.model != 'update'||crud.query.status!='66'">保存</el-button>
                </span>
              </template>
            </el-dialog>
          </div>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
      ref,
      onBeforeMount,
      onMounted,
      onActivated,
      computed,
      watch,
      reactive
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
  import approveProcess from '@/component/ApproveProcess.vue';
  import _ from 'lodash'
      import { getDepartTree, queryCheckedByDept, getTreeList } from '@/api/bdsData';
      import { GetProjectInfoByCompanyId,GetReceiveBills } from '@/api/financeapi';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
const approveProcessRef = ref(null);
import {
      ElTable,
      ElForm,
      ElMessageBox,
      ElMessage,
      ElLoading
} from 'element-plus';
import { AbatedStatus, PaymentTypeEnum } from '@/api/metaInfo';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
const functionUris = {
      add: 'metadata://fam/refundApply-Query/routes/createAndeditRefundApply',
      del: 'metadata://fam/refundApply-Query/routes/deleteRefundApply'
};
const tableItem = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
      {
        title: '应用',
        url: '/api/RefundApplyQuery/GetList',
        idField: 'Id',
        method: 'post',
        crudMethod: {},
        tablekey: 'tablekeyItemhty', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
      query: {
        refundParty:''
      },
        userNames: ['creator'],
        optShow: {
          add: false,
          edit: false,
          del: false,
          download: false,
          reset: true //重置按钮
        },
        hooks: {
          [CRUD.HOOK.afterRefresh]: (_crud) => {
            //默认选中第一行
            if (crud.data.length && crud.data.length > 0) {
              crud.singleSelection(crud.data[0]);
            }
          }
        },
        resultKey: {
          list: 'list',
          total: 'total'
        }
      },
      {
        table: tableItem
      }
);
const selectionValue = computed(() => {
      let value = 0;
      crud.selections.forEach((item) => {
        if (item.value) {
          value += item.payeeamount;
        }
      });
      return value;
});
const activeName = ref('all');

const search = (tab: TabsPaneContext, event: Event) => {
      crud.toQuery();
};

  onMounted(() => {
  crud.query.status = '-1'
  crud.toQuery();
      // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  let resetQuery = crud.resetQuery
  crud.resetQuery = (toQuery = true, noReset) => {
    resetQuery(false, noReset)
    crud.query.status = '-1'
    crud.toQuery()
    toQuery && crud.refresh;
  }

      GetSettlementType();
});

const props = defineProps({
      __refresh: Boolean
});

onActivated(() => {
      if (props.__refresh) {
        crud.toQuery();
      }
});

const queryObject = computed(() =>
      Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

//高级检索
const queryList = computed(() => {
      return [
        {
          show: true,
          key: 'paymentNum',
          label: '公司',
          type: 'remoteSelect',
          multiple: true,
          method: 'post',
          url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
          labelK: 'name',
          valueK: 'id',
          props: { KeyWord: 'name', resultKey: 'data.data' }
        },
        {
          key: 'customerNum',
          label: '客户',
          multiple: true,
          type: 'remoteSelect',
          method: 'post',
          url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
          labelK: 'name',
          valueK: 'id',
          props: { KeyWord: 'name', resultKey: 'data.data' },
          show: true
        },
        {
          key: 'startDate',
          endDate: 'endDate',
          type: 'custom', // 自定义组件使用
          label: '单据日期',
          component: () => {
            return (
              <inno-select-date
                vModel:start-date={crud.query['startDate']}
                vModel:endDate={crud.query['endDate']}
                start-placeholder={'开始单据日期'}
                end-placeholder={'结束单据日期'}
                type="daterange"
                format="YYYY-MM-DD"
              />
            );
          },
          show: true
        },
        {
          key: 'status',
          label: '单据状态',
          type: 'select',
          labelK: 'name',
          valueK: 'id',
          dataList: [
            {
              id: '0',
              name: '待提交'
            },
            {
              id: '1',
              name: '已提交'
            },
            {
              id: '99',
              name: '审核通过'
            },
            {
              id: '100',
              name: '已冲销'
            },
            {
              id: '66',
              name: '审核不通过'
            },
            {
              id: '-1',
              name: '全部'
            }
          ],
          show: true
        },
        {
          key: 'paidstatus',
          label: '付款状态',
          type: 'select',
          labelK: 'name',
          valueK: 'id',
          dataList: [
            {
              id: 'A',
              name: '未付款'
            },
            {
              id: 'C',
              name: '已付款'
            }
          ],
          show: true
        },
        //{
        //  key: 'refundParty',
        //  label: '退款形式',
        //  type: 'select',
        //  labelK: 'name',
        //  valueK: 'id',
        //  dataList: [
        //    {
        //      id: '-1',
        //      name: '全部'
        //    },
        //    {
        //      id: '0',
        //      name: '客商退我方'
        //    },
        //    {
        //      id: '1',
        //      name: '我方退客商'
        //    }
        //  ],
        //  show: false
        //},
      ];
});

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
      const { columns, data } = param;
      const sums: string[] = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (['isSum'].includes(column.className)) {
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => Number.isNaN(value))) {
            sums[index] = `${values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!Number.isNaN(value)) {
                return parseFloat((prev + curr).toFixed(4));
              } else {
                return prev;
              }
            }, 0)}`;
            sums[index] = rbstateFormat(sums[index]);
          } else {
            sums[index] = '';
          }
        } else {
          sums[index] = '';
        }
      });
      return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
      return asyncNumeral(cellValue, '0,0.00');
};
//审批过程
const auditProcessClick = (row) => {
      //审批过程
      approveProcessRef.value.requestId = row.jfzx_requestid;
      approveProcessRef.value.dialogApproveProcessVisible = true;
      approveProcessRef.value.activeName = 'first';
      approveProcessRef.value.GetRemark();
      approveProcessRef.value.goAduit = false;
};
const formData = reactive({
    companies: {companyId: '', companyName: '', extraInfo: { nameCode: ''} },
      newDepart: {
        id: '',
        name: '',
        path: '',
        fullName: '',
        item: {}
      },
      businessGuests: { id: '', name: '' },
      refundType: '',
      payType: '',
      receivedAccount: '',
      refundAmount: 0,
    credits: [],
    projects: { id: '', name: '', code: '' },
      refundReason: '',
      transferPostscript:'',
      model: '',
      billno: '',
      billdate:'',
    receivablesNumber: '',
    receiveAndPayment: [],
    refundItemId:''
});
const rules = reactive<FormRules<RuleForm>>({
      'newDepart.id': [
        { required: true, message: '请选择核算部门', trigger: 'change' }
      ],
      'companies.id': [
        { required: true, message: '请选择公司', trigger: 'change' }
      ],
      'customer.id': [{ required: true, message: '请选择客户', trigger: 'change' }],
      payType: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
      refundType: [
        { required: true, message: '请选择退款类型', trigger: 'change' }
      ],
      receivedAccount: [
        { required: true, message: '请选择收款账号', trigger: 'change' }
      ],
      refundParty: [
        { required: true, message: '请选择退款形式', trigger: 'change' }
      ],
      'businessGuests.id': [
        { required: true, message: '请选择客商名称', trigger: 'change' }
      ],
      businessGuestsType: [
        { required: true, message: '请选择客商类型', trigger: 'change' }
      ],
      refundAmount: [
        { required: true, message: '请输入退款金额并正确填写', trigger: 'blur',
          validator: (rule, value, callback) => {
            const regex = /^-?\d+(\.\d{1,4})?$/;
            return regex.test(value)
          }
        }
      ],
      receivablesNumber: [
        { required: true, message: '请填写收款单号', trigger: 'blur',
          validator: (rule, value, callback) => {
            if ((formData.refundType === '996'||formData.refundType === '228'||formData.refundType === '247') && value === '') {
              callback(new Error('请填写收款单号'));
            } else {
              callback();
            }
          }
        }
      ],
      refundReason: [{ required: true, message: '请输入退款理由', trigger: 'blur' }],
});

let CompanyList = ref([]);
const businessDeptsChange = (node, notclear) => {
      if (!notclear) {
        formData.companies = { companyId: '', companyName: '', extraInfo: { nameCode: '' } };
        formData.projects = {};
    formData.receiveAndPayment=[];
      }
      if (node !== undefined) {
        //  formData.id = node.data.id;
        queryCheckedByDept(node).then((res) => {
          CompanyList.value = res.data.data;
        });
      }
};
      let ProjectList = ref([]);
      const companyChange = (node, notclear) => {
        if (!notclear) {
          formData.projects = {};
          formData.receiveAndPayment = [];
          formData.receivablesNumber = '';
        }
        if (node !== undefined) {
          getReceiveBills();
          GetProjectInfoByCompanyId(node.id).then((res) => {
            ProjectList.value = res.data.data;
          });
        }
      };

const getReceiveBills = () => {
  GetReceiveBills(formData.companies.extraInfo.nameCode,formData.businessGuests.name).then((res) => {
    receivablesNumberLst.value = res.data.data.list;
  });
};

let payClassifyLst = ref([])
let receivablesNumberLst = ref([])
const GetSettlementType=()=>{
      request({
        url: '/api/RefundApplyQuery/GetSettlementType',
        method: 'POST',
        data: {"pageSize":100,"pageNo":1 }
      }).then((res) => {
        if (res.data.code == 200) {
          payClassifyLst.value=  res.data.data
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      });
}

//提交
const submitOpt = () => {
       ElMessageBox.confirm('确认提交该条数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(JSON.stringify(crud.rowData))
        request({
          url: '/api/RefundApply/SubmitKingdee',
          method: 'POST',
          data: crud.rowData
        })
          .then((res) => {
            if (res.data.code == '200') {
              ElMessage({
                showClose: true,
                message: '提交成功',
                type: 'success',
                duration: 3 * 1000
              });
              createDialogVisible.value = false;
              crud.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
      });
}

//创建
const createDialogVisible = ref(false);
const submitFormRef = ref();

const createOpt = () => {
    createDialogVisible.value = true;

      initOptform();
    formData.model = 'insert';
    reSelectRefundParty();
};
      const isDisabled = ref(false);
//编辑
      const editOpt = (row) => {
        isDisabled.value = true;
        if (row.refundParty===0) {
          formData.refundParty = '0';
          formData.businessGuestsType = '0';
        } else {
          formData.refundParty = '1';
          formData.businessGuestsType = '1';
        }
        businessDeptsChange(row.orgNum, true);
        formData.companies.name = row.payment;
        formData.companies.id = row.companyId;
        formData.companies.extraInfo.nameCode = row.paymentNum;
        companyChange(formData.companies,true)

      createDialogVisible.value = true;
        formData.model = crud.query.status === '66' ? 'update' : 'insert';
        formData.billno = row.billno;
        formData.refundItemId = row.id;
      formData.billdate = row.applydate.split(' ')[0];
      formData.newDepart.id = row.orgNum;
        formData.projects.id = row.projectId;
        formData.projects.code = row.projectCode;
        formData.projects.name = row.projectName;
        formData.businessGuests.id = row.e_payeeNum;
        formData.businessGuests.name = row.e_payee;
      formData.payType = row.e_settlementtypeid;
        if (row.paymenttype === '负数应收') {
          formData.refundType = '203';
        } else if (row.paymenttype === '退回收到的押金') {
          formData.refundType = '228';
        } else if (row.paymenttype === '重新支付供应商货款') {
          formData.refundType = '247';
        } else if (row.paymenttype === '退返利款') {
          formData.refundType = '240';
        } else if (row.paymenttype === '退预收款') {
          formData.refundType = '204';
        } else if (row.paymenttype === '退回收到的保证金') {
          if (row.e_payeetype === 'bd_supplier') {
            formData.businessGuestsType = '0';
            formData.refundType = '230';
          } else if (row.e_payeetype === 'bd_customer') {
            formData.refundType = '229';
          } else {
            formData.refundType = '229';
          }
        } else if (row.paymenttype === '供应商打错款') {
          formData.refundType = '246';
          formData.businessGuestsType = '0';
        }

      formData.receivedAccount = row.e_payeeaccbanknum;
      formData.receivablesNumber = '';
      if (row.receivablesNumberList !== null && row.receivablesNumberList.length > 0) {
        formData.receivablesNumber = row.receivablesNumberList[0];
      }
      formData.refundAmount = row.payeeamount;
      formData.credits = [];
      formData.receiveAndPayment = [];
      row.refundEntry.forEach((element) => {
        if (formData.refundType === '203') {
          formData.credits.push({
            creditNoLable: '负数应收单',
            minusNumber: element.jfzx_minusnumber,
            creditAmountlabel: '退款金额',
            refundMoney: element.e_payeeamount
          });
        }
        if (row.refundParty === 0)  {
          formData.receiveAndPayment.push({
            receiveLable: '收款单号',
            receiveCode: element.receiveCode + '  金额  ' + element.receiveAmount,
            paymentLable: '游离付款单',
            paymentCode: element.paymentCode + '  金额  ' + element.paymentAmount
          });
        }
      });

      formData.creator = row.creator;
      formData.refundReason = row.applycause;
      formData.transferPostscript = row.transferPostscript;

};

//删除
const deleteOpt = (row) => {
    ElMessageBox.confirm('确认删除该条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: `/api/RefundApply/Delete`,
        method: 'POST',
        data: { billno: row.billno, creator: row.creator,id:row.id }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '操作成功！',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    });
};
const initOptform = () => {
    formData.companies = { companyId: '', companyName: '', extraInfo: { nameCode: '' } };
      (formData.newDepart = { id: '', name: '', path: '', fullName: '', item: {} }),
        isDisabled.value = false;
      formData.refundType = '';
      formData.payType = '';
      formData.receivedAccount = '';
      formData.refundAmount = 0;
      formData.receivablesNumber = '';
      formData.credits = [];
      formData.receiveAndPayment = [];
      formData.businessGuestsType = '';
      formData.refundReason = '';
      formData.transferPostscript = '';
      formData.billno = '';
      formData.creator = '';
      formData.businessGuests = [];
    formData.projects = { id: '', name: '', code: '' };
    formData.refundItemId = '';
};
const cancelRefundApply = () => {
      createDialogVisible.value = false;
};
      const addCredit = () => {
        if (formData.refundParty === '0') {
          if (
            formData.businessGuests.id === '' || formData.businessGuests.id === undefined||
            formData.newDepart.id === '' || formData.newDepart.id === undefined ||
            formData.companies.id === '' || formData.companies.id === undefined ||
            formData.projects.id === '' || formData.projects.id === undefined
          ) {
            ElMessage({
              showClose: true,
              message: '操作失败，原因：请先选择核算部门、公司、项目、客商后操作',
              type: 'error',
              duration: 3 * 1000
            });
          } else {
            addReceiveAndPayment()
          }

        } else {
          if (formData.refundType === '203' || formData.refundType === '240') {
            addInfo();
          } else {
            ElMessage({
              showClose: true,
              message: '操作失败，原因：请选择客户和退款类型为负数应付或退返利款',
              type: 'error',
              duration: 3 * 1000
            });
          }
        }
};
      const removeCredit = () => {
        if (formData.refundParty === '0') {
          if (formData.receiveAndPayment != null && formData.receiveAndPayment.length > 0) {
            formData.receiveAndPayment.pop();
          }
        }
        else {
          if (formData.credits != null && formData.credits.length > 0) {
            formData.refundAmount = (formData.refundAmount - formData.credits[formData.credits.length - 1].refundMoney).toFixed(2)
            formData.credits.pop();
          }
        }

};
let receivedAccountLst =ref([]);
const customerChange = () => {
    getReceiveBills();
    formData.receivedAccount = '';
    var urlStr = 'SelectReceiptInfoByAgentId';
    if (formData.businessGuestsType === '1') {
      urlStr = 'SelectReceiptInfo';
    } 
    
         request({
          url: `/api/RefundApplyQuery/${urlStr}`,
          method: 'POST',
           data: { id: formData.businessGuests.id }
        })
          .then((res) => {
            if (res.data.code === 200) {
              receivedAccountLst.value = res.data.data;
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          });
}
//提交
const submitRefundApply = () => {
      if (!submitFormRef.value) return;
      submitFormRef.value.validate((valid, field) => {
        if (formData.businessGuests === null || formData.businessGuests.id === ''|| formData.businessGuests.id === null) {
          ElMessage({
            showClose: true,
            message: '请选择客商名称',
            type: 'error',
            duration: 3 * 1000
          });
          return;
        }
        var list = [];
        if (valid) {
          if (formData.refundParty === '0') {
            list = formData.receiveAndPayment;
            for (var i = 0; i < list.length; i++) {
              const receiveParts = list[i].receiveCode.split('金额');
              const paymentParts = list[i].paymentCode.split('金额');
              if (receiveParts.length > 1) {
                list[i].receiveCode = receiveParts[0].trim()
                list[i].receiveAmount = receiveParts[1].trim();
              }
              if (paymentParts.length > 1) {
                list[i].paymentCode = paymentParts[0].trim()
                list[i].paymentAmount = paymentParts[1].trim();
              }
            }
          }
          else {
            list = formData.credits;
            if (formData.refundType === '996' || formData.refundType === '228' || formData.refundType === '247' || formData.refundType == '229' || formData.refundType == '246' || formData.refundType === '204') {
              list = [
                {
                  refundMoney: formData.refundAmount,
                  receivablesNumber: formData.receivablesNumber
                }
              ]
            }
          }      
          let param = {
            refundAllMoney: formData.refundAmount, //退款金额
            bankAccount: '', //银行账号
            bankBranchNumber: '', //银行编码
            bankBranchName: '', //银行支行名称
            bankName: '', //	账号名称
            bankNo: '', //联行号
            payModel: formData.payType, //付款方式
            remark: formData.refundReason,
            dept: formData.newDepart.id,
            businessDeptFullPath: formData.newDepart.path,
            businessDeptFullName: formData.newDepart.fullName,
            moneyNumber: '', //币种
            refundType: formData.refundType === '230' ? '246' : formData.refundType,//供应商打错款等于246
            e_payeetype: formData.businessGuestsType === '0' ? 'bd_supplier' :'bd_customer',
            OAUserName: formData.creator, //OA账号
            client: formData.businessGuests.id, //客户
            model: formData.model,
            company: formData.companies.id,
            companyName: formData.companies.name,
            billno: formData.billno,
            refundItemId: formData.refundItemId,
            projectNumber: formData.projects.code,
            projectId: formData.projects.id,
            projectName: formData.projects.name,
            list: list,
            refundParty: formData.refundParty,
            nameCode: formData.companies.extraInfo?.nameCode,
            customerName: formData.businessGuests.name,
            transferPostscript: formData.transferPostscript,
          };

          if (formData.receivedAccount != ''&&formData.receivedAccount.length>0) {
            var bankInfos = receivedAccountLst.value.filter(p => p.bankNo == formData.receivedAccount);
            if (bankInfos != null && bankInfos.length > 0) {
              param.bankAccount = bankInfos[0].bankNo;
              param.bankBranchNumber = bankInfos[0].bankCode;
              param.bankBranchName = bankInfos[0].bank;
              param.bankName = bankInfos[0].account;
              param.bankNo = bankInfos[0].bankCode;
            }
          }
          if (formData.refundParty === '0') {
            ElMessageBox.confirm('提交会直接将收款单冲销游离付款单, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              const loading = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              request({
                url: '/api/RefundApply/Create',
                method: 'POST',
                data: param
              })
                .then((res) => {
                  loading.close();

                  if (res.data.code == '200') {
                    ElMessage({
                      showClose: true,
                      message: '提交成功',
                      type: 'success',
                      duration: 3 * 1000
                    });
                    createDialogVisible.value = false;
                    crud.toQuery();
                  } else {
                    ElMessage({
                      showClose: true,
                      message: res.data.message,
                      type: 'error',
                      duration: 3 * 1000
                    });
                  }
                })
                .catch((t) => {
                  loading.close();
                });
            });
          } else {
            const loading = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            request({
              url: '/api/RefundApply/Create',
              method: 'POST',
              data: param
            })
              .then((res) => {
                loading.close();

                if (res.data.code == '200') {
                  ElMessage({
                    showClose: true,
                    message: '提交成功',
                    type: 'success',
                    duration: 3 * 1000
                  });
                  createDialogVisible.value = false;
                  crud.toQuery();
                } else {
                  ElMessage({
                    showClose: true,
                    message: res.data.message,
                    type: 'error',
                    duration: 3 * 1000
                  });
                }
              })
              .catch((t) => {
                loading.close();
              });
          }
        }
      });
};
//保存
const saveRefundApply = () => {
      if (!submitFormRef.value) return;
      submitFormRef.value.validate((valid, field) => {
        var list = [];
        if (valid) {
          if (formData.refundParty === '0') {
            list = _.cloneDeep(formData.receiveAndPayment);
            for (var i = 0; i < list.length; i++) {
              const receiveParts =list[i].receiveCode!=undefined?list[i].receiveCode.split('金额'):[];
              const paymentParts =list[i].paymentCode!=undefined? list[i].paymentCode.split('金额'):[];
              if (receiveParts.length > 1) {
                list[i].receiveCode = receiveParts[0].trim()
                list[i].receiveAmount = receiveParts[1].trim();
              }
              if (paymentParts.length > 1) {
                list[i].paymentCode = paymentParts[0].trim()
                list[i].paymentAmount = paymentParts[1].trim();
              }
            }
          }
          else
          {
             list = formData.credits;
            if (formData.refundType === '996' || formData.refundType === '228' || formData.refundType === '247' || formData.refundType == '229' || formData.refundType == '246' || formData.refundType === '204') {
              list = [
                {
                  refundMoney: formData.refundAmount,
                  receivablesNumber: formData.receivablesNumber
                }
              ]
            }
          }
          let param = {};
          if (formData.refundItemId !== '') {
             param = {
              billCode: formData.billno,
              id: formData.refundItemId,
              billDate: formData.billdate,
              businessDeptFullPath: formData.newDepart.path,
              businessDeptFullName: formData.newDepart.fullName,
              businessDeptId: formData.newDepart.id,
              companyId: formData.companies.id,
              companyName: formData.companies.name,
              nameCode: formData.companies.extraInfo?.nameCode,
              customerId: formData.businessGuests.id, //客户
              customerName: formData.businessGuests.name,
              payModel: formData.payType, //付款方式
               refundType: formData.refundType === '230' ? '246' : formData.refundType,//供应商打错款等于246
               e_payeetype: formData.businessGuestsType === '0' ? 'bd_supplier' : 'bd_customer',
              bankAccount: '', //银行账号
              bankName: '', //	账号名称
              bankNo: '', //联行号
              bankBranchName: '', //银行支行名称
              bankBranchNumber: '', //银行编码
              remark: formData.refundReason,
              refundAllMoney: formData.refundAmount, //退款金额
              moneyNumber: '', //币种
              projectCode: formData.projects.code,
              projectName: formData.projects.name,
              projectId: formData.projects.id,
              list: list,
              refundParty: formData.refundParty,
              receivablesNumber: formData.receivablesNumber,
              transferPostscript: formData.transferPostscript,
            };
          } else {
             param = {
              billCode: formData.billno,
              //id: formData.refundItemId,
              billDate: formData.billdate,
              businessDeptFullPath: formData.newDepart.path,
              businessDeptFullName: formData.newDepart.fullName,
              businessDeptId: formData.newDepart.id,
              companyId: formData.companies.id,
              companyName: formData.companies.name,
              nameCode: formData.companies.extraInfo?.nameCode,
              customerId: formData.businessGuests.id, //客户
              customerName: formData.businessGuests.name,
              payModel: formData.payType, //付款方式
               refundType: formData.refundType === '230' ? '246' : formData.refundType,//供应商打错款等于246
               e_payeetype: formData.businessGuestsType === '0' ? 'bd_supplier' : 'bd_customer',
              bankAccount: '', //银行账号
              bankName: '', //	账号名称
              bankNo: '', //联行号
              bankBranchName: '', //银行支行名称
              bankBranchNumber: '', //银行编码
              remark: formData.refundReason,
              refundAllMoney: formData.refundAmount, //退款金额
              moneyNumber: '', //币种
              projectCode: formData.projects.code,
              projectName: formData.projects.name,
              projectId: formData.projects.id,
              list: list,
              refundParty: formData.refundParty,
              receivablesNumber: formData.receivablesNumber,
              transferPostscript: formData.transferPostscript,
            };
          }
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          if (formData.receivedAccount != ''&&formData.receivedAccount.length>0) {
            var bankInfos = receivedAccountLst.value.filter(p => p.bankNo == formData.receivedAccount);
            if (bankInfos != null && bankInfos.length > 0) {
              param.bankAccount = bankInfos[0].bankNo;
              param.bankBranchNumber = bankInfos[0].bankCode;
              param.bankBranchName = bankInfos[0].bank;
              param.bankName = bankInfos[0].account;
              param.bankNo = bankInfos[0].bankCode;
            }
          }
          console.log(JSON.stringify(param));
          request({
            url: '/api/RefundApply/Save',
            method: 'POST',
            data: param
          })
            .then((res) => {
              loading.close();

              if (res.data.code == '200') {
                ElMessage({
                  showClose: true,
                  message: '保存成功',
                  type: 'success',
                  duration: 3 * 1000
                });
                createDialogVisible.value = false;
                crud.toQuery();
              } else {
                ElMessage({
                  showClose: true,
                  message: res.data.message,
                  type: 'error',
                  duration: 3 * 1000
                });
              }
            })
            .catch((t) => {
              loading.close();
            });
        }
      });
};
      const selectCreditItem = ref({});
      const selectReceiveItem = ref({});
      const selectPaymentItem = ref({});
const selectCredit = (nonAbatedValue, row) => {
      if (!isNaN(nonAbatedValue)) {
        row.refundMoney = nonAbatedValue;
        formData.refundAmount = formData.credits.reduce(
          (total, item) => total + parseFloat(item.refundMoney),
          0
        );
        formData.refundAmount = formData.refundAmount.toFixed(2);
      }
};
const reSelectCredit = () => {
      if (formData.refundType !== '203'&&formData.refundType!=='240') {
        formData.credits = [];
        formData.refundAmount = 0;
        if (formData.refundType === '246' ||formData.refundType === '247') {
          if (formData.businessGuestsType !== '0') {
            formData.businessGuestsType = '0';
            formData.businessGuests = [];
          }
        } else {
          if (formData.businessGuestsType !== '1') {
            formData.businessGuestsType = '1';
            formData.businessGuests = [];
          }
        }
      } else if (formData.credits.length === 0) {
        //addInfo();
      }
};
///退款形式
      const reSelectRefundParty = () => {
        if (formData.refundParty === '0' && formData.receiveAndPayment.length === 0) {
          //addReceiveAndPayment();
          formData.businessGuestsType = '0';
        } else{
          formData.receiveAndPayment = [];
          formData.businessGuestsType = '1';
        }
        formData.companies = { companyId: '', companyName: '', extraInfo: { nameCode: '' } };
        formData.projects = {};
        formData.newDepart = {};
        formData.businessGuests = {};
      }

      const addReceiveAndPayment = () => {
        formData.receiveAndPayment.push({
          receiveLable: '收款单号',
          receiveAmount: '',
          paymentLable: '游离付款单',
          paymentAmount: ''
        });
      }

const addInfo = () => {
      console.log(JSON.stringify(formData));
       if (
       formData.businessGuests.id === '' || formData.businessGuests.id === undefined||
       formData.newDepart.id === '' || formData.newDepart.id === undefined ||
       formData.companies.id === '' || formData.companies.id === undefined 
      
     ) {
       ElMessage({
         showClose: true,
         message: '操作失败，原因：请先选择核算部门、公司、客商后操作',
         type: 'error',
         duration: 3 * 1000
       });
     }  else {
        formData.credits.push({
          creditNoLable: '负数应收单',
          minusNumber: '',
          creditAmountlabel: '退款金额',
          refundMoney: 0
        });
      }
};
const downloadFile = (code) => {
      request({
        url: '/api/RefundApplyQuery/GetKDFilePath?code=' + code,
        method: 'get'
      })
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data != null && res.data.data.length > 0) {
              window.open(res.data.data[0].previewAddress);
            } else {
              ElMessage({
                showClose: true,
                message: '未找到金蝶回执单，请稍后再试！',
                type: 'error',
                duration: 3 * 1000
              });
            }
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((t) => {});
};

//页签切换事件
    const tabhandleClick = () => {
      if (crud.query.status == '100') {
        crud.query.refundParty = '0';
      } else {
        crud.query.refundParty = '-1';
      }
      crud.toQuery();
};

const inputChange = (val) => {
  formData.refundAmount = formData.credits.reduce((total, item) => total + Number(item.refundMoney) , 0);
};
</script>
