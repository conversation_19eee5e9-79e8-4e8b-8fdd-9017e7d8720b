﻿using Dapr;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 进项票
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class SubLogController : ControllerBase
    {
        private readonly ISubLogService _subLogService;
        private readonly IUnitOfWork _unitOfWork;
        public SubLogController(ISubLogService subLogService, IUnitOfWork unitOfWork)
        {
            _subLogService = subLogService;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 记录sublog日志
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Topic(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG)]
        [HttpPost("SaveSubLog")]
        public async Task SaveSubLog(SaveSubLogInput input)
        {
            await _subLogService.SaveLogToDB(input.Source,input.Content,input.Operate);
           
        }

    }
}
