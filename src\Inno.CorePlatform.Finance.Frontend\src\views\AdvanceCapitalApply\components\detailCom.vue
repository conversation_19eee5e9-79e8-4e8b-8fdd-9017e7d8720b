<template>
  <div class="detail-box">
    <div class="btn-box">
      <el-button type="primary"  v-if="detailctive === '1' && !props.isDisable" @click="getPaymentPlan">获取付款计划</el-button>
      <el-button type="primary"  v-if="detailctive === '1' && !props.isDisable" @click="SetDeliveryDate">设定实际支付日期</el-button>
      <el-button type="primary"  v-if="detailctive === '1' && !props.isDisable" @click="apportionGoods">分摊到货品</el-button>
      <el-button type="primary"  v-if="detailctive === '1' && !props.isDisable" @click="deletePaymentDetail">删除</el-button>
      <el-button type="primary"  v-if="detailctive === '1'" @click="exportPurchaseDetail(model.bindModel.paymentList)">导出</el-button>
      <el-button type="primary"  v-if="detailctive === '2'" @click="exportDetail(model.bindModel.detaisList)">导出</el-button>
    </div>
    <el-tabs v-model="detailctive" class="demo-tabs" @tab-change="tabDetailActiveClick">
    <el-tab-pane :label="`付款计划明细`" name="1">
      <el-table
        v-if="detailctive === '1'"
        ref="refTable"
        :data="model.bindModel.paymentList"
        max-height="450"
        border
        stripe
        style="margin-top: 4px"
        show-summary
        :summary-method="getSummaries"
        @selection-change="handleSelectionChange"
        @row-click="rowClick"
      >
          <el-table-column type="selection" width="50" fixed="left"/>
          <el-table-column label="应付单号" width="200" property="debtBillNo" show-overflow-tooltip fixed="left">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.debtBillNo }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="账期类型" property="accountPeriodTypeStr" show-overflow-tooltip></el-table-column>
          <el-table-column label="开票日期" property="invoiceTime" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="回款天数" width="100" property="returnDays" show-overflow-tooltip></el-table-column>
          <el-table-column label="预计回款日期" property="estimateReturnDate" width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.estimateReturnDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="实际支付上游日期" property="actualPaymentDate" width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.actualPaymentDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="垫资天数" width="100" property="advancePaymentDays" show-overflow-tooltip></el-table-column>
          <el-table-column label="月利率(%)" width="100" property="monthRate" show-overflow-tooltip></el-table-column>
          <el-table-column label="供应链金额折扣(%)" width="200" property="financeDiscount" show-overflow-tooltip></el-table-column>
          <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
            </template>
          </el-table-column>
          <!-- <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip></el-table-column>
          <el-table-column label="终端医院" width="200" property="hospitalName" show-overflow-tooltip></el-table-column> -->
          <el-table-column class-name="isSum" label="付款金额" property="paymentAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.paymentAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column class-name="isSum" label="对应应收金额" width="120" property="creditValue" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.creditValue" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column label="垫资金额" property="advanceAmount" width="150" show-overflow-tooltip fixed="right">
            <template #default="scope">
              <!-- <inno-numeral :value="scope.row.advanceAmount" format="0,0.00" /> -->
               <el-input-number v-model="scope.row.advanceAmount" placeholder="请输入垫资金额" @change="(e)=>changeAdvanceAmount(e,scope.row)" :disabled="props.isDisable" :min="0" :controls="false" style="width: 100%;"/>
            </template>
          </el-table-column>
        <el-table-column class-name="isSum" width="150" sortable prop="advanceTaxAmount" label="含税垫资毛利" fixed="right">
          <template #default="scope">
            <!-- <inno-numeral :value="scope.row.advanceTaxAmount" format="0,0.00" /> -->
              <el-input-number v-model="scope.row.advanceTaxAmount" placeholder="请输入含税垫资毛利" :disabled="props.isDisable" :min="0" :max="scope.row.advanceAmount" :controls="false" style="width: 100%;"/>

          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="货品明细" name="2">
      <el-table
        v-if="detailctive === '2'"
        ref="refTable"
        :data="model.bindModel.detaisList"
        max-height="450"
        border
        stripe
        style="margin-top: 4px"
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip>
          <!-- <template #default="scope">
            {{ (crudDetail.page.page - 1) * crudDetail.page.size + scope.$index + 1 }}
          </template> -->
        </el-table-column>
        <el-table-column label="业务单据号" width="200" property="businessBillNo" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.businessBillNo }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="货号" property="productNo" show-overflow-tooltip></el-table-column>
        <el-table-column label="品名" property="productName" show-overflow-tooltip></el-table-column>

        <el-table-column label="数量" width="100" property="quantity" show-overflow-tooltip></el-table-column>
        <el-table-column class-name="isSum" label="单位毛利" property="profit" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.profit" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="毛利小计" property="subTotal" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.subTotal" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="originalCost" label="原始成本" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.originalCost" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="originalSalePrice" label="原始售价" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.originalSalePrice" format="0,0.00" />
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
  </div>
  <el-dialog v-model="dialogFormVisible" title="设置实际支付日期" width="500">
    <el-form :model="setForm">
      <el-form-item label="实际支付日期">
        <el-date-picker
          v-model="setForm.deliveryDate"
          type="date"
          placeholder="请选择实际支付日期"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDeliveryDate">取消</el-button>
        <el-button type="primary" @click="confirmDeliveryDate">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>

  <appendDetailModalCom></appendDetailModalCom>
  <!-- <addDebtByCreditModal></addDebtByCreditModal> -->
</template>
<script setup lang="tsx">
import { onMounted, inject, ref, reactive } from 'vue';
import { Delete } from '@element-plus/icons-vue';
import { ElMessage, TableColumnCtx, ElMessageBox } from 'element-plus';
import appendDetailModalCom from './appendDetailModalCom.vue';
import {
  AdvanceCapitalApplyVModel,
  CONST_ADVANCECAPITALAPPLY_INJECTIONKEY
} from '../models/AdvanceCapitalApplyVModel';
import _, { max } from 'lodash';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { Decimal } from 'decimal.js';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { exportXlsxFromJson } from '@inno/inno-mc-vue3/lib/utils/util';
import { AllocateToGoods } from '../apis/api';

const model = inject<AdvanceCapitalApplyVModel>(CONST_ADVANCECAPITALAPPLY_INJECTIONKEY) as AdvanceCapitalApplyVModel;
export interface IStoreInDetailProps {
  basic: {};
}
const props = defineProps<{
  isDisable: boolean;
}>();
const refTable = ref();
const detailctive = ref('1');
const dialogFormVisible = ref(false);
const setForm =  reactive({
  deliveryDate:''
})
const handleSelectionChange = (items: Array<any>) => {
  model.bindModel.selectionDetails?.splice(0);
  model.bindModel.selectionDetails.push(...JSON.parse(JSON.stringify(items)));
};
const handleDebtByCreditChange = (items: Array<any>) => {
  model.bindModel.selectionDebtByCreditDetails?.splice(0);
  model.bindModel.selectionDebtByCreditDetails.push(...JSON.parse(JSON.stringify(items)));
};

const rowClick = (row: any) => {
  let isSelected: boolean = !model.bindModel.selectionDetails.find((el) => {
    return el == row;
  });
  refTable.value!.toggleRowSelection(row, isSelected);
};
// 打开添加应收弹框
const openAppendModal = async () => {
  model.dataSource.selectCreditDetails = [];
  model.openAppendModal();
};
// 打开添加应付弹框
const openDebtByCreditModal = async () => {
  model.dataSource.selectDebtByCreditDetails = [];
  model.openDebtByCreditModal();
};
const formatMarkName = (row: any) => {
  if (row.markName !== '集团寄售') {
    return '寄售';
  } else {
    return row.markName;
  }
};
const edit = async () => {
  if (model && model.bindModel.selectionDetails.length > 0) {
    model.controlModel.showEditDetailModal = true;
  } else {
    ElMessage.warning('请先选择要编辑的数据。');
  }
};
const remove = async () => {
  model.removeCredit();
};
const debtRemove = async () => {
  model.removeDebtByCredit();
};
const tabDetailActiveClick = async (tab: any) => {
  if (detailctive.value === '2' && model.bindModel.detaisList.length === 0 && model.bindModel.basic.id) {
    model.getDetailsList();
  }
}

onMounted(() => {});

//合计
interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'paymentAmount') {
      const values = data.map((item) => item.paymentAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  else if (column.property === 'advanceAmount') {
      const values = data.map((item) => item.advanceAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'advanceTaxAmount') {
      const values = data.map((item) => item.advanceTaxAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'subTotal') {
      const values = data.map((item) => item.subTotal || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalCost') {
      const values = data.map((item) => item.originalCost || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalSalePrice') {
      const values = data.map((item) => item.originalSalePrice || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
  });
  return sums;
};
const getPaymentPlan = async() =>{
  let validateResult = true;
  await model.controlModel.basicForm.validate((valid, fields) => {
    if (!valid) {
      validateResult = false;
      const scrollToFieldName = Object.keys(fields)[0];
      model.controlModel.basicForm.scrollToField(scrollToFieldName); // 滚动到验证错误的第一个字段
    }
  });
  if (!validateResult) {
    return;
  }
  model.controlModel.showAppendModal = true;
  model.queryProduct();
}
const SetDeliveryDate = () =>{
  if(model.bindModel.selectionDetails.length > 0){
    dialogFormVisible.value = true;
  }else{
    ElMessage.warning('请选择需要设置的数据！');
  }
}
const confirmDeliveryDate = () =>{
  if(setForm.deliveryDate !== '' && setForm.deliveryDate !== null && setForm.deliveryDate !== undefined){
    model.bindModel.selectionDetails.map(item =>{
      item.actualPaymentDate = setForm.deliveryDate;
      // el.debtBillNo === r.debtBillNo && el.debtId === r.debtId
      model.bindModel.paymentList.find(el =>{
        return el.debtBillNo === item.debtBillNo && el.debtId === item.debtId
      }).actualPaymentDate = new Date(setForm.deliveryDate)
    })
    console.log( model.bindModel.paymentList,'==================1111111111111111111111111111')
    // model.bindModel.paymentList.map(item =>{
    //   item.actualPaymentDate = setForm.deliveryDate;
    // })
    model.setPaymentDate();
    dialogFormVisible.value = false;
  }else{
    ElMessage.warning('请选择实际支付日期！');
  }
  
}
const cancelDeliveryDate = () =>{
  setForm.deliveryDate = '';
  dialogFormVisible.value = false;
}
const deletePaymentDetail = () =>{
  if(model.bindModel.selectionDetails.length > 0){
    ElMessageBox.confirm('确定要删除数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      let postData = {
        id: model.bindModel.basic.id,
        advancePaymentDebtDetails: model.bindModel.selectionDetails
      }
      model.deleteAdvancePaymenDetail(postData)
    })
    
  }else{
    ElMessage.warning('请选择要删除的数据！');
  }
}
const exportPurchaseDetail = (data:any) => {
  if(data && data.length > 0){
    data.forEach((el: any) => {
      el.invoiceTime = dateFormat(el.invoiceTime, 'YYYY-MM-DD');
      el.estimateReturnDate = dateFormat(el.estimateReturnDate, 'YYYY-MM-DD');
      el.actualPaymentDate = dateFormat(el.actualPaymentDate, 'YYYY-MM-DD');
    })
    exportXlsxFromJson(
      [
        {
          name: `付款计划明细`,
          data: data,
          mapping: {
            debtBillNo: '应付单号',
            accountPeriodTypeStr: '账期类型',
            invoiceTime: '开票日期',
            returnDays: '回款天数',
            estimateReturnDate: '预计回款日期',
            actualPaymentDate: '实际支付上游日期',
            advancePaymentDays: '垫资天数',
            monthRate: '月利率(%)',
            financeDiscount: '供应链金额折扣(%)',
            agentName: '供应商',
            paymentAmount: '付款金额',
            advanceAmount: '垫资金额',
            advanceTaxAmount: '含税垫资毛利',
          }
        }
      ],
      `付款计划明细-${model.bindModel.basic.billCode}`
    );
  }else{
    ElMessage.warning('导出数据不能为空！');
  }
    
}
const exportDetail = (data:any) => {
  if(data && data.length > 0){
    exportXlsxFromJson(
      [
        {
          name: `货品明细`,
          data: data,
          mapping: {
            businessBillNo: '业务单据号',
            productNo: '货号',
            productName: '品名',
            quantity: '数量',
            profit: '单位毛利',
            subTotal: '毛利小计',
            originalCost: '原始成本',
            originalSalePrice: '原始售价',
          }
        }
      ],
      `货品明细-${model.bindModel.basic.billCode}`
    );
  }else{
    ElMessage.warning('导出数据不能为空！');
  }
    
}
const apportionGoods = async() =>{
  if(model.bindModel.paymentList.length > 0){
    ElMessageBox.confirm('确定要分摊到货品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      model.AllocateArrivalGoods();
    })
    
  }else{
    ElMessage.warning('请选择要分摊的数据！');
  }
}
const changeAdvanceAmount = (items:any,row:any) =>{
  row.advanceTaxAmount = asyncNumeral(new Decimal(items).times(new Decimal(row.financeDiscount).div(new Decimal(100))), '0,0.00');
  row.paymentAmount = asyncNumeral(new Decimal(items).mul(new Decimal(row.rate)), '0,0.00');
  if(model.bindModel.basic.id){
    model.saveDetails();
  }
}
</script>
<style lang="scss" scoped >
.detail-box{
  position: relative;
  .btn-box{
    position: absolute;
    right: 5px;
    top: 5px;
    z-index: 1;
  }
}
</style>
