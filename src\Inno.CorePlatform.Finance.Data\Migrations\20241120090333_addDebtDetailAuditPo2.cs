﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addDebtDetailAuditPo2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DebtDetailAudit",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "应付付款计划审核明细单号"),
                    DebtDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "应付明细单Id"),
                    OriginProbablyPayTime = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "原始预计付款日期"),
                    CurrentProbablyPayTime = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "当前预计付款日期"),
                    OARequestId = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "OARequestId"),
                    Status = table.Column<int>(type: "int", nullable: true, comment: "审核状态"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AttachFileIds = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "附件Ids"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtDetailAudit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DebtDetailAudit_DebtDetail_DebtDetailId",
                        column: x => x.DebtDetailId,
                        principalTable: "DebtDetail",
                        principalColumn: "Id");
                },
                comment: "应付付款计划审核明细");

            migrationBuilder.CreateIndex(
                name: "IX_DebtDetailAudit_DebtDetailId",
                table: "DebtDetailAudit",
                column: "DebtDetailId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DebtDetailAudit");
        }
    }
}
