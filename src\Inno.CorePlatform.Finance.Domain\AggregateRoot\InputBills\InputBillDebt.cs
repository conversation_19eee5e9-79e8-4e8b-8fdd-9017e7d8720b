﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills
{
    public class InputBillDebt : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 进项发票id
        /// </summary>
        public Guid InputBillId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public virtual InputBill InputBill { get; set; }

        /// <summary>
        /// 关联应付金额
        /// </summary>
        public decimal DebtAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DebtCode { get; set; }
    }
}
