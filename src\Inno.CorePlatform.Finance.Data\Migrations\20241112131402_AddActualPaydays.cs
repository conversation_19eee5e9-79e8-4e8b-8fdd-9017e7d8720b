﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddActualPaydays : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ActualFinanceDiscount",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ActualPaydays",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AdvanceDays",
                table: "AdvanceFundBusinessCheckDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AdvanceExpireTime",
                table: "AdvanceFundBusinessCheckDetail",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "BasicGrossProfit",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "FundUsedValue",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HintRisk",
                table: "AdvanceFundBusinessCheckDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "IntrestIncome",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OverdueDays",
                table: "AdvanceFundBusinessCheckDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OverdueStatus",
                table: "AdvanceFundBusinessCheckDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalGrossProfit",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Verify",
                table: "AdvanceFundBusinessCheckDetail",
                type: "decimal(18,6)",
                precision: 18,
                scale: 6,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ActualFinanceDiscount",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "ActualPaydays",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "AdvanceDays",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "AdvanceExpireTime",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "BasicGrossProfit",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "FundUsedValue",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "HintRisk",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "IntrestIncome",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "OverdueDays",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "OverdueStatus",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "TotalGrossProfit",
                table: "AdvanceFundBusinessCheckDetail");

            migrationBuilder.DropColumn(
                name: "Verify",
                table: "AdvanceFundBusinessCheckDetail");
        }
    }
}
