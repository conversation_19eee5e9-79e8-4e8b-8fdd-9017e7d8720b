﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("DebtPaymentUseDetail")]
    [Comment("应付当着付款单使用明细表")]
    public class DebtPaymentUseDetailPo : BasePo
    {
        /// <summary>
        /// 使用金额
        /// </summary>
        [Comment("使用金额")]
        [Precision(18, 4)]
        public decimal UseAmount { get; set; }

        /// <summary>
        /// 使用单号
        /// </summary>
        [Comment("使用单号")]
        [MaxLength(200)]
        public string UseCode { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        [Comment("应付单Id")]
        public Guid? DebtId { get; set; }

         

        /// <summary>
        /// 应付单号
        /// </summary>
        [Comment("应付单号")]
        public string? DebtCode { get; set; }

        /// <summary>
        /// 付款申请Id
        /// </summary>
        [Comment("付款申请Id")]
        public Guid? RelateId { get; set; }
    }
}
