<template>
  <div class="detail-box">
    <div class="detail-item">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px' }"
        class="zx-box-card"
        style="padding-bottom: 0px; margin-bottom: 10px"
      >
        
        <template #header>
          <div class="card-header" style="width: 100%">
            <span style="line-height: 42px; height: 42px">应收明细</span>

            <el-button
              v-if="!model.controlModel.disabled"
              type="danger"
              :icon="Delete"
              size="small"
              style="line-height: 42px; float: right; margin: 10px 0px 0px 10px"
              @click="remove"
            >
              删除
            </el-button>
            <el-button
              v-if="!model.controlModel.disabled"
              type="primary"
              size="small"
              style="line-height: 42px; float: right; margin-top: 10px"
              @click="openAppendModal"
            >
              添加应收
            </el-button>
          </div>
        </template>
        <div>
          <el-table
            ref="refTable"
            :data="model.bindModel.creditList"
            max-height="450"
            border
            stripe
            style="margin-top: 4px"
            show-summary
            :summary-method="getSummaries"
            @selection-change="handleSelectionChange"
            @row-click="rowClick"
          >
            <el-table-column type="selection" width="45" fixed="left" show-overflow-tooltip />
            <!-- <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip /> -->
            <el-table-column label="应收单号" width="198" property="billCode" fixed="left" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单据日期" property="billDate" width="100" show-overflow-tooltip>
                <template #default="scope">
                  {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="项目名称" width="120" property="projectName" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                </template>
              </el-table-column>
              <!-- <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip></el-table-column> -->
              <el-table-column label="终端医院" width="120" property="hospitalName" show-overflow-tooltip></el-table-column>
              <el-table-column class-name="isSum" label="应收单金额" width="120" property="value" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <div class="detail-item">
      <el-card
        shadow="never"
        :body-style="{ padding: '10px' }"
        class="zx-box-card"
        style="padding-bottom: 0px; margin-bottom: 10px"
      >
        
        <template #header>
          <div class="card-header" style="width: 100%">
            <span style="line-height: 42px; height: 42px">应收对应应付</span>

            <el-button
              v-if="!model.controlModel.disabled"
              type="danger"
              :icon="Delete"
              size="small"
              style="line-height: 42px; float: right; margin: 10px 0px 0px 10px"
              @click="debtRemove"
            >
              删除
            </el-button>
            <el-button
              v-if="!model.controlModel.disabled"
              type="primary"
              size="small"
              style="line-height: 42px; float: right; margin-top: 10px"
              @click="openDebtByCreditModal"
            >
              添加应付
            </el-button>
          </div>
        </template>
        <div>
          <el-table
            ref="refTable"
            :data="model.bindModel.debtByCredilist"
            max-height="450"
            border
            stripe
            style="margin-top: 4px"
            
            show-summary
            :summary-method="getSummaries"
            @selection-change="handleDebtByCreditChange"
            @row-click="rowClick"
          >
            <el-table-column type="selection" width="45" fixed="left" show-overflow-tooltip />
            <!-- <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip /> -->
            <el-table-column label="应付单号" width="198" property="billCode" fixed="left" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="应收单号" width="198" property="creditBillCode" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="单据日期" property="billDate" width="120" show-overflow-tooltip>
                <template #default="scope">
                  {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
                </template>
              </el-table-column>
              <el-table-column label="项目名称" width="120" property="projectName" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip></el-table-column>
              <!-- <el-table-column label="终端医院" width="120" property="hospitalName" show-overflow-tooltip></el-table-column> -->
              <el-table-column class-name="isSum" label="应付单金额" property="value" width="120" show-overflow-tooltip>
                <template #default="scope">
                  <inno-numeral :value="scope.row.value" format="0,0.00" />
                </template>
              </el-table-column>
              <el-table-column label="已冲销" property="abatmentAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" sortable prop="leftAmount" label="余额(元)" min-width="110">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column fixed="right" prop="badAmount" label="确认坏账金额" min-width="120">
              <template #default="scope">
                <el-input-number v-model="scope.row.badAmount" :max="scope.row.leftAmount" :min="0" :disabled="model.controlModel.disabled" :controls="false" :precision="2" style="width: 100%;" />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
  

  <appendDetailModalCom></appendDetailModalCom>
  <addDebtByCreditModal></addDebtByCreditModal>
</template>
<script setup lang="tsx">
import { onMounted, inject, ref } from 'vue';
import { Delete } from '@element-plus/icons-vue';
import { ElMessage, TableColumnCtx, ElMessageBox } from 'element-plus';
import appendDetailModalCom from './appendDetailModalCom.vue';
import addDebtByCreditModal from './addDebtByCreditModal.vue';
import {
  LossRecognitionApplyVModel,
  IProductListItem,
  CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY
} from '../models/LossRecognitionApplyVModel';
import _, { max } from 'lodash';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { Decimal } from 'decimal.js';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
const model = inject<LossRecognitionApplyVModel>(CONST_LOSSRECOGNITIONAPPLY_INJECTIONKEY) as LossRecognitionApplyVModel;
export interface IStoreInDetailProps {
  basic: {};
}
const props = defineProps<IStoreInDetailProps>();
const refTable = ref();

const handleSelectionChange = (items: Array<IProductListItem>) => {
  model.bindModel.selectionDetails?.splice(0);
  model.bindModel.selectionDetails.push(...JSON.parse(JSON.stringify(items)));
};
const handleDebtByCreditChange = (items: Array<IProductListItem>) => {
  model.bindModel.selectionDebtByCreditDetails?.splice(0);
  model.bindModel.selectionDebtByCreditDetails.push(...JSON.parse(JSON.stringify(items)));
};

const rowClick = (row: IProductListItem) => {
  let isSelected: boolean = !model.bindModel.selectionDetails.find((el) => {
    return el == row;
  });
  refTable.value!.toggleRowSelection(row, isSelected);
};
// 打开添加应收弹框
const openAppendModal = async () => {
  model.dataSource.selectCreditDetails = [];
  model.openAppendModal();
};
// 打开添加应付弹框
const openDebtByCreditModal = async () => {
  model.dataSource.selectDebtByCreditDetails = [];
  model.openDebtByCreditModal();
};
const formatMarkName = (row: any) => {
  if (row.markName !== '集团寄售') {
    return '寄售';
  } else {
    return row.markName;
  }
};
const edit = async () => {
  if (model && model.bindModel.selectionDetails.length > 0) {
    model.controlModel.showEditDetailModal = true;
  } else {
    ElMessage.warning('请先选择要编辑的数据。');
  }
};
const remove = async () => {
  model.removeCredit();
};
const debtRemove = async () => {
  model.removeDebtByCredit();
};

onMounted(() => {});

//合计
interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'applyQuantity') {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0)}`;
      }
    }else if (column.property === 'badAmount') {
      const values = data.map((item) => item.badAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  else if (column.property === 'leftAmount') {
      const values = data.map((item) => item.leftAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'value') {
      const values = data.map((item) => item.value);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'abatmentAmount') {
      const values = data.map((item) => item.abatmentAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
  });
  return sums;
};
</script>
<style lang="scss" scoped >
.detail-box{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  .detail-item{
    width: 49%;
  }
}
</style>
