﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddInvoiceReceiptItemBusinessDept : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullName",
                table: "InvoiceReceiptItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptFullPath",
                table: "InvoiceReceiptItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessDeptId",
                table: "InvoiceReceiptItem",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BusinessDeptFullName",
                table: "InvoiceReceiptItem");

            migrationBuilder.DropColumn(
                name: "BusinessDeptFullPath",
                table: "InvoiceReceiptItem");

            migrationBuilder.DropColumn(
                name: "BusinessDeptId",
                table: "InvoiceReceiptItem");
        }
    }
}
