using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    /// <summary>
    /// 盘点记录仓储接口
    /// </summary>
    public interface IInventoryRecordRepository : IRepositorySupportCrudAndUow<InventoryRecord, Guid>
    {
        /// <summary>
        /// 批量添加盘点记录
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        Task<int> AddManyAsync(List<InventoryRecord> records);

        /// <summary>
        /// 根据盘点单ID和动作类型获取记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <param name="actionType"></param>
        /// <returns></returns>
        Task<InventoryRecord?> GetByInventoryItemIdAndActionTypeAsync(Guid inventoryItemId, InventoryActionType actionType);

        /// <summary>
        /// 根据公司ID和系统月度获取所有记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetByCompanyIdAndSysMonthAsync(Guid companyId, string sysMonth);

        /// <summary>
        /// 根据盘点单ID获取所有记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetByInventoryItemIdAsync(Guid inventoryItemId);

        /// <summary>
        /// 获取待处理的记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetPendingRecordsAsync(Guid? companyId = null, string? sysMonth = null);

        /// <summary>
        /// 批量更新记录状态
        /// </summary>
        /// <param name="recordIds"></param>
        /// <param name="status"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        Task<int> BatchUpdateStatusAsync(List<Guid> recordIds, InventoryRecordStatus status, string? errorMessage = null);

        /// <summary>
        /// 检查指定盘点单的所有记录是否都已完成
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        Task<bool> CheckAllRecordsCompletedAsync(Guid inventoryItemId);
    }
}
