namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 提供公司业务日期相关的管理服务。
    /// 用于根据公司配置和系统状态获取实际的业务日期。
    /// </summary>
    public interface ICompanyDateService
    {
        /// <summary>
        /// 根据公司ID获取实际业务日期。
        /// 如果当前系统月份大于公司系统月份，则根据库存状态决定使用公司系统月份还是当前日期；
        /// 否则直接使用公司系统月份作为实际业务日期。
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <returns>实际业务日期</returns>
        Task<DateTime> GetActualDateAsync(string companyId);
    }
}
