﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addcanceltime : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "SubmitTime",
                table: "MergeInputBill",
                type: "datetime",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CancelReconciliationTime",
                table: "MergeInputBill",
                type: "datetime",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCancelledReconciliation",
                table: "MergeInputBill",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CancelReconciliationTime",
                table: "InputBill",
                type: "datetime",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCancelledReconciliation",
                table: "InputBill",
                type: "bit",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CancelReconciliationTime",
                table: "MergeInputBill");

            migrationBuilder.DropColumn(
                name: "IsCancelledReconciliation",
                table: "MergeInputBill");

            migrationBuilder.DropColumn(
                name: "CancelReconciliationTime",
                table: "InputBill");

            migrationBuilder.DropColumn(
                name: "IsCancelledReconciliation",
                table: "InputBill");

            migrationBuilder.AlterColumn<DateTime>(
                name: "SubmitTime",
                table: "MergeInputBill",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime",
                oldNullable: true);
        }
    }
}
