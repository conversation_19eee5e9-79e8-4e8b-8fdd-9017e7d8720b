﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive
{
    public class RecognizeReceiveTempDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>    
        public Guid ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 项目单号
        /// </summary>
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 认款时间
        /// </summary>
        public DateTime RecognizeDate { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal Value { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary> 
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary> 
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int BusinessDeptId { get; set; }

        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public int? CollectionType { get; set; }

        /// <summary>
        /// 付款单位Id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RecognizeReceiveDetailEnum? Status { get; set; }

        /// <summary>
        /// 撤销金额
        /// </summary>
        public decimal? CancelValue { get; set; }
    }
}
