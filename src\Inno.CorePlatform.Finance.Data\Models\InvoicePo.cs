﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 发票表
    /// </summary>
    [Table("Invoice")]
    public class InvoicePo : BasePo
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 发票号(提交给阳采的发票号)
        /// </summary> 
        [MaxLength(200)]
        public string? SunPurchaseInvoiceNo { get; set; }
        

        /// <summary>
        /// 发票代码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票代码(提交给阳采的发票代码)
        /// </summary> 
        [MaxLength(200)]
        public string? SunPurchaseInvoiceCode { get; set; }
        /// <summary>
        /// 发票状态(接受阳采的发票状态)
        /// </summary> 
        [MaxLength(100)]
        public string? SunPurchaseInvoiceStatus { get; set; }
        /// <summary>
        /// 发票验证码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string Type { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [Comment("公司名称")]
        [MaxLength(200)]
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        [Comment("公司Code")]
        [MaxLength(200)]
        public string? NameCode { get; set; }

        /// <summary>
        /// 运营制作开票Code
        /// </summary>  
        [Comment("运营制作开票Code")]
        [MaxLength(200)]
        public string? CustomizeInvoiceCode { get; set; }


        /// <summary>
        /// 预开票Code
        /// </summary>  
        [Comment("预开票Code")]
        [MaxLength(200)]
        public string? PreCustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>

        [Comment("备注")]
        public string? Remark { get; set; }
        /// <summary>
        /// 是否取消
        /// </summary>

        [Comment("是否取消")]
        public bool? IsCancel { get; set; }

        /// <summary>
        /// SPD状态
        /// </summary>
        public SPDStatusEnum? SPDStatus { get; set; }
         
        /// <summary>
        /// 阳采状态
        /// </summary>
        public SunPurchaseStatusEnum? SunPurchaseStatus { get; set; }

        #region 冗余
        /// <summary>
        /// 认款金额
        /// </summary>
        [Comment("认款金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ReceiveAmount { get; set; }

        /// <summary>
        /// 是否红冲
        /// </summary>
        [Comment("是否红冲")]
        public bool? IsRedOff { get; set; }

        /// <summary>
        /// 红/蓝票号
        /// </summary>
        [Comment("红/蓝票号")]
        public string? RelateInvoiceNo { get; set; }

        /// <summary>
        /// 红冲金额
        /// </summary>
        [Comment("红冲金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? RedAmount { get; set; }

        #endregion
        /// <summary>
        /// 是否初始化数据
        /// </summary>
        [Comment("是否初始化数据")]
        public bool? IsInit { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")] 
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")] 
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }


        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 初始发票入账（1=是，反之是否）
        /// </summary>
        public int? InitInvoiceReceipt { get; set; }

        /// <summary>
        /// 终端医院Id
        /// </summary> 
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }


        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }
        /// <summary>
        ///价格来源
        /// </summary>  
        [Comment("价格来源")]
        public PriceSourceEnum? PriceSource { get; set; }
    }
}
