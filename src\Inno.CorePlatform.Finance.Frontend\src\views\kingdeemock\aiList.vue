<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>****管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left></inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" border hidden-opts-right rightAdjust>
        <template #opts-left>
          <el-tabs v-model="crud.query.status" @tab-change="tabActiveClick">
            <el-tab-pane :label="`待提交(${statusTabCount.waitSubmitCount})`" name="0" />
            <el-tab-pane :label="`审批中(${statusTabCount.holdingCount})`" name="1" />
            <el-tab-pane :label="`已完成(${statusTabCount.finishedCount})`" name="2" />
            <el-tab-pane :label="`已驳回(${statusTabCount.rejectedCount})`" name="11" />
            <el-tab-pane :label="`全部(${statusTabCount.allCount})`" name />
          </el-tabs>
        </template>
        <template #right></template>
      </inno-crud-operation>
      <inno-table-container v-slot="{ height }">
        <el-table
          ref="tableRef"
          v-inno-loading="crud.loading"
          :height="height - 2"
          highlight-current-row
          :header-cell-style="{ background: '#EBEEF5', color: '#909399' }"
          border
          :data="crud.data"
          stripe
          @selection-change="crud.selectionChangeHandler"
          @row-click="crud.rowClick"
        >
          <el-table-column type="selection" fixed="left" width="55" />
          <el-table-column property="billCode" label="销售订单号" width="130" fixed="left" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.billCode" :crud="crud" :column="column" @search="() => crud.toQuery()" />
            </template>
          </el-table-column>
          <el-table-column label="单据日期" property="billDate" width="85" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="单据状态" property="statusName" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column label="订单属性" property="saleProp" width="80" show-overflow-tooltip />
          <el-table-column label="加急" property="isUrgent" width="50" show-overflow-tooltip>
            <template #default="scope">{{ scope.row.isUrgent ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column label="销售公司" property="companyName" width="80" show-overflow-tooltip />
          <el-table-column label="业务单元" property="serviceName" width="80" show-overflow-tooltip />
          <el-table-column label="订货单位" property="customerName" width="80" show-overflow-tooltip />
          <el-table-column label="订货部门" property="customerName" width="80" show-overflow-tooltip />
          <el-table-column label="订货人" property="customerPersonName" width="80" show-overflow-tooltip />
          <el-table-column label="客户地址" property="defaultAddress" width="120" show-overflow-tooltip>
            <template #default="scope">
              {{
              scope.row.defaultAddress
              ? scope.row.defaultAddress.contact +
              '/' +
              scope.row.defaultAddress.contactWay +
              '/' +
              scope.row.defaultAddress.address
              : '-'
              }}
            </template>
          </el-table-column>
          <el-table-column label="操作者" property="createdBy" width="80" show-overflow-tooltip />
          <el-table-column label="审核人/执行人" property="oaAuditInfo" width="80" show-overflow-tooltip />
          <el-table-column label="审核意见/退回意见" property="oaAuditInfo" width="80" show-overflow-tooltip />
          <el-table-column label="更新时间" min-width="80" sortable property="updatedTime" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
          </el-table-column>
          <el-table-column property="patienInfo.name" label="病人姓名" min-width="80" show-overflow-tooltip />
          <el-table-column property="patienInfo.sex" label="病人性别" min-width="80" show-overflow-tooltip />
          <el-table-column property="patienInfo.patienNo" label="住院号" min-width="80" show-overflow-tooltip />
          <el-table-column property="patienInfo.bedNo" label="病床号" min-width="80" show-overflow-tooltip />
          <el-table-column property="createdByName" label="出库库区" min-width="80" show-overflow-tooltip />
          <el-table-column property="description" label="说明" min-width="130" show-overflow-tooltip />
        </el-table>
      </inno-table-container>

      <div class="app-page-footer background">
        已选择 {{ crud.selections.length }} 条
        <div class="flex-1" />
        <inno-crud-pagination :crud="crud" />
      </div>
    </div>

    <!--加载创建表单-->
    <saleOutCreate
      v-if="crud.status.cu > 0"
      :ref="getRef"
      v-model:ruleFormP="crud.form"
      :dialog-visible="crud.status.cu > 0"
      :isEdit="crud.status.edit === 1"
      :loading="crud.status.cu === 2"
      @submit="crud.submitCU"
      @cancel="crud.cancelCU"
    ></saleOutCreate>
  </div>
</template>

<script lang="tsx" setup>
/* eslint-disable */
import { ref, onBeforeMount, onMounted, onActivated, computed, reactive } from 'vue';
import { ElTable } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter } from 'vue-router';
import service from '@/utils/request'; 
import { permission } from '@inno/inno-mc-vue3/lib/permission';
const router = useRouter();
const tableRef = ref<InstanceType<typeof ElTable>>();
// const addRef = ref(null);

const crud = CRUD(
  {
    title: '应用',
    url: '/api/saleout/page',
    downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      status: '1',
      saleType:"1"
    },
    defaultForm: () => {
      return {
        companyId: '',
        serviceId: '',
        customerId: '',
        customerPersonId: '',
        customerAddress: { contact: '', address: '', contactWay: '' },
        saleType: '1',
        saleProp: '',
        patienInfo: { name: '', sex: '', patienNo: '', bedNo: '' },
        description: ''
      }
    },
    method: 'get',
    crudMethod: {       
      del: (ids) => service.delete(`/api/saleout/${ids}`),
      edit: (data) => {
       return service.put(`/api/saleout`, data)
      },
      add: (data) => {
       return service.post(`/api/saleout`,data);
      }
    },
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      add: true,
      edit: true,
      del: true,
      download: true,
      reset: true
    },
    hooks: {
      // [CRUD.HOOK.afterToAdd]: (_crud) => {
      //   createVisible.value = true;
      // },
      // [CRUD.HOOK.afterToEdit]: (_crud) => {
      //   console.log("CRUD.HOOK.afterToEdit",_crud);
      //   createVisible.value = true;
      // }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableRef,
    form: {}
  }
);
const getRef = (e) => {
  if (e) {
    crud.refs.form = e.ruleFormRef
  }
  
}
const props = defineProps({
  __refresh: Boolean
}); 

const  statusTabCount =reactive( {
  waitSubmitCount: 0,
  holdingCount: 0,
  finishedCount: 0,
  rejectedCount: 0,
  allCount: 0,

});
const loadTabCount = () => {  
  service.get("/api/saleout/querySaleStatusTabCount", { params: crud.query }).then(req => {
    statusTabCount.allCount = req.data.data.allCount;
    statusTabCount.finishedCount = req.data.data.finishedCount;
    statusTabCount.holdingCount = req.data.data.holdingCount;
    statusTabCount.rejectedCount = req.data.data.rejectedCount;
    statusTabCount.waitSubmitCount = req.data.data.waitSubmitCount;
  }).catch(err => {
    crud.loading = false;
  });  
};

onBeforeMount(() => {
  crud.toQuery();
  loadTabCount();
});

onMounted(() => {
  
  // 表头拖拽必须在
  tableDrag(tableRef)
});

onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();  
     loadTabCount();  
  }
});

//tab切换事件
const tabActiveClick = (tab: TabsPaneContext, event: Event) => {
    
   loadTabCount();
   crud.toQuery();  
};

const queryList = computed(() => [
  {
    key: 'billCode',
    label: '销售订单号',
    show: true
  }
]);
 
const queryObject = computed(() => Object.fromEntries(queryList.value.map((item) => [item.key, item])));
</script>

<style scoped lang="scss"></style>
