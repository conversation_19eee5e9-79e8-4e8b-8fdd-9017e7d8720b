﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addCreditSaleSubType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CreditSaleSubType",
                table: "CustomizeInvoiceClassify",
                type: "int",
                nullable: true,
                comment: "1：个人消费者， 2：平台");

            migrationBuilder.AddColumn<int>(
                name: "CreditSaleSubType",
                table: "Credit",
                type: "int",
                nullable: true,
                comment: "1：个人消费者， 2：平台");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreditSaleSubType",
                table: "CustomizeInvoiceClassify");

            migrationBuilder.DropColumn(
                name: "CreditSaleSubType",
                table: "Credit");
        }
    }
}
