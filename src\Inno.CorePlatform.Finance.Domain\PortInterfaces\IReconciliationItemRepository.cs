﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    public interface IReconciliationItemRepository : IRepositorySupportCrudAndUow<ReconciliationItem, Guid>
    {
        Task<bool> IsExist(ReconciliationItem model);
    }
}
