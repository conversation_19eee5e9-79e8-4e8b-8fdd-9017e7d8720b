﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreated : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AgentName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId",
                table: "LossRecognitionDetail",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HospitalId",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院Id");

            migrationBuilder.AddColumn<string>(
                name: "HospitalName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院");

            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目单号");

            migrationBuilder.AddColumn<Guid>(
                name: "ProjectId",
                table: "LossRecognitionDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "项目Id");

            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "LossRecognitionDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目名称");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AgentName",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "HospitalName",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "ProjectId",
                table: "LossRecognitionDetail");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "LossRecognitionDetail");
        }
    }
}
