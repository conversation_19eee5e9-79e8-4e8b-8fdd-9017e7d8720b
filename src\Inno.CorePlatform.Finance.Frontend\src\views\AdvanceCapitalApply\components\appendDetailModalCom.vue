<template>
  <el-dialog v-model="model.controlModel.showAppendModal" title="获取付款计划" width="80%" @close="cancel" draggable>
    <span>
      <el-row :gutter="20">
        <el-col :span="6" :offset="0">
          <el-form-item label="应付单号">
            <el-input
              v-model="model.bindModel.detailModal.debtBillNo"
              placeholder="输入应付单号查询"
              clearable
              @change="()=>{
                model.controlModel.productPagination.pageIndex = 1;
                queryProduct()
              }"
            ></el-input>
          </el-form-item>
        </el-col>
         <el-col :span="6" :offset="0">
          <el-form-item label="采购单号">
            <el-input
              v-model="model.bindModel.detailModal.purchaseCode"
              placeholder="输入采购单号查询"
              clearable
              @change="()=>{
                model.controlModel.productPagination.pageIndex = 1;
                queryProduct()
              }"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
            <el-form-item label="供应商">
              <inno-remote-select
                v-model="model.bindModel.detailModal.agent"
                multiple
                isObject
                :is-guid="2"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                labelK="name"
                valueK="id"
                placeholder="请选择供应商"
                :url="`${gatewayUrl}v1.0/bdsapi/api/agents/meta`"
              />
            </el-form-item>
          </el-col>
        <el-col :span="6" :offset="0">
          <el-button type="primary" size="small" @click="queryProduct">查询</el-button>
        </el-col>
        </el-row>
      <el-table
        ref="refTable"
        v-inno-loading="model.controlModel.appendDetailTableLoading"
        max-height="350"
        :data="model?.dataSource.selectCreditDetails"
        style="width: 100%"
        border
        stripe
        @selection-change="handleSelectionChange"
        @row-click="clickRow"
      >
        <el-table-column type="selection" width="40" fixed="left" show-overflow-tooltip />
        <el-table-column label="应付单号" width="220" property="debtBillNo" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.debtBillNo }}</inno-button-copy>
            </template>
          </el-table-column>
            <el-table-column label="订单号" width="220" property="orderNo" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="账期类型" width="220" property="accountPeriodTypeStr" show-overflow-tooltip></el-table-column>
          <el-table-column label="预计回款日期" property="estimateReturnDate" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.estimateReturnDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="供应商" width="260" property="agentName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column class-name="isSum" label="付款金额" property="paymentAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.paymentAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column class-name="isSum" label="对应应收金额" property="creditValue" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.creditValue" format="0,0.00" />
            </template>
          </el-table-column>
      </el-table>
      <el-pagination
        v-model:currentPage="model.controlModel.productPagination.pageIndex"
        small
        style="margin-top: 10px; margin-bottom: 10xp"
        :page-sizes="[20, 40, 80, 100, 200, 500]"
        :page-size="model.controlModel.productPagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="model.controlModel.productPagination.total"
        background
        @size-change="
          (size) => {
            model.controlModel.productPagination.pageSize = size;
            queryProduct();
          }
        "
        @current-change="
          (currentPage) => {
            model.controlModel.productPagination.pageIndex = currentPage;
            queryProduct();
          }
        "
      >
      </el-pagination>
    </span>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="append"> 确定 </el-button>
        <el-button @click="cancel">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { inject, ref } from 'vue';
import {
  AdvanceCapitalApplyVModel,
  CONST_ADVANCECAPITALAPPLY_INJECTIONKEY
} from '../models/AdvanceCapitalApplyVModel';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
const model = inject<AdvanceCapitalApplyVModel>(CONST_ADVANCECAPITALAPPLY_INJECTIONKEY) as AdvanceCapitalApplyVModel;
const refTable = ref();
const gatewayUrl = window.gatewayUrl;
// 处理行选中
const handleSelectionChange = (items: Array<any>) => {
  model.bindModel.detailModal.selectProducts = items;
};
// 处理行点击
const clickRow = (row: any) => {
  let isSelected: boolean = !model.bindModel.detailModal.selectProducts.find((item) => {
    return item == row;
  });
  refTable.value!.toggleRowSelection(row, isSelected);
};
const append = async() => {
  model.appendToDetails();
};
const queryProduct = () => {
  model.queryProduct();
};
const cancel = () => {
  model.bindModel.detailModal.billCode = '';
  model.bindModel.detailModal.projectId = '';
  model.bindModel.detailModal.serviceId  = '';
  model.controlModel.showAppendModal = false;
};
</script>
