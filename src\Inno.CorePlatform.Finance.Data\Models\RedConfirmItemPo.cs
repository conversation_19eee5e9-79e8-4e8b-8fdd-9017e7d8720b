﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 红字确认单表
    /// </summary>
    [Table("RedConfirmItem")]
    public class RedConfirmItemPo : BasePo
    {
        /// <summary>
        /// 税局确认单唯一uuid
        /// </summary>  

        [Comment("税局确认单唯一uuid")]
        [MaxLength(500)]
        public string? GovRedConfirmBillUuid { get; set; }
        /// <summary>
        /// 确认单状态
        /// </summary>
        [Comment("确认单状态")]
        [MaxLength(500)]
        public string? RedConfirmBillStatus { get; set; }
        /// <summary>
        /// 红字确认单编号
        /// </summary>

        [Comment("红字确认单编号")]
        [MaxLength(500)]
        public string? RedInfoBillNo { get; set; }
        /// <summary>
        /// 红字确认单录入日期
        /// </summary>
        [Comment("红字确认单录入日期")]
        [MaxLength(500)]
        public string? RedConfirmEnterDate { get; set; }
        /// <summary>
        /// 红冲原因
        /// </summary>
        [Comment("红冲原因")]
        [MaxLength(500)]
        public string? RedReason { get; set; }
        /// <summary>
        /// 录入方身份
        /// </summary>
        [Comment("录入方身份")]
        [MaxLength(500)]
        public string? EnterIdentity { get; set; }
        /// <summary>
        /// 购方名称
        /// </summary>
        [Comment("购方名称")]
        [MaxLength(500)]
        public string? BuyerName { get; set; }
        /// <summary>
        /// 购方税号，蓝票如果购方税号为空，这里也会返回为空字符串
        /// </summary>
        [Comment("购方税号，蓝票如果购方税号为空，这里也会返回为空字符串")]
        [MaxLength(500)]
        public string? BuyerTaxpayerId { get; set; }
        /// <summary>
        /// 销方名称
        /// </summary>
        [Comment("销方名称")]
        [MaxLength(500)]
        public string? SellerName { get; set; }
        /// <summary>
        /// 销方税号
        /// </summary>
        [Comment("销方税号")]
        [MaxLength(500)]
        public string? SellerTaxpayerId { get; set; }
        /// <summary>
        /// 红票的发票号码,如果确认单已经开具红票，这个字段会返回红票的发票号码，否则为空字符串
        /// </summary>
        [Comment("红票的发票号码,如果确认单已经开具红票，这个字段会返回红票的发票号码，否则为空字符串")]
        [MaxLength(500)]
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 红字冲销金额
        /// </summary>
        [Comment("红字冲销金额")]
        [MaxLength(500)]
        public string? TotalAmount { get; set; }
        /// <summary>
        /// 红字冲销税额
        /// </summary>
        [Comment("红字冲销税额")]
        [MaxLength(500)]
        public decimal? TotalTaxAmount { get; set; }
        /// <summary>
        /// 红票对应的开票日期，如果已经开具红票，返回精确到秒的格式：2022-11-23 15:36:25，否则返回空字符串
        /// </summary>
        [Comment("红票对应的开票日期，如果已经开具红票，返回精确到秒的格式：2022-11-23 15:36:25，否则返回空字符串")]
        [MaxLength(500)]
        public string? InvoiceDate { get; set; }
        /// <summary>
        /// 蓝字发票类型,26全电类型普票发票，27全电类型专用发票
        /// </summary>
        [Comment("蓝字发票类型,26全电类型普票发票，27全电类型专用发票")]
        [MaxLength(500)]
        public string? OriginalInvoiceType { get; set; }
        /// <summary>
        /// 蓝字发票代码，如果是全电蓝字发票，发票代码返回空字符串，否则返回对应蓝票的发票代码
        /// </summary>
        [Comment("蓝字发票代码，如果是全电蓝字发票，发票代码返回空字符串，否则返回对应蓝票的发票代码")]
        [MaxLength(500)]
        public string? OriginalInvoiceCode { get; set; }
        /// <summary>
        /// 蓝字发票号码
        /// </summary>
        [Comment("蓝字发票号码")]
        [MaxLength(500)]
        public string? originalInvoiceNumber { get; set; }
        /// <summary>
        /// 蓝字发票不含税金额
        /// </summary> 
        [Comment("蓝字发票不含税金额")] 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalInvoiceAmount { get; set; }
        /// <summary>
        /// 蓝字发票税额
        /// </summary>

        [Comment("蓝字发票税额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalTotalTaxAmount { get; set; }
        /// <summary>
        /// 蓝字发票日期，精确到秒格式：2022-11-23 15:36:25
        /// </summary>
        [Comment("蓝字发票日期，精确到秒格式：2022-11-23 15:36:25")]
        [MaxLength(500)]
        public string? originalIssueTime { get; set; }
    }
}
