﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("DebtDetailAudit")]
    [Comment("应付付款计划审核明细")]
    public class DebtDetailAuditPo : BasePo
    {
        /// <summary>
        /// 应付付款计划审核明细单号
        /// </summary>
        [Comment("应付付款计划审核明细单号")] 
        [MaxLength(200)]
        public string Code { get; set; }
         
        /// <summary>
        /// 应付单Id
        /// </summary>
        [Comment("应付明细单Id")]
        public Guid? DebtDetailId { get; set; }

        [ForeignKey("DebtDetailId")]
        public virtual DebtDetailPo? DebtDetail { get; set; }
          
        /// <summary>
        /// 预计付款日期
        /// </summary>  
        [Comment("原始预计付款日期")]
        public DateTime? OriginProbablyPayTime { get; set; }
         
        /// <summary>
        /// 当前预计付款日期
        /// </summary>  
        [Comment("当前预计付款日期")]
        public DateTime? CurrentProbablyPayTime { get; set; }
      
        /// <summary>
        /// OARequestId
        /// </summary>
        [Comment("OARequestId")]
        [MaxLength(500)]
        public string? OARequestId { get; set; }
        
        /// <summary>
        /// 审核状态
        /// </summary>
        [Comment("审核状态")]
        public StatusEnum? Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
         
        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; } 

    }
}
