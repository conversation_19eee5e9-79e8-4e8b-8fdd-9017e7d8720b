﻿﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.MergeInputBills
{
    /// <summary>
    /// 合并进项发票明细
    /// </summary>
    public class MergeInputBillDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        public virtual MergeInputBill MergeInputBill { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [MaxLength(500)]
        public string ProductName { get; set; }

        /// <summary>
        /// 品名Id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [MaxLength(500)]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [MaxLength(500)]
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [MaxLength(500)]
        public string? Model { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 含税金额（计算属性：TaxCost * Quantity）
        /// </summary>
        public decimal? TotalAmount { get; set; }
    }
}
