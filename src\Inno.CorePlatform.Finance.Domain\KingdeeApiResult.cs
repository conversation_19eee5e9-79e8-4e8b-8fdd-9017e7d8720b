using System.Diagnostics.CodeAnalysis;

namespace Inno.CorePlatform.Finance.Domain
{
    /// <summary>
    /// 金蝶API调用结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class KingdeeApiResult<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get;  set; }

        /// <summary>
        /// 数据
        /// </summary>
        public T? Data { get;  set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get;  set; }

        /// <summary>
        /// 金蝶错误码
        /// </summary>
        public string? ErrorCode { get;  set; }

        /// <summary>
        /// 是否为业务异常（如数据已存在）
        /// </summary>
        public bool IsBusinessError { get;  set; }

        /// <summary>
        /// 原始响应内容（用于调试）
        /// </summary>
        public string? RawResponse { get;  set; }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        public KingdeeApiResult() { }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static KingdeeApiResult<T> Success(T? data = default, string? rawResponse = null)
        {
            return new KingdeeApiResult<T>
            {
                IsSuccess = true,
                Data = data,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static KingdeeApiResult<T> Failure(
            string errorMessage, 
            string? errorCode = null, 
            bool isBusinessError = false,
            string? rawResponse = null)
        {
            return new KingdeeApiResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode,
                IsBusinessError = isBusinessError,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 创建业务错误结果（如数据已存在）
        /// </summary>
        public static KingdeeApiResult<T> BusinessError(
            string errorMessage, 
            string? errorCode = null,
            string? rawResponse = null)
        {
            return new KingdeeApiResult<T>
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode,
                IsBusinessError = true,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 隐式转换为bool（检查是否成功）
        /// </summary>
        public static implicit operator bool(KingdeeApiResult<T> result) => result.IsSuccess;

        /// <summary>
        /// 确保成功，否则抛出异常
        /// </summary>
        public T EnsureSuccess()
        {
            if (!IsSuccess)
            {
                throw new KingdeeApiException(ErrorMessage ?? "金蝶API调用失败", ErrorCode, IsBusinessError);
            }
            return Data!;
        }

        /// <summary>
        /// 尝试获取数据
        /// </summary>
        public bool TryGetData([NotNullWhen(true)] out T? data)
        {
            data = Data;
            return IsSuccess && data != null;
        }

        /// <summary>
        /// 匹配模式处理结果
        /// </summary>
        public TResult Match<TResult>(
            Func<T?, TResult> onSuccess,
            Func<string, string?, bool, TResult> onFailure)
        {
            return IsSuccess 
                ? onSuccess(Data) 
                : onFailure(ErrorMessage ?? "未知错误", ErrorCode, IsBusinessError);
        }

        /// <summary>
        /// 异步匹配模式处理结果
        /// </summary>
        public async Task<TResult> MatchAsync<TResult>(
            Func<T?, Task<TResult>> onSuccess,
            Func<string, string?, bool, Task<TResult>> onFailure)
        {
            return IsSuccess 
                ? await onSuccess(Data) 
                : await onFailure(ErrorMessage ?? "未知错误", ErrorCode, IsBusinessError);
        }

        /// <summary>
        /// 转换数据类型
        /// </summary>
        public KingdeeApiResult<TNew> Map<TNew>(Func<T?, TNew> mapper)
        {
            if (!IsSuccess)
            {
                return KingdeeApiResult<TNew>.Failure(ErrorMessage!, ErrorCode, IsBusinessError, RawResponse);
            }

            try
            {
                var newData = mapper(Data);
                return KingdeeApiResult<TNew>.Success(newData, RawResponse);
            }
            catch (Exception ex)
            {
                return KingdeeApiResult<TNew>.Failure($"数据转换失败: {ex.Message}", null, false, RawResponse);
            }
        }

        /// <summary>
        /// 绑定操作（Monad模式）
        /// </summary>
        public KingdeeApiResult<TNew> Bind<TNew>(Func<T?, KingdeeApiResult<TNew>> binder)
        {
            return IsSuccess ? binder(Data) : KingdeeApiResult<TNew>.Failure(ErrorMessage!, ErrorCode, IsBusinessError, RawResponse);
        }

        /// <summary>
        /// 获取数据或默认值
        /// </summary>
        public T? GetDataOrDefault(T? defaultValue = default) => IsSuccess ? Data : defaultValue;

        /// <summary>
        /// 重写ToString
        /// </summary>
        public override string ToString()
        {
            if (IsSuccess)
            {
                return $"Success: {Data}";
            }
            
            var errorInfo = $"Error: {ErrorMessage}";
            if (!string.IsNullOrEmpty(ErrorCode))
            {
                errorInfo += $" (Code: {ErrorCode})";
            }
            if (IsBusinessError)
            {
                errorInfo += " [Business Error]";
            }
            return errorInfo;
        }
    }

    /// <summary>
    /// 无数据的金蝶API结果
    /// </summary>
    public class KingdeeApiResult : KingdeeApiResult<object>
    {
        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static new KingdeeApiResult Success(string? rawResponse = null)
        {
            return new KingdeeApiResult
            {
                IsSuccess = true,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static new KingdeeApiResult Failure(
            string errorMessage, 
            string? errorCode = null, 
            bool isBusinessError = false,
            string? rawResponse = null)
        {
            return new KingdeeApiResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode,
                IsBusinessError = isBusinessError,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 创建业务错误结果
        /// </summary>
        public static new KingdeeApiResult BusinessError(
            string errorMessage, 
            string? errorCode = null,
            string? rawResponse = null)
        {
            return new KingdeeApiResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode,
                IsBusinessError = true,
                RawResponse = rawResponse
            };
        }

        /// <summary>
        /// 确保成功，否则抛出异常
        /// </summary>
        public new void EnsureSuccess()
        {
            if (!IsSuccess)
            {
                throw new KingdeeApiException(ErrorMessage ?? "金蝶API调用失败", ErrorCode, IsBusinessError);
            }
        }
    }

    /// <summary>
    /// 金蝶API异常
    /// </summary>
    public class KingdeeApiException : Exception
    {
        /// <summary>
        /// 金蝶错误码
        /// </summary>
        public string? ErrorCode { get; }

        /// <summary>
        /// 是否为业务异常
        /// </summary>
        public bool IsBusinessError { get; }

        public KingdeeApiException(string message, string? errorCode = null, bool isBusinessError = false) 
            : base(message)
        {
            ErrorCode = errorCode;
            IsBusinessError = isBusinessError;
        }

        public KingdeeApiException(string message, Exception innerException, string? errorCode = null, bool isBusinessError = false) 
            : base(message, innerException)
        {
            ErrorCode = errorCode;
            IsBusinessError = isBusinessError;
        }
    }
}
