﻿using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate
{
    public class DebtDetailExcute : EntityWithBasicInfo<Guid>
    {
        /// <summary>
        /// 应付执行计划Id
        /// </summary>
        public Guid DebtDetailId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; } 

        /// <summary>
        /// 执行类型 debt,payment
        /// </summary> 
        public string? ExcuteType { get; set; }
    }
}
