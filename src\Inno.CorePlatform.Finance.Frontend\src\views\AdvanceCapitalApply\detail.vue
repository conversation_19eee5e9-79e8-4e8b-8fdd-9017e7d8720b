<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb :separator-icon="ArrowRight">
        <el-breadcrumb-item :to="'/advanceCapitalApply/index'" replace>提前付款垫资申请</el-breadcrumb-item>
        <el-breadcrumb-item>提前付款垫资详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="app-page-body">
      <div class="flex-1" style="overflow: auto">
        <basicCom ref="BasicComRef"></basicCom>
        <detailCom :isDisable="true"></detailCom>
      </div>
    </div>
    <div class="app-page-footer">
      <div class="flex-1"></div>
      <el-button type="default" @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { ArrowRight } from '@element-plus/icons-vue';
import basicCom from './components/basicCom.vue';
import detailCom from './components/detailCom.vue';
import { AdvanceCapitalApplyVModel, CONST_ADVANCECAPITALAPPLY_INJECTIONKEY } from './models/AdvanceCapitalApplyVModel';
import { ref, provide, reactive, onBeforeMount, onActivated, onMounted} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import _ from 'lodash';

const { query } = useRoute();
const router = useRouter();
const activeCollapse = ref(['1', '2']);
const BasicComRef = ref();
const model = reactive(new AdvanceCapitalApplyVModel());
provide(CONST_ADVANCECAPITALAPPLY_INJECTIONKEY, model);
onBeforeMount(() => {
  if (query.id) {
    model.controlModel.isAllow = true;
    model.controlModel.isView = true;
    model.propsModel.id = query.id.toString();
  }
});
onMounted(async() => {
  model.controlModel.isCreate = false;
  if (useRoute().query && useRoute().query.id) {
    model.propsModel.id = useRoute().query.id?.toString();
    model.bindModel.basic.disabled = true;
    model.controlModel.detailIsAllow = true;
    model.controlModel.disabled = true;
    await model.loadDetail();
  }
});
// 保存
async function save() {
  let validateResult = true;
  await model.controlModel.basicForm.validate((valid, fields) => {
    if (!valid) {
      validateResult = false;
      const scrollToFieldName = Object.keys(fields)[0];
      model.controlModel.basicForm.scrollToField(scrollToFieldName); // 滚动到验证错误的第一个字段
    }
  });
  if (!validateResult) {
    return;
  }
  await model.save();
}
// 提交
async function submit() {
  let validateResult = true;
  await model.controlModel.basicForm.validate((valid, fields) => {
    if (!valid) {
      validateResult = false;
      const scrollToFieldName = Object.keys(fields)[0];
      model.controlModel.basicForm.scrollToField(scrollToFieldName); // 滚动到验证错误的第一个字段
    }
  });
  if (!validateResult) {
    return;
  }
  // let result = await model.submit();
  if (result) {
    // 跳转列表页
    router.replace({
      path: '/advanceCapitalApply/index',
      params: {
        __refresh: true
      }
    });
  }
}
// 取消
function cancel() {
  // 跳转列表页
  router.replace({
    path: '/advanceCapitalApply/index',
    params: {
      __refresh: true
    }
  });
}

const queryObject = reactive<{
  page_id?: string;
}>({});
onActivated(() => {
  if (
    queryObject.page_id &&
    queryObject.page_id !== '' &&
    useRoute().query.__page_id !== queryObject.page_id
  ) {
    router.replace({
      path: `/redirect` + useRoute().path,
      query: useRoute().query
    });
  }
  queryObject.page_id = useRoute().query.__page_id?.toString();
});

</script>
<style scoped>
:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-collapse-item) {
  margin-bottom: 10px;
}

:deep(.el-collapse-item__arrow) {
  margin-left: 8px;
}

:deep(.el-collapse-item__header) {
  background-color: #ebeef5;
  padding-left: 10px;
  font-weight: bold;
  border: 1px solid #ebeef5;
}

:deep(.el-collapse-item__wrap) {
  padding-top: 20px;
  padding-right: 20px;
  padding-left: 20px;
  border: 1px solid #ebeef5;
}
</style>
