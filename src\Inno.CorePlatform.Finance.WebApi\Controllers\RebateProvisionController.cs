using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 返利计提接口
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class RebateProvisionController : ControllerBase
    {
        private readonly IRebateProvisionQueryService _rebateProvisionQueryService;

        public RebateProvisionController(IRebateProvisionQueryService rebateProvisionQueryService)
        {
            _rebateProvisionQueryService = rebateProvisionQueryService;
        }

        /// <summary>
        /// 获取未完成的返利计提单据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetNotFinishBill")]
        public async Task<BaseResponseData<List<NotFinishBillOutput>>> GetNotFinishBill([FromBody] NotFinishBillQueryInput input)
        {
            try
            {
                var result = await _rebateProvisionQueryService.GetNotFinishBillAsync(input);
                return BaseResponseData<List<NotFinishBillOutput>>.Success(result);
            }
            catch (Exception ex)
            {
                return BaseResponseData<List<NotFinishBillOutput>>.Failed(500, ex.Message);
            }
        }
    }
}
