import request from '@/utils/request';

//经销入库导入明细
export function ImportDetail(data: any) {
    return request({
      url: '/api/CustomizeInvoice/ExportDetails',
      method: 'post',
      data
    });
  }
  //下载错误报告
export function GetTempFileCode(tempFileId: string) {
    return request({
      url: window.gatewayUrl + 'api/FileDownload/code?fileId=' + tempFileId,
      method: 'get'
    });
  }

//批量开票
export function ExportBatchInvoicing(data: any) {
    return request({
      url: '/api/CustomizeInvoice/ExportBatchInvoicing',
      method: 'post',
      data
    });
    
  }

  //批量开票
export function ExportBatchInvoiceDetail(data: any) {
  return request({
    url: '/api/CustomizeInvoice/ExportBatchInvoiceDetail',
    method: 'post',
    data
  });
  
}
  //批量开票
  export function CheckWDTMerge(data: any) {
    return request({
      url: '/api/CustomizeInvoice/CheckWDTMerge',
      method: 'post',
      data
    });
    
  }