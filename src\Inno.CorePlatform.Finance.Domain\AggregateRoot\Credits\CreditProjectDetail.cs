﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits
{
    public class CreditProjectDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public Guid DebtId { get; set; }

        public virtual Credit Credit { get; set; }

        public Guid ProjectId { get; set; }

        public string ProjectName { get; set; } = "";

        public string ProjectCode { get; set; } = "";

        public decimal Amount { get; set; }
    }
}
