<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-debtQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>核心平台暂存出库对账</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="keywordSearch" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <el-row>
        <el-col :span="12">
          <el-descriptions class="margin-top" style="margin-right: 5px" :column="3" :size="size" border>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">公司:</div>
              </template>
              <el-tooltip class="box-item" effect="dark" placement="top">{{crud.data.coreSysData?.companyName}}</el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">核心总金额:</div>
              </template>
              <el-tooltip class="box-item" effect="dark" content="1221" placement="top">
                <div class="desc-text-overflow">{{crud.data.coreSysData?.totalAmount}}</div>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">金蝶总金额:</div>
              </template>
              <el-tooltip class="box-item" effect="dark" content="1221" placement="top">
                <div class="desc-text-overflow">{{crud.data.kingdeeData?.totalAmount}}</div>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      <inno-split-pane split="vertical">
        <template #paneL>
        <inno-split-pane split="vertical">
          <template #paneL>
          <inno-crud-operation :crud="crudAgent" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="setDetailTab" class="demo-tabs">
                <el-tab-pane :label="`核心平台`" name="-1"></el-tab-pane>
              </el-tabs>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            :data="crud.data?.coreSysData?.codeList"
            stripe
            fit
            border
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column label="单号" property="code" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="amount" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.amount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="日期" property="dateStr" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.dateStr }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            共 {{ crud.data?.coreSysData?.codeList.length }} 条
            <div class="flex-1" />
          </div>
          </template>
          <template #paneR>
            <inno-crud-operation :crud="crudAgent" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
              <template #opts-left>
                <el-tabs v-model="setDetailTab" class="demo-tabs">
                  <el-tab-pane :label="`金蝶信息`" name="-1"></el-tab-pane>
                </el-tabs>
              </template>
            </inno-crud-operation>
            <el-table
              ref="tableItem"
              v-inno-loading="crud.loading"
              class="auto-layout-table"
              highlight-current-row
              
              :data="crud.data?.kingdeeData?.codeList"
              stripe
              fit
              border
              :row-class-name="crud.tableRowClassName"
              @sort-change="crud.sortChange"
              @selection-change="crud.selectionChangeHandler"
            >
              <el-table-column label="单号" property="code" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="金额" property="amount" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.amount }}</inno-button-copy>
                </template>
              </el-table-column>
              <el-table-column label="日期" property="dateStr" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row.financeDate }}</inno-button-copy>
                </template>
              </el-table-column>
            </el-table>
            <div class="app-page-footer background">
              共 {{ crud.data?.kingdeeData?.codeList.length }} 条
              <div class="flex-1" />
            </div>
          </template>
        </inno-split-pane>
        </template>
        <template #paneR>
          <inno-split-pane split="vertical">
            <template #paneL>
              <inno-crud-operation :crud="crudAgent" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="setDetailTab" class="demo-tabs">
                <el-tab-pane :label="`核心平台多出`" name="-1"></el-tab-pane>
              </el-tabs>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            :data="crud.data?.coreSysMore"
            stripe
            fit
            border
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column label="单号" property="code" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            共 {{ crud.data?.coreSysMore?.length }} 条
            <div class="flex-1" />
          </div>
            </template>
            <template #paneR>
              <inno-crud-operation :crud="crudAgent" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="setDetailTab" class="demo-tabs">
                <el-tab-pane :label="`金蝶多出`" name="-1"></el-tab-pane>
              </el-tabs>
            </template>
          </inno-crud-operation>
            <el-table
              ref="tableItem"
              v-inno-loading="crud.loading"
              class="auto-layout-table"
              highlight-current-row
              
              :data="crud.data?.kingdeeMore"
              stripe
              fit
              border
              :row-class-name="crud.tableRowClassName"
              @sort-change="crud.sortChange"
              @selection-change="crud.selectionChangeHandler"
            >
              <el-table-column label="单号" property="code" min-width="200" show-overflow-tooltip>
                <template #default="scope">
                  <inno-button-copy :link="false">{{ scope.row }}</inno-button-copy>
                </template>
              </el-table-column>
            </el-table>
            <div class="app-page-footer background">
              共 {{ crud.data?.kingdeeMore?.length }} 条
              <div class="flex-1" />
            </div>
            </template>
          </inno-split-pane>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElTable, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import AddInputbillDetail from './component/AddInputbillDetail.vue';
import EditInputbillDetail from './component/EditInputbillDetail.vue';
import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
import {
  SubmitInputBill,
  DeleteBillSbumit,
  EliminatingErrors
} from '@/api/financeapi';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import request from '@/utils/request';
onMounted(() => {
  crud.toQuery()
})
onActivated(() => {
  crud.toQuery()
})
const setDetailTab = ref('-1');
const crud = CRUD(
  {
    title: '出库签收',
    url: '/api/Values/CheckDataWithKingdee',
    idField: 'id1',
    userNames: ['createdBy'],
    method: 'post',
    query: {
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    hooks: {},
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: 'tableItem'
  }
);
const queryList = computed(() => {
  return [
    {
      key: 'companyId',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      placeholder: '公司搜索',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'startTime',
      endDate: 'endTime',
      label: '日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    }
  ];
});
</script>
