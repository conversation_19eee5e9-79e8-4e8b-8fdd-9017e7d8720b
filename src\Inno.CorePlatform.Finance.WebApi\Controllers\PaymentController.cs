﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaymentController : ControllerBase
    {
        private ILogger<PaymentController> _logger;
        private IPaymentQueryService _paymentQueryService;
        private IKingdeeApiClient _kingdeeApiClient;
        private IExchangeRateService _exchangeRateService;
        public PaymentController(
            ILogger<PaymentController> logger,
            IPaymentQueryService paymentQueryService,
            IKingdeeApiClient kingdeeApiClient,
            IExchangeRateService exchangeRateService)
        {
            this._logger = logger;
            this._paymentQueryService = paymentQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._exchangeRateService = exchangeRateService;
        }

        /// <summary>
        /// 根据采购订单获取付款单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getbypurchasecodes")]
        public async Task<BaseResponseData<List<PaymentQueryOutput>>> GetByPurchaseCodes(PaymentWebApiInput input)
        {
            var ret = BaseResponseData<List<PaymentQueryOutput>>.Success("操作成功！");
            ret.Data = await _paymentQueryService.GetByPurchaseCodes(input);
            return ret;
        }

        /// <summary>
        /// 获取付款单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<BaseResponseData<BasePagedData<PaymentQueryListOutput>>> GetList([FromBody] PaymentQueryInput query)
        {
            try
            {
                var (list, count) = await _paymentQueryService.GetListAsync(query);
                return new BaseResponseData<BasePagedData<PaymentQueryListOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new BasePagedData<PaymentQueryListOutput>
                    {
                        Total = count,
                        List = list,
                    },
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取付款单列表  (通过采购单号)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListByPurchaseCode")]
        public async Task<BaseResponseData<BasePagedData<PaymentQueryListOutput>>> GetListByPurchaseCode([FromBody] PaymentQueryInput query)
        {
            try
            {
                var (list, count) = await _paymentQueryService.GetListByPurchaseCodeAsync(query);
                return new BaseResponseData<BasePagedData<PaymentQueryListOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new BasePagedData<PaymentQueryListOutput>
                    {
                        Total = count,
                        List = list,
                    },
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 导出付款计划
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("downloadPaymentPlan")]
        public async Task<ResponseData<PaymentDetailOutput>> DownloadPaymentPlan(QueryById query)
        {
            var list = await _paymentQueryService.GetPaymentPlan(query);
            return new ResponseData<PaymentDetailOutput>
            {
                Code = 200,
                Data = new Data<PaymentDetailOutput>
                {
                    List = list,
                    Total = list.Count(),
                }
            };
        }

        /// <summary>
        /// 导出付款计划
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("downloadPayment")]
        public async Task<ResponseData<PaymentQueryListOutput>> DownloadPayment(PaymentQueryInput query)
        {
            var (list, count) = await _paymentQueryService.DownloadPayment(query);
            return new ResponseData<PaymentQueryListOutput>
            {
                Code = 200,
                Data = new Data<PaymentQueryListOutput>
                {
                    List = list,
                    Total = count,
                }
            };
        }

        /// <summary>
        /// 通过采购单号和项目Id获取付款单信息（通道项目用）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getByPurchaseAndProject")]
        public async Task<BaseResponseData<List<PaymentByPurchaseAndProjectOutput>>> GetByPurchaseAndProject(PaymentByPurchaseAndProjectInput input)
        {
            try
            {
                var result = await _paymentQueryService.GetPaymentByPurchaseAndProject(input);
                var ret = BaseResponseData<List<PaymentByPurchaseAndProjectOutput>>.Success("操作成功！");
                ret.Data = result;
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<List<PaymentByPurchaseAndProjectOutput>>.Failed((int)CodeStatusEnum.Failed, $"查询失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 导出付款清单付款信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("downloadPaymentInfo")]
        public async Task<ResponseData<PaymentPayInfoListOutput>> DownloadPaymentInfoAsync(PaymentQueryInput query)
        {
            var result = await _paymentQueryService.DownloadPaymentInfoAsync(query);
            return new ResponseData<PaymentPayInfoListOutput>
            {
                Code = 200,
                Data = new Data<PaymentPayInfoListOutput>
                {
                    List = result.Data,
                    Total = result.Total,
                }
            };
        }
    }
}
