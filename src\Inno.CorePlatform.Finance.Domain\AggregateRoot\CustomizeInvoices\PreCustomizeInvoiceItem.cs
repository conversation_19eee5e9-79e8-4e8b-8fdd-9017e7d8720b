﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    public class PreCustomizeInvoiceItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(200)]
        public string Code { get; set; }
        /// <summary>
        /// 单号日期
        /// </summary>
        [Comment("日期")]
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        [MaxLength(200)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        [MaxLength(200)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public PreCustomizeInvoiceItemStatusEnum? Status { get; set; }

    }
}

