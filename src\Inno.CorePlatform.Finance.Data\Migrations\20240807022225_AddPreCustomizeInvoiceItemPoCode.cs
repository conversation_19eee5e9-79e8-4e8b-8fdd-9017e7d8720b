﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddPreCustomizeInvoiceItemPoCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "BillDate",
                table: "PreCustomizeInvoiceItem",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                comment: "日期");

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "PreCustomizeInvoiceItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                comment: "单号");

            migrationBuilder.AddColumn<int>(
                name: "Classify",
                table: "CustomizeInvoiceClassify",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RelationCode",
                table: "CustomizeInvoiceClassify",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BillDate",
                table: "PreCustomizeInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "PreCustomizeInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Classify",
                table: "CustomizeInvoiceClassify");

            migrationBuilder.DropColumn(
                name: "RelationCode",
                table: "CustomizeInvoiceClassify");
        }
    }
}
