﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCreditDetailProjectIdAndServiceId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ProjectId",
                table: "CreditDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "项目Id");

            migrationBuilder.AddColumn<Guid>(
                name: "ServiceId",
                table: "CreditDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "业务单元Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProjectId",
                table: "CreditDetail");

            migrationBuilder.DropColumn(
                name: "ServiceId",
                table: "CreditDetail");
        }
    }
}
