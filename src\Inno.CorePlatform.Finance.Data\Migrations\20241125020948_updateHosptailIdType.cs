﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class updateHosptailIdType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "RecognizeReceiveDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true,
                oldComment: "终端医院Id");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "Invoice",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "Credit",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true,
                oldComment: "终端医院Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "HospitalId",
                table: "RecognizeReceiveDetail",
                type: "uniqueidentifier",
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "终端医院Id");

            migrationBuilder.AlterColumn<Guid>(
                name: "HospitalId",
                table: "Invoice",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "HospitalId",
                table: "Credit",
                type: "uniqueidentifier",
                nullable: true,
                comment: "终端医院Id",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "终端医院Id");
        }
    }
}
