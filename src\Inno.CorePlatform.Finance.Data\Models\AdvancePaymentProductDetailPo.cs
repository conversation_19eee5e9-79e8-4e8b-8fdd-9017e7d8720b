﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 提前付款垫资货品明细
    /// </summary>    
    [Table("AdvancePaymentProductDetail")]
    public class AdvancePaymentProductDetailPo : BasePo
    { 
        /// <summary>
        /// 提前付款垫资Id
        /// </summary>
        public Guid AdvancePaymentItemId { get; set; }

        /// <summary>
        /// 提前付款垫资
        /// </summary>
        [ForeignKey("AdvancePaymentItemId")]
        public virtual AdvancePaymentItemPo AdvancePaymentItem { get; set; }

        /// <summary>
        /// 业务单据号
        /// </summary>
        [MaxLength(200)]
        public string? BusinessBillNo { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [MaxLength(200)]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        [MaxLength(500)]
        public string? ProductName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位毛利
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Profit { get; set; }

        /// <summary>
        /// 毛利小计
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? SubTotal { get; set; }

        /// <summary>
        /// 原始成本
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalCost { get; set; }

        /// <summary>
        /// 原始售价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalSalePrice { get; set; }
         
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 采购明细id
        /// </summary>
        public Guid? PurchaseDetailId { get; set; }

        /// <summary>
        /// 订单id
        /// </summary>
        public Guid? PurchaseOrderId { get; set; }
    }
}
