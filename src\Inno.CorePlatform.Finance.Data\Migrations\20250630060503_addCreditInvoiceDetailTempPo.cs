﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addCreditInvoiceDetailTempPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CreditInvoiceDetailTemp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreditDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "应收明细Id"),
                    OriginDetailId = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "原明细Id"),
                    ProductNo = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "货号"),
                    OriginalPackSpec = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "包装规格"),
                    ProductName = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "品名"),
                    OriginProductName = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "原始品名（不变）"),
                    PackUnit = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "单位"),
                    OriginPackUnit = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "原始单位"),
                    Specification = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "规格"),
                    Quantity = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "数量"),
                    Price = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "单价"),
                    OriginalPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true, comment: "原始单价"),
                    OriginalCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "原始成本"),
                    Value = table.Column<decimal>(type: "decimal(18,4)", nullable: false, comment: "金额"),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "税率"),
                    TaxTypeNo = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "税收分类编码"),
                    CreditBillCode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "应收单号"),
                    RelateCode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "关联单号"),
                    OrderNo = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "关联单号"),
                    CustomerId = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "客户Id"),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "客户名称"),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "公司"),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "公司名称"),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "公司Code"),
                    OriginalId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "OriginalId"),
                    OriginSpecification = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "原始规格型号"),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "货号Id"),
                    AgentId = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "供应商Id"),
                    IFHighValue = table.Column<int>(type: "int", nullable: true),
                    NoInvoiceAmount = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    BatchId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SaleDetailId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ServiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    PriceSource = table.Column<int>(type: "int", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditInvoiceDetailTemp", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CreditInvoiceDetailTemp_CreditDetail_CreditDetailId",
                        column: x => x.CreditDetailId,
                        principalTable: "CreditDetail",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "InventoryRecord",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InventoryItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "盘点单ID"),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "公司ID"),
                    SysMonth = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "系统月度"),
                    ActionType = table.Column<int>(type: "int", nullable: false, comment: "盘点动作类型"),
                    ActionName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "动作名称"),
                    InventoryCode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "盘点单号"),
                    Status = table.Column<int>(type: "int", nullable: false, comment: "状态：0-待处理，1-处理中，2-已完成，99-失败"),
                    RetryCount = table.Column<int>(type: "int", nullable: false, comment: "重试次数"),
                    ErrorMessage = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "错误信息"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryRecord", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CreditInvoiceDetailTemp_CreditDetailId",
                table: "CreditInvoiceDetailTemp",
                column: "CreditDetailId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CreditInvoiceDetailTemp");

            migrationBuilder.DropTable(
                name: "InventoryRecord");
        }
    }
}
