﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addIFHighValue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "IFHighValue",
                table: "CustomizeInvoiceSubDetail",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "IFHighValue",
                table: "CustomizeInvoiceDetail",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IFHighValue",
                table: "CustomizeInvoiceSubDetail");

            migrationBuilder.DropColumn(
                name: "IFHighValue",
                table: "CustomizeInvoiceDetail");
        }
    }
}
