﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddMigrationRecords : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MigrationRecords",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OriginBillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "原始单号"),
                    MiddleBillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "中间单号"),
                    NewBillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "新单号"),
                    DataType = table.Column<int>(type: "int", nullable: false, comment: "数据类型"),
                    Remark = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "备注"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MigrationRecords", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MigrationRecords");
        }
    }
}
