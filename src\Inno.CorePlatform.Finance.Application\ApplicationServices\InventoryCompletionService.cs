using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.Extensions.Logging;
using System;
using System.Globalization;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices
{
    /// <summary>
    /// 盘点完成服务 - 负责盘点完成后的业务逻辑编排
    /// </summary>
    public class InventoryCompletionService : IInventoryCompletionService
    {
        private readonly ILogger<InventoryCompletionService> _logger;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IAutoInventoryService _autoInventoryService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public InventoryCompletionService(
            ILogger<InventoryCompletionService> logger,
            IInventoryMgmAppService inventoryMgmAppService,
            IBDSApiClient bDSApiClient,
            IAutoInventoryService autoInventoryService)
        {
            _logger = logger;
            _inventoryMgmAppService = inventoryMgmAppService;
            _bDSApiClient = bDSApiClient;
            _autoInventoryService = autoInventoryService;
        }

        /// <summary>
        /// 检查并自动完成盘点（如果满足条件）
        /// </summary>
        /// <param name="inventory">盘点记录</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        public async Task CheckAndAutoCompleteInventoryAsync(InventoryDTO inventory, string sysMonth)
        {
            try
            {
                // 1. 检查所有盘点单号是否都已完成（使用与CheckInventoryCompletionAsync相同的逻辑）
                var (isCompleted, checkMessage, missingFields) = await _autoInventoryService.CheckInventoryCompletionAsync(inventory.CompanyId, sysMonth);
                if (!isCompleted)
                {
                    _logger.LogInformation("盘点单 {InventoryId} 还有未完成的盘点单号，不执行自动完成: {Message}", inventory.Id, checkMessage);
                    return;
                }

                // 2. 检查当前日期是否是系统盘点月度的下个月
                if (!IsCurrentDateInNextMonth(sysMonth))
                {
                    _logger.LogInformation("当前日期不在系统盘点月度 {SysMonth} 的下个月，不执行自动完成", sysMonth);
                    return;
                }

                // 3. 满足条件，自动完成盘点
                _logger.LogInformation("满足自动完成条件，开始自动完成盘点 - 盘点单ID: {InventoryId}, 系统月度: {SysMonth}",
                    inventory.Id, sysMonth);

                // 完成盘点并处理后续操作（更新状态、系统月度、发送广播）
                await CompleteInventoryAndHandlePostActionsAsync(inventory, sysMonth, "系统自动完成");

                _logger.LogInformation("自动完成盘点成功 - 盘点单ID: {InventoryId}", inventory.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查并自动完成盘点时发生异常 - 盘点单ID: {InventoryId}", inventory.Id);
            }
        }

        /// <summary>
        /// 完成盘点并处理后续操作（更新状态、系统月度、发送广播）
        /// </summary>
        /// <param name="inventory">盘点记录</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="completeReason">完成原因</param>
        /// <returns></returns>
        public async Task CompleteInventoryAndHandlePostActionsAsync(InventoryDTO inventory, string sysMonth, string completeReason)
        {
            try
            {
                _logger.LogInformation("开始完成盘点并处理后续操作 - 盘点单ID: {InventoryId}, 公司: {CompanyId}, 月度: {SysMonth}, 原因: {Reason}",
                    inventory.Id, inventory.CompanyId, sysMonth, completeReason);

                // 1. 更新盘点状态为已完成
                await _inventoryMgmAppService.UpdateInventoryStatus(inventory.Id, 99, completeReason);
                _logger.LogInformation("盘点状态更新为已完成 - 盘点单ID: {InventoryId}", inventory.Id);

                // 2. 发送盘点完成广播（系统月度更新由批量处理统一处理）
                await SendInventoryCompleteNotificationAsync(inventory.CompanyId, sysMonth, inventory.Id);

                _logger.LogInformation("完成盘点并处理后续操作成功 - 盘点单ID: {InventoryId}", inventory.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成盘点并处理后续操作失败 - 盘点单ID: {InventoryId}, 错误: {Message}",
                    inventory.Id, ex.Message);
                throw;
            }
        }



        /// <summary>
        /// 发送盘点完成通知广播
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="inventoryId">盘点单ID</param>
        /// <returns></returns>
        public async Task SendInventoryCompleteNotificationAsync(Guid companyId, string sysMonth, Guid inventoryId)
        {
            try
            {
                // 这里可以发送Dapr事件或其他通知机制
                _logger.LogInformation("发送盘点完成通知 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单ID: {InventoryId}",
                    companyId, sysMonth, inventoryId);

                // TODO: 根据业务需求实现具体的通知逻辑
                // 例如：发送到消息队列、通知其他系统等
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送盘点完成通知失败 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);
                // 通知失败不应该影响主流程，所以这里不抛出异常
            }
        }





        /// <summary>
        /// 检查当前日期是否在系统盘点月度的下个月
        /// </summary>
        /// <param name="sysMonth">系统月度 (格式: yyyy-MM)</param>
        /// <returns></returns>
        private static bool IsCurrentDateInNextMonth(string sysMonth)
        {
            try
            {
                // 解析系统月度
                if (!DateTime.TryParseExact(sysMonth + "-01", "yyyy-MM-dd", null, DateTimeStyles.None, out var sysMonthDate))
                {
                    return false;
                }

                // 计算下个月
                var nextMonth = sysMonthDate.AddMonths(1);
                var currentDate = DateTime.Now.Date;

                // 检查当前日期是否在下个月
                return currentDate.Year == nextMonth.Year && currentDate.Month == nextMonth.Month;
            }
            catch
            {
                return false;
            }
        }
    }
}
