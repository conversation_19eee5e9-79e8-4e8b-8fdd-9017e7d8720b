﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 合并进项发票明细
    /// </summary>
    [Table("MergeInputBillDetail")]
    public class MergeInputBillDetailPo : BasePo
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        [ForeignKey("MergeInputBillId")]
        public virtual MergeInputBillPo MergeInputBill { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [MaxLength(500)]
        public string ProductName { get; set; }

        /// <summary>
        /// 品名Id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [MaxLength(500)]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [MaxLength(500)]
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [MaxLength(500)]
        public string? Model { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal NoTaxCost { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal NoTaxAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 含税金额（计算属性：不含税 + 税额）
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }
    }
}
