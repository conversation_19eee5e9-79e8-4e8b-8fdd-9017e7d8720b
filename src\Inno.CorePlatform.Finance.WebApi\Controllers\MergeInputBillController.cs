using Dapr;
using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 合并进项发票WebAPI控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class MergeInputBillController : BaseApiController<MergeInputBillController>
    {
        private readonly IMergeInputBillAppService _mergeInputBillAppService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="daprClient">Dapr客户端</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="edaFailureMsgClient">EDA失败消息客户端</param>
        /// <param name="mergeInputBillAppService">合并进项发票应用服务</param>
        public MergeInputBillController(
            DaprClient daprClient, 
            ILogger<MergeInputBillController> logger, 
            IEDAFailureMsgClient edaFailureMsgClient,
            IMergeInputBillAppService mergeInputBillAppService)
            : base(daprClient, logger, edaFailureMsgClient)
        {
            _mergeInputBillAppService = mergeInputBillAppService;
        }

        /// <summary>
        /// 处理匹配任务
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>处理结果</returns>
        [HttpPost("processMatchTask")]
        [Topic("pubsub-default", "finance-match-task")]
        public async Task<ActionResult> ProcessMatchTask(StartAsyncMatchRequest request)
        {
            try
            {
                _logger.LogInformation("接收到匹配任务消息, MergeInputBillId: {MergeInputBillId}, IsReload: {IsReload}",
                    request.MergeInputBillId, request.IsReload);
                await _mergeInputBillAppService.ProcessMatchTask(request);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理匹配任务失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);

                // 处理匹配任务失败时，调用 SendEDAFailureMsg 方法进行重试
                try
                {
                    var inputJson = JsonConvert.SerializeObject(request);
                    await SendEDAFailureMsg(inputJson, "finance-match-task", ex, "/api/MergeInputBill/processMatchTask");
                    _logger.LogInformation("匹配任务处理失败消息已发送到重试队列, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                }
                catch (Exception retryEx)
                {
                    _logger.LogError(retryEx, "发送匹配任务处理失败消息到重试队列失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}", 
                        request.MergeInputBillId, retryEx.Message);
                }

                return StatusCode(500, ex.Message);
            }
        }
    }
}
