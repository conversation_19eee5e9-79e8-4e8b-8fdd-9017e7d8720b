﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance
{
    public class AdvanceBusinessDetail: EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        
        public Guid AdvanceBusinessApplyId { get; set; }

        public int AccountPeriod { get; set; }

        public int ReceivePeriod { get; set; }

        public decimal Discount { get; set; }

        public decimal BaseDiscount { get; set; }

        public decimal SCFDiscount { get; set; }

        public decimal SPDDiscount { get; set; }

        public Guid CreditId { get; set; }

        public Guid DebtDetailId { get; set; }

        public string CreditCode { get; set; }

        public DateTime CreditDate { get; set; }

        public decimal CreditValue { get; set; }

        public decimal? SalesTaxRate { get; set; }

        public DateTime? InvoiceDate { get; set; }

        public string DebtCode { get; set; }

        public DateTime? DebtDate { get; set; }

        public decimal DebtValue { get; set; }

        public string ReceiveCode { get; set; }      

        public DateTime? ReceiveDate { get; set; }

        public DateTime? ExpectReceiveDate { get; set; }

        public string PaymentCode { get; set; }

        public DateTime? PaymentDate { get; set; }

        public DateTime? ExpectPaymentDate { get; set; }

        public decimal? ADFDiscount { get; set; }

    }
}
