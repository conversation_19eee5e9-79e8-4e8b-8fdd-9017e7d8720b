﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddAdvancePaymentItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdvancePaymentItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    BillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "单号"),
                    BillDate = table.Column<DateTime>(type: "date", nullable: true, comment: "单据日期"),
                    BusinessDeptFullPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptFullName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessDeptId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProjectCode = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "项目单号"),
                    ProjectName = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "项目名称"),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "项目Id"),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StartTime = table.Column<DateTime>(type: "date", nullable: true),
                    EndTime = table.Column<DateTime>(type: "date", nullable: true),
                    Day = table.Column<int>(type: "int", nullable: true),
                    MonthRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    ConfirmPaymentDateMode = table.Column<int>(type: "int", nullable: true),
                    PaySupplierGoodsDay = table.Column<int>(type: "int", nullable: true),
                    AdvanceCreditAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AdvanceCreditTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvancePaymentItem", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AdvancePaymentCreditDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AdvancePaymentItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreditBillNo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    InvoiceNo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "发票号"),
                    ReturnDays = table.Column<int>(type: "int", nullable: true),
                    EstimateReturnDays = table.Column<int>(type: "int", nullable: true),
                    ActualPaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdvancePaymentDays = table.Column<int>(type: "int", nullable: true),
                    MonthRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    FinanceDiscount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AgentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AgentName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AdvanceCreditAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AdvanceCreditTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvancePaymentCreditDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdvancePaymentCreditDetail_AdvancePaymentItem_AdvancePaymentItemId",
                        column: x => x.AdvancePaymentItemId,
                        principalTable: "AdvancePaymentItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AdvancePaymentProductDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AdvancePaymentItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BusinessBillNo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ProductNo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ProductName = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Quantity = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Profit = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    OriginalCost = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    OriginalSalePrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvancePaymentProductDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdvancePaymentProductDetail_AdvancePaymentItem_AdvancePaymentItemId",
                        column: x => x.AdvancePaymentItemId,
                        principalTable: "AdvancePaymentItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdvancePaymentCreditDetail_AdvancePaymentItemId",
                table: "AdvancePaymentCreditDetail",
                column: "AdvancePaymentItemId");

            migrationBuilder.CreateIndex(
                name: "IX_AdvancePaymentProductDetail_AdvancePaymentItemId",
                table: "AdvancePaymentProductDetail",
                column: "AdvancePaymentItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdvancePaymentCreditDetail");

            migrationBuilder.DropTable(
                name: "AdvancePaymentProductDetail");

            migrationBuilder.DropTable(
                name: "AdvancePaymentItem");
        }
    }
}
