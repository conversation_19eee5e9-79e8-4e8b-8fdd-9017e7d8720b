<template>
  <el-dialog
    v-model="detailImportModel.showDialog"
    title="导入"
    width="50%"
    draggable
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <span>
      <el-steps :active="detailImportModel.importStepActive" finish-status="success">
        <el-step title="选择文件" />
        <el-step title="导入数据" />
        <el-step title="导入结果" />
      </el-steps>
      <div v-inno-loading="loading" style="width: 100%; height: 200px" element-loading-text="导入中...">
        <div
          v-if="detailImportModel.importStepActive === 1"
          style="text-align: center; padding-top: 90px"
        >
          <inno-file-uploader
            v-model="detailImportModel.uploadTempFileId"
            :fileMode="'temp'"
            :appId="'pua'"
            :bizType="'pua'"
            list-type="text"
            :limit="1"
            :limitType="['xls', 'xlsx']"
          >
            <el-button type="primary">选择文件</el-button>
          </inno-file-uploader>
          <el-button type="default" @click="downloadTemplate">模板下载</el-button>
        </div>
        <div v-if="detailImportModel.importStepActive === 2"></div>
        <div v-if="detailImportModel.importStepActive === 3" style="padding-top: 50px">
          <el-descriptions title="" direction="vertical" :column="4" border>
            <el-descriptions-item :align="'center'" :label-align="'center'" label="总计（条）">{{
              importResult.total
            }}</el-descriptions-item>
            <el-descriptions-item :align="'center'" :label-align="'center'" label="成功（条）">{{
              importResult.successNumber
            }}</el-descriptions-item>
            <el-descriptions-item :align="'center'" :label-align="'center'" label="失败（条）">
              <el-text type="danger">{{ importResult.failNumber }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item
              :width="120"
              :align="'center'"
              :label-align="'center'"
              label="操作"
            >
              <el-button
                v-if="importResult.failReportFileId && importResult.failReportFileId !== ''"
                type="primary"
                @click="downloadErrReport"
                >下载错误报告</el-button
              >
            </el-descriptions-item>
          </el-descriptions>
          <el-text type="info"><strong>注：下载错误报告，将错误的修正后重新导入。</strong></el-text>
        </div>
      </div>
    </span>
    <template #footer>
      <div style="width: 100%; text-align: center">
        <span class="dialog-footer">
          <el-button
            @click="
              () => {
                closeDialog();
              }
            "
            >取消</el-button
          >
          <el-button
            v-if="detailImportModel.importStepActive === 1"
            type="primary"
            :disabled="!detailImportModel.uploadTempFileId"
            @click="confirmImport"
          >
            导入
          </el-button>
        </span>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, inject, ref } from 'vue';
import { downloadFile } from '@inno/inno-mc-vue3/lib/utils/util';
// import {
//   ImportDetail,
//   GetTempFileCode
// } from '@/api/bffApi';
import { ImportDetail, GetTempFileCode } from '../apis/api';
import { cdnHost,cdnPrefix,cdnUrl,getStaticUrl,getTemplateUrl,ENV} from '@inno/inno-mc-vue3/lib/config';

const detailImportModel = ref({
  showDialog: false,
  importStepActive: 1,
  templateFileUrl:
    'https://static.innostic.com/template/StoreInApply/returntemp_storeinapply_detail_import_tempate_v3.xlsx',
  uploadTempFileId: '',
  importDetailInput: {
    fileId: ''
  }
});
const importResult = ref({
  total: 0,
  successNumber: 0,
  failNumber: 0,
  failReportFileId: ''
});

const controlModel = ref({
  isAllow: false,
  detailIsAllow: false
});

const emit = defineEmits(['onImportSuccess']);
const loading = ref(false);

const downloadTemplate = () => {
  // downloadFile(
  //   `${detailImportModel.value.templateFileUrl}?v=${new Date().getTime()}`,
  //   '暂存调回入库明细导入模板',
  //   'xlsx'
  // );
  let url = getTemplateUrl('fam/付款信息导入模板.xlsx',true) 
  downloadFile(url, '付款信息导入模板', 'xlsx');
};
const downloadErrReport = async () => {
  await downloadFailReportFile();
};
const confirmImport = async () => {
  loading.value = true;
  try {
    let result = await excuteImport();
    if (result) {
      emit('onImportSuccess', result);
      console.log('import-result========', result);
      controlModel.value.detailIsAllow = true;
      controlModel.value.isAllow = true;
      loading.value = false;
    } else {
      loading.value = false;
      controlModel.value.detailIsAllow = false;
      controlModel.value.isAllow = false;
    }
  } catch (error) {
    loading.value = false;
    detailImportModel.value.importStepActive = 1;
  }
};
const closeDialog = () => {
  detailImportModel.value.showDialog = false;
  detailImportModel.value.importStepActive = 1;
  loading.value = false;
  detailImportModel.value.uploadTempFileId = '';
};
const downloadFailReportFile = async () => {
  if (importResult.value.failReportFileId && importResult.value.failReportFileId !== '') {
    const resCode = await GetTempFileCode(importResult.value.failReportFileId);
    if (resCode) {
      const code = resCode.data.code;
      const fileUrl = `${window.gatewayUrl}api/FileDownload/temp/direct?fileId=${importResult.value.failReportFileId}&code=${code}`;
      window.open(fileUrl);
    } else {
      ElMessage.error(resCode.data.message);
    }
  } else {
    ElMessage.warning('错误报告文件id为空。');
  }
};
const excuteImport = async () => {
  if (detailImportModel.value.importDetailInput) {
    detailImportModel.value.importStepActive = 2;
    detailImportModel.value.importDetailInput.fileId = detailImportModel.value.uploadTempFileId;
    const res = await ImportDetail(detailImportModel.value.importDetailInput);
    
    if (res && res.data.code === 200) {
      detailImportModel.value.importStepActive = 3;
      importResult.value.failNumber = res.data.data.failNumber;
      importResult.value.successNumber = res.data.data.successNumber;
      importResult.value.total = res.data.data.total;
      importResult.value.failReportFileId = res.data.data.failReportFileId; 
      ElMessage.success('导入完成。');
      return true;
      
    } else {
      detailImportModel.value.importStepActive = 1;
      ElMessage.error(res.data.message);
      return undefined;
    }
  } else {
    ElMessage.warning('请设置导入条件。');
    return undefined;
  }
};
const setImportCondition = (obj: any) => {
  console.log('这是传过来的值============', obj);
  detailImportModel.value.importDetailInput = obj;
};
defineExpose({
  detailImportModel,
  importResult,
  controlModel,
  setImportCondition
});
</script>
<style scoped>
:deep(.el-loading-spinner .el-loading-text) {
  color: #67c23a;
}

:deep(.el-loading-spinner .path) {
  stroke: #67c23a;
}
</style>
