﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 合并进项发票提交明细
    /// </summary>
    [Table("MergeInputBillSubmitDetail")]
    public class MergeInputBillSubmitDetailPo : BasePo
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票
        /// </summary>
        [ForeignKey("MergeInputBillId")]
        public virtual MergeInputBillPo MergeInputBill { get; set; }

        /// <summary>
        /// 合并进项发票明细ID
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [MaxLength(500)]
        public string ProductName { get; set; }

        /// <summary>
        /// 产品名称Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [MaxLength(500)]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名Id
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [MaxLength(500)]
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [MaxLength(500)]
        public string? Model { get; set; }

        /// <summary>
        /// 业务单号
        /// </summary>
        [MaxLength(500)]
        public string? BussinessItemCode { get; set; }

        /// <summary>
        /// 业务时间
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime? BussinessDate { get; set; }

        /// <summary>
        /// 累计数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 已入票数
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal ReceivedNumber { get; set; }

        /// <summary>
        /// 匹配数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal MatchQuantity { get; set; }

        /// <summary>
        /// 匹配维度
        /// </summary>
        public MatchPrecisionEnum MatchPrecision { get; set; } = MatchPrecisionEnum.ProductName;

        /// <summary>
        /// 含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,10)")]
        public decimal? NoTaxCost { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 不含税金额（计算属性：NoTaxCost * Quantity）
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? NoTaxAmount { get; set; }

        /// <summary>
        /// 含税金额（计算属性：TaxCost * Quantity）
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 匹配金额
        /// 对于普通业务类型：匹配金额 = 匹配数量 × 含税单价
        /// 对于服务费和购货修订：匹配金额 = 匹配数量（实际上是金额）
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? MatchedAmount { get; set; }

        /// <summary>
        /// 勾稽的业务类型
        /// </summary>
        public int? BusinessType { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        [MaxLength(500)]
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        [MaxLength(500)]
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        [MaxLength(500)]
        public string? ContractNo { get; set; }

        /// <summary>
        /// 匹配键，用于快速匹配
        /// </summary>
        [MaxLength(500)]
        public string? MatchKey { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        [MaxLength(500)]
        public string? ProducerName { get; set; }

        /// <summary>
        /// 厂家ID
        /// </summary>
        public Guid? ProducerId { get; set; }
    }
}
