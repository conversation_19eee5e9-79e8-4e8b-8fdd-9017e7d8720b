<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
</style>
<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>进项发票</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud">
        <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
      </inno-crud-operation>
    </div>

    <div class="app-page-body" style="padding-top: 0px">
      <inno-query-operation :crud="crud" :query-list.sync="queryList" />

      <inno-split-pane split="horizontal" :default-percent="50">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs v-model="activeNameStatus" @tab-click="search">
                <el-tab-pane :label="`全部`" name="all" />
                <el-tab-pane :label="`临时发票`" name="unSubmit" />
                <el-tab-pane :label="`已提交`" name="Submit" />
                <el-tab-pane :label="`忽略发票`" name="ignore" />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip
                :loading="submitBillLoading"
                type="primary"
                :disabled="
                  !crud.selections.length ||
                  crud.rowData.statusName === '已提交'
                "
                @click="submitBill"
              >提交</inno-button-tooltip>
              <inno-button-tooltip
                :loading="cancelBillLoading"
                type="primary"
                :disabled="
                  crud.rowData.statusName !== '已提交'
                "
                v-if= "crud.rowData.statusName === '已提交'"
                @click="cancelBill"
              >取消勾稽</inno-button-tooltip>
              <!-- v-if="crud.rowData.statusName === '临时发票'"  -->
              <inno-button-tooltip :loading="submitBillLoading" type="warning" v-if="ishf === crud.selections.length" :disabled="!crud.selections.length" @click="ignoreBill">忽略发票</inno-button-tooltip>
              <inno-button-tooltip :loading="submitBillLoading" type="warning" v-if="ishl === crud.selections.length"  :disabled="!crud.selections.length" @click="restoreBill">恢复发票</inno-button-tooltip>
              <inno-button-tooltip :loading="exportBillLoading" @click="exportBill">导出数据</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>

          <el-table
            ref="tableRef"
            v-inno-loading="crud.loading"
            highlight-current-row
            
            stripe
            border
            class="auto-layout-table"
            :data="crud.data"
            :row-class-name="crud.tableRowClassName"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column prop="invoiceNumber" label="发票号" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNumber }}</inno-button-copy>
              </template>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.InvoiceNumber" :crud="crud" :column="column" />
              </template>
            </el-table-column>
            <el-table-column prop="companName" label="公司名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.CompanName" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="agentName" label="供应商名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.AgentName" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="billTime" label="开票时间" min-width="110">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.beginCreatedTime" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{
                scope.row.billTime === null
                ? ''
                : dateFormat(scope.row.billTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="票据类型" />
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建时间" min-width="110">
              <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
            </el-table-column>
            <el-table-column prop="invoiceCode" label="发票代码" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="purchaseDutyNumber" label="购买方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="saleDutyNumber" label="销售方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="statusName" label="发票状态" />

            <el-table-column sortable prop="notaxAmount" label="金额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.notaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="taxAmount" label="税额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="amount" label="总金额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.amount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link
                  v-if="scope.row.invoiceNumber"
                  style="font-size: 12px"
                  type="primary"
                  @click.stop="
                    downloadFile(scope.row.invoiceNumber, scope.row.invoiceCode)
                  "
                >查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>

          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <el-tabs v-model="activeName" class="app-page-tabs">
            <el-tab-pane label="发票明细" name="发票明细">
              <inno-crud-operation :crud="crudDe" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>
                  <inno-button-tooltip type="primary" v-if="errbtn" @click="downloadErrInfoExcel">下载错误信息</inno-button-tooltip>
                  <inno-button-tooltip type="primary" :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    v-if="crud.rowData.statusName!=='已忽略'"
                    @click="BatchExcelImportHandler">批量导入明细</inno-button-tooltip>
                  <inno-button-tooltip type="danger" :loading="eliminatingErrorsLoading" @click="eliminatingErrors" v-if="false">一键平尾差</inno-button-tooltip>
                  <inno-button-tooltip type="primary" :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    " 
                    v-if="crud.rowData.statusName!=='已忽略'"
                    @click="ExcelImportHandler">按Excel导入单据</inno-button-tooltip>
                  <inno-button-tooltip
                    type="primary"
                    :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    v-if="crud.rowData.statusName!=='已忽略'"
                    @click="inReceiptImport"
                  >按单据导入</inno-button-tooltip>
                  <inno-button-tooltip
                    :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    v-if="crud.rowData.statusName!=='已忽略'"
                    type="primary"
                    @click="createdDialog"
                  >添加</inno-button-tooltip>
                  <!-- <inno-button-tooltip :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                  " type="primary" @click="editDetail">编辑数量</inno-button-tooltip>-->
                  <inno-button-tooltip
                    :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    type="primary"
                    @click="deleteDetail"
                  >删除</inno-button-tooltip>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableRefDe"
                v-inno-loading="crudDe.loading"
                class="auto-layout-table"
                highlight-current-row
                stripe
                border
                :data="crudDe.data"
                show-summary
                :summary-method="getSummaries"
                :row-class-name="crudDe.tableRowClassName"
                @selection-change="crudDe.selectionChangeHandler"
                @row-click="crudDe.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="storeInItemCode" :label="detailLable" min-width="180" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.storeInItemCode }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.StoreInItemCode" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productNo" label="货号" sortable :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" sortable label="产品名称" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="model" label="型号" min-width="100" />
                <el-table-column prop="producerOrderNo" label="厂家订单号" min-width="160">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" sortable label="本次入票数" min-width="100" class-name="isSum" />
                <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <el-table-column prop="taxAmount" sortable label="税额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxAmount" sortable label="金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="taxRate" sortable label="税率(%)" min-width="100" />
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudDe.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudDe" :pageSizes="[10, 20, 50, 100, 200, 500, 1000]" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="发票明细(金蝶)" name="发票明细(金蝶)">
              <inno-crud-operation :crud="crudDeJd" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>

              <el-table
                ref="tableRefDeJd"
                v-inno-loading="crudDeJd.loading"
                class="auto-layout-table"
                highlight-current-row
                
                stripe
                border
                :data="crudDeJd.data"
                show-summary
                :summary-method="getSummariesJd"
                :row-class-name="crudDeJd.tableRowClassName"
                @selection-change="crudDeJd.selectionChangeHandler"
                @row-click="crudDeJd.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="productNo" label="货号" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudDeJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" label="产品名称" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudDeJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" sortable label="数量" class-name="isSum">
                  <template #default="scope">{{ scope.row.quantity }}</template>
                </el-table-column>
                <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxAmount" sortable label="金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="taxRate" sortable label="税率(%)" min-width="100" />
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudDeJd.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudDeJd" :pageSizes="[20, 30, 50, 100, 200, 300, 500]" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </template>
      </inno-split-pane>
    </div>
    <AddInputbillDetail
      ref="addInputbill"
      :CompanyId="addCompanyId"
      :AgentId="addAgentId"
      :InputBillId="InputBillId"
      :showDialog="createdDialogShow"
      :amount="addAmount"
      :invoiceInfo="crud.rowData"
      :kingdeeDetails="crudDeJd.data"
      @closeDialog="closeDialogCallBack"
    />
    <EditInputbillDetail
      ref="editInputbill"
      :CompanyId="addCompanyId"
      :editProductNos="editProductNos"
      :StoreInCodeS="editStoreInCodeS"
      :AgentId="addAgentId"
      :StoreInItemCode="crudDe.selections[0]?.storeInItemCode"
      :InputBillId="InputBillId"
      :showDialog="EditcreatedDialogShow"
      :amount="addAmount"
      @closeDialog="EditcloseDialogCallBack"
    />
    <excel-import
      v-model:visibel="ExcelImportVisibel"
      title="按Excel导入"
      ref="excelImportActionRef"
      :action="excelImportAction"
      :tipStyle="{ color: 'red' }"
      tip="提示: 此功能支持用Excel导入一张发票对应一张业务单据的场景（业务单据所有明细都开在这张发票上，且发票上没有开其他单据的金额）"
      @confirmSubmitHandler="confirmSubmitHandler"
      @submitSuccess="handleSuccess"
    >
      <template v-slot:importTemplate>
        <a href="https://static.innostic.com/template/进项发票导入模板.xlsx?v=1.2" style="color: red">下载进项发票导入模板</a>
      </template>
    </excel-import>
    <!-- gatewayUrl + 'v1.0/finance-backend -->
    <!-- 'http://localhost:6211 -->
    <excel-import
      v-model:visibel="BatchExcelImportVisibel"
      title="按Excel批量导入"
      :action="
        gatewayUrl + 'v1.0/finance-backend/api/InputBillExecute/BatchExportInvoiceForBusinessBillExcel' 
      "
      :tipStyle="{ color: 'red' }"
      tip="提示: 此功能支持用Excel导入多张发票对应多张业务单据的场景（已存在的明细数据将被静默清除，请仔细核对数量等信息）"
      @submitSuccess="handleSuccess2"
    >
      <template v-slot:importTemplate>
        <a href="https://static.innostic.com/template/进项发票导入模板 (N对N).xlsx?v=1.2" style="color: red">下载进项发票导入模板 (N对N)</a>
      </template>
    </excel-import>

    <el-dialog v-model="inReceiptImportVisible" title="按单据导入" draggable>
      <p style="color: red">注意：此功能只适用于一张发票对应多个寄售转购货单(或货经销入库单)且单据下的所有货号的金额都是开在这张发票上的场景，如非此场景，请勿用此功能</p>
      <el-form>
        <el-input v-model="importReceiptContent" type="textarea" rows="15" placeholder="请输入单号，名称以分号或换行分隔" />
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="inReceiptImportVisible = false">取消</el-button>
          <el-button type="primary" :loading="inReceiptImportOkLoading" @click="inReceiptImportOk">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElTable, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import AddInputbillDetail from './component/AddInputbillDetail.vue';
import EditInputbillDetail from './component/EditInputbillDetail.vue';
import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
import {
  SubmitInputBill,
  DeleteBillSbumit,
  EliminatingErrors
} from '@/api/financeapi';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
  import request from '@/utils/request';
const activeName = ref('发票明细');
const route = useRoute();
const tableRef = ref<InstanceType<typeof ElTable>>();
const ruleFormRef = ref(null);
let createdDialogShow = ref(false);
let EditcreatedDialogShow = ref(false);
let addCompanyId = ref('');
let InputBillId = ref('');
let addAgentId = ref('');
let addAmount = ref(0);
  let editStoreInCodeS = ref([]);
const Token = ref(window.microApp.getData().token);
const crud = CRUD(
  {
    title: '进项发票抬头',
    url: '/api/InputBillQuery/GetList',
    idField: 'id2',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InvoiceNumber: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        crud.data.length && crud.singleSelection(crud.data[0]);
      }
    }
  },
  {
    table: tableRef,
    form: ruleFormRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDeJd = CRUD(
  {
    title: '进项发票金蝶详情',
    url: '/api/InputBillQuery/GetDetailJdList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadDetailSumJd(crudDeJd.query.InputBillId, crudDeJd.query.CompanName, crudDeJd.query.AgentName);
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 500,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDeJd
  }
);
const tableRefDe = ref<InstanceType<typeof ElTable>>();
const crudDe = CRUD(
  {
    title: '进项发票提交详情',
    url: '/api/InputBillQuery/GetDetailList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: '',
      StoreInItemCode: ''
    },

    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadDetailSum(crudDe.query.InputBillId, crudDe.query.CompanName, crudDe.query.AgentName);
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDe
  }
);
  let sumJd = ref({
    NumberSum: 0,
    ValueSum: 0
  });
  let sum = ref({
    NumberSum: 0,
    ValueSum: 0,
    TaxValueSum: 0,
  });
  const excelImportAction = ref(gatewayUrl + 'v1.0/finance-backend/api/InputBillExecute/ExportInvoiceForBusinessBill')
  const loadDetailSumJd = (id, CompanName, AgentName) => {
    request({
      url: '/api/InputBillQuery/GetInputBillDetailSumJd',
      data: {
        searchKey: '',
        InputBillId: id,
        ProductName: '',
        ProductNo: '',
        CompanName: CompanName,
        AgentName: AgentName,
        StoreInItemCode: ''
      },
      method: 'post'
    }).then((res) => {
      sumJd.value = res.data.data;
    });
  };
  const loadDetailSum = (id, CompanName, AgentName) => {
    request({
      url: '/api/InputBillQuery/GetInputBillDetailSum',
      data: {
        searchKey: '',
        InputBillId: id,
        ProductName: '',
        ProductNo: '',
        CompanName: CompanName,
        AgentName: AgentName,
        StoreInItemCode: ''
      },
      method: 'post'
    }).then((res) => {
      sum.value = res.data.data;
    });
  };

const datatypelist = [
  { type: 0, name: '全部' },
  { type: 1, name: '普票' },
  { type: 2, name: '专票' }
];
const Statustypelist = [
  { type: '0', name: '全部' },
  { type: '1', name: '临时发票' },
  { type: '2', name: '已提交' }
];
  const hasDetaillist = [
    { type: 0, name: '全部' },
    { type: 1, name: '是' },
    { type: 2, name: '否' }
  ];

const queryList = computed(() => [
  {
    key: 'InvoiceNumber',
    label: '发票号',
    show: true
  },
  {
    show: true,
    key: 'companyId',
    label: '公司名称',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  {
    key: 'storeInItemCode',
    label: '业务单号',
    show: true
  },
  {
    show: true,
    key: 'agentIds',
    label: '供应商名称',
    type: 'remoteSelect',
    multiple: true,
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  // {
  //   key: 'CompanName',
  //   label: '公司名称',
  //   show: true
  // },
  // {
  //   key: 'AgentName',
  //   label: '供应商名称',
  //   show: true
  // },
  {
    key: 'Type',
    label: '票据类型',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: datatypelist,
    show: true
  },
  {
    key: 'hasDetail',
    label: '是否已有发票明细',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: hasDetaillist,
    show: true
  },
  // {
  //   key: 'Status',
  //   label: '发票状态',
  //   type: 'select',
  //   labelK: 'name',
  //   valueK: 'type',
  //   dataList: Statustypelist,
  //   show: true
  // },
  {
    key: 'beginCreatedTime',
    endDate: 'endCreatedTime',
    label: '开票时间',
    type: 'daterange',
    formart: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'productNo',
    label: '货号',
    show: true
  }
]);
// 按Excel导入
const ExcelImportVisibel = ref(false);
const ExcelImportHandler = () => {
  ExcelImportVisibel.value = true;
};
// 批量导入
const BatchExcelImportVisibel = ref(false);
const BatchExcelImportHandler = () => {
  BatchExcelImportVisibel.value = true;
};

const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
const queryDeList = computed(() => [
  {
    key: 'ProductName',
    label: '产品名称',
    show: true
  },
  {
    key: 'ProductNo',
    label: '货号',
    show: true
  },
  {
    key: 'StoreInItemCode',
    label: '入库单',
    show: true
  }
]);
const queryDeObject = computed(() =>
  Object.fromEntries(queryDeList.value.map((item) => [item.key, item]))
);
onMounted(() => {
  crud.toQuery();
  // 表头拖拽必须在这里执行
  tableDrag(tableRef);
  tableDrag(tableRefDe);
  tableDrag(tableRefDeJd);
});
let detailLable = ref('业务单号');
watch(
  () => crud.rowData.id,
  (n, o) => {
    if (n != null) {
      InputBillId.value = crud.rowData.id;
      addCompanyId.value = crud.rowData.companyId;
      addAgentId.value = crud.rowData.agentId;
      addAmount.value = crud.rowData.amount;
      //if (addAmount.value >= Number(0)) {
      //  detailLable.value = '入库单号/应付单号';
      //} else {
      //  detailLable.value = '出库单号';
      //}
      crudDeJd.query = {
        searchKey: '',
        InputBillId: crud.rowData.id,
        ProductName: '',
        ProductNo: '',
        CompanName: crud.rowData.companName,
        AgentName: crud.rowData.agentName
      };
      crudDe.query = {
        searchKey: '',
        InputBillId: crud.rowData.id,
        ProductName: '',
        ProductNo: '',
        CompanName: crud.rowData.companName,
        AgentName: crud.rowData.agentName
      };
      crudDeJd.toQuery();
      crudDe.toQuery();
    } else {  
      crudDeJd.data=''
      crudDe.data=''
    }
  },
  { deep: true }
);
watch(
  () => route.query.invoiceNo,
  (n, o) => {
    if (n != null) {
      crud.query.invoiceNumber = n;
      crud.toQuery();
    }
  },
  { deep: true }
);
const ishl = ref(0);
const ishf = ref(0);
watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      ishl.value = 0;
      ishf.value = 0;
      crud.selections.forEach((item) => {
        if (item.statusName === '已忽略') {
          ishl.value += 1;
        } else if (item.statusName === '临时发票') {
          ishf.value += 1;
        } else {
          // continue
        }
      });
    }
  },
  { deep: true }
);
const eliminatingErrorsLoading = ref(false);
//一键平尾差
const eliminatingErrors = () => {
  ElMessageBox.confirm(
    '此操作将会同步明细与进项票的税率以及金额，是否继续?',
    '提示',
    {
      confirmButtonText: '继续',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    eliminatingErrorsLoading.value = true;
    console.log(crud.rowData.id);
    EliminatingErrors(crud.rowData.id)
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crudDe.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        eliminatingErrorsLoading.value = false;
      });
  });
};
// 按单据导入内容
const importReceiptContent = ref();
// 按单据导入dialog
const inReceiptImportVisible = ref(false);
// 按单据导入
const inReceiptImport = () => {
  importReceiptContent.value = '';
  inReceiptImportVisible.value = true;
};
// 按单据导入确定
const inReceiptImportOkLoading = ref(false);
const inReceiptImportOk = () => {
  var content = importReceiptContent.value.replace(/(^\s*)|(\s*$)/g, '');
  if (!content) {
    ElMessage({
      showClose: true,
      message: '请输入名称,名称以分号或换行分隔',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  content = content.replace(/\n/g, ',').replace(/;/g, ',').split(',');
  if (content.length > 500) {
    ElMessage({
      showClose: true,
      message: '按单据批量导入，单次不能超过500条',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  inReceiptImportOkLoading.value = true;
  console.log(content);
  request({
    url: '/api/InputBillExecute/AddDetailByCodes',
    method: 'POST',
    data: { inputBillId: crud.rowData.id, codes: content }
  }).then((res) => {
    console.log(res);
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: '导入成功',
        type: 'success',
        duration: 3 * 1000
      });
      inReceiptImportVisible.value = false;
      inReceiptImportOkLoading.value = false;
      crudDe.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      inReceiptImportOkLoading.value = false;
    }
  });
};
//是否显示弹窗
const createdDialog = () => {
  createdDialogShow.value = true;
};
const addInputbill = ref(null);
const closeDialogCallBack = () => {
  createdDialogShow.value = false;
  // var refshow = addInputbill.value.isRefresh;
  // if (refshow) {
  //crud.toQuery();
  crudDe.toQuery();

  // }
};
let editProductNos = ref([]);
const editDetail = async () => {
  if (crudDe.selections.length != 1) {
    ElMessage({
      showClose: true,
      message: '请选择要操作的数据,最多一条数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var sumData = [];
  var producNo = [];
  for (let index = 0; index < crudDe.selections.length; index++) {
    const element = crudDe.selections[index];
    sumData = sumData.concat(element.storeInItemCode);
    producNo = producNo.concat(element.productNo);
  }
  editStoreInCodeS.value = sumData;
  editProductNos.value = producNo;
  EditcreatedDialogShow.value = true;
};
const deleteDetail = async () => {
  if (crudDe.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请选择要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var sumData = [];
  var detailIds = [];
  for (let index = 0; index < crudDe.selections.length; index++) {
    const element = crudDe.selections[index];
    detailIds.push(crudDe.selections[index].id.toUpperCase());
  }
  var selectRow = crud.selections.map((item) => {
    return item['id'];
  });
  if (selectRow.length > 1) {
    ElMessage({
      showClose: true,
      message: '提交时只能选择一条数据',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  console.log(sumData);
  console.log(detailIds);
  DeleteBillSbumit(sumData, selectRow[0].toUpperCase(), detailIds)
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage({
          showClose: true,
          message: '删除成功！',
          type: 'success',
          duration: 3 * 1000
        });
        //crud.toQuery();
        crudDe.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
const submitBillLoading = ref(false);
const submitBill = async () => {
  if (crud.selections.length != 1) {
    ElMessage({
      showClose: true,
      message: '请选择一条要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var ids = crud.selections.map((item) => {
    return item['id'];
  });
  if (ids.length > 1) {
    ElMessage({
      showClose: true,
      message: '提交时只能选择一条数据',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  submitBillLoading.value = true;
  SubmitInputBill(ids)
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage({
          showClose: true,
          message: '提交成功！',
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
        //crudDe.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      submitBillLoading.value = false;
    });
};
// 忽略发票
const ignoreBill = async () => {
  if (crud.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请至少选择一条要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let ids = crud.selections.map((item) => {
    return item['id'];
  });
  ElMessageBox.confirm('只能忽略临时发票，确定要将发票忽略?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    submitBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/IgnoreBill',
      method: 'POST',
      data: ids
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        submitBillLoading.value = false;
      });
  });
};
// 恢复发票
const restoreBill = async () => {
  if (crud.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请至少选择一条要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let ids = crud.selections.map((item) => {
    return item['id'];
  });
  ElMessageBox.confirm('只能恢复已忽略的发票，确定要将发票恢复?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    submitBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/RestoreBill',
      method: 'POST',
      data: ids
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        submitBillLoading.value = false;
      });
  });
}
const editInputbill = ref(null);
const EditcloseDialogCallBack = async () => {
  EditcreatedDialogShow.value = false;
  var refshow = editInputbill.value.isRefresh;
  if (refshow) {
    //crud.toQuery();
    crudDe.toQuery();
  }
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总合计';
      return;
    }
    if (column.property == 'quantity') {
      sums[index] = sum.value.numberSum
      sums[index] = rbstateFormat(sums[index], column.property);
    } else if (column.property == 'noTaxAmount') {
      sums[index] = sum.value.valueSum
    } else if (column.property == 'taxAmount') {
      sums[index] = sum.value.taxValueSum
    }else {
      sums[index] = '';
    }
    
  });
  return sums;
};
  const getSummariesJd = (param: SummaryMethodProps) => {
    const { columns, data } = param;
    const sums: string[] = [];
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '总合计';
        return;
      }
      if (column.property == 'quantity') {
        sums[index] = sumJd.value.numberSum
        sums[index] = rbstateFormat(sums[index], column.property);
      } else if (column.property == 'noTaxAmount') {
        sums[index] = sumJd.value.valueSum
      }  else {
        sums[index] = '';
      }
     
    });
    return sums;
  };
// 合计金额千分位
const rbstateFormat = (cellValue, property) => {
  return asyncNumeral(
    cellValue,
    property === 'quantity' ? '0,0.0000000000' : '0,0.00'
  );
};
const activeNameStatus = ref('all');
const search = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.label == '已提交') {
    crud.query.Status = '2';
  } else if (tab.props.label == '临时发票') {
    crud.query.Status = '1';
  } else if (tab.props.label == '忽略发票') {
    crud.query.Status = '9';
  } else {
    crud.query.Status = '0';
  }
  crud.toQuery();
};
const searchCrud = () => {
  activeNameStatus.value = 'all';
  crud.query.Status = '0';
  crud.toQuery();
};
const downloadFile = (invoiceNo, invoiceCode) => {
  request({
    url:
      '/api/InputBillQuery/GetKDFilePath?invoiceNo=' +
      invoiceNo +
      '&invoiceCode=' +
      invoiceCode,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          FileViewer.show(
            res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
            0, // 默认打开的下标
            {} // FileViewer props
          );
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶附件，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((t) => {});
};
  const excelImportActionRef = ref()
  const confirmSubmitHandler = async () => {
    // 先保留默认的地址
    let oldAction = Object.assign(excelImportAction.value)
    excelImportAction.value = gatewayUrl + 'v1.0/finance-backend/api/InputBillExecute/ExportInvoiceForBusinessBillAndSubmit' // 改为临时提交地址
    // 导入
    await excelImportActionRef.value.uploadRefSubmit()
    // 再修改为默认地址
    excelImportAction.value = oldAction
  }
// 按Excel导入成功后的回调
const handleSuccess = (res) => {
  console.log(123);
  if (res.code === 200) {
    if (res.submitType.value == 2) {//确认并提交
      crudDe.toQuery();
      submitBill()
    } else {
      crudDe.toQuery();
      ElMessage({
        showClose: true,
        message: res.message,
        type: 'success',
        duration: 3 * 1000
      });
    }
  } else {
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'error',
      duration: 3 * 1000
    });
  }
};
// 按Excel导入成功后的回调
  const errbtn = ref(false);
  const errFileId = ref('');
const handleSuccess2 = (res) => {
  errbtn.value = false;
  if (res.code === 200) {
    crudDe.toQuery();
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'success',
      duration: 3 * 1000
    });
  } else {
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'error',
      duration: 3 * 1000
    });
  }
  // 参数校验失败
  if (res.data !== '' && res.data !== null) {
    errFileId.value = res.data;
    errbtn.value = true;
  }
};
// 下载错误信息文件
const downloadErrInfoExcel = () => {
  handleDownload(errFileId.value);
};
const handleDownload = (id) => {
  request({
    url: window.gatewayUrl + 'api/FileDownload/code',
    method: 'get'
  }).then((res) => {
    if (res.data.code) {
      window.open(
        window.gatewayUrl +
          'api/FileDownload/temp/direct?&fileId=' +
          id +
          '&code=' +
          res.data.code
      );
    }
  });
};
//导出数据
const exportBillLoading = ref(false);
const exportBill = () => {
  ElMessageBox.confirm('是否导出符合条件的所有数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    exportBillLoading.value = true;
    return request({
      url: '/api/InputBillQuery/ExportBill',
      data: crud.query,
      method: 'POST',
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
      responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
    })
      .then((res) => {
        const xlsx =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
        const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
        a.download = '导出进项发票文件' + new Date().getTime() + '.xlsx';
        a.href = window.URL.createObjectURL(blob);
        a.click();
        a.remove();
      })
      .catch((err) => {
        throw '请求错误';
      })
      .finally(() => {
        exportBillLoading.value = false;
      });
  });
};
// 取消勾稽
const cancelBillLoading = ref(false);
const cancelBill = () => {
  console.log(JSON.stringify(crud.rowData))
  ElMessageBox.confirm('确认取消勾稽此发票', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    cancelBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/CancelBill?id='+crud.rowData.id,
      method: 'POST'
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        cancelBillLoading.value = false;
      });
  });
}
</script>