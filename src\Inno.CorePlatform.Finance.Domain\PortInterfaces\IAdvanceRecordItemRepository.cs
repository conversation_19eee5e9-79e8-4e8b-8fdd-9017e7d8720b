﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    public interface IAdvanceRecordItemRepository : IRepositorySupportCrudAndUow<AdvanceFundBusinessCheckItem, Guid>
    {
        Task<int> InsetrAdvance(AdvanceFundBusinessCheckItem AdvanceRecordItem, List<AdvanceFundBusinessCheckDetail> AdvanceRecordDetail);
    }
}
