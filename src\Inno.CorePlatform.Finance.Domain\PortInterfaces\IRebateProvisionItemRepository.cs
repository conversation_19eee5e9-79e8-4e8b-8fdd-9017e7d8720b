﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    public interface IRebateProvisionItemRepository: IRepositorySupportCrudAndUow<RebateProvisionItem, Guid>
    {
        Task<bool> AddRebateProvisionItems(List<RebateProvisionItem> items);

        /// <summary>
        /// 根据公司ID和单据日期范围查询返利计提记录
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>返利计提记录列表</returns>
        Task<List<RebateProvisionItem>> GetByCompanyIdAndDateRangeAsync(Guid companyId, DateTime startDate, DateTime endDate);
    }
}
