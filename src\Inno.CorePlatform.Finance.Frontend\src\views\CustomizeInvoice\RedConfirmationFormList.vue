<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>红字确认单编号查询</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="100" split="horizontal" style="padding: 0">
        <template #paneL>
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane label="红字确认单编号查询" />
              </el-tabs>
            </template>
            <template #default>
              <!-- <el-button v-auth="functionUris.add" type="primary" style="margin-left: 15px" @click="createOpt">创建</el-button> -->
               <el-button type="primary" style="margin-left: 15px" @click="createdDialog">生成确认单</el-button> 
            </template>
          </inno-crud-operation>
          <!-- show-summary
          :summary-method="getSummaries"-->
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            :data="crud.data"
            stripe
            border
            fit
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox :checked="
                    scope.row.govRedConfirmBillUuid ===
                    crud.rowData.govRedConfirmBillUuid
                  " />
              </template>
            </el-table-column>
            <!-- <el-table-column type="expand">
              <template #default="{ row }">
                <div v-if="row.invoiceDetail.length > 0" style="padding-left: 105px">
                  <el-table
                    :data="row.invoiceDetail"
                    class="auto-layout-table"
                    stripe
                    border
                    fit
                    highlight-current-row
                    
                  >
                    <el-table-column label="蓝字发票明细序号" property="blueInvoiceItemIndex" width="120" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.blueInvoiceItemIndex }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="序号" property="index" width="58" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.index }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="商品编码" property="revenueCode" min-width="180" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.revenueCode }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="项目名称" property="goodsName" min-width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.goodsName }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="规格型号" property="specification" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="单位" property="units" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.units }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="数量" property="quantity" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.quantity }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="单价" class-name="isSum" property="price" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.price }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="不含税金额" class-name="isSum" property="amount" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.amount }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="税额" class-name="isSum" property="taxAmount" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.taxAmount }}</inno-button-copy>
                      </template>
                    </el-table-column>
                    <el-table-column label="税率" property="taxRate" width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <inno-button-copy :link="false">{{ scope.row.taxRate }}</inno-button-copy>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>-->
            <el-table-column label="红字确认单编号" property="number" min-width="180" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.number }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="确认单状态" property="redConfirmBillStatusName" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.redConfirmBillStatusName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红字确认单录入日期" property="redConfirmEnterDate" min-width="150">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.redConfirmEnterDate }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红冲原因" property="redReasonName" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.redReasonName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="录入方身份" property="enterIdentityName" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.enterIdentityName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="购方名称" property="buyerName" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.buyerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="销方税号" property="saleTaxNo" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleTaxNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票号" min-width="160" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红字冲销不含税金额" property="invoiceAmount" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceAmount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红票冲销税额" property="totalTax" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.totalTax }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红票开票日期" property="issueTime" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.issueTime }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票类型" property="originalInvoiceTypeName" min-width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalInvoiceTypeName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票代码" property="originalInvoiceCode" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalInvoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票号码" property="originalInvoiceNo" min-width="160" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalInvoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票不含税金额" property="originalInvoiceAmount" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalInvoiceAmount }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票税额" property="originalTotalTax" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalTotalTax }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="蓝字发票日期" property="originalIssueTime" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalIssueTime }}</inno-button-copy>
              </template>
            </el-table-column>
            <!-- items -->
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <RedConfirmationFormGenerate ref="generateCreatedRef" class="crud-opts" :showDialog="generatedialogShow" @closeDialog="closeDialogCallBack" @refreshIndex="refreshIndexCallBack"></RedConfirmationFormGenerate>
  </div>
</template>

<script lang="tsx" setup>
import { ref, onMounted, onActivated, computed } from 'vue';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';
import RedConfirmationFormGenerate from '@/views/CustomizeInvoice/RedConfirmationFormGenerate.vue';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
const functionUris = {
  add: ''
};
const tableItem = ref<InstanceType<typeof ElTable>>();
const crud = CRUD(
  {
    title: '应用',
    url: '/api/RedConfirmationFormNumberQuery/GetList',
    idField: 'Id',
    method: 'post',
    crudMethod: {},
    tablekey: 'tablekeyItemhty', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    query: {},
    userNames: ['creator'],
    optShow: {
      add: false,
      reset: true //重置按钮
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRequest]: (_crud, result) => {
        if (result.data.code == -1 && (result.data.msg != '' || result.data.msg != null)) {
           ElMessage({
            showClose: true,
            message: result.data.msg,
            type: 'warning',
            duration: 3 * 1000
          });
        }
      },
      [CRUD.HOOK.afterRefresh]: () => {
      }
    }
  },
  {
    table: tableItem
  }
);

onMounted(() => {
  crud.toQuery();
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
});

const props = defineProps({
  showDialog: Boolean,
  __refresh: Boolean
});

onActivated(() => {
  if (props.__refresh) {
    generatedialogShow.value = false;
    crud.toQuery();
  }
});

const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

//高级检索
const queryList = computed(() => {
  return [
    {
      key: 'org',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      valueK: 'id',
      labelK: 'name',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { functionUri: 'metadata://fam' }
      },
      show: true 
    },
    {
      key: 'taxNo',
      label: '销方税号',
      show: true
    },
    {
      key: 'startDate',
      endDate: 'endDate',
      type: 'custom', // 自定义组件使用
      label: '起止日期',
      component: () => {
        return (
          <inno-select-date
            vModel:start-date={crud.query['startDate']}
            vModel:endDate={crud.query['endDate']}
            start-placeholder={'开始日期'}
            end-placeholder={'结束日期'}
            type="daterange"
            format="YYYY-MM-DD"
          />
        );
      },
      show: true
    }
  ];
});

//创建
const createOpt = () => {};

const generateCreatedRef = ref();
let generatedialogShow = ref(false);
let orderNo = ref('');
let invoiceCode = ref('');
let invoiceNo = ref('');
let redReason = ref('');
//是否显示弹窗
const createdDialog = () => {
  generatedialogShow.value = true;
};
const closeDialogCallBack = () => {
  generatedialogShow.value = false;
};
const refreshIndexCallBack = () => {
  crud.toQuery();
};
//搜索列表
const searchList = () => {
  debugger
  console.log('taxNo:' + crud.query.taxNo);
  console.log('org:' + crud.query.org);
  if (
    (crud.query.taxNo === null ||
    crud.query.taxNo === '') &&
    (crud.query.org === null ||
    crud.query.org === '')
  ) {
    ElMessage({
      showClose: true,
      message: '公司和销方税号二者必填其一',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  } else {
    crud.toQuery();
  }
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0);
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
</script>
