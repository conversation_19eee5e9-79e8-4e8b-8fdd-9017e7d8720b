<template>
  <el-dialog v-model="generateDialog" draggable title="生成红字确认单" 
  modal-class="position-fixed" width="80%" hight="70%" destroy-on-close :close-on-click-modal="false" :before-close="handleClose">
    <div>
      <el-form ref="generateFormRef" :model="generateformData" label-position="right" label-width="90px" :rules="generateformRules">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="销售订单号" prop="orderNo">
              <el-tooltip
                effect="dark"
                content="原应收对应的销售订单号"
                placement="top-start"
              >
                <p class="tip-box"></p>
              </el-tooltip>
              
              <el-input v-model="generateformData.orderNo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="蓝票号" prop="invoiceNo">
              <el-input v-model="generateformData.invoiceNo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="蓝票代码" prop="invoiceCode">
              <el-input v-model="generateformData.invoiceCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发票种类" prop="invoiceType">
              <el-select v-model="generateformData.invoiceType" filterable placeholder="请选择发票种类" style="width: 200px">
                <el-option v-for="item in invoiceTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="红冲原因" prop="redReason">
              <el-select v-model="generateformData.redReason" filterable placeholder="请选择红冲原因" style="width: 100%">
                <el-option v-for="item in redReasonList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="crud-opts">
          <inno-button-tooltip type="success" @click="searchDetails" :loading="serachLoading">搜索明细</inno-button-tooltip>
          <!-- <inno-button-tooltip type="primary" @click="addRows">新增行</inno-button-tooltip> -->
          <inno-button-tooltip type="danger" @click="delRows">删除行</inno-button-tooltip>
        </div>
        <div style="margin-bottom: 10px"></div>
        <el-table
          id="tableDetail"
          ref="tableRef1"
          class="auto-layout-table"
          highlight-current-row
          :data="generateformData.invoiceDetail"
          maxHeight="450"
          border
          @selection-change="selectionChange"
        >
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column label="行性质" fixed="left" property="lineProperty">
            <template #default="scope">
              <el-select v-model="scope.row.lineProperty" filterable placeholder="请选择行性质">
                <el-option v-for="item in linePropertyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="原蓝字发票明细序号" property="originalSeq" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.originalSeq"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="业务系统明细id" property="billSourceId" width="110">
            <template #default="scope">
              <el-input v-model="scope.row.billSourceId"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="星瀚商品编码" property="goodsCode" width="100">
            <template #default="scope">
              <el-input v-model="scope.row.goodsCode"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="税收项目名称" property="goodsName" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.goodsName"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="税收分类编码" property="revenueCode" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.revenueCode"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="规格型号" property="specification" width="100">
            <template #default="scope">
              <el-input v-model="scope.row.specification"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="计量单位" property="units" width="80">
            <template #default="scope">
              <el-input v-model="scope.row.units"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="数量" property="quantity" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.quantity" :controls="false" style="width: 100%;" @input="debouncedComputeAmount(scope.row)"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="含税单价" property="price" width="120">
            <template #default="scope">
              <el-input-number v-model="scope.row.priceTax" :controls="false" :min="0" style="width: 100%;" @input="debouncedComputeAmount(scope.row)" disabled></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="不含税单价" property="price" width="80">
            <template #default="scope">
              <el-input v-model="scope.row.price" disabled></el-input>
            </template>
          </el-table-column>
          <el-table-column label="红冲金额（不含税）" property="amount" width="140">
            <template #default="scope">
              <el-input v-model="scope.row.amount"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="税率" property="taxRate" width="80">
            <template #default="scope">
              <el-input v-model="scope.row.taxRate" disabled></el-input>
              <!-- <el-form-item :prop="'datas'+ scope.$index + 'taxRate'" label-width="0" :rules="'generateformRules.datas'+ scope.$index + 'taxRate'">
                  <el-input  v-model="scope.row.taxRate" style="margin-top: 18px;"></el-input>
              </el-form-item>-->
            </template>
          </el-table-column>
          <el-table-column label="税额" property="taxAmount" width="80">
            <template #default="scope">
              <el-input v-model="scope.row.taxAmount"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="save">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup>
import { ref, watch, reactive } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { GenerateRedConfirmation, GetDetails, ComputeAmount } from '@/api/financeapi';
import { FormRules } from 'element-plus';
import { debounce } from 'lodash-es'; // 引入 lodash-es 的防抖函数
const props = defineProps({
  showDialog: Boolean
});
watch(
  () => props.showDialog,
  (newVulue) => {
    generateDialog.value = newVulue;
  }
);
//发票种类枚举
const invoiceTypeList = [
  {
    id: '01',
    name: '数字化电子专票'
  },
  {
    id: '02',
    name: '数字化电子普票'
  }
];
//红冲原因枚举
const redReasonList = [
  {
    id: '1',
    name: '销货退回'
  },
  {
    id: '2',
    name: '开票有误'
  },
  {
    id: '3',
    name: '服务中止'
  },
  {
    id: '4',
    name: '销售折让'
  }
];
const linePropertyList = [
  {
    id: '0',
    name: '正常商品行'
  },
  {
    id: '1',
    name: '被折扣行'
  }
];
let saveLoading = ref(false);
let generateDialog = ref(false);
//生成确认单表单提交数据
let generateformData = reactive({
  orderNo: '',
  invoiceCode: '',
  invoiceNo: '',
  redReason: '',
  invoiceDetail: [
    {
      lineProperty: '',
      originalSeq: '',
      billSourceId: '',
      goodsCode: '',
      goodsName: '',
      revenueCode: '',
      specification: '',
      units: '',
      quantity: '',
      amount: '',
      taxRate: '',
      taxAmount: ''
    }
  ]
});
//生成确认表单规则校验
const generateformRules = reactive<FormRules>({
  // orderNo: [
  //   {
  //     required: true,
  //     message: '请输入订单号',
  //     trigger: 'blur'
  //   }
  // ],
  invoiceNo: [
    {
      required: true,
      message: '请输入发票号',
      trigger: 'blur'
    }
  ],
  redReason: [
    {
      required: true,
      message: '请选择红冲原因',
      trigger: 'change'
    }
  ]
});
let createDialog = ref(false);
const emits = defineEmits(['closeDialog', 'refreshIndex']);
const generateFormRef = ref();
//弹窗关闭
const handleClose = () => {
  emits('closeDialog'); // 取消和 x 按钮的事件，防止重复操作showDialog变量
  generateDialog.value = false;
  generateformData.value = {
    orderNo: '',
    invoiceCode: '',
    invoiceNo: '',
    redReason: ''
  };
  generateformData.invoiceDetail = [];
  generateFormRef.value.resetFields();
  // generateformData.invoiceDetail = [
  //   {
  //     lineProperty: '',
  //     originalSeq: '',
  //     billSourceId: '',
  //     goodsCode: '',
  //     goodsName: '',
  //     revenueCode: '',
  //     specification: '',
  //     units: '',
  //     quantity: '',
  //     amount: '',
  //     taxRate: '',
  //     taxAmount: ''
  //   }
  // ]
};
//确定
const save = () => {
  if (!generateFormRef.value) return;
  generateFormRef.value.validate((valid, field) => {
    console.log(JSON.stringify(valid));
    if (valid) {
      console.log(JSON.stringify(generateformData));
      GenerateRedConfirmation(generateformData).then((res) => {
        if (res.data.code == '200') {
          ElMessage({
            showClose: true,
            message: '生成成功',
            type: 'success',
            duration: 3 * 1000
          });
          saveLoading.value = false;
          //调用父组件重新查询列表
          handleClose();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
          saveLoading.value = false;
        }
      });
    }
  });
};

const tableDetail = ref<InstanceType<typeof ElTable>>();
const tableRef1 = ref<InstanceType<typeof ElTable>>();
//新增行
const addRows = () => {
  generateformData.invoiceDetail.push({
    lineProperty: '',
    originalSeq: '',
    billSourceId: '',
    goodsCode: '',
    goodsName: '',
    revenueCode: '',
    specification: '',
    units: '',
    quantity: '',
    amount: '',
    taxRate: '',
    taxAmount: ''
  });
};
//搜索明细
const serachLoading = ref(false);
const searchDetails = () => {
  generateFormRef.value.validate((valid, field) => {
    if (valid) {
      serachLoading.value = true;
      GetDetails(generateformData).then((res) => {
        console.log(JSON.stringify(res.data.data.list));
        if (res.data.code == 1) {
          generateformData.invoiceDetail = res.data.data.list;
          serachLoading.value = false;
        }
      })
      .finish(() => {
        serachLoading.value = false;
      });
    }
  });
};
const multipleSelection = ref([]);
//选择事件
const selectionChange = (val) => {
  multipleSelection.value = val;
}
//删除行
const delRows = () => {
  //generateformData.invoiceDetail.pop();
  generateformData.invoiceDetail = generateformData.invoiceDetail.filter(
    (row) => !multipleSelection.value.includes(row)
  );
  // generateformData.invoiceDetail.map((item,index) => {
  //   item.originalSeq = index.toString()
  // })
  multipleSelection.value = [];
};

//计算红冲金额
const computeAmount = (row) => {
  ComputeAmount(row.quantity,row.priceTax,row.taxRate).then((res) => {
    console.log(JSON.stringify(res.data.data));
    if (res.data.code == 200) {
      row.amount = Number(res.data.data.amount);
      row.taxAmount = res.data.data.taxAmount;
      row.price = res.data.data.price;
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
    }
  })
}

// 创建防抖函数，注意这里只是定义了防抖函数，并没有调用它
const debouncedComputeAmount = debounce(computeAmount, 300);

</script>
<style scoped>
.tip-box{
  padding: 0;
  margin: 0;
  width: 90px;
  height: 24px;
  position: absolute;
  left: -90px;
  top: 0;
  z-index: 999999;
}
</style>