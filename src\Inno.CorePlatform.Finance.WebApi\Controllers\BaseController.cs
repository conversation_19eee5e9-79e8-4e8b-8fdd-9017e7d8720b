﻿using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json.Serialization;
using System.Text.Json;
using Inno.CorePlatform.Common.Http;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 基类控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public abstract class BaseController : ControllerBase
    {
        /// <summary>
        /// 锁定超时时间，单位为秒
        /// </summary>
        public const int LockTimeoutSeconds = 60;
        protected readonly ISubLogService _logService;
        public virtual bool EnableParameterLogging { get; set; } = false;

        public BaseController(ISubLogService logService)
        {
            _logService = logService;
        }

        protected BaseResponseData<T> Success<T>(string msg)
        {
            return BaseResponseData<T>.Success(msg);
        }

        protected BaseResponseData<T> Success<T>(T data, string msg)
        {
            return BaseResponseData<T>.Success(data, msg);
        }

        protected BaseResponseData<T> Failed<T>(string msg)
        {
            return BaseResponseData<T>.Failed((int)CodeStatusEnum.Failed, msg);
        }

        protected BaseResponseData<T> Failed<T>(T data, string msg)
        {
            return BaseResponseData<T>.Failed((int)CodeStatusEnum.Failed, msg);
        }

        /// <summary>
        /// 获取操作日志的标识
        /// </summary>
        /// <returns></returns>
        protected virtual string GetLogOperation()
        {
            //默认获取边车请求头
            HttpContext.Request.Headers.TryGetValue("dapr-topic", out var appId);
            return appId;
        }

        // 子类可重写此方法自定义日志逻辑
        [HttpPost]
        public virtual async Task LogParametersAsync(Dictionary<string, object> parameters, string operationName = "")
        {
#if !DEBUG  
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = false,
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            // 获取控制器全名（包括命名空间）
            var controllerName = ControllerContext.ActionDescriptor.ControllerTypeInfo.FullName;

            // 获取方法名
            var actionName = ControllerContext.ActionDescriptor.ActionName;

            // 组合全路径
            var fullMethodPath = $"{controllerName}.{actionName}";
            var json = JsonSerializer.Serialize(parameters, jsonOptions);
            await _logService.LogAsync(
                fullMethodPath,
                json,
                string.IsNullOrEmpty(operationName) ? GetLogOperation() : operationName
            );
#endif
        }
    }
}
