<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-creditQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>应收清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />

      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.abatedStatus" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`未冲销(${tabCount.nonAbatedCount})`" name="0" lazy />
                <el-tab-pane :label="`已冲销(${tabCount.abatedCount})`" name="1" lazy />
                <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
              </el-tabs>
            </template>

            <template #default>
              <inno-button-tooltip
                type="primary"
                icon="Download"
                v-if="hasPermission(functionUris.export)"
                :loading="downLoading"
                @click="
                  downloadAsync(
                    'api/CreditQuery/exportCreditTask',
                    '应收清单',
                    crud.query
                  )
                "
              >导出数据</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table ref="tableRef0"
                    v-inno-loading="crud.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    border
                    :data="crud.data"
                    stripe
                    show-summary
                    :summary-method="getSummaries"
                    :row-class-name="crud.tableRowClassName"
                    @sort-change="crud.sortChange"
                    @selection-change="crud.selectionChangeHandler"
                    @row-click="crud.singleSelection">
            <el-table-column type="selection" fixed="left" width="55"></el-table-column>
            <el-table-column label="应收单号" property="billCode" min-width="220" fixed="left" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billCode" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单据日期" property="billDate" width="90" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                scope.row.billDate === null
                ? ''
                : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="关联单号" property="relateCode" min-width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.relateCode" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detailRelate(scope.row.relateCode)">{{ scope.row.relateCode }}</el-link>
                </inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="销售子系统" property="saleSystemName" width="110" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.saleSystemName" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="company" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" min-width="200" property="customer" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="部门" property="deptName" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.deptName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="业务单元" property="service" width="125" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.servicesId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="终端医院" min-width="200" property="hospitalName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.hospitalName }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="应收类型" property="service" width="125" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="businessDeptFullName" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" min-width="200" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.orderNo" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detail(scope.row.orderNo)">{{ scope.row.orderNo }}</el-link>
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="原始订单号" property="originOrderNo" min-width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.originOrderNo" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originOrderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" class-name="isSum" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="已开票金额" property="invoiceAmount" class-name="isSum" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="未开票金额" property="noInvoiceAmount" class-name="isSum" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.noInvoiceAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订货人" property="customerPersonName" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerPersonName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户订单号" property="customerOrderCode" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerOrderCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column 
              class-name="isSum" 
              label="已冲销" 
              property="abatmentAmount" 
              width="120" 
              show-overflow-tooltip
            >
              <template #default="scope">
                <inno-numeral 
                  :value="!scope.row.isContainsAccountPeriodTypeBySale?scope.row.abatmentAmount:'-'" 
                  format="0,0.00" 
                />
              </template>
            </el-table-column>
            <el-table-column 
              class-name="isSum" 
              label="余额" 
              property="leftAmount" 
              width="80" 
              show-overflow-tooltip
            >
              <template #default="scope">
                <!-- 仅当 isContainsAccountPeriodTypeBySale 为 false 时才显示 -->
                <inno-numeral 
                  v-if="scope.row.value < 0 && scope.row.leftAmount > 0" 
                  :value="!scope.row.isContainsAccountPeriodTypeBySale?-scope.row.leftAmount:'-'" 
                  format="0,0.00" 
                />
                <inno-numeral 
                  v-else
                  :value="!scope.row.isContainsAccountPeriodTypeBySale?scope.row.leftAmount:'-'" 
                  format="0,0.00" 
                />
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="损失确认金额" property="lossRecognitionValue" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.lossRecognitionValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="确认收入" property="isSureIncome" width="95" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.isSureIncome" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{ scope.row.isSureIncome === 1 ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="签收时间" property="isSureIncomeDate" width="90" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                scope.row.isSureIncomeDate === null
                ? ''
                : dateFormat(scope.row.isSureIncomeDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="预计回款日期" property="probablyBackTime" width="120" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.probablyBackTime === null
                ? ''
                : dateFormat(scope.row.probablyBackTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="逾期天数" property="overdueDay" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.overdueDay" />
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{ scope.row.createdByName }}
              </template>
            </el-table-column>
            <el-table-column label="价格类型" property="priceSourceStr" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.priceSourceStr }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>

          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <span style="margin-left: 20px">
              金额：
              <inno-numeral :value="selectionValue" format="0,0.00" />
            </span>
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <el-tabs type="border-card" style="height: 100%" class="mycard_css">
            <el-tab-pane label="冲销信息">
              <el-table
                ref="tableRef3"
                v-inno-loading="crud3.loading"
                class="auto-layout-table"
                highlight-current-row
                
                border
                show-summary
                :summary-method="getSummaries"
                :data="crud3.data"
                stripe
                :row-class-name="crud3.tableRowClassName"
                @selection-change="crud3.selectionChangeHandler"
                @row-click="crud3.rowClick"
              >
                <el-table-column label="应收单号" property="creditBillCode" width="300" show-overflow-tooltip>
                  <template #default>
                    <inno-button-copy :link="false">{{ crud.rowData.billCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="冲销单号" property="creditBillCode" width="300" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{
                      crud.rowData.billCode === scope.row.creditBillCode
                      ? scope.row.debtBillCode
                      : scope.row.creditBillCode
                      }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="冲销日期" property="abtdate" width="100" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.abtdate, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column class-name="isSum" label="冲销金额 " property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="发票信息">
              <el-table
                ref="tableRef2"
                v-inno-loading="crud2.loading"
                class="auto-layout-table"
                highlight-current-row
                
                border
                show-summary
                :summary-method="getSummaries"
                :data="crud2.data"
                stripe
                :row-class-name="crud2.tableRowClassName"
                @selection-change="crud2.selectionChangeHandler"
                @row-click="crud2.rowClick"
              >
                <el-table-column label="发票号" property="invoiceNo" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="发票代码" property="invoiceCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="发票验证码" property="invoiceCheckCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.invoiceCheckCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="开票时间" property="invoiceTime" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column class-name="isSum" label="应收单金额" property="creditAmount" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.creditAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="开票金额" property="invoiceAmount" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="开票状态" property="isCancel" show-overflow-tooltip>
                  <template #default="scope">
                    <span>
                      <el-tag :type="!scope.row.isCancel ? '' : 'danger'" size="small">{{ !scope.row.isCancel ? '已开票' : '已作废' }}</el-tag>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="红冲状态" property="changedStatusStr" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <el-tag :type="scope.row.changedStatus !== 1&&scope.row.changedStatus !== 2 ? 'success' : 'error'" disable-transitions>{{ scope.row.changedStatusStr }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="蓝字发票号" property="blueInvoiceNo" min-width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.blueInvoiceNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="备注" property="remark" show-overflow-tooltip />
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="`付款计划`">
              <el-table ref="tableRefDebtDetail"
                        v-inno-loading="crudDebtDetail.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        border
                        show-summary
                        :summary-method="getSummaries"
                        :data="crudDebtDetail.data"
                        stripe
                        row-key="id"
                        :row-class-name="crudDebtDetail.tableRowClassName"
                        :expand-row-keys="expands"
                        @row-click="clickRowHandle">
                <el-table-column label="应付单号" property="debt.billCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.debt.billCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="账期类型" property="accountPeriodTypeStr" width="80" show-overflow-tooltip></el-table-column>
                <el-table-column label="预计付款日期" property="probablyPayTime" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.probablyPayTime, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="收款单号" property="receiveCode" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="原始金额" property="originValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.originValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="基础折扣" property="distributionDiscount" show-overflow-tooltip />
                <el-table-column label="供应链金融折扣" property="financeDiscount" show-overflow-tooltip />
                <el-table-column label="SPD折扣" property="spdDiscount" show-overflow-tooltip />
                <el-table-column label="税率折扣" property="taxDiscount" show-overflow-tooltip />
                <el-table-column label="厂家折扣" property="costDiscount" show-overflow-tooltip />
                <el-table-column class-name="isSum" label="付款金额" property="value" min-width="80" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="供应商" property="agentName" width="130" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{
                        scope.row.debt != null &&
                        scope.row.debt.agentName != null
                        ? scope.row.debt.agentName
                        : ''
                      }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="客户" property="customer" width="130" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{
                      scope.row.credit != null &&
                      scope.row.credit.customerName != null
                      ? scope.row.credit.customerName
                      : ''
                      }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="结算方式" property="settletype" show-overflow-tooltip />
                <el-table-column label="到期日" property="draftBillExpireDate" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.draftBillExpireDate, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" property="status" width="80" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    scope.row.status === null
                    ? ''
                    : scope.row.status === 0
                    ? '待执行'
                    : scope.row.status === 99
                    ? '已完成'
                    : ''
                    }}
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane :label="`收入确认明细`" v-if="crud.rowData.serviceConfirmRevenuePlanModeEnum === 1">
              <el-table ref="tableRef4"
                        v-inno-loading="crud4.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        
                        border
                        :data="crud4.data"
                        stripe
                        :row-class-name="crud4.tableRowClassName"
                        @selection-change="crud4.selectionChangeHandler"
                        @row-click="crud4.rowClick">
                <el-table-column class-name="isSum" label="确认收入金额 " property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="确认收入不含税金额 " property="noTaxValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="含税成本 " property="cost" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.cost" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" label="不含税成本 " property="noTaxCost" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="计划分批确认收入时间" property="sureIncomeDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.sureIncomeDate, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="实际确认收入时间" property="createdTime" show-overflow-tooltip>
                  <template #default="scope">
                    {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="备注" property="remark" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.remark }}
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </template>
      </inno-split-pane>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { ElTable, ElForm, ElMessage, ElMessageBox } from 'element-plus';
  import { getTreeList, getDepartTree } from '@/api/bdsData';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import {
  whetherGroup,
  DebtTypeEnum,
  CreditTypeEnum,
  AbatedStatus,
  InvoiceStatus
} from '@/api/metaInfo';
import request from '@/utils/request';
  import _ from 'lodash';
  import { useRouter } from 'vue-router';
  let router = useRouter();

const tableRef0 = ref<InstanceType<typeof ElTable>>();
const addRef = ref<InstanceType<typeof ElForm>>();
let dataListType = reactive([]);
let dataListBusinessDept = reactive([]);

const crudPermission = ref({
  add: ['permission.add.uri']
  // download: ['permission.export.uri']
});
const crud = CRUD(
  {
    title: '应收单',
    url: '/api/CreditQuery/GetListALL',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: { abatedStatus: '0' },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadTableData();
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
      }
    },
    optShow: {
      exportCurrentPage: false
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0,
    form: addRef
  }
);
const selectionValue = computed(() => {
  let value = 0;
  crud.selections.forEach((item) => {
    if (item.value) {
      value += item.value;
    }
  });
  return value;
});
 
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '应收发票',
    url: '/api/InvoiceQuery/GetList',
    method: 'post',
    query: {
      
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef2'
  },
  {
    table: tableRef2
  }
);

const tableRef3 = ref<InstanceType<typeof ElTable>>();
const crud3 = CRUD(
  {
    title: '应用冲销',
    url: '/api/AbatementQuery/GetListByCredit',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef3'
  },
  {
    table: tableRef3
  }
);

// 收入确认
const tableRef4 = ref<InstanceType<typeof ElTable>>();
const crud4 = CRUD(
  {
    title: '收入确认',
    url: '/api/CreditQuery/GetPartialIncome',
    method: 'post',
    query: {
      creditId: crud.rowData.id
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef4'
  },
  {
    table: tableRef4
  }
);
const tableRefDebtDetail = ref<InstanceType<typeof ElTable>>();
const crudDebtDetail = CRUD(
  {
    title: '付款计划',
    url: '/api/DebtQuery/GetListDetail',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRefDebtDetail'
  },
  {
    table: tableRefDebtDetail
  }
);

//高级检索
const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'department',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId',
      props: { queryData: { functionUri: 'metadata://fam' } }
    }
  );
  return [
  {
    key: 'billDateS',
    endDate: 'billDateE',
    label: '单据日期',
    type: 'daterange',
    show: true
  },
  {
    key: 'isSureIncomeDateS',
    endDate: 'isSureIncomeDateE',
    label: '签收日期',
    type: 'daterange',
    show: true
  },
  ...items,
  {
    key: 'customerIds',
    label: '客户',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
    show: true,
    multiple:true,
  },
  {
    key: 'creditTypes',
    label: '应收类型',
    type: 'select',
    multiple: true,
    labelK: 'name',
    valueK: 'id',
    dataList: CreditTypeEnum,
    show: true
  },

  {
    key: 'servicesName',
    label: '业务单元',
    show: true
  },
  {
    key: 'isSureIncome',
    label: '是否确认收入',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: whetherGroup,
    show: true
    },
    {
      key: 'isLossRecognition',
      label: '是否坏账',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: whetherGroup,
      show: true
    },
  {
    key: 'isOverdue',
    label: '是否逾期',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: whetherGroup,
    show: true
  },
  {
    key: 'invoiceStatus',
    label: '发票状态',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: InvoiceStatus,
    show: true
  },
  {
    key: 'invoiceNo',
    label: '发票号',
    show: true
  },
  {
    key: 'billCode',
    label: '应收单号',
    show: true
  },
  {
    key: 'orderNo',
    label: '订单号',
    show: true
  },
  {
    key: 'originOrderNo',
    label: '原始订单号',
    show: true
  },
  {
    key: 'relateCode',
    label: '关联单号',
    show: true
  },
  {
    key: 'saleSystemName',
    label: '销售子系统',
    show: true
  },
  {
    key: 'createdByName',
    label: '创建人',
    show: true
  },
  {
    key: 'deptName',
    label: '部门',
    show: true
  },
  {
    key: 'projectName',
    label: '项目名称',
    show: true
  },
  {
    key: 'customerOrderCode',
    label: '客户订单号',
    show: true
  },
  {
    key: 'customerPersonName',
    label: '订货人',
    show: true
  },
  ]
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

onMounted(() => {
  //获取核算部门下拉列表
  getDepartTree(
    'metadata://fam/credit-debt-Query/routes/creditQuery-view'
  ).then((res) => {
    const getid = (list) => {
      return list.map((i) => {
        return {
          ...i,
          children: i.extraInfo.children
            ? getid(i.extraInfo.children)
            : undefined
        };
      });
    };
    const tree1 = res.data.data;
    dataListBusinessDept.push(...getid(tree1));
  });

  // 表头拖拽必须在这里执行
  tableDrag(tableRef0);
  //tableDrag(tableRef1);
  tableDrag(tableRef2);
  tableDrag(tableRef3);
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  crud.query.billDateS = firstDay.getTime();
  crud.query.billDateE = lastDay.getTime();
  crud.toQuery();
  loadTableData();
});

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud2.query = { creditId: crud.rowData.id };
      crud2.page.size = 2000;
      crud2.toQuery();
      crud3.query = { creditBillCode: crud.rowData.billCode};
      crud3.page.size = 2000;
      crud3.toQuery();
      crudDebtDetail.query = { creditId: crud.rowData.id};
      crudDebtDetail.page.size = 2000;
      crudDebtDetail.toQuery();
      crud4.query = { creditId: crud.rowData.id };
      crud4.toQuery();
    }
  },
  { deep: true }
);

//点击确认收入
const confirmClick = () => {
  ElMessageBox.confirm('此操作将确认收入, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: `/api/CreditQuery/ConfirmReceipt`,
      method: 'POST',
      data: { creditId: crud.rowData.id }
    })
      .then((res) => {
        if (res.data.code === 200) {
          crud.toQuery();
          loadTableData();
          ElMessage({
            showClose: true,
            message: '确认收入成功！',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  });
};
let downLoading = ref(false);
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
  //协调服务导出
  const downloadAsync = (
    url: String,
    fileName: String,
    data: any,
    type: String = 'post'
  ) => {
    downLoading.value = true;
    request({
      url: url,
      method: type,
      data: data,
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
            type: 'success',
            duration: 3 * 1000
          });
          downLoading.value = false;
          return true;
        }
        else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
          downLoading.value = false;
          return false;
        }
      })
      .catch((t) => {
        downLoading.value = false;
      });
  };
//点击反向确认
const reverseConfirmClick = () => {
  ElMessageBox.confirm('此操作将反向确认, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: `/api/CreditQuery/ReverseConfirm`,
      method: 'POST',
      data: { creditId: crud.rowData.id }
    })
      .then((res) => {
        if (res.data.code === 200) {
          loadTableData();
          crud.toQuery();
          ElMessage({
            showClose: true,
            message: '反向确认成功！',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  });
};

// 下载账龄
const downAgingHandler = async () => {
  if (!crud.query.companyId) {
    ElMessage({
      showClose: true,
      message: '请先在搜索框内选择公司',
      type: 'error',
      duration: 3 * 1000
    });
  } else {
    await download(
      'api/CreditQuery/DownloadCreditAge?companyId=' + crud.query.companyId,
      '应收账龄',
      {
        companyId: crud.query.companyId
      },
      'get'
    );
  }
};

const functionUris = {
  confirmReceipt: 'metadata://fam/credit-debt-Query/functions/confirmReceipt',
  export: 'metadata://fam/finance-Credit/functions/excute-export'
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0);
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
 
};

let tabCount = ref({
  nonAbatedCount: 0,
  abatedCount: 0,
  allCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/CreditQuery/GetTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
const searchCrud = () => {
  crud.query.abatedStatus = '-1';
  crud.toQuery();
};
//获取订单详情
const detail = (orderNo) => {
  if(/SA/.test(orderNo)){
    request({
      url: '/api/CreditQuery/GetOrderId',
      data: {
        orderNo:orderNo
      },
      method: 'post'
    }).then((res) => {
      if(res.data.message != '' && res.data.message != null){
        if (window.__MICRO_APP_ENVIRONMENT__) {
          var app = window.microApp.getData();
          var path = '/sell/saleout-oa'
          app.pushState({
            path: path,
            query: {
              id: res.data.message,
              title: '销售出库详情'
            },
          });
        } else {
          console.error('非微前端环境');
        }
      }
    });
  } else {
    ElMessage({
      showClose: true,
      message: "非销售出库订单不支持查看详情",
      type: 'error',
      duration: 3 * 1000
    });
  }
}
const detailRelate = (relateCode) => {
if (window.__MICRO_APP_ENVIRONMENT__) {
  router.goAppPage({
    path: '/inventory/docrelate', // fm为要跳转的子应用前缀
    query: {
      code: relateCode,
      title: '关联单据查询'
    },
    params: {
      // 是否需要刷新目标页面
      __reload: true
    }
  });
} else {
  console.error('非微前端环境');
  }
}
</script>

<style scoped lang="scss">
.mycard_css {
  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  :deep(.el-tabs__content) {
    padding: 0px;
  }
}
</style>
