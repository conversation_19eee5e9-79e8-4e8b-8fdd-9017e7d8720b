﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRecognizeReceiveTempDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RecognizeReceiveTempDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RecognizeReceiveItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: true, comment: "项目Id"),
                    ProjectName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "项目名称"),
                    ProjectCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "项目单号"),
                    RecognizeDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Value = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecognizeReceiveTempDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RecognizeReceiveTempDetail_RecognizeReceiveItem_RecognizeReceiveItemId",
                        column: x => x.RecognizeReceiveItemId,
                        principalTable: "RecognizeReceiveItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "认款单详情");

            migrationBuilder.CreateIndex(
                name: "IX_RecognizeReceiveTempDetail_RecognizeReceiveItemId",
                table: "RecognizeReceiveTempDetail",
                column: "RecognizeReceiveItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RecognizeReceiveTempDetail");
        }
    }
}
