﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments
{
    public class Payment : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>

        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }


        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
        /// <summary>
        /// 付款单类型
        /// </summary>
        public PaymentTypeEnum Type { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 近期订单使用金额
        /// </summary> 
        public decimal? NearOrderUseValue { get; set; }
        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime? PaymentDate { get; set; }


        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary>  
        public string? PayClassify { get; set; }

        /// <summary>
        /// 采购申请Id
        /// </summary>
        public Guid? RelateId { get; set; }

        public AbatedStatusEnum? AbatedStatus { get; set; }
        /// <summary>
        /// 预付方式
        /// </summary>
        public AdvancePayModeEnum? AdvancePayMode { get; set; }

        /// <summary>
        /// 额度金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 币种代码 
        /// </summary>

        public string? CoinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>
        [MaxLength(200)]
        public string? CoinName { get; set; }
        /// <summary>
        /// 人民币金额
        /// </summary> 
        public decimal? RMBAmount { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary>  
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>  
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 批量付款单号
        /// </summary>
        public string? PaymentAutoItemCode { get; set; }
        /// <summary>
        /// 采购合同单号
        /// </summary> 
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>  
        public string? CustomerName { get; set; }

        /// <summary>
        /// 原始付款单号
        /// </summary>
        public string? OriginCode { get; set; }
        /// <summary>
        /// 限定折扣金额
        /// </summary>

        public decimal? LimitedDiscount { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        public string? AttachFileIds { get; set; }
    }

    public class PaymentInputOfKd
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public string CompanyNameCode { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid AgentId { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public long PaymentDateTimestamp { get; set; }

        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary>  
        public string PayClassify { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string BusinessDeptId { get; set; }

        /// <summary>
        /// 币种代码 
        /// </summary>
        public string CoinCode { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary> 
        public decimal RMBAmount { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary>  
        public string ProjectCode { get; set; }

    }
}
