using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 盘点记录查询服务接口
    /// </summary>
    public interface IInventoryRecordQueryService
    {
        /// <summary>
        /// 根据盘点单ID获取所有记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetByInventoryItemIdAsync(Guid inventoryItemId);

        /// <summary>
        /// 根据公司ID和系统月度获取记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetByCompanyIdAndSysMonthAsync(Guid companyId, string sysMonth);

        /// <summary>
        /// 获取待处理的记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<List<InventoryRecord>> GetPendingRecordsAsync(Guid? companyId = null, string? sysMonth = null);

        /// <summary>
        /// 检查指定盘点单的所有记录是否都已完成
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        Task<bool> CheckAllRecordsCompletedAsync(Guid inventoryItemId);

        /// <summary>
        /// 获取盘点记录统计信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        Task<InventoryRecordStatisticsDto> GetStatisticsAsync(Guid companyId, string sysMonth);


    }

    /// <summary>
    /// 盘点记录统计信息DTO
    /// </summary>
    public class InventoryRecordStatisticsDto
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 待处理记录数
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 处理中记录数
        /// </summary>
        public int ProcessingCount { get; set; }

        /// <summary>
        /// 已完成记录数
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// 失败记录数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate => TotalCount > 0 ? (decimal)CompletedCount / TotalCount * 100 : 0;
    }
}
