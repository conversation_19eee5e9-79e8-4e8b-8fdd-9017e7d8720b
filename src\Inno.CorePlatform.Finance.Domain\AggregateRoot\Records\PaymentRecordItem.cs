﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Records
{
    public class PaymentRecordItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 单号日期
        /// </summary>
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 操作人账号中文名
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// 操作人账号
        /// </summary>
        public string UserName { get; set; }

    }
}
