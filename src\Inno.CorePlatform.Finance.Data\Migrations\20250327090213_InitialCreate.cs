﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "LossAgentBearValue",
                table: "Debt",
                type: "decimal(18,2)",
                nullable: true,
                comment: "损失供应商承担金额");

            migrationBuilder.AddColumn<decimal>(
                name: "LossRecognitionValue",
                table: "Credit",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LossAgentBearValue",
                table: "Debt");

            migrationBuilder.DropColumn(
                name: "LossRecognitionValue",
                table: "Credit");
        }
    }
}
