﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("CustomizeInvoiceClassify")]
    [Comment("运营制作开票单归类")]
    [Index("BillCode", IsUnique = true)]
    public class CustomizeInvoiceClassifyPo : BasePo
    {
        /// <summary>
        ///  编号
        /// </summary>
        [Comment("编号")]
        [MaxLength(200)]
        public string BillCode { get; set; }

        public Guid CompanyId { get; set; }
        public Guid CustomerId { get; set; }

        /// <summary>
        ///  开票序号
        /// </summary>
        [Comment("备注")]
        [MaxLength(500)]
        public string? Remark { get; set; }

        [Comment("公司")]
        [MaxLength(500)]
        public string? CompanyName { get; set; }

        [Comment("客户")]
        [MaxLength(500)]
        public string? CustomerName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Comment("状态")]
        public CustomizeInvoiceStatusEnum? Status { get; set; }

        /// <summary>
        /// OARequestId
        /// </summary>
        [Comment("OARequestId")]
        [MaxLength(500)]
        public string? OARequestId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        [Comment("销售子系统名称")]
        public string? SaleSystemName { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 客户邮箱
        /// </summary>
        [Comment("客户邮箱")]
        public string? CustomerEmail { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public CustomizeInvoiceClassifyEnum? Classify { get; set; }

        /// <summary>
        /// 关系Code
        /// </summary>
        [MaxLength(200)]
        public string? RelationCode { get; set; }

        /// <summary>
        /// 开票主体
        /// </summary>
        [Comment("开票主体")]
        [MaxLength(2000)]
        public string? Invoiceofclassfiy { get; set; }

        /// <summary>
        /// 是否推送默认邮箱
        /// </summary>
        [Comment("是否推送默认邮箱")]
        public bool? IsPushDefaultEmail { get; set; }
        /// <summary>
        /// 销售应收子类型
        /// </summary>
        [Comment("1：个人消费者， 2：平台")]
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
    }
}
