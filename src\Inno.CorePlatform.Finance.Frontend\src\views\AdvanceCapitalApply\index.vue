<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>提前付款垫资申请</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation
        :crud="crud"
        :permission="functionUris"
        :hiddenColumns="[]"
        hidden-opts-left
      >
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <inno-split-pane :min-percent="20" split="horizontal" style="padding: 0 15px 15px">
      <template #paneL="{ full, onFull }">
        <inno-query-operation v-model:query-list="queryList" :crud="crud" />
        <inno-crud-operation
          style="padding: 0px 0px 6px 0px"
          :crud="crud"
          rightAdjust
          :permission="functionUris"
          :hiddenColumns="[]"
          border
          hidden-opts-right
        >
          <template #opts-left>
            <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="changeTabClick">
              <el-tab-pane :label="`待提交(${tabModel.waitSubmitCount})`" name="0"></el-tab-pane>
              <el-tab-pane :label="`待审核(${tabModel.waitAuditCount})`" name="1"></el-tab-pane>
              <el-tab-pane :label="`已完成(${tabModel.complateCount})`" name="99"></el-tab-pane>
              <el-tab-pane :label="`已拒绝(${tabModel.refuseCount})`" name="66"></el-tab-pane>
              <el-tab-pane :label="`全部(${tabModel.allCount})`" name="-1"></el-tab-pane>
              <el-tab-pane :label="`我的审批(${tabModel.myCount})`" name="5000"></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <inno-button-tooltip type="primary"
              icon="CirclePlus"
              @click.stop="jumpCreate">
              新增
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              @click.stop="submitApply(crud.rowData.id)">
              提交
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              icon="Edit"
              @click.stop="jumpEdit(crud.rowData.id)">
              编辑
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.id&&crud.rowData.status===0"
              type="primary"
              @click.stop="deleteApply(crud.rowData.id)">
              删除
            </inno-button-tooltip>
            <inno-button-tooltip v-if="crud.rowData.oaRequestId"
              type="primary"
              icon="Document"
              :loading="btnLoading"
              @click="auditProcessClick(crud.rowData)">
              查看审批过程
            </inno-button-tooltip>
            <!-- <inno-button-tooltip v-if="crud.rowData.oaRequestId"
              type="primary"
              icon="Document"
              :loading="btnLoading"
              @click="auditProcessClick(crud.rowData)">
              去审批
            </inno-button-tooltip> -->
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <el-table
          ref="tableItem"
          v-inno-loading="crud.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crud.data"
          stripe
          @sort-change="crud.sortChange"
          @selection-change="crud.selectionChangeHandler"
          @row-click=" (e) => { getDetailData(e);}"
        >
          <el-table-column fixed="left" width="55">
            <template #default="scope">
              <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
            </template>
          </el-table-column>
          <!-- <el-table-column type="index" fixed="left" width="50" /> -->
          <el-table-column fixed="left" label="申请单号" property="billCode" width="150" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link @click.stop="jumpDetail(scope.row.id, scope.row.type)">
                {{ scope.row.billCode }}
              </inno-button-copy>
            </template>
          </el-table-column>
           <el-table-column label="名称" property="name" min-width="150" show-overflow-tooltip />
          <!--<el-table-column label="单据日期" width="100" property="billDate" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
          </el-table-column> -->
          <el-table-column label="公司" width="150" property="companyName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="业务单元" width="200" property="serviceName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" width="200" property="projectName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="取数开始日期" width="100" property="startTime" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.startTime, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="取数结束日期" width="100" property="endTime" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.endTime, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column sortable class-name="isSum" prop="advanceCreditAmount" label="垫资应收金额" width="150">
            <template #default="scope">
              <inno-numeral :value="scope.row.advanceCreditAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column sortable class-name="isSum" prop="advanceCreditTaxAmount" label="含税垫资毛利合计" width="150">
            <template #default="scope">
              <inno-numeral :value="scope.row.advanceCreditTaxAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="状态" property="statusStr" show-overflow-tooltip />
          <el-table-column label="备注" property="remark" show-overflow-tooltip /> -->
          <el-table-column
            label="创建时间"
            min-width="150"
            property="createdTime"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>
            <template #header="{ column }">
              <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
            </template>
            <template #default="scope">{{ scope.row.createdByName }}</template>
          </el-table-column>
          <el-table-column label="附件" fixed="right" property="attachFileIds"  min-width="100">
            <template #default="scope">
                <el-button type="primary" v-if="scope.row.attachFileIds!==undefined && scope.row.attachFileIds !=='' && scope.row.attachFileIds !== null" size="small" @click.stop="showAttachFile(scope.row.attachFileIds, scope.row.id)">
                    查看文件
                </el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="app-page-footer background">
          已选择 {{ crud.selections.length }} 条
          <div class="flex-1" />

          <inno-crud-pagination :crud="crud" />
        </div>
      </template>
      <template #paneR="{ full, onFull }">
        <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
          <template #opts-left>
            <el-tabs v-model="setDetailTab" class="demo-tabs" @tab-change="tabDetailActiveClick">
              <el-tab-pane :label="`付款计划明细`" name="1"></el-tab-pane>
              <el-tab-pane label="货品明细" name="2"></el-tab-pane>
            </el-tabs>
          </template>
          <template #right>
            <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
            </el-button>
          </template>
        </inno-crud-operation>
        <!-- 付款计划明细 -->
        <el-table
          v-if="setDetailTab === '1'"
          ref="tableDetail"
          v-inno-loading="crudPaymentDetail.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crudPaymentDetail.data"
          stripe
          show-summary
          :summary-method="getSummaries"
          @sort-change="crudPaymentDetail.sortChange"
          @selection-change="crudPaymentDetail.selectionChangeHandler"
          @row-click="crudPaymentDetail.rowClick"
        >
          <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip>
            <template #default="scope">
              {{ (crudPaymentDetail.page.page - 1) * crudPaymentDetail.page.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="应付单号" width="200" property="debtBillNo" show-overflow-tooltip fixed="left">
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.debtBillNo }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="账期类型" property="accountPeriodTypeStr" show-overflow-tooltip></el-table-column>
          <el-table-column label="开票日期" property="invoiceTime" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="回款天数" width="100" property="returnDays" show-overflow-tooltip></el-table-column>
          <el-table-column label="预计回款日期" property="estimateReturnDate" width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.estimateReturnDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="实际支付上游日期" property="actualPaymentDate" width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ dateFormat(scope.row.actualPaymentDate, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="垫资天数" width="100" property="advancePaymentDays" show-overflow-tooltip></el-table-column>
          <el-table-column label="月利率(%)" width="100" property="monthRate" show-overflow-tooltip></el-table-column>
          <el-table-column label="供应链金额折扣(%)" width="200" property="financeDiscount" show-overflow-tooltip></el-table-column>
          <el-table-column label="供应商" width="200" property="agentName" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
            </template>
          </el-table-column>
          <!-- <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip></el-table-column>
          <el-table-column label="终端医院" width="200" property="hospitalName" show-overflow-tooltip></el-table-column> -->
          <el-table-column class-name="isSum" label="付款金额" property="paymentAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.paymentAmount" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column class-name="isSum" label="对应应收金额" width="120" property="creditValue" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.creditValue" format="0,0.00" />
            </template>
          </el-table-column>
          <el-table-column label="垫资金额" property="advanceAmount" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.advanceAmount" format="0,0.00" />
            </template>
          </el-table-column>
        <el-table-column class-name="isSum" width="150" sortable prop="advanceTaxAmount" label="含税垫资毛利" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.advanceTaxAmount" format="0,0.00" />
          </template>
        </el-table-column>
        </el-table>
        <el-table
          v-if="setDetailTab === '2'"
          ref="tableDetail"
          v-inno-loading="crudDetail.loading"
          class="auto-layout-table"
          highlight-current-row
          
          border
          :data="crudDetail.data"
          stripe
          show-summary
          :summary-method="getSummaries"
          @sort-change="crudDetail.sortChange"
          @selection-change="crudDetail.selectionChangeHandler"
          @row-click="crudDetail.rowClick"
        >
          <el-table-column label="序号" type="index" width="55" fixed="left" show-overflow-tooltip>
            <template #default="scope">
              {{ (crudDetail.page.page - 1) * crudDetail.page.size + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="业务单据号" width="200" property="businessBillNo" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.businessBillNo }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="货号" property="productNo" show-overflow-tooltip></el-table-column>
        <el-table-column label="品名" property="productName" show-overflow-tooltip></el-table-column>

        <el-table-column label="数量" width="100" property="quantity" show-overflow-tooltip></el-table-column>
        <el-table-column class-name="isSum" label="单位毛利" property="profit" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.profit" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="毛利小计" property="subTotal" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.subTotal" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="originalCost" label="原始成本" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.originalCost" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" sortable prop="originalSalePrice" label="原始售价" min-width="110">
          <template #default="scope">
            <inno-numeral :value="scope.row.originalSalePrice" format="0,0.00" />
          </template>
        </el-table-column>
        </el-table>
      </template>
    </inno-split-pane>
    <el-dialog v-model="setFormVisible" title="审批" width="500">
      <el-form :model="setForm" :rules="setRules" ref="setRef">
        <el-form-item label="审批结果" prop="isApproved">
          <el-radio-group v-model="setForm.isApproved">
            <el-radio value="1">审批通过</el-radio>
            <el-radio   value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input v-model="setForm.auditRemark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveCancel">取消</el-button>
          <el-button type="primary" @click="approveConfirm(setRef)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
     <!-- 查看附件 -->
     <el-dialog v-model="comfile_show"
      title="查看附件"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      modal-class="position-fixed"
      draggable
    >
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ format(scope.row.length) }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="openViewFile(scope.row.id)">
              查看
            </span>

            <!-- <span style="cursor: pointer" @click="deleteFile_letter(scope.row.id)">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, Ref, onBeforeMount, onMounted, onActivated, computed, reactive, provide, nextTick } from 'vue';
import { ElTable, TableColumnCtx, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { useColumnStrategies } from '@inno/inno-mc-vue3/lib/utils/hooks';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { pageRedirect } from '@/utils/utils';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import _, { constant } from 'lodash';
import { formatDate, normalizeDate } from '@vueuse/shared';
import { Loading } from 'element-plus/es/components/loading/src/service';
import { Decimal } from 'decimal.js';
import approveProcess from '@/component/ApproveProcess.vue';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';

interface ListItem {
  value: string;
  label: string;
}
//服务商审批意见
interface AuditOpinionItem {
  auditOpinion: string;
  createTime: string;
  userName: string;
}

const CreditTypeList = [
  {
    id: 4,
    name: '销售应收'
  },
  {
    id: 6,
    name: '经销销售应收'
  },
  {
    id: 2,
    name: '初始应收'
  }
]

const { username } = useUserStore();

const setDetailTab = ref('1');
const detailActiveName = ref('1');
//获取路由
const router = useRouter();
//数据策略权限
const functionUris = {
  audit: 'metadata://sia/aier-prereturn-apply',
};

//选项卡对象
const tabModel = ref({
  waitSubmitCount:0,
  waitAuditCount:0,
  refuseCount:0,
  allCount:0,
  complateCount:0,
  myCount:0,
});
//上传附件
const comfile_show = ref(false);
const showfiles = ref([]);
const approveProcessRef = ref();
//合计统计数据
const detailFooterStatistics = reactive<{
  totalQty: number;
  totalStoreInQty: number;
  totalReceiveQty: number;
  totalTariffAmount: number;
  totalImportAddAmount: number;
  totalUnitCost: number;
  totalSaleAmount: number;
  totalOriginalAmount: number;
  totalEstimatedAmount: number;
  totalInQty:number;
}>({
  totalQty: 0,
  totalStoreInQty: 0,
  totalReceiveQty: 0,
  totalTariffAmount: 0,
  totalImportAddAmount: 0,
  totalUnitCost: 0,
  totalSaleAmount: 0,
  totalOriginalAmount: 0,
  totalEstimatedAmount: 0,
  totalInQty: 0
});
const selectTemplateModel = reactive<{
  type: string;
}>({
  type: '00000009'
});
const dlgProjectVisable = ref(false);
const projectFormModel = {
  projectName: '',
  projectId: ''
};
//退货明细打印是否显示
const showPrintDetail = ref(false);
//选择退货明细打印模板类型
const dialogFormVisible = ref(false);
const setFormVisible = ref(false);
const setBtnLoading = ref(false);
const btnLoading = ref(false);
const downloadBtnLoading = ref(false);
const timeRef = ref();
const setRef = ref();
const producerList = ref([]);
const agentList = ref([]);
const serviceList = ref([]);
const projectList = ref([]);
const debtByCredilist = ref([]);
const creditList = ref([]);
const timeForm = reactive({
  startTime:''
})
const expandRowKeys = ref()
const setForm = reactive({
  auditRemark:undefined,
  isApproved:undefined,
})
const rules = reactive<FormRules<any>>({
  startTime: [
    {
      required: true,
      message: '请选择同步时间',
      trigger: 'change',
    },
  ]
})
// 表单验证
const setRules = reactive<FormRules>({
  isApproved: [
    {
      required: true,
      message: '请选择审批结果',
      trigger: 'change',
    },
  ],
})
const matchProjectList = ref<ListItem[]>([]);
const selectProjectId = ref('');
const showGroupCreateTypeModal = ref(false);
const groupCreateType = ref(1);
const appleType = ref('');
const strategies = ref({
  hidden: []
});
const auditOpinionShow = ref(false);
const auditOpinionList = ref<AuditOpinionItem[]>([]);


interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'paymentAmount') {
      const values = data.map((item) => item.paymentAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  else if (column.property === 'advanceAmount') {
      const values = data.map((item) => item.advanceAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'advanceTaxAmount') {
      const values = data.map((item) => item.advanceTaxAmount || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'subTotal') {
      const values = data.map((item) => item.subTotal || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalCost') {
      const values = data.map((item) => item.originalCost || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalSalePrice') {
      const values = data.map((item) => item.originalSalePrice || 0);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
  });
  return sums;
};


const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const tableInApplyDetail = ref<InstanceType<typeof ElTable>>();

const crud = CRUD(
  {
    title: '应用',
    url: `/api/AdvancePaymentQuery/GetList`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    query: { status: '0' },
    tablekey: 'tablekey',
    userNames: ['createdBy', 'updatedBy'],
    optShow: {
      add: false,
      edit: false,
      del: false,
      download: false,
      reset: true
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
        showProjectModel();
      },
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          // appleType.value = crud.data[0].applyTypeName;
          crud.singleSelection(crud.data[0]);
          if(setDetailTab.value === '1'){
            crudPaymentDetail.query.advancePaymentItemId = crud.data[0].id;
            crudPaymentDetail.toQuery(); 
          }else{
            crudDetail.query.advancePaymentItemId = crud.data[0].id;
            crudDetail.toQuery(); 
          }
          
        }else {
          crudDetail.data = [];
          crudPaymentDetail.data = [];
        }
        getTabCount();
        // getMyAuditTabCount();
      }
    },
    pageConfig: {
      // 分页参数按项目可以单独配置
      pageIndex: 'pageIndex',
      pageSize: 'pageSize'
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
const crudDetail = CRUD(
  {
    title: '应用',
    url: `api/AdvancePaymentQuery/GetProductDetails`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    tablekey: 'tablekeyCopy', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    page: {
      isPage: false
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {

      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const crudPaymentDetail = CRUD(
  {
    title: '付款计划明细',
    url: `/api/AdvancePaymentQuery/GetDebtDetails`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    sort: ['createdTime,desc'],
    method: 'post',
    query: {},
    tablekey: 'tablekeyInApply', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
      },
      [CRUD.HOOK.afterRefresh]: (_crud, data) => {
        // detailFooterStatistics.totalInQty = data.footer.totalQty;
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableInApplyDetail
  }
);

const getTabCount = () => {
  request({
    url: `/api/AdvancePaymentQuery/GetTabCount`,
    method: 'post',
    data: crud.query
  })
    .then((res) => {
      // tabModel.value.tabAwaitSubAudit = res.data.data.awaitSubAuditCount;
      tabModel.value.waitSubmitCount = res.data.data.waitSubmitCount;
      tabModel.value.waitAuditCount = res.data.data.auditingCount;
      tabModel.value.allCount = res.data.data.allCount;
      tabModel.value.complateCount = res.data.data.completedCount;
      tabModel.value.myCount = res.data.data.myAuditCount;
      tabModel.value.refuseCount = res.data.data.refuse;
    })
    .catch((error) => {
      console.log(error);
    });
};



const changeTabClick = () => {
  crudDetail.data = [];
  crudPaymentDetail.data = [];
  crud.toQuery();
};

const getDetailData = (e:any) => {
  crud.singleSelection(e);
  if(setDetailTab.value === '1'){
    crudPaymentDetail.query.advancePaymentItemId = crud.rowData.id;
    crudPaymentDetail.toQuery(); 
  }else{
    crudDetail.query.advancePaymentItemId = crud.rowData.id;
    crudDetail.toQuery(); 
  }
  
};

function showProjectModel() {
  dlgProjectVisable.value = true;
}

const props = defineProps({
  __refresh: Boolean
});

onBeforeMount(() => {
  crud.toQuery();
});

onMounted(() => {
  // 表头拖拽必须在
  tableDrag(tableItem);
  tableDrag(tableDetail);
  // tableDrag(tableInApplyDetail);
  const strategiesConfig = {
    functionUri: 'metadata://sia/storein-apply/routes/index-search',
    url:
      window.gatewayUrl +
      `v1.0/sia-backend/api/purchaseapply/getStrategy?functionUri=metadata://sia/storein-apply/routes/index-search`,
    method: 'get'
  };
  strategies.value = useColumnStrategies(strategiesConfig);
  // test();
});

onActivated(() => {
  crud.toQuery();
});
//高级检索
const queryList = computed(() => [
  {
    key: 'billCode',
    label: '申请单号',
    show: true
  },
  // {
  //   key: 'orderNo',
  //   label: '订单号',
  //   show: true
  // },
  // {
  //   key: 'salesOrderNo',
  //   label: '销售订单号',
  //   show: true
  // },
  // {
  //   key: 'originalOrderNo',
  //   label: '原始订单号',
  //   show: true
  // },
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: false
  },
  {
    key: 'customerId',
    label: '客户',
    show: true,
    type: 'remoteSelect',
    method: 'post',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    placeholder: '客户名称搜索'
  },
  // {
  //   key: 'creditType',
  //   label: '应收类型',
  //   type: 'select',
  //   labelK: 'name',
  //   valueK: 'id',
  //   multiple: false,
  //   dataList: CreditTypeList,
  //   show: true
  // },
  // {
  //   key: 'invoiceTitle',
  //   label: '发票抬头',
  //   show: false
  // },
  // {
  //   key: 'taxpayerNo',
  //   label: '纳税人识别号',
  //   show: false
  // },
  // {
  //   key: 'invoicePerson',
  //   label: '开票人',
  //   method: 'post',
  //   multiple: false,
  //   type: 'remoteSelect',
  //   url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
  //   placeholder: '用户名称搜索',
  //   valueK: 'name',
  //   labelK: 'displayName',
  //   props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
  //   slots: {
  //     option: ({ item }) => (
  //       <>
  //         <span>{item.displayName}</span>
  //         <span style="float:right">{item.name}</span>
  //       </>
  //     )
  //   }
  // },
  // {
  //   key: 'applyDateStart',
  //   endDate: 'applyDateEnd',
  //   label: '申请日期',
  //   type: 'daterange',
  //   defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
  //   props: { format: 'YYYY-MM-DD' }
  // },
  // {
  //   key: 'invoiceDateStart',
  //   endDate: 'invoiceDateEnd',
  //   label: '开票日期',
  //   type: 'daterange',
  //   defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
  //   props: { format: 'YYYY-MM-DD' }
  // },  
  
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

//切换
const tabDetailActiveClick = async (tab: any) => {
  if (tab === '2' && crud.rowData.id) {
    crudDetail.query.advancePaymentItemId = crud.rowData.id;
    crudDetail.toQuery();
    // crud.singleSelection(crud.rowData);
  } else if(tab === '1' && crud.rowData.id) {
    crudPaymentDetail.query.advancePaymentItemId = crud.rowData.id;
    crudPaymentDetail.toQuery();
    // crud.singleSelection(crud.rowData);
  }else {
    crudPaymentDetail.data = [];
    crudDetail.data = [];
  }
};

const approveConfirm = async(formEl:any) =>{
  if (!formEl) return
    let checkResult = await formEl.validate((valid:any, fields:any) => {return valid})
     if (!checkResult) {
        return false;
    }
  let postData = {
    id: crud.rowData.id,
    isApproved: setForm.isApproved === '1'? true : setForm.isApproved === '2' ? false :null,
    auditRemark: setForm.auditRemark
  }
  request({
    url: `/api/AierPreReturnApply/Audit`,
    method: 'POST',
    data: postData
  }).then(res=>{
    if(res.data.code ===200){
      ElMessage.success({ showClose: true, message: '审批成功！' });
      crud.toQuery();
      setFormVisible.value = false;
      approveCancel();
    }else{
      ElMessage.error({ showClose: true, message: res.data.message });
    }
  }).catch((error)=>{
    ElMessage.error({ showClose: true, message: error });
  })
  
 
}
  const submitApply =(id:any) => {
    let postData = {
      id: id
    }
    request({
      url: `/api/AdvancePaymentQuery/SubmitOA`,
      method: 'post',
      data: postData
    }).then(res => {
      if (res.data.code === 200) {
        ElMessage.success({ showClose: true, message: '提交成功！' });
        crud.toQuery();
      } else {
        ElMessage.error({ showClose: true, message: res.data.message });
      }
    }).catch((error) => {
      ElMessage.error({ showClose: true, message: error });
    })
  }
  const deleteApply = (id:any) => {
    ElMessageBox.confirm('此操作将删除当前单据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      let postData = {
        advancePaymentItemId: id
      }
      request({
        url: `/api/AdvancePaymentQuery/DeleteItemById`,
        method: 'post',
        data: postData
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success({ showClose: true, message: '删除成功！' });
          crud.toQuery();
        } else {
          ElMessage.error({ showClose: true, message: res.data.message });
        }
      }).catch((error) => {
        ElMessage.error({ showClose: true, message: error });
      })
    });
  };

const approveCancel = () =>{
  setFormVisible.value = false;
  setForm.isApproved = undefined;
  setForm.auditRemark = undefined;
}
const openApprove = () =>{
  if(!crud.rowData.id || crud.rowData.id === ''){
    ElMessage.warning({ showClose: true, message: '请选择需要审批的数据！' });
    return false;
  }
  setFormVisible.value = true;
}
const jumpCreate = () =>{
  router.push({
    path: '/advanceCapitalApply/create',
    query: {
      __page_id: `${new Date().getTime()}`,
    },
    params: {
      __reload: true
    }
  });
}
const jumpEdit = (id:any) =>{
  router.push({
    path: '/advanceCapitalApply/edit',
    query: {
      id: id,
      __page_id: `${new Date().getTime()}`,
    },
    params: {
      __reload: true
    }
  });
}
//审批过程
const auditProcessClick = (row) => {
  //审批过程
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
const openViewFile = (ids:any) =>{
    let arr = ids.split(",");
    FileViewer.show(arr,  0,  {});
    // FileViewer.show([ids], 0, {});
}
const jumpDetail = (id: string, type: number) => {
  let routeName = '/advanceCapitalApply/detail';
  router.push({
    path: routeName,
    query: {
      id: id,
      __page_id: `${new Date().getTime()}`
    },
    params: {
      __reload: true
    }
  });
};
 //附件
 const showAttachFile = (fileIds:any,id:any) => {
  request({
    url: `/api/AdvancePaymentQuery/GetAttachFile`,
    method: 'POST',
    data: {
      advancePaymentItemId: id,
      fileIds:fileIds
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_show.value = true;
        showfiles.value = res.data.data;
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
    
  };
  //文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
};
const handleClick = (tab: any, event: Event) => {
  console.log(tab, event)
}
</script>
<style scoped lang="scss">
:deep(.el-tabs__item) {
  padding: 0 12px;
}
:deep(.right-adjust) {
  display: block;
  max-width: 42%;
  display: flex;
  flex-wrap: wrap;
  height: auto;
  justify-content: flex-end;
  margin-left: 20px;
  align-items: center;
}
:deep(.crud-opts-border) {
  justify-content: space-between;
}
/* :deep(.el-button) {
  margin-top: 8px;
} */
:deep(.crud-opts) {
  justify-content: space-between;
}
.detail-box{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-height: 40%;
  .detail-item{
    width: 49%;
  }
}
</style>
