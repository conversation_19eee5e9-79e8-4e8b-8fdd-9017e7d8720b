﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills
{
    public class InputBillDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }
        /// <summary>
        /// 进项发票Id
        /// </summary>
        public Guid InputBillId { get; set; }
    }
}
