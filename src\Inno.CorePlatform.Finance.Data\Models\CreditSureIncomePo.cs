﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    ///  应收收入确认
    /// </summary>
    [Table("CreditSureIncome")]
    public class CreditSureIncomePo : BasePo
    {

        [Comment("应付id")]
        public Guid CreditId { get; set; }

        [Comment("应收id")]
        [ForeignKey("CreditId")]
        public virtual CreditPo Credit { get; set; }

        /// <summary>
        /// 收入确认金额(含税)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        /// <summary>
        /// 收入确认金额(不含税含税)
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? NoTaxValue { get; set; }
        /// <summary>
        /// 收入确认日期
        /// </summary>
        public DateTimeOffset SureIncomeDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 含税成本
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// 不含税成本
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? NoTaxCost { get; set; }
    }
}
