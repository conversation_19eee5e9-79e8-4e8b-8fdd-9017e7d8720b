﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 提前付款垫资
    /// </summary>    
    [Table("AdvancePaymentItem")]
    public class AdvancePaymentItemPo : BasePo
    {
        /// <summary>
        /// 提前付款名称
        /// </summary>
        [MaxLength(200)]
        public string Name { get; set; }
        /// <summary>
        /// 单号
        /// </summary> 
        [Comment("单号")]
        [MaxLength(200)]
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        [Column(TypeName = "date")]
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        [MaxLength(200)]
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        [MaxLength(200)]
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        [MaxLength(200)]
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 取数开始时间
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 取数结束时间
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 回款天数
        /// </summary>
        public int? Day { get; set; }

        /// <summary>
        /// 月利率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MonthRate { get; set; }
        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }
        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }
        /// <summary>
        /// 付供应商货款日期
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime? PaySupplierGoodsDate { get; set; }
        /// <summary>
        /// 垫资应收金额合计 
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AdvanceCreditAmount { get; set; }
        /// <summary>
        /// 含税垫资毛利合计
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AdvanceCreditTaxAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 垫资单应付明细
        /// </summary>
        public virtual List<AdvancePaymentDebtDetailPo> AdvancePaymentDebtDetails { get; set; }

        /// <summary>
        /// 提前付款垫资货品明细
        /// </summary>
        public virtual List<AdvancePaymentProductDetailPo> AdvancePaymentProductDetails { get; set; }
         
        /// <summary>
        /// 状态
        /// </summary>
        public AdvancePaymentStatusEnum Status { get; set; }
        /// <summary>
        /// 流程请求Id
        /// </summary>

        [MaxLength(200)]
        public string? OARequestId { get; set; }

        /// <summary>
        /// 附件ids
        /// </summary>
        public string? AttachFileIds { get; set; }
    }
}
