<template>
  <el-card
    shadow="never"
    :body-style="{ padding: '10px' }"
    class="zx-box-card"
    style="padding-bottom: 0px; margin-bottom: 10px"
  >
    <template #header>
      <div class="card-header" style="width: 100%">
        <span>基本信息</span>
      </div>
    </template>
    <div>
      <el-form
        ref="refForm"
        :model="model.bindModel.basic"
        :rules="rules"
        label-position="right"
        label-width="130px"
        :inline="false"
      >
        <el-row :gutter="20">
          <el-col :span="12" :offset="0">
            <el-form-item label="申请单号">
              <el-input
                v-model="model.bindModel.basic.billCode"
                :disabled="true"
                placeholder="保存后自动生成"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="单据日期">
              <el-date-picker
                v-model="model.bindModel.basic.billDate"
                type="date"
                :disabled="true"
                placeholder="单据日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="model.bindModel.basic.name"
                placeholder="请输入名称"
                :disabled="model.controlModel.isAllow"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="核算部门" prop="businessDeptId" :rules="rules.businessDeptId">
              <inno-department-select
                v-model="model.bindModel.basic.businessDeptId"
                v-model:path="model.bindModel.basic.businessDeptFullPath"
                v-model:fullName="model.bindModel.basic.businessDeptFullName"
                v-model:shortName="model.bindModel.basic.businessDeptShortName"
                v-model:deptShortName="model.bindModel.basic.businessDeptShortName"
                functionUri="metadata://pm/project-apply/routes/projectApply-index-search"
                :disabled="model.controlModel.isAllow"
                isShort
                @change="businessDeptsChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="公司" prop="company">
              <el-select
                v-model="model.bindModel.basic.company"
                value-key="id"
                placeholder="请选择公司"
                :disabled="model.controlModel.isAllow"
                clearable
                filterable
                @change="changeSelectCompany"
              >
                <el-option
                  v-for="item in model.dataSource.basic.companys"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="project">
              <el-select
                v-model="model.bindModel.basic.project"
                value-key="id"
                placeholder="请选择项目"
                :disabled="model.controlModel.isAllow"
                clearable
                filterable
                fit-input-width
              >
                <el-option
                  v-for="item in model.dataSource.basic.projects"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="客户" prop="customer">
              <el-select
                v-model="model.bindModel.basic.customer"
                value-key="id"
                placeholder=""
                :disabled="model.controlModel.isAllow"
                clearable
                filterable
              >
                <el-option
                  v-for="item in model.dataSource.basic.customers"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="12">
            <el-form-item label="项目" prop="project">
              <inno-remote-select
                v-model="model.bindModel.basic.project"
                :queryData="{status:2,type:'0000400008',companyId:model.bindModel.basic.company?.id}"
                default-first-option
                isObject
                placeholder="请选择项目"
                :url="gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta'"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="客户" prop="customer">
              <inno-remote-select
                v-model="model.bindModel.basic.customer"
                :max-collapse-tags="1"
                :disabled="model.controlModel.isAllow"
                isObject
                :is-guid="2"
                placeholder="请选择客户"
                :queryData="{
                  functionUri: 'metadata://fam',
                  projectId:model.bindModel.basic.project?.id
                }"
                labelK="name"
                valueK="id"
                :url="`${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/GetCustomersByProjectId?projectId=${model.bindModel.basic.project?.id??''}`"
              />
            </el-form-item>
          </el-col>
          
        
          <el-col :span="12">
            <el-form-item label="业务单元" prop="service">
              <inno-remote-select
                v-model="model.bindModel.basic.service"
                :disabled="model.controlModel.isAllow"
                :is-guid="2"
                isObject
                placeholder="请选择业务单元"
                labelK="businessUnitName"
                valueK="businessUnitID"
                :props = "{ KeyWord: 'nameLike'}"
                :queryData="{ functionUri: 'metadata://fam' }"
                :url="`${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="取数开始日期" prop="startTime">
              <el-date-picker
                v-model="model.bindModel.basic.startTime"
                :disabled="model.controlModel.detailIsAllow"
                type="date"
                placeholder="取数开始日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="取数结束日期" prop="endTime">
              <el-date-picker
                v-model="model.bindModel.basic.endTime"
                :disabled="model.controlModel.detailIsAllow"
                type="date"
                placeholder="取数结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="回款天数" prop="day">
              <el-input-number v-model="model.bindModel.basic.day" :disabled="model.controlModel.detailIsAllow" placeholder="请输入回款天数" :min="0" :controls="false" style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="月利率" prop="monthRate">
              <el-input-number v-model="model.bindModel.basic.monthRate" :disabled="model.controlModel.detailIsAllow" placeholder="请输入月利率" :min="0" :max="100" :controls="false" style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0">
            <el-form-item label="确认支付日期方式" prop="confirmPaymentDateMode">
                <el-select
                v-model="model.bindModel.basic.confirmPaymentDateMode"
                :disabled="model.controlModel.detailIsAllow"
                placeholder="确认支付日期方式"
                fit-input-width
                clearable
                filterable
              >
                <el-option
                  v-for="item in ConfirmPaymentDateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0" v-if="model.bindModel.basic.confirmPaymentDateMode === 1">
            <el-form-item label="付供应商货款天数" prop="paySupplierGoodsDay">
              <el-input-number v-model="model.bindModel.basic.paySupplierGoodsDay" :disabled="model.controlModel.detailIsAllow" placeholder="请输入付供应商货款天数" :min="0" :controls="false" style="width: 100%;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="0" v-if="model.bindModel.basic.confirmPaymentDateMode === 2">
            <el-form-item label="付供应商货款日期" prop="paySupplierGoodsDate">
             <el-date-picker
                v-model="model.bindModel.basic.paySupplierGoodsDate"
                type="date"
                :disabled="model.controlModel.detailIsAllow"
                placeholder="付供应商货款日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注">
              <el-input
                v-model="model.bindModel.basic.remark"
                type="textarea"
                :rows="3"
                placeholder=""
                :maxlength="-1"
                :show-word-limit="false"
                :autosize="{ minRows: 2, maxRows: 4 }"
                :disabled="model.controlModel.detailIsAllow"
              >
              </el-input>
            </el-form-item>
          </el-col>
          
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" :offset="0">
            <el-form-item label="附件">
              <inno-file-uploader
                v-model="model.bindModel.basic.attachFileIds"
                :appId="model.bindModel.basic.appId"
                :bizType="model.bindModel.basic.bizType"
                :fileMode="'large'"
                list-type="text"
                style="width: 100%;"
                :drag="!model.bindModel.basic.disabled"
                :disabled="model.bindModel.basic.disabled"
                multiple
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  拖动文件到这 或者
                  <em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip"></div>
                </template>
                <template #fileList="{ list, remove, preview, download }">
                  <el-table :data="list" border style="width: 100%">
                    <el-table-column prop="name" label="文件名称" show-overflow-tooltip/>
                    <el-table-column prop="length" label="文件大小">
                      <template #default="scope">
                        {{ scope.row.length + '字节' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="ext" label="文件类型">
                    </el-table-column>
                    <el-table-column prop="uploadedByName" label="操作人" />
                    <el-table-column prop="uploadedTime" label="操作时间">
                      <template #default="scope">
                        {{ dateFormat(scope.row.uploadedTime) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作">
                      <template #default="{ row }">
                        <el-button
                          v-if="!model.bindModel.basic.disabled"
                          type="primary"
                          @click="remove(row)"
                        >
                          删除
                        </el-button>
                        <el-button type="primary" @click="preview(row)">预览</el-button>
                        <el-button
                          type="primary"
                          @click="download(row)"
                        >
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </inno-file-uploader>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </el-card>
</template>
<script setup lang="tsx">
import { inject, onMounted, reactive, ref } from 'vue';
import { FormRules, FormInstance } from 'element-plus';
import {
  AdvanceCapitalApplyVModel,
  IProjectListItem,
  ICompany,
  CONST_ADVANCECAPITALAPPLY_INJECTIONKEY
} from '../models/AdvanceCapitalApplyVModel';
import { CreditTypeEnum } from '@/api/metaInfo';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { queryCheckedByDept, GetProjectInfoByCompanyId } from '@/api/bdsData';
import { getCompanySysMonth } from '../apis/api';
import request from '@/utils/request';
const model = inject<AdvanceCapitalApplyVModel>(CONST_ADVANCECAPITALAPPLY_INJECTIONKEY) as AdvanceCapitalApplyVModel;
// 定义表单
const refForm = ref<FormInstance>();
model.controlModel.basicForm = refForm;
// const GetProjectURL = '/api/bff/GetProjectList?type=2&firstBusinessName=寄售';
const GetProjectURL = '/api/bff/SearchRunProjects';

const ConfirmPaymentDateList = [
  {
    id: 1,
    name: '按固定天数'
  },
  {
    id: 2,
    name: '按固定日期'
  },
]
// 表单验证
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: '请输入名称',
      trigger: 'change'
    }
  ],
  company: [
    {
      required: true,
      message: '请选择公司',
      trigger: 'change'
    }
  ],
  businessDeptId: [
    {
      required: true,
      message: '请选择核算部门',
      trigger: 'change'
    }
  ],
  project: [
    {
      required: true,
      message: '请选择项目',
      trigger: 'change'
    }
  ],
  customer: [
    {
      required: true,
      message: '请选择客户',
      trigger: 'change'
    }
  ],
  service: [
    {
      required: true,
      message: '请选择业务单元',
      trigger: 'change'
    }
  ],
  // startTime: [
  //   {
  //     required: true,
  //     message: '请选择取数开始日期',
  //     trigger: 'change'
  //   }
  // ],
  // endTime: [
  //   {
  //     required: true,
  //     message: '请选择取数结束日期',
  //     trigger: 'change'
  //   }
  // ],
  startTime: [
    { 
      validator: (rule, value, callback) => {
        if (model.bindModel.basic.endTime && new Date(value) > new Date(model.bindModel.basic.endTime)) {
          callback(new Error('取数开始日期不能晚于取数结束日期'));
        }else if(!value || value ==='' || value ===null){
          callback(new Error('请输入取数开始日期'));    
        } else {
          callback();
        }
      },
      required: true,
      trigger: 'change'
    }
  ],
  endTime: [
    { 
      validator: (rule, value, callback) => {
        if (model.bindModel.basic.startTime && new Date(value) < new Date(model.bindModel.basic.startTime)) {
          callback(new Error('取数结束日期不能早于取数开始日期'));
        }else if(!value || value ==='' || value ===null){
          callback(new Error('请输入取数结束日期'));    
        } else {
          callback();
        }
      },
      required: true,
      trigger: 'change'
    }
  ],
  day: [
    {
      required: true,
      message: '请输入回款天数',
      trigger: 'change'
    }
  ],
  monthRate: [
    {
      required: true,
      message: '请输入月利率',
      trigger: 'change'
    }
  ],
  confirmPaymentDateMode: [
    {
      required: true,
      message: '请输入确认支付日期方式',
      trigger: 'change'
    }
  ],
  paySupplierGoodsDay: [
    {
      required: true,
      message: '请输入付供应商货款天数',
      trigger: 'change'
    }
  ],
});

type Props = {
  disabled: boolean;
  isView: boolean;
};
const props = withDefaults<Props, any>(defineProps<Props>(), {
  disabled: () => {
    return false;
  },
  isView: () => {
    return false;
  }
});

const changeSelectCompany = async (selectItem: ICompany) => {
  console.log(selectItem,'============11111111111111');
  if(selectItem.id){
    GetProjectInfoByCompanyId(selectItem.id).then((res:any) => {
      model.dataSource.basic.projects = res.data.data;
    });
    request({
      url: '/api/InvoiceReceipts/GetCustomers',
      method: 'POST',
      data: {
        companyId: selectItem.id,
      }
    })
    .then((res) => {
       model.dataSource.basic.customers= res.data.data;
      // customer_arrayA.value = res.data.data;
      // console.log(JSON.stringify(customer_arrayA.value))
    })
  }
};

const businessDeptsChange = async(node, notclear) => {
  if (!notclear) {
    model.bindModel.basic.company = { id: '', name: '', extraInfo: { nameCode: '' } };
  }
  if (node !== undefined) {
    //  formData.id = node.data.id;
   await queryCheckedByDept(node).then((res) => {
      model.dataSource.basic.companys = res.data.data;
    });
  }
};
// 如果路由参数有传projectId，则根据id设置默认选择项目
const setDefaultProject = () => {
  // if (model.propsModel.projectId) {
  //   if (model.dataSource.basic.projects) {
  //     let item = model.dataSource.basic.projects.find((el) => {
  //       return el.id === model.propsModel.projectId;
  //     });
  //     if (item) {
  //       changeProject(item);
  //     }
  //   }
  // }
};
onMounted(() => {
  // 初始化时加载数据
  setDefaultProject();
});
const allow = ref(false);
const allowEdit = (flag: boolean) => {
  allow.value = flag;
};
defineExpose({
  allowEdit
});
</script>
