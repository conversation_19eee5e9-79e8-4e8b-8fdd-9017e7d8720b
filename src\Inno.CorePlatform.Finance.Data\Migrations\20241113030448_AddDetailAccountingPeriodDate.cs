﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddDetailAccountingPeriodDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RowNo",
                table: "OutputInvoice",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AccountingPeriodDate",
                table: "DebtDetail",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AttachFileIds",
                table: "DebtDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "附件Ids");

            migrationBuilder.AddColumn<int>(
                name: "AuditStatus",
                table: "DebtDetail",
                type: "int",
                nullable: true,
                comment: "审核状态");

            migrationBuilder.AddColumn<string>(
                name: "OARequestId",
                table: "DebtDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "OARequestId");

            migrationBuilder.AddColumn<string>(
                name: "Remark",
                table: "DebtDetail",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RowNo",
                table: "OutputInvoice");

            migrationBuilder.DropColumn(
                name: "AccountingPeriodDate",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "AttachFileIds",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "AuditStatus",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "OARequestId",
                table: "DebtDetail");

            migrationBuilder.DropColumn(
                name: "Remark",
                table: "DebtDetail");
        }
    }
}
