using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 盘点记录数据模型
    /// </summary>
    [Table("InventoryRecord")]
    public class InventoryRecordPo : BasePo
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        [Comment("盘点单ID")]
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        [Comment("公司ID")]
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        [MaxLength(10)]
        [Comment("系统月度")]
        public string SysMonth { get; set; }

        /// <summary>
        /// 盘点动作类型
        /// </summary>
        [Comment("盘点动作类型")]
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 动作名称
        /// </summary>
        [MaxLength(100)]
        [Comment("动作名称")]
        public string ActionName { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        [MaxLength(500)]
        [Comment("盘点单号")]
        public string? InventoryCode { get; set; }

        /// <summary>
        /// 状态：0-待处理，1-处理中，2-已完成，99-失败
        /// </summary>
        [Comment("状态：0-待处理，1-处理中，2-已完成，99-失败")]
        public int Status { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        [Comment("重试次数")]
        public int RetryCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(2000)]
        [Comment("错误信息")]
        public string? ErrorMessage { get; set; }
    }
}
