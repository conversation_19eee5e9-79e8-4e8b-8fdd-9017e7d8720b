﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class updateAdvancePaymentService : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MatchPrecision",
                table: "MergeInputBillSubmitDetail",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "MatchQuantity",
                table: "MergeInputBillSubmitDetail",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "MergeInputBillDetailId",
                table: "MergeInputBillSubmitDetail",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DetailClassify",
                table: "AdvancePaymentItem",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "OARequestId",
                table: "AdvancePaymentItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ServiceId",
                table: "AdvancePaymentItem",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceName",
                table: "AdvancePaymentItem",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "AdvancePaymentItem",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MatchPrecision",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "MatchQuantity",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "MergeInputBillDetailId",
                table: "MergeInputBillSubmitDetail");

            migrationBuilder.DropColumn(
                name: "DetailClassify",
                table: "AdvancePaymentItem");

            migrationBuilder.DropColumn(
                name: "OARequestId",
                table: "AdvancePaymentItem");

            migrationBuilder.DropColumn(
                name: "ServiceId",
                table: "AdvancePaymentItem");

            migrationBuilder.DropColumn(
                name: "ServiceName",
                table: "AdvancePaymentItem");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AdvancePaymentItem");
        }
    }
}
