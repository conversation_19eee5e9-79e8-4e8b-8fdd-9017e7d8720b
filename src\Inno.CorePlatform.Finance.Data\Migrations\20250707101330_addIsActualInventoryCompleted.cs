﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addIsActualInventoryCompleted : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActualInventoryCompleted",
                table: "InventoryItem",
                type: "bit",
                nullable: false,
                defaultValue: false,
                comment: "是否完成实盘");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActualInventoryCompleted",
                table: "InventoryItem");
        }
    }
}
