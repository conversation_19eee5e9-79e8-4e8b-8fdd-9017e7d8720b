﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class CreditProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PaymentAutoItemCode",
                table: "Payment",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "批量付款单号");

            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "Credit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号");

            migrationBuilder.AddColumn<Guid>(
                name: "ProjectId",
                table: "Credit",
                type: "uniqueidentifier",
                nullable: true,
                comment: "项目Id");

            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "Credit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目名称");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaymentAutoItemCode",
                table: "Payment");

            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "Credit");

            migrationBuilder.DropColumn(
                name: "ProjectId",
                table: "Credit");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "Credit");
        }
    }
}
