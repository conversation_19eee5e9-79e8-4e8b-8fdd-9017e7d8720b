﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCreditDetailOriginalid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Specification",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "原始规格",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ProductName",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "原始品名",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PackUnit",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "原始单位",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PackSpec",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                comment: "包装规格",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "Originalid",
                table: "CreditDetail",
                type: "uniqueidentifier",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Originalid",
                table: "CreditDetail");

            migrationBuilder.AlterColumn<string>(
                name: "Specification",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "原始规格");

            migrationBuilder.AlterColumn<string>(
                name: "ProductName",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "原始品名");

            migrationBuilder.AlterColumn<string>(
                name: "PackUnit",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "原始单位");

            migrationBuilder.AlterColumn<string>(
                name: "PackSpec",
                table: "CreditDetail",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "包装规格");
        }
    }
}
