﻿using Inno.CorePlatform.Finance.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("AdvanceFundBusinessCheckItem")]
    [Comment("垫资盘点单")]
    public class AdvanceFundBusinessCheckItemPO:BasePo
    {
        public DateTime BillDate { get; set; }

        [MaxLength(100)]
        public string Code { get; set; }
        
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; }

        public DateTime CreateTime { get; set; }

        [MaxLength(50)]
        public string Operator { get; set; }

        [MaxLength(50)]
        public string UserName { get; set; }

        /// <summary>
        /// 垫资盘点明细
        /// </summary>
        public virtual List<AdvanceFundBusinessCheckDetailPO>? AdvanceRecordDetail { get; set; }

    }
}
