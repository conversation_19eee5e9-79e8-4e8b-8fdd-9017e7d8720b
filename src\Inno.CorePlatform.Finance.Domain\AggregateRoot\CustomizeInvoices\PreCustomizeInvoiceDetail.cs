﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    public class PreCustomizeInvoiceDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 预开票Id
        /// </summary>
        public Guid PreCustomizeInvoiceItemId { get; set; }


        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }


        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 行性质（商品行、折扣行）
        /// </summary>
        public string? Tag { get; set; }

        /// <summary>
        /// 是否高值
        /// </summary>
        public int? IFHighValue { get; set; }
    }
}
