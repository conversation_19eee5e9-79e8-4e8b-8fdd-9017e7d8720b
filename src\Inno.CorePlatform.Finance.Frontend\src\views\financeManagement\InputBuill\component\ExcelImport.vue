<template>
  <el-dialog :modelValue="visibel" :title="title" draggable @close="close">
    <el-upload
      ref="elUploadRef"
      class="upload-demo"
      :before-upload="handleBeforeUpload"
      drag
      :action="uploadAction"
      :limit="1"
      :headers="{
        Authorization: `Bearer ${Token}`
      }"
      show-file-list
      :auto-upload="false"
      :on-error="handleError"
      :on-success="handleSuccess"
      :accept="accept"
    >
      <el-icon class="el-icon--upload">
        <upload-filled />
      </el-icon>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip" :style="tipStyle">{{ tip }}</div>
        <slot name="importTemplate"></slot>
      </template>
    </el-upload>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" v-if="props.title==='按Excel导入'" @click="confirmSubmit">确定并提交</el-button>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submit()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElTable, ElMessage, ElMessageBox } from 'element-plus';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import request from '@/utils/request';
const props = defineProps({
  visibel: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  // 上传文件的地址
  action: {
    type: String,
    default: ''
  },
  // 提示
  tip: {
    type: String,
    default: ''
  },
  // 自定义tip区域的样式
  tipStyle: {
    type: Object,
    default: null
  },
  // 限制上传的文件
  accept: {
    type: String,
    default: '.xlsx,.xls'
  }
});
  const uploadAction = ref('')
  const emit = defineEmits(['update:visibel', 'submitSuccess', 'confirmSubmitHandler']);
const Token = ref(window.microApp.getData().token);

const elUploadRef = ref();
const close = () => {
  elUploadRef.value.clearFiles();
  emit('update:visibel');
};
// 上传前拦截
const handleBeforeUpload = (file) => {
  // 验证文件类型是否在限制之内
  const fileType = file.name.split('.').pop().toLowerCase(); // 获取文件扩展名并转为小写
  if (props.accept.indexOf(fileType) === -1) { // 检查扩展名是否在允许列表中
    ElMessage({
      showClose: true,
      message: '文件类型不正确',
      type: 'error',
      duration: 3 * 1000
    });
    return false; // 返回 false 阻止上传
  } else {
    // 如果文件类型正确，可以继续上传流程...（这里省略）
    return true; // 返回 true 允许上传
  }
};
const submitType = ref('');
  const submit = (file) => {
    submitType.value = 1;
  elUploadRef.value.submit();
};
  const confirmSubmit = () => {
    submitType.value = 2;
    emit('confirmSubmitHandler');
  };
  const uploadRefSubmit = async () => {
    await elUploadRef.value.submit();
   return true
  }
// 文件上传成功回调
const handleSuccess = (res) => {
  res.submitType = submitType
  emit('submitSuccess', res);
  if (res.code === 200) {
    close();
  }
};
// 文件上传失败处理逻辑
const handleError = (err) => {
  ElMessage({
    showClose: true,
    message: err,
    type: 'error',
    duration: 3 * 1000
  });
};
  watch(
    () => props.action,
    (n, o) => {
      uploadAction.value = n
    }, {
        immediate: true
  })
  defineExpose({
    uploadRefSubmit
})
</script>
<style lang="less" scoped></style>
