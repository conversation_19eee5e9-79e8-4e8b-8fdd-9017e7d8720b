﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddAttachFileIdsOfPyamentAuto : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AttachFileIds",
                table: "PaymentAutoItem",
                type: "nvarchar(max)",
                nullable: true,
                comment: "附件Ids");

            migrationBuilder.AddColumn<decimal>(
                name: "LimitedDiscount",
                table: "PaymentAutoDetail",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true,
                comment: "限定折扣金额");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AttachFileIds",
                table: "PaymentAutoItem");

            migrationBuilder.DropColumn(
                name: "LimitedDiscount",
                table: "PaymentAutoDetail");
        }
    }
}
