﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AlterRefundItemProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "RefundItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号");

            migrationBuilder.AddColumn<Guid>(
                name: "ProjectId",
                table: "RefundItem",
                type: "uniqueidentifier",
                nullable: true,
                comment: "项目Id");

            migrationBuilder.AddColumn<string>(
                name: "ProjectName",
                table: "RefundItem",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目名称");

            migrationBuilder.AddColumn<string>(
                name: "PaymentCode",
                table: "RefundDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReceiveCode",
                table: "RefundDetail",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "RefundItem");

            migrationBuilder.DropColumn(
                name: "ProjectId",
                table: "RefundItem");

            migrationBuilder.DropColumn(
                name: "ProjectName",
                table: "RefundItem");

            migrationBuilder.DropColumn(
                name: "PaymentCode",
                table: "RefundDetail");

            migrationBuilder.DropColumn(
                name: "ReceiveCode",
                table: "RefundDetail");
        }
    }
}
