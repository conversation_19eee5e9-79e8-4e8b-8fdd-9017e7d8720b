﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 红字确认单表
    /// </summary>
    [Table("RedConfirmDetail")]
    public class RedConfirmDetailPo : BasePo
    {
        public Guid RedConfirmItemId { get; set; }

        [ForeignKey("RedConfirmItemId")]
        public RedConfirmItemPo RedConfirmItem { get; set; }

        /// <summary>
        /// 蓝字发票明细序号
        /// </summary>
        [Comment("蓝字发票明细序号")]
        [MaxLength(500)]
        public string BlueInvoiceItemIndex { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        [Comment("序号")]
        [MaxLength(500)]
        public string Index { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        [Comment("商品编码")]
        [MaxLength(500)]
        public string RevenueCode { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        [Comment("项目名称")]
        [MaxLength(500)]
        public string GoodsName { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        [Comment("规格型号")]
        [MaxLength(500)]
        public string Specification { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Comment("单位")]
        [MaxLength(500)]
        public string Units { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Comment("数量")]
        [MaxLength(500)]
        public string Quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        [Comment("单价")]
        [MaxLength(500)]
        public string Price { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        [Comment("不含税金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        /// <summary>
        /// 税额
        /// </summary>       
        [Comment("税额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [Comment("税率")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxRate { get; set; }
    }
}
