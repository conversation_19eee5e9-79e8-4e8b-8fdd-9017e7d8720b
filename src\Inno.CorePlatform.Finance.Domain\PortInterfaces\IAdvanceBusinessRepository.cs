﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.PortInterfaces
{
    public interface IAdvanceBusinessRepository : IRepositorySupportCrudAndUow<AdvanceBusinessApply, Guid>
    {
        Task<bool> AddAdvanceDetails(List<AdvanceBusinessDetail> details);
        Task<bool> DeleteAdvanceDetails(List<Guid> ids);
        Task<int> AddManyAsync(List<AdvanceBusinessApply> list);
        Task<int> UpdateManyAsync(List<AdvanceBusinessApply> list);
        Task<List<AdvanceBusinessApply>> GetByCodes(List<string?> codes);
    }
}
