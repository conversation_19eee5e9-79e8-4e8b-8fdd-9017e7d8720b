<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>数据自查列表</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation
        :crud="crud"
        :permission="functionUris"
        :hiddenColumns="[]"
        hidden-opts-left
      >
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
        <inno-query-operation v-model:query-list="queryList" :crud="crud" />
        <inno-crud-operation
          style="padding: 0px 0px 6px 0px;margin:10px 0 4px 0;"
          :crud="crud"
          rightAdjust
          :permission="functionUris"
          :hiddenColumns="[]"
          border
          hidden-opts-right
        >
          <template #opts-left>
            <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="changeTabClick">
            </el-tabs>
          </template>
          <template #right>
            <!-- <inno-button-tooltip type="primary"
              icon="CirclePlus"
              @click.stop="jumpCreate">
              新增
            </inno-button-tooltip> -->
            <inno-button-tooltip
              type="primary"
              icon="Refresh"
              @click.stop="verifyApplyList()">
              校验
            </inno-button-tooltip>
            <!-- <el-button type="primary" @click="onFull">
              <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
            </el-button> -->
          </template>
        </inno-crud-operation>
        <el-table
          ref="tableItem"
          v-inno-loading="crud.loading"
          class="auto-layout-table"
          highlight-current-row
          border
          :data="crud.data"
          stripe
          @sort-change="crud.sortChange"
          @selection-change="crud.selectionChangeHandler"
          @row-click=" (e) => { getDetailData(e);}"
        > 
          <el-table-column type="index" fixed="left" width="50" />
          <el-table-column fixed="left" label="单号" property="billCode" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy link @click.stop="jumpDetail(scope.row.id, scope.row.type)">
                {{ scope.row.billCode }}
              </inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="单据日期" property="billDate" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
          </el-table-column>
          <el-table-column label="逻辑自洽" property="businessDeptFullName" show-overflow-tooltip />
          <el-table-column label="生成相关单据" property="businessDeptFullName" show-overflow-tooltip />
          <el-table-column label="缺失单据" property="businessDeptFullName" show-overflow-tooltip />
        </el-table>
        <div class="app-page-footer background">
          已选择 {{ crud.selections.length }} 条
          <div class="flex-1" />

          <inno-crud-pagination :crud="crud" />
        </div>
  </div>
</template>

<script lang="tsx" setup>
import { ref, Ref, onBeforeMount, onMounted, onActivated, computed, reactive, provide, nextTick } from 'vue';
import { ElTable, TableColumnCtx, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { useColumnStrategies } from '@inno/inno-mc-vue3/lib/utils/hooks';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { formatDate, normalizeDate } from '@vueuse/shared';
import { Loading } from 'element-plus/es/components/loading/src/service';
import { Decimal } from 'decimal.js';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';

interface ListItem {
  value: string;
  label: string;
}
//服务商审批意见
interface AuditOpinionItem {
  auditOpinion: string;
  createTime: string;
  userName: string;
}

const CreditTypeList = [
  {
    id: 4,
    name: '销售应收'
  },
  {
    id: 6,
    name: '经销销售应收'
  },
  {
    id: 2,
    name: '初始应收'
  }
]

const { username } = useUserStore();

const setDetailTab = ref('1');
//获取路由
const router = useRouter();
//数据策略权限
const functionUris = {
  audit: 'metadata://sia/aier-prereturn-apply',
};

//选项卡对象
const tabModel = ref({
  waitSubmitCount:0,
  waitAuditCount:0,
  refuseCount:0,
  allCount:0,
  complateCount:0,
  myCount:0,
});
//上传附件
const comfile_show = ref(false);
const showfiles = ref([]);
const approveProcessRef = ref();
//合计统计数据
const detailFooterStatistics = reactive<{
  totalQty: number;
  totalStoreInQty: number;
  totalReceiveQty: number;
  totalTariffAmount: number;
  totalImportAddAmount: number;
  totalUnitCost: number;
  totalSaleAmount: number;
  totalOriginalAmount: number;
  totalEstimatedAmount: number;
  totalInQty:number;
}>({
  totalQty: 0,
  totalStoreInQty: 0,
  totalReceiveQty: 0,
  totalTariffAmount: 0,
  totalImportAddAmount: 0,
  totalUnitCost: 0,
  totalSaleAmount: 0,
  totalOriginalAmount: 0,
  totalEstimatedAmount: 0,
  totalInQty: 0
});
const selectTemplateModel = reactive<{
  type: string;
}>({
  type: '00000009'
});
const dlgProjectVisable = ref(false);
const projectFormModel = {
  projectName: '',
  projectId: ''
};
const setFormVisible = ref(false);
const debtByCredilist = ref([]);
const creditList = ref([]);

const setForm = reactive({
  auditRemark:undefined,
  isApproved:undefined,
})
const rules = reactive<FormRules<any>>({
  startTime: [
    {
      required: true,
      message: '请选择同步时间',
      trigger: 'change',
    },
  ]
})
// 表单验证
const setRules = reactive<FormRules>({
  isApproved: [
    {
      required: true,
      message: '请选择审批结果',
      trigger: 'change',
    },
  ],
})
const strategies = ref({
  hidden: []
});
const auditOpinionShow = ref(false);
const auditOpinionList = ref<AuditOpinionItem[]>([]);


interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums = [] as any;
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    } else if (column.property === 'amount') {
      const values = data.map((item) => item.amount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'badAmount') {
      const values = data.map((item) => item.badAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }  else if (column.property === 'leftAmount') {
      const values = data.map((item) => item.leftAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    } else if (column.property === 'value') {
      const values = data.map((item) => item.value);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'abatmentAmount') {
      const values = data.map((item) => item.abatmentAmount);
      const total = values.reduce((prev, curr) => new Decimal(prev).add(curr), 0) ;
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(total, '0,0.00'); // 可根据需求选择保留小数位数
      return;
    }else if (column.property === 'originalAmount') {
      const values = data.map((item) => (item.originUnitPrice * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalOriginalAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } else if (column.property === 'estimatedAmount') {
      const values = data.map((item) => (item.unitCost * 10000 * item.quantity) / 10000);
      const total = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
      //添加千分之标识
      // sums[index] = asyncNumeral(total, '0,0.0000'); // 可根据需求选择保留小数位数
      sums[index] = asyncNumeral(detailFooterStatistics.totalEstimatedAmount, '0,0.0000'); // 可根据需求选择保留小数位数
      // return;
    } 
    if (data == null) return;
    const values = data.map((item) => Number(item[column.property]));
    if (column.property === 'quantity') {
      const total = values.reduce((prev, curr) => (prev  + curr), 0);
      sums[index] = total;
      return;
    }
    if (column.property === 'arrivedQty') {
      sums[index] = detailFooterStatistics.totalReceiveQty;
      return;
    }
    if (column.property === 'storeInQty') {
      sums[index] = detailFooterStatistics.totalStoreInQty;
      return;
    }
    if (column.property === 'originUnitPrice') {
      sums[index] = detailFooterStatistics.totalOriginalAmount;
      return;
    }
    if (column.property === 'storeInDetails.quantity') {
      sums[index] = detailFooterStatistics.totalInQty;
      return;
    }
  });
  return sums;
};


const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const tableInApplyDetail = ref<InstanceType<typeof ElTable>>();

const crud = CRUD(
  {
    title: '应用',
    url: `/api/AutoInventoryTaking/GetListAsync`,
    // downloadUrl: '/api/agents/export', // 如果不传则： '/api/agents/' + ‘download’
    // sort: ['createdTime,desc'],
    method: 'post',
    crudMethod: {
      // del: (ids) => deleteItems(ids)
    },
    query: { status: '0' },
    tablekey: 'tablekey',
    userNames: ['createdBy', 'updatedBy'],
    optShow: {
      add: false,
      edit: false,
      del: false,
      download: false,
      reset: true
    },
    hooks: {
      [CRUD.HOOK.afterToAdd]: (_crud) => {
        //点击列表自带添加按钮的事件
        showProjectModel();
      },
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          // appleType.value = crud.data[0].applyTypeName;
          crud.singleSelection(crud.data[0]);
        }else {
          creditList.value = [];
          debtByCredilist.value = [];
        }
      }
    },
    pageConfig: {
      // 分页参数按项目可以单独配置
      pageIndex: 'pageIndex',
      pageSize: 'pageSize'
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);




const changeTabClick = () => {
  crud.toQuery();
};

const getDetailData = (e:any) => {
  crud.singleSelection(e);
};

function showProjectModel() {
  dlgProjectVisable.value = true;
}

const props = defineProps({
  __refresh: Boolean
});

onBeforeMount(() => {
  crud.toQuery();
});

onMounted(() => {
  // 表头拖拽必须在
  tableDrag(tableItem);
  tableDrag(tableDetail);
  // tableDrag(tableInApplyDetail);
  const strategiesConfig = {
    functionUri: 'metadata://sia/storein-apply/routes/index-search',
    url:
      window.gatewayUrl +
      `v1.0/sia-backend/api/purchaseapply/getStrategy?functionUri=metadata://sia/storein-apply/routes/index-search`,
    method: 'get'
  };
  strategies.value = useColumnStrategies(strategiesConfig);
  // test();
});

onActivated(() => {
  crud.toQuery();
});
//高级检索
const queryList = computed(() => [
  {
    key: 'billCode',
    label: '单号',
    show: true
  },
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: true
  },
  {
    key: 'applyDateStart',
    endDate: 'applyDateEnd',
    label: '单据日期',
    type: 'daterange',
    defaultTime: [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)],
    props: { format: 'YYYY-MM-DD' },
    show: true
  },
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);


const approveConfirm = async(formEl:any) =>{
  if (!formEl) return
    let checkResult = await formEl.validate((valid:any, fields:any) => {return valid})
     if (!checkResult) {
        return false;
    }
  let postData = {
    id: crud.rowData.id,
    isApproved: setForm.isApproved === '1'? true : setForm.isApproved === '2' ? false :null,
    auditRemark: setForm.auditRemark
  }
  request({
    url: `/api/AierPreReturnApply/Audit`,
    method: 'POST',
    data: postData
  }).then(res=>{
    if(res.data.code ===200){
      ElMessage.success({ showClose: true, message: '审批成功！' });
      crud.toQuery();
      setFormVisible.value = false;
      approveCancel();
    }else{
      ElMessage.error({ showClose: true, message: res.data.message });
    }
  }).catch((error)=>{
    ElMessage.error({ showClose: true, message: error });
  })
  
 
}

  const verifyApplyList= () => {
    ElMessageBox.confirm('校验数据时将重新更新列表数据，是否确认操作！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: `/api/AutoInventoryTaking/CheckData`,
        method: 'post',
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success({ showClose: true, message: '校验成功！' });
          crud.toQuery();
        } else {
          ElMessage.error({ showClose: true, message: res.data.message });
        }
      }).catch((error) => {
        ElMessage.error({ showClose: true, message: error });
      })
    });
  };

const approveCancel = () =>{
  setFormVisible.value = false;
  setForm.isApproved = undefined;
  setForm.auditRemark = undefined;
}
const jumpCreate = () =>{
  router.push({
    path: '/lossRecognitionApply/create',
    query: {
      __page_id: `${new Date().getTime()}`,
    },
    params: {
      __reload: true
    }
  });
}
//审批过程
const auditProcessClick = (row) => {
  //审批过程
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
const openViewFile = (ids:any) =>{
    let arr = ids.split(",");
    FileViewer.show(arr,  0,  {});
    // FileViewer.show([ids], 0, {});
}
const jumpDetail = (id: string, type: number) => {
  let routeName = '/lossRecognitionApply/detail';
  router.push({
    path: routeName,
    query: {
      id: id,
      __page_id: `${new Date().getTime()}`
    },
    params: {
      __reload: true
    }
  });
};
 //附件
 const showAttachFile = (fileIds:any,id:any) => {
  request({
    url: `/api/LossRecognition/GetAttachFile`,
    method: 'POST',
    data: {
      lossRecognitionItemId: id,
      fileIds:fileIds
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_show.value = true;
        showfiles.value = res.data.data;
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
    
  };
  //文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
};

const formatCreditType = (creditType) => {
  return CreditTypeList.filter((item) => {
    return item.id === creditType;
  })[0].name;
}
</script>
<style scoped lang="scss">
:deep(.el-tabs__item) {
  padding: 0 12px;
}
:deep(.right-adjust) {
  display: block;
  max-width: 42%;
  display: flex;
  flex-wrap: wrap;
  height: auto;
  justify-content: flex-end;
  margin-left: 20px;
  align-items: center;
}
:deep(.crud-opts-border) {
  justify-content: space-between;
}
/* :deep(.el-button) {
  margin-top: 8px;
} */
:deep(.crud-opts) {
  justify-content: space-between;
}
.detail-box{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-height: 40%;
  .detail-item{
    width: 49%;
  }
}
</style>
