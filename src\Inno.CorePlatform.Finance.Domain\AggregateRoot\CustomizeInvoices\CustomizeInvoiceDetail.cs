﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    public class CustomizeInvoiceDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        ///  原明细id
        /// </summary> 
        public string OriginDetailId { get; set; }

        /// <summary>
        ///  原始货品名称（不可变）
        /// </summary>
        [MaxLength(200)]
        public string? OriginProductName { get; set; }

        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        ///  计量单位
        /// </summary> 
        public string PackUnit { get; set; }

        /// <summary>
        ///  原始计量单位
        /// </summary>
        public string? OriginPackUnit { get; set; }

        /// <summary>
        ///  规格型号
        /// </summary>
        public string Specification { get; set; }
        /// <summary>
        /// 原始规格型号
        /// </summary>
        public string? OriginSpecification { get; set; }
        /// <summary>
        ///  数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }
        /// <summary>
        ///  单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")] 
        public decimal? OriginalPrice { get; set; }
        /// <summary>
        ///  金额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Value { get; set; }
        /// <summary>
        ///  税率
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal TaxRate { get; set; }

        /// <summary>
        ///  税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }

        /// <summary>
        ///  税额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal TaxAmount { get; set; }


        /// <summary>
        ///  应收单号
        /// </summary>
        public string CreditBillCode { get; set; }

        /// <summary>
        ///  关联单号
        /// </summary>
        public string RelateCode { get; set; }
        /// <summary>
        ///  订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        ///  付款单位ID
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        ///  付款单位名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }

        /// <summary>
        ///  货号Id
        /// </summary> 
        public Guid? ProductId { get; set; }

        /// <summary>
        ///  货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        ///  标签
        /// </summary>
        public string? Tag { get; set; }

        /// <summary>
        /// 分组号
        /// </summary>
        public string? CustomizeInvoiceIndex { get; set; }


        /// <summary>
        /// 开票序号
        /// </summary>  
        public int? Sort { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高价值
        /// </summary>
        public int? IFHighValue { get; set; }

        public Guid? ParentId { get; set; }


        /// <summary>
        /// 关联Id
        /// </summary>
        public Guid? RelateId { get; set; }
        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }
    }
}
