<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item :to="{ name: 'financeManagement-InvoiceReceipts' }">发票入账清单</el-breadcrumb-item>
        <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
    </div>
    <div class="app-page-body" style="padding-top: 20px">
      <inno-crud-operation hidden-opts-right></inno-crud-operation>
      <el-form
        ref="formRef"
        :model="formData"
        label-position="right"
        label-width="120px"
        :rules="generateformRules"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="核算部门:" prop="newDepart.id">
              <inno-department-select
                v-model="formData.newDepart.id"
                v-model:name="formData.newDepart.name"
                v-model:path="formData.newDepart.path"
                v-model:fullName="formData.newDepart.fullName"
                functionUri="metadata://fam"
                @change="businessDeptsChange"
              ></inno-department-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="公司" prop="companys.id">
              <el-select
                v-model="formData.companys.id"
                filterable
                placeholder="请选择公司"
                @change="companyChange"
              >
                <el-option
                  v-for="item in CompanyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <!-- <inno-remote-select
                v-model="formData.companys.id"
                
                :is-guid="2"
                placeholder="请选择公司"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                :url="`${gatewayUrl}v1.0/bdsapi/api/companies/meta`"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务单元" prop="services.id">
              <!-- <inno-remote-select
                v-model="formData.services"
                isObject
                :is-guid="2"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                labelK="name"
                valueK="id"
                placeholder="请选择业务单元"
                :url="`${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`"
              /> -->
              <el-select
                v-model="formData.services.id"
                filterable
                placeholder="请选择业务单元"
                @change="serviceChange"
              >
                <el-option
                  v-for="item in ServiceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="客户" prop="customers.id">
             <!-- <inno-remote-select
                v-model="formData.customers"
                :max-collapse-tags="1"
                isObject
                :is-guid="2"
                placeholder="请选择客户"
                :queryData="{
                  functionUri: 'metadata://fam'
                }"
                @change="customerChange"
                labelK="name"
                valueK="id"
                :url="`${gatewayUrl}v1.0/bdsapi/api/customers/meta`"
              /> -->
              <el-select
                v-model="formData.customers.id"
                filterable
                placeholder="请选择客户"
                @change="customerChange"
              >
                <el-option
                  v-for="item in CustomerList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合约回款天数" prop="backAmountDays">
              <el-input v-model="formData.backAmountDays"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="销售账期天数" prop="saleAccountPeriodDays">
              <el-input v-model="formData.saleAccountPeriodDays"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际回款天数" prop="actualBackAmountDays">
              <el-input v-model="formData.actualBackAmountDays"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="附件" prop="attachment">
              <comfile :exceptFiles="formData.attachment" />
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="操作">
              <div class="crud-opts">
                <inno-button-tooltip
                  type="primary"
                  @click="openInvoiceCheck"
                >
                  添加发票
                </inno-button-tooltip>
                <inno-button-tooltip type="danger" @click="delRows">
                  删除
                </inno-button-tooltip>
              </div>
            </el-form-item>
            <div style="margin-bottom: 10px"></div>
            <el-form-item label="选择发票" prop="invoiceDetail">
              <el-table
                id="tableDetail"
                ref="tableRef"
                class="auto-layout-table table-container"
                highlight-current-row
                :data="formData.invoiceDetail"
                
                border
                @selection-change="selectionChange"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column label="应收单号" property="creditCode">
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.creditCode }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="发票号" property="invoiceNo" width="180">
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.invoiceNo }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="发票代码" property="invoiceCode">
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.invoiceCode }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column
                  label="发票验证码"
                  property="invoiceCheckCode"
                >
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.invoiceCheckCode }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column
                  label="开票时间"
                  property="invoiceTime"
                  width="150"
                >
                  <template #default="scope">
                    {{
                      scope.row.billDate === null
                        ? ''
                        : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="开票金额"
                  property="invoiceAmount"
                  width="100"
                >
                  <template #default="scope">
                    <inno-numeral
                      :value="scope.row.invoiceAmount"
                      format="0,0.00"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="开票状态"
                  property="isCancel"
                  width="100"
                >
                  <template #default="scope">
                    <inno-button-copy :link="false">
                      {{ scope.row.isCancel === 1 ? '已取消' : '已开票' }}
                    </inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column
                  label="备注"
                  property="remark"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ scope.row.remark }}
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item label="">
              <div class="crud-opts">
                <inno-button-tooltip
                  type="success"
                  :loading="saveLoading"
                  @click="submit"
                >
                  确定
                </inno-button-tooltip>
                <inno-button-tooltip type="" @click="cancel">
                  取消
                </inno-button-tooltip>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 选择发票组件 -->
    <el-dialog
      v-model="dialogShow"
      title="选择发票"
      width="80%"
      hight="70%"
      destroy-on-close
      :close-on-click-modal="false"
      draggable
      @closed="handleClose"
    >
      <el-form :model="dialogQuery" label-position="right" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="5">
            <el-form-item label="发票号">
              <el-input v-model="dialogQuery.invoiceNo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="应收单号">
              <el-input v-model="dialogQuery.creditCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起止时间">
            <el-date-picker
              v-model="availableTime"
              :default-time="defaultTime"
              type="daterange"
              range-separator="-"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handledateBlur"
              >
            </el-date-picker>
          </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="">
              <div class="crud-opts">
                <inno-button-tooltip type="primary" @click="dialogSearch">
                  搜索
                </inno-button-tooltip>
                <inno-button-tooltip type="" @click="dialogReset">
                  重置
                </inno-button-tooltip>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="margin-bottom: 10px"></div>
      </el-form>
      <el-table
        ref="tableInvoiceRef"
        class="auto-layout-table"
        highlight-current-row
        :data="crudInvoice.data"
        
        border
        :row-class-name="crudInvoice.tableRowClassName"
        @selection-change="crudInvoice.selectionChangeHandler"
        @row-click="crudInvoice.singleSelection"
      >
        <el-table-column type="selection" fixed="left" width="55" />
        <el-table-column label="应收单号" property="creditCode">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ scope.row.creditCode }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="发票号" property="invoiceNo" width="180">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ scope.row.invoiceNo }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="发票代码" property="invoiceCode">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ scope.row.invoiceCode }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="发票验证码" property="invoiceCheckCode">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ scope.row.invoiceCheckCode }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="开票时间" property="invoiceTime" width="150" sortable>
          <template #default="scope">
            {{
              scope.row.billDate === null
                ? ''
                : dateFormat(scope.row.invoiceTime, 'YYYY-MM-DD')
            }}
          </template>
        </el-table-column>
        <el-table-column label="开票金额" property="invoiceAmount" width="100">
          <template #default="scope">
            <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="开票状态" property="isCancel" width="100">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ scope.row.isCancel === 1 ? '已取消' : '已开票' }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="备注" property="remark" show-overflow-tooltip>
          <template #default="scope">{{ scope.row.remark }}</template>
        </el-table-column>
      </el-table>
      <div class="app-page-footer background">
        已选择 {{ crudInvoice.selections.length }} 条
        <div class="flex-1" />
        <inno-crud-pagination :crud="crudInvoice" :pageSizes="[5, 10, 20]" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="check">确定</el-button>
          <el-button @click="dialogShow = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import comfile from '@/component/com-files.vue';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { queryCheckedByDept } from '@/api/bdsData';
import request from '@/utils/request';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';

//弹窗搜索参数
const dialogQuery = ref({
  invoiceNo: '',
  creditCode: '',
  startDate:0,
  endDate:0
});
const exceptFiles = ref();
const tableInvoiceRef = ref<InstanceType<typeof ElTable>>();
const crudInvoice = CRUD(
  {
    title: '发票明细',
    url: '/api/InvoiceQuery/GetReceiptInvoiceList',
    method: 'post',
    sort: ['invoiceTime,desc'],
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableInvoiceRef',
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableInvoiceRef
  }
);
const formRef = ref();
const tableRef = ref<InstanceType<typeof ElTable>>();

const route = useRoute();

//生成表单提交数据
let formData = reactive({
  id: '',
  companyId: '',
  serviceId: '',
  customerId: '',
  companyName: '',
  serviceName: '',
  customerName: '',
  businessDeptId: '',
  businessDeptFullPath: '',
  businessDeptFullName: '',
  newDepart: {
    id: '',
    name: '',
    path: '',
    fullName: ''
  },
  companys: { id: '', name: '' },
  customers: { id: '', name: '' },
  services: { name: '', id: '' },
  backAmountDays: '',
  saleAccountPeriodDays: '',
  actualBackAmountDays: '',
  attachment: [],
  remark: '',
  invoiceDetail: [
    
  ]
});

const title = ref('新建发票入账单')
onMounted(() => {
  reset();
  //查询数据
  if (route.query.id) {
    getData(); 
  }
});
watch(
  () => route.query.id,
  (n, o) => {
    if (n != null) {
      reset();
      //查询数据
      getData();
    }
  },
  { deep: true }
);
//部门改变事件
let CompanyList = ref([]);
let company_arrayA = ref([]);
let company_arrayB = ref([]);
const businessDeptsChange = (node, notclear) => {
  formData.backAmountDays = '';
  formData.saleAccountPeriodDays = '';
  if (!notclear) {
    formData.companys.id = '';
  }
  if (node !== undefined) {
    queryCheckedByDept(node).then((res) => {
      company_arrayA.value = res.data.data;
      //根据数据策略获取公司
      request({
        url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
        method: 'POST',
        data: {
          functionUri: 'metadata://fam'
        }
      }).then((r) => {
        //取交集
        company_arrayB.value = r.data.data
        const idsA = new Set(company_arrayA.value.map(item => item.id));
        const idsB = new Set(company_arrayB.value.map(item => item.id));
        CompanyList.value = company_arrayA.value.filter(item => idsB.has(item.id));
      })
    });
  }
};
//公司改变事件
let ServiceList = ref([]);
const companyChange = (node,notclear) =>{
  formData.backAmountDays = '';
  formData.saleAccountPeriodDays = '';
  if (!notclear) {
    formData.services.id = '';
  }
  if (node !== undefined) {
    //查询公司下的业务单元
    request({
      url: '/api/InvoiceReceipts/GetServices',
      method: 'POST',
      data: {
        companyId: node,
      }
    })
    .then((res) => {
      ServiceList.value = res.data.data;
    })
  }
};
//业务单元改变事件
let CustomerList = ref([]);
let customer_arrayA = ref([]);
let customer_arrayB = ref([]);
const serviceChange = (node,notclear) =>{
  formData.backAmountDays = '';
  formData.saleAccountPeriodDays = '';
  if (!notclear) {
    formData.customers.id = '';
  }
  if (node !== undefined) {
    console.log(JSON.stringify(node))
    //查询客户
    request({
      url: '/api/InvoiceReceipts/GetCustomers',
      method: 'POST',
      data: {
        companyId: formData.companys.id,
        serviceId: node
      }
    })
    .then((res) => {
       CustomerList.value = res.data.data;
      // customer_arrayA.value = res.data.data;
      // console.log(JSON.stringify(customer_arrayA.value))
    })
    //根据数据策略获取客户
    // request({
    //   url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    //   method: 'POST',
    //   data: {
    //     functionUri: 'metadata://fam'
    //   }
    // }).then((r) => {
    //   //取交集
    //   customer_arrayB.value = r.data.data
    //   const idsA = new Set(customer_arrayA.value.map(item => item.id));
    //   const idsB = new Set(customer_arrayB.value.map(item => item.id));
    //   CustomerList.value = customer_arrayA.value.filter(item => idsB.has(item.id));
    // })
  }
};
//客户改变事件
const customerChange = (node, notclear) => {
  //自动带出回款天数、销售账期
  if (!notclear) {
    formData.backAmountDays = '';
    formData.saleAccountPeriodDays = '';
  }
  if (node !== undefined) {
    formData.customerId = node.id
    formData.customerName = node.name
    //查询数据
    request({
      url: '/api/InvoiceReceipts/GetDayData',
      method: 'POST',
      data: {
        companyId: formData.companys.id,
        serviceId: formData.services.id,
        customerId: formData.customers.id
      }
    })
    .then((res) => {
      formData.backAmountDays = res.data.data.backAmountDays;
      formData.saleAccountPeriodDays =  res.data.data.saleAccountPeriodDays;
    })
  }
}
//编辑时查询数据
const getData = () => {
  console.log('开始查询');
  request({
    url: '/api/InvoiceReceipts/GetItem',
    method: 'POST',
    data: {
      invoiceReceiptItemId: route.query.id
    }
  }).then((res) => {
    if (res.data.code === 200) {
      //拼装数据
      if(res.data.data){
        title.value = '编辑发票入账单';
      }
      formData.companyId = res.data.data.companyId;
      formData.companyName = res.data.data.companyName;
      formData.serviceId = res.data.data.serviceId;
      formData.serviceName = res.data.data.serviceName;
      formData.customerId = res.data.data.customerId;
      formData.customerName = res.data.data.customerName;
      formData.backAmountDays = res.data.data.backAmountDays;
      formData.saleAccountPeriodDays = res.data.data.saleAccountPeriodDays;
      formData.actualBackAmountDays = res.data.data.actualBackAmountDays;
      formData.remark = res.data.data.remark;
      formData.invoiceDetail = res.data.data.invoiceDetail;

      formData.newDepart = {
        id: res.data.data.businessDeptId,
        name: '',
        path: res.data.data.businessDeptFullPath,
        fullName: res.data.data.businessDeptFullName
      };


      CompanyList.value = [{
        id: res.data.data.companyId,
        name: res.data.data.companyName
      }];
      CustomerList.value = [{
        id: res.data.data.customerId,
        name: res.data.data.customerName
      }];
      ServiceList.value = [{
        id: res.data.data.serviceId,
        name: res.data.data.serviceName
      }];

      formData.companys = {
        id: res.data.data.companyId,
        name: res.data.data.companyName
      };
      formData.customers = {
        id: res.data.data.customerId,
        name: res.data.data.customerName
      };
      formData.services = {
        id: res.data.data.serviceId,
        name: res.data.data.serviceName
      };

      var list = res.data.data.attachments;
      list.forEach((item) => {
        formData.attachment.push({
          attachFileId: item.id,
          attachFileName: item.name,
          attachFileSize: item.length
        });
      });

      // formData.attachment = res.data.data.attachments
      console.log(JSON.stringify(formData));
    }
  });
};

const dialogShow = ref(false);
let dialogData = reactive([]);
//添加发票
const openInvoiceCheck = () => {
  //查询选择发票组件数据
  dialogSearch();
};
//选择的发票集合
const checkSelection = ref([]);
const selectionChange = (val) => {
  checkSelection.value = val;
};
//删除发票
const delRows = () => {
  // 调用删除接口
  if (route.query.id) {
    ElMessageBox.confirm(
      '确认删除发票明细吗？删除后将不可还原',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(()=>{
      var ids = checkSelection.value.map(x=>x.id);
      request({
        url: '/api/InvoiceReceipts/DeleteDetails',
        method: 'POST',
        data: ids
      })
      .then((res) => {
        if (res.data.code == '200') {
          ElMessage({
            showClose: true,
            message: '删除成功',
            type: 'success',
            duration: 3 * 1000
          });
          formData.invoiceDetail = formData.invoiceDetail.filter((itemA) => {
            // 遍历集合 B 来检查 itemA 是否存在于其中
            return !checkSelection.value.some((itemB) => {
              // 这里假设 creditCode 和 invoiceNo 的组合是唯一的，用于比较两个对象是否相同
              return (
                itemA.creditCode === itemB.creditCode &&
                itemA.invoiceNo === itemB.invoiceNo
              );
            });
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
    })
  }
  else {
    formData.invoiceDetail = formData.invoiceDetail.filter((itemA) => {
      // 遍历集合 B 来检查 itemA 是否存在于其中
      return !checkSelection.value.some((itemB) => {
        // 这里假设 creditCode 和 invoiceNo 的组合是唯一的，用于比较两个对象是否相同
        return (
          itemA.creditCode === itemB.creditCode &&
          itemA.invoiceNo === itemB.invoiceNo
        );
      });
    });
  }
};
const saveLoading = ref(false);
//确定添加
const submit = () => {
  console.log(JSON.stringify(formData));
  if (!formRef.value) return;
  formRef.value.validate((valid, field) => {
    if (valid) {
      saveLoading.value = true;
      //封装提交参数
      if (typeof route.query.id === 'string') {
        formData.id = route.query.id?.toString(); // 只有在变量类型为"string"时才将其赋值给stringValue
      }
      formData.businessDeptId = formData.newDepart.id;
      formData.businessDeptFullPath = formData.newDepart.path;
      formData.businessDeptFullName = formData.newDepart.fullName;

      formData.companyId = formData.companys.id;
      var companyName = '';
      CompanyList.value.forEach((item)=>{
        if(item.id === formData.companys.id){
          companyName = item.name
        }
      })
      formData.companyName = companyName;

      formData.serviceId = formData.services.id;
      var serviceName = '';
      ServiceList.value.forEach((item)=>{
        if(item.id === formData.services.id){
          serviceName = item.name
        }
      })
      formData.serviceName = serviceName;

      formData.customerId = formData.customers.id;
      var customerName = '';
      CustomerList.value.forEach((item)=>{
        if(item.id === formData.customers.id){
          customerName = item.name
        }
      })
      formData.customerName = customerName;

      console.log(JSON.stringify(formData));
      request({
        url: '/api/InvoiceReceipts/Create',
        method: 'POST',
        data: formData
      })
        .then((res) => {
          if (res.data.code == '200') {
            ElMessage({
              showClose: true,
              message: '保存成功',
              type: 'success',
              duration: 3 * 1000
            });
            reset();
            //返回
            cancel();
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
          saveLoading.value = false;
        })
        .catch((t) => {
          saveLoading.value = false;
        });
    }
  });
};
const router = useRouter();
//取消
const cancel = () => {
  router.replace({
    path: 'InvoiceReceipts',
    params: {
      __refresh: true
    }
  });
};
//关闭弹窗
const handleClose = () => {
  dialogShow.value = false;
};
//弹窗确定
const check = () => {
  formData.invoiceDetail.push(...crudInvoice.selections);
  handleClose();
};
//时间
const availableTime = ref([]);
const defaultTime = reactive([new Date(0,0,0,0,0,0), new Date(0,0,0,23,59,59)])
const handledateBlur = () => {
  if (availableTime.value != null && availableTime.value.length === 2) {
    dialogQuery.value.startDate = availableTime.value[0]
    dialogQuery.value.endDate = availableTime.value[1]
  } else {
    dialogQuery.value.startDate = 0
    dialogQuery.value.endDate = 0
  }
};
//弹窗搜索
const dialogSearch = () => {
  var ids = formData.invoiceDetail.map(x=>x.invoiceNo);
  crudInvoice.query.excludeIds = ids;
  crudInvoice.query.companyId = formData.companys.id;
  crudInvoice.query.serviceId = formData.services.id;
  crudInvoice.query.customerId = formData.customers.id;
  crudInvoice.query.creditCode = dialogQuery.value.creditCode;
  crudInvoice.query.invoiceNo = dialogQuery.value.invoiceNo;
  crudInvoice.query.startDate = new Date(dialogQuery.value.startDate).getTime(),
  crudInvoice.query.endDate = new Date(dialogQuery.value.endDate).getTime(),
  crudInvoice.toQuery();
  dialogShow.value = true;
};
//弹窗重置
const dialogReset = () => {
  availableTime.value = [];
  dialogQuery.value.invoiceNo = '';
  dialogQuery.value.creditCode = '';
  dialogQuery.value.startDate = 0;
  dialogQuery.value.endDate = 0;
  dialogData = [];
};
//正整数校验方法
const validatePositiveInteger = (rule, value, callback) => {
  console.log(rule.field);
  // #113368 发票入账单，实际回款天数改为必填
  if (!value && rule.field !== 'actualBackAmountDays') {
    callback();
  } else {
    const reg = /^[1-9]\d*$/
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正整数'))
    }
  }
}
//表单规则校验
const generateformRules = reactive<FormRules>({
  'newDepart.id': [
    {
      required: true,
      message: '请选择核算部门',
      trigger: 'change'
    }
  ],
  'companys.id': [
    {
      required: true,
      message: '请选择公司',
      trigger: 'change'
    }
  ],
  'services.id': [
    {
      required: true,
      message: '请选择业务单元',
      trigger: 'change'
    }
  ],
  'customers.id': [
    {
      required: true,
      message: '请选择客户',
      trigger: 'change'
    }
  ],
  attachment: [
    {
      required: true,
      message: '请上传附件',
      trigger: 'change'
    }
  ],
  invoiceDetail: [
    {
      required: true,
      message: '请选择发票',
      trigger: 'change'
    }
  ],
  backAmountDays: [
    { validator: validatePositiveInteger, trigger: 'blur' }
  ],
  saleAccountPeriodDays: [
    { validator: validatePositiveInteger, trigger: 'blur' }
  ],
  actualBackAmountDays: [
    { required: true, validator: validatePositiveInteger, trigger: 'blur' }
  ],
});
//清空数据
const reset = () => {
  formData.invoiceDetail.value = [];
    formData.attachment.value = [];
    formData.companys = { id: '', name: '' };
    formData.customers = { id: '', name: '' };
    formData.services = { name: '', id: '' };
    formData.backAmountDays = '';
    formData.saleAccountPeriodDays = '';
    formData.actualBackAmountDays= '';
    formData.attachment= [];
    formData.remark= '';
    formData.invoiceDetail= [
      
    ];
    formData.newDepart = {
      id: '',
      name: '',
      path: '',
      fullName: ''
    };
}
</script>