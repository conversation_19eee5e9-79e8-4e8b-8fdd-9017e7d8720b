﻿using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments
{
    public class PurchasePayPlan : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public PurchasePayPlan() { }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>

        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        public Guid? ProductId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary> 
        public string? PurchaseCode { get; set; }

        public Guid? PurchaseId { get; set; }
        /// <summary>
        /// 付款计划账期
        /// </summary> 
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }

        /// <summary>
        /// 付款计划账期
        /// </summary> 
        public decimal Ratio { get; set; }

        /// <summary>
        /// 数量
        /// </summary> 
        public int Quantity { get; set; }

        /// <summary>
        /// 比例价格
        /// </summary> 
        public decimal RatioPrice { get; set; }

        /// <summary>
        /// 价格
        /// </summary> 
        public decimal Price { get; set; }

        /// <summary>
        /// 预计付款时间
        /// </summary> 
        public DateTime? ProbablyPayTime { get; set; }

        /// <summary>
        /// 账期天数
        /// </summary>  
        public int? AccountPeriodDays { get; set; }
        public string? ForwardPurchaseCode { get; set; }

        /// <summary>
        /// 采购单详情Id
        /// </summary>
        public Guid? PurchaseDetailId { get; set; }
    }
}
