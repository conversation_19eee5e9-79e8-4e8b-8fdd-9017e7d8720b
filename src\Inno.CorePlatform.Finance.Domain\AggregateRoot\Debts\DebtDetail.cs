﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate
{
    public class DebtDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public DebtDetail()
        {
            DebtDetailExcutes = new List<DebtDetailExcute>();
        }

        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期 4=验收账期 5=质保账期
        /// </summary>
        public int AccountPeriodType { get; set; }

        public decimal? Discount { get; set; }
        public decimal? DistributionDiscount { get; set; }

        public decimal? FinanceDiscount { get; set; }

        public decimal? SpdDiscount { get; set; }

        public decimal? TaxDiscount { get; set; }

        public decimal? CostDiscount { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }

        public virtual Debt? Debt { get; set; }

        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }

        /// <summary>
        /// 新核算部门下的应收Id，主要用于核算部门变更
        /// 适用场景：将原核算部门下的应收转换到新核算部门下
        /// </summary>
        public Guid? NewBussinessDeptCreditId { get; set; }

        public virtual Credit? Credit { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }


        /// <summary>
        /// 认款单号
        /// </summary> 
        public string? RecognizeReceiveCode { get; set; }

        private decimal _value;

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value
        {
            get => _value;
            set
            {
                // 四舍五入到两位小数
                _value = Math.Round(value, 2, MidpointRounding.AwayFromZero);
            }
        }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }


        public DateTime? ProbablyPayTime { get; set; }

        /// <summary>
        /// 回款日期
        /// </summary>  
        [Comment("回款日期")]
        public DateTime? BackPayTime { get; set; }

        /// <summary>
        /// 批量付款明细
        /// </summary>
        public virtual List<DebtDetailExcute>? DebtDetailExcutes { get; set; } = new List<DebtDetailExcute>();

        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary> 
        [MaxLength(200)]
        public string? OrderNo { get; set; }

        /// <summary>
        /// 是否发票入账
        /// </summary> 
        public bool? IsInvoiceReceipt { get; set; }

        public string? Settletype { get; set; }
        public DateTime? DraftBillExpireDate { get; set; }

        /// <summary>
        /// 账期天数
        /// </summary>
        public decimal? AccountPeriodDays { get; set; } = 0;

        /// <summary>
        /// 原始订单号
        /// </summary>
        [MaxLength(200)]
        public string? OriginOrderNo { get; set; }

        /// <summary>
        /// 添加应付付款计划执行明细
        /// </summary>
        /// <param name="detail"></param>
        /// <param name="userName"></param>
        /// <exception cref="DomainException"></exception>
        public void AddDebtDetailExcute(DebtDetailExcute detail, string userName)
        {
            if (detail == null)
            {
                throw new DomainException("明细不能为空!");
            }

            // detail.Id = Guid.NewGuid();
            detail.CreateBy(userName);
            DebtDetailExcutes.Add(detail);
        }

        /// <summary>
        /// 修改应付执行明细
        /// </summary>
        /// <param name="paymentAutoDetail"></param>
        /// <param name="userName"></param>
        /// <exception cref="DomainException"></exception>
        public void UpdateDebtDetailExcute(DebtDetailExcute detail, string userName)
        {
            if (detail == null)
            {
                throw new DomainException("明细不能为空!");
            }

            var res = DebtDetailExcutes.FirstOrDefault(x => x.Id == detail.Id);

            if (res == null)
            {
                throw new DomainException("明细不存在!");
            }

            detail.UpdateBy(userName);

            res = detail.Adapt(res);
        }

        /// <summary>
        /// 删除批量付款执行明细
        /// </summary>
        /// <param name="detail"></param>
        /// <exception cref="DomainException"></exception>
        public void RemoveDebtDetailExcute(DebtDetailExcute detail)
        {
            if (detail == null)
            {
                throw new DomainException("明细不能为空!");
            }

            var res = DebtDetailExcutes.FirstOrDefault(x => x.Id == detail.Id);

            if (res == null)
            {
                throw new DomainException("明细不存在!");
            }

            DebtDetailExcutes.Remove(res);
        }
    }

    public class DebtCodeDetailId
    {
        public string DebtCode { get; set; }
        public Guid DebtDetailId { get; set; }
    }
}