using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 未完成单据输出
    /// </summary>
    public class NotFinishBillOutput
    {
        /// <summary>
        /// 库存单据编码
        /// </summary>
        public string BillCode { get; set; } = string.Empty;

        /// <summary>
        /// 单据状态
        /// </summary>
        public string BillType { get; set; } = string.Empty;

        /// <summary>
        /// 单据类型
        /// </summary>
        public string BillTypeDesc { get; set; } = "返利计提";

        /// <summary>
        /// 单据日期
        /// </summary>
        public long BillDate { get; set; }

        /// <summary>
        /// 单据状态
        /// </summary>
        public string BillStatusDesc { get; set; } = string.Empty;

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 0（删除） ，1（挂起）,2（限制审批）,3（系统提醒）
        /// </summary>
        public int ProcessType { get; set; } = 3;

        /// <summary>
        /// 处理类型
        /// </summary>
        public string ProcessTypeDesc { get; set; } = "系统提醒";
    }
}
