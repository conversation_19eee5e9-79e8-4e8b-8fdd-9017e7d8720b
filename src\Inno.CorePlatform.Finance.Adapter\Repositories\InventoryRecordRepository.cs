using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Finance.Domain.Enums;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    /// <summary>
    /// 盘点记录仓储实现
    /// </summary>
    public class InventoryRecordRepository : EfBaseRepository<Guid, InventoryRecord, InventoryRecordPo>, IInventoryRecordRepository
    {
        private readonly FinanceDbContext _db;

        public InventoryRecordRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
            UowJoined = true;
        }

        /// <summary>
        /// 重写更新方法
        /// </summary>
        /// <param name="root"></param>
        /// <returns></returns>
        public override async Task<int> UpdateAsync(InventoryRecord root)
        {
            var isExist = await _db.InventoryRecord.AnyAsync(x => x.Id == root.Id);
            if (!isExist)
            {
                throw new Exception("盘点记录不存在！");
            }

            var po = root.Adapt<InventoryRecordPo>();
            _db.InventoryRecord.Update(po);

            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        /// <summary>
        /// 创建删除PO对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        protected override InventoryRecordPo CreateDeletingPo(Guid id)
        {
            return new InventoryRecordPo { Id = id };
        }

        /// <summary>
        /// 获取包含关联数据的PO对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        protected override Task<InventoryRecordPo> GetPoWithIncludeAsync(Guid id)
        {
            return _db.InventoryRecord.FirstOrDefaultAsync(x => x.Id == id);
        }

        /// <summary>
        /// 批量添加盘点记录
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        public async Task<int> AddManyAsync(List<InventoryRecord> records)
        {
            var recordPos = records.Adapt<List<InventoryRecordPo>>();
            await _db.InventoryRecord.AddRangeAsync(recordPos);
            return await _db.SaveChangesAsync();
        }

        /// <summary>
        /// 根据盘点单ID和动作类型获取记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <param name="actionType"></param>
        /// <returns></returns>
        public async Task<InventoryRecord?> GetByInventoryItemIdAndActionTypeAsync(Guid inventoryItemId, InventoryActionType actionType)
        {
            var recordPo = await _db.InventoryRecord
                .Where(r => r.InventoryItemId == inventoryItemId && r.ActionType == actionType)
                .FirstOrDefaultAsync();

            return recordPo?.Adapt<InventoryRecord>();
        }

        /// <summary>
        /// 根据公司ID和系统月度获取所有记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetByCompanyIdAndSysMonthAsync(Guid companyId, string sysMonth)
        {
            var recordPos = await _db.InventoryRecord
                .Where(r => r.CompanyId == companyId && r.SysMonth == sysMonth)
                .OrderBy(r => r.ActionType)
                .ToListAsync();

            return recordPos.Adapt<List<InventoryRecord>>();
        }

        /// <summary>
        /// 根据盘点单ID获取所有记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetByInventoryItemIdAsync(Guid inventoryItemId)
        {
            var recordPos = await _db.InventoryRecord
                .Where(r => r.InventoryItemId == inventoryItemId)
                .OrderBy(r => r.ActionType)
                .ToListAsync();

            return recordPos.Adapt<List<InventoryRecord>>();
        }

        /// <summary>
        /// 获取待处理的记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetPendingRecordsAsync(Guid? companyId = null, string? sysMonth = null)
        {
            var query = _db.InventoryRecord.Where(r => r.Status == (int)InventoryRecordStatus.Pending);

            if (companyId.HasValue)
            {
                query = query.Where(r => r.CompanyId == companyId.Value);
            }

            if (!string.IsNullOrEmpty(sysMonth))
            {
                query = query.Where(r => r.SysMonth == sysMonth);
            }

            var recordPos = await query.OrderBy(r => r.CreatedTime).ToListAsync();
            return recordPos.Adapt<List<InventoryRecord>>();
        }

        /// <summary>
        /// 批量更新记录状态
        /// </summary>
        /// <param name="recordIds"></param>
        /// <param name="status"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        public async Task<int> BatchUpdateStatusAsync(List<Guid> recordIds, InventoryRecordStatus status, string? errorMessage = null)
        {
            var records = await _db.InventoryRecord
                .Where(r => recordIds.Contains(r.Id))
                .ToListAsync();

            foreach (var record in records)
            {
                record.Status = (int)status;
                record.UpdatedTime = DateTime.UtcNow;
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    record.ErrorMessage = errorMessage;
                }

                if (status == InventoryRecordStatus.Failed)
                {
                    record.RetryCount++;
                }
            }

            return await _db.SaveChangesAsync();
        }

        /// <summary>
        /// 检查指定盘点单的所有记录是否都已完成
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        public async Task<bool> CheckAllRecordsCompletedAsync(Guid inventoryItemId)
        {
            var incompleteCount = await _db.InventoryRecord
                .Where(r => r.InventoryItemId == inventoryItemId && r.Status != (int)InventoryRecordStatus.Completed)
                .CountAsync();

            return incompleteCount == 0;
        }

    }
}
