﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_AdvancePaymentDebtDetailPo2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdvancePaymentDebtDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AdvancePaymentItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DebtBillNo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    AccountPeriodType = table.Column<int>(type: "int", nullable: false, comment: "账期类型"),
                    InvoiceTime = table.Column<DateTime>(type: "datetime2", maxLength: 200, nullable: true, comment: "开票日期"),
                    ReturnDays = table.Column<int>(type: "int", nullable: true),
                    EstimateReturnDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActualPaymentDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AdvancePaymentDays = table.Column<int>(type: "int", nullable: true),
                    MonthRate = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    FinanceDiscount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AgentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    AgentName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AdvanceAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    AdvanceCreditTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvancePaymentDebtDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdvancePaymentDebtDetail_AdvancePaymentItem_AdvancePaymentItemId",
                        column: x => x.AdvancePaymentItemId,
                        principalTable: "AdvancePaymentItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdvancePaymentDebtDetail_AdvancePaymentItemId",
                table: "AdvancePaymentDebtDetail",
                column: "AdvancePaymentItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdvancePaymentDebtDetail");
        }
    }
}
