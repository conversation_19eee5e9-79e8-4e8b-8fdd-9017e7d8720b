﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addCustomizeInvoiceDetailAmount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "CreditDetailAmount",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,4)",
                nullable: true,
                comment: "应收明细金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldComment: "应收明细金额");

            migrationBuilder.AddColumn<decimal>(
                name: "CustomizeInvoiceDetailAmount",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m,
                comment: "开票明细金额");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomizeInvoiceDetailAmount",
                table: "CustomizeInvoiceCredit");

            migrationBuilder.AlterColumn<decimal>(
                name: "CreditDetailAmount",
                table: "CustomizeInvoiceCredit",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "应收明细金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true,
                oldComment: "应收明细金额");
        }
    }
}
