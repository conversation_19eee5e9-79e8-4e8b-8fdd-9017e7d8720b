﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("AdvanceBusinessDetail")]
    [Comment("垫资评价明细")]
    public class AdvanceBusinessDetailPO:BasePo
    {
        
        public Guid AdvanceBusinessApplyId { get; set; }

        public int AccountPeriod { get; set; }

        public int ReceivePeriod { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal Discount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal BaseDiscount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal SCFDiscount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal SPDDiscount { get; set; }

        public Guid CreditId { get; set; }

        public Guid DebtDetailId { get; set; }

        [MaxLength(50)]
        public string CreditCode { get; set; }

        public DateTime CreditDate { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal CreditValue { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal? SalesTaxRate { get; set; }

        public DateTime? InvoiceDate { get; set; }

        [MaxLength(50)]
        public string DebtCode { get; set; }

        public DateTime? DebtDate { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal DebtValue { get; set; }

        [MaxLength(50)]
        public string ReceiveCode { get; set; }

        public DateTime? ReceiveDate { get; set; }

        public DateTime? ExpectReceiveDate { get; set; }

        [MaxLength(200)]
        public string PaymentCode { get; set; }

        public DateTime? PaymentDate { get; set; }

        public DateTime? ExpectPaymentDate { get; set; }

        /// <summary>
        /// 垫资比例Ratio
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal? ADFDiscount { get; set; }

        [ForeignKey("AdvanceBusinessApplyId")]
        public virtual AdvanceBusinessApplyPO AdvanceBusinessApply { get; set; }
    }
}
