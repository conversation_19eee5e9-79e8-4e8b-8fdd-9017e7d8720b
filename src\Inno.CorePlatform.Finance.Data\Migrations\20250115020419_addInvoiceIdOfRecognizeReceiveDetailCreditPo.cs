﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addInvoiceIdOfRecognizeReceiveDetailCreditPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "InvoiceNo",
                table: "RecognizeReceiveDetailCredit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AddColumn<Guid>(
                name: "InvoiceId",
                table: "RecognizeReceiveDetailCredit",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OrderId",
                table: "RecognizeReceiveDetailCredit",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderNo",
                table: "RecognizeReceiveDetailCredit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvoiceId",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.DropColumn(
                name: "OrderNo",
                table: "RecognizeReceiveDetailCredit");

            migrationBuilder.AlterColumn<string>(
                name: "InvoiceNo",
                table: "RecognizeReceiveDetailCredit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);
        }
    }
}
