﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class updatevarcharlength : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "CreditNo",
                table: "OutputInvoice",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "Credit",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "Credit",
                type: "nvarchar(max)",
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true,
                oldComment: "项目单号");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalName",
                table: "Credit",
                type: "nvarchar(max)",
                nullable: true,
                comment: "终端医院",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true,
                oldComment: "终端医院");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "CreditNo",
                table: "OutputInvoice",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectName",
                table: "Credit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目名称",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "项目名称");

            migrationBuilder.AlterColumn<string>(
                name: "ProjectCode",
                table: "Credit",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "项目单号",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "项目单号");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalName",
                table: "Credit",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "终端医院",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true,
                oldComment: "终端医院");
        }
    }
}
