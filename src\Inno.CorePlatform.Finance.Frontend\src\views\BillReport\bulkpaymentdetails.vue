<template>
    <div class="app-page-container">
        <div class="app-page-header">
            <el-breadcrumb separator-icon="ArrowRight">
                <el-breadcrumb-item>批量付款明细报表</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="app-page-body" style="padding-top: 10px;">
            <template v-if="embedConfig.accessToken">
                <PowerBIReportEmbed class="PowerBI" :embed-config="embedConfig" cssClassName="reportClass"
                    :phased-embedding="false" :eventHandlers=eventHandlers @report-obj="setReportObj" />
            </template>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.PowerBI {
    height: 100%; 
    :deep(iframe) {
        border: none;
    }
}
</style>
<script lang="tsx" setup>
import {
    onMounted,
    reactive,
} from 'vue'; 
import PowerBIReportEmbed from '@inno/inno-mc-vue3/lib/components/powerBIReportEmbed';
import { models, Report } from 'https://static.innostic.com/web/static/powerbi-client/2.22.3/powerbi.min.js';
import { useRoute } from 'vue-router';
import request from '@/utils/request';
import { useUserStore } from '@inno/inno-mc-vue3/template/stores/user';
import { ElMessage } from 'element-plus';
//获取路由
const route = useRoute();
const { userId,username } = useUserStore();
let report: Report;
const embedConfig = reactive({
    type: 'report',
    id: '',
    embedUrl: '',
    accessToken: '',
    filters: [
        {
            $schema: 'http://powerbi.com/product/schema#basic',
            target: {
                table: '批量付款明细',
                column: '项目编码'
            },
            operator: 'In',
            values: [route.query.code],
            filterType: models.FilterType.BasicFilter,
            requireSingleSelection: true
        }
    ],
    tokenType: models.TokenType.Embed,
    settings: {
        navContentPaneEnabled: false,
        panes: {
            filters: {
                expanded: false,
                visible: false
            }
        }
    },
})

const eventHandlers = new Map([
    ['loaded', () => {
        console.log('报表已加载完毕', report);
        report.setPage('ReportSection1872fda82936037017b5');
    }
    ],
    ['rendered', () => console.log('报表开始渲染', report)],
    ['error', (event?) => {
        if (event) {
            console.error(event.detail);
        }
    },
    ],
    ['visualClicked', () => console.log('视图点击')],
    ['pageChanged', (event) => console.log('页面改变', event)],
])

const setReportObj = (value: Report) => {
    report = value;
}

const load = () => {
  request({
    url: window.gatewayUrl + 'api/config?key=finance-backend:BiConfig',
    method: 'get'
  }).then((res) => {
    if (res.data.BatchPaymentConfig == undefined) {
      console.log('未找到对应的大数据报表配置信息')
      return;
    }

    var paraWorkspaceId = res.data.BatchPaymentConfig.WorkspaceId;
    var paraReportId = res.data.BatchPaymentConfig.ReportId;
    request({
      url: window.gatewayUrl + 'v1.0/reportPortal/api/ReportForMobile/getEmbedToken',
      method: 'get',
      noMessage: true,
      params: { workspaceId: paraWorkspaceId, reportId: paraReportId,userId:userId,userName:username}
    }).then(({ data }) => {
      if (data.embedToken) {
        if (data.embedToken && data.embedReports.length) {
          const embedToken = data.embedToken;
          const embedReport = data.embedReports[0];
          embedConfig.embedUrl = embedReport?.embedUrl;
          embedConfig.id = embedReport?.reportId;
          embedConfig.accessToken = embedToken?.token;
        }
      }
    }).catch((err)=>{
        ElMessage({
          showClose: true,
          message: '账号未配置大数据报表权限',
          type: 'error',
          duration: 3 * 1000
        });
    });
  });
}

onMounted(() => {
    load()
});
</script>
