﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    public class DtmResponse
    {
        [JsonProperty("dtm_result")]
        public string DtmResult { get; set; }
        [JsonProperty("gid")]
        public string? Gid { get; set; }
        public static DtmResponse Success(string gid)
        {
            return new DtmResponse { DtmResult = "SUCCESS", Gid = gid };
        }
        public static DtmResponse Fail(Guid gid)
        {
            return new DtmResponse { DtmResult = "FAILURE", Gid = gid.ToString() };
        }
    }

    /// <summary>
    /// webApi返回基本对象
    /// </summary>
    public class SimpleReturnObject<T>
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public List<T> Details { get; set; }
    }

    public class RedisSaveObject<T>
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public List<T> Details { get; set; }
    }
    public abstract class BaseApiController<T> : ControllerBase
    {
        protected readonly DaprClient _daprClient;
        /// <summary>
        /// 基类日志对象
        /// </summary>
        protected readonly ILogger<T> _logger;
        /// <summary>
        /// EDA错误处理
        /// </summary>
        protected readonly IEDAFailureMsgClient _edaFailureMsgClient;
        public BaseApiController(DaprClient daprClient, ILogger<T> logger, IEDAFailureMsgClient edaFailureMsgClieng)
        {
            _daprClient = daprClient;
            _logger = logger;
            _edaFailureMsgClient = edaFailureMsgClieng;
        }
        /// <summary>
        /// 成功状态返回结果
        /// </summary>
        /// <param name="result">返回的数据</param>
        /// <returns></returns>
        protected SimpleReturnObject<Entity> Success<Entity>((Guid, string) result, List<Entity> details)
        {
            return new SimpleReturnObject<Entity> { Id = result.Item1.ToString(), Code = result.Item2, Details = details };
        }
        /// <summary>
        /// 通用返回，之所以有是因为要在swagger有
        /// </summary>
        /// <typeparam name="Entity"></typeparam>
        /// <param name="result"></param>
        /// <returns></returns>
        protected BaseResponseData<Entity> CommonReturn<Entity>(Entity result)
        {
            return new BaseResponseData<Entity> { Data = result };
        }
        protected async Task SendEDAFailureMsg(string inputJson, string topicName, Exception ex, string? callBackMethodRoute)
        {
            await _edaFailureMsgClient.SendFailureMsg(new FailureMsgInput
            {
                AppId = "finance-webapi",
                MsgBody = inputJson,
                ExceptionMessage = ex.StackTrace,
                FailReason = ex.Message,
                Topic = topicName,
                CallBackMethodRoute = callBackMethodRoute
            });
        }

        /// <summary>
        /// 获取其他能力中心调用时候的幂等标识Key
        /// </summary>
        /// <returns></returns>
        protected string? GetIdempotentKey()
        {
            return HttpContext.Request.Headers["MiDengFlag"];
        }
    }
}
