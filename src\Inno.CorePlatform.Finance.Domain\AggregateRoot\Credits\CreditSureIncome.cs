﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits
{
    public class CreditSureIncome
    {

        [Comment("应付id")]
        public Guid CreditId { get; set; }
          

        /// <summary>
        /// 收入确认金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        /// <summary>
        /// 收入确认日期
        /// </summary>
        public DateTimeOffset SureIncomeDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
