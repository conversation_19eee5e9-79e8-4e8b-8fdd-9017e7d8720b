﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CreditRecordItemRepository : EfBaseRepository<Guid, CreditRecordItem, CreditRecordItemPo>, ICreditRecordItemRepository
    {
        private FinanceDbContext _db;
        public CreditRecordItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> InsetrCredit(CreditRecordItem CreditRecordItem, List<CreditRecordDetail> CreditRecordDetail)
        {
            var CreditRecordItemPo = CreditRecordItem.Adapt<CreditRecordItemPo>();
            var CreditRecordDetailPo = CreditRecordDetail.Adapt<List<CreditRecordDetailPo>>();
            CreditRecordItemPo.CreditRecordDetail = CreditRecordDetailPo;
            _db.CreditRecordItems.Add(CreditRecordItemPo);
            return await _db.SaveChangesAsync();
        }

        public override async Task<int> UpdateAsync(CreditRecordItem root)
        {
            var isExist = await _db.CreditRecordItems.AnyAsync(x => x.Id == root.Id);
            if (isExist)
            {
                var po = root.Adapt<CreditRecordItemPo>();
                _db.CreditRecordItems.Update(po);
                if (UowJoined) return 0;
                return await _db.SaveChangesAsync();
            }
            else {
                return 0;
            }
        }

        protected override CreditRecordItemPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<CreditRecordItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
