﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Logging;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate
{
    public class PaymentAutoItem : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public PaymentAutoItem()
        {
            PaymentAutoDetails = new List<PaymentAutoDetail>();
        }

        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 附言
        /// </summary> 
        public string? TransferDiscourse { get; set; }

        #region 请求Id(OA流程)
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// OA最后审批人
        /// </summary>
        public string? OAAuditor { get; set; }
        /// <summary>
        /// OA最后审批时间
        /// </summary>
        public DateTimeOffset? OAAuditTime { get; set; }
        /// <summary>
        /// OA审批意见
        /// </summary>
        public string? OARemark { get; set; }
        #endregion

        /// <summary>
        /// 状态
        /// </summary>
        public PaymentAutoItemStatusEnum Status { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }


        /// <summary>
        /// 批量付款明细
        /// </summary>
        public virtual List<PaymentAutoDetail> PaymentAutoDetails { get; set; } = new List<PaymentAutoDetail>();
         
        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }
        /// <summary>
        /// 添加批量付款明细
        /// </summary>
        /// <param name="paymentAutoDetail"></param>
        /// <param name="userName"></param>
        /// <exception cref="DomainException"></exception>
        public void AddPaymentAutoDetail(PaymentAutoDetail paymentAutoDetail,string userName)
        {
            if(paymentAutoDetail == null)
            {
                throw new DomainException("批量付款明细不能为空!");
            }
            //paymentAutoDetail.Id=Guid.NewGuid();
            paymentAutoDetail.CreateBy(userName);
            PaymentAutoDetails.Add(paymentAutoDetail);
        }

        /// <summary>
        /// 修改批量付款明细
        /// </summary>
        /// <param name="paymentAutoDetail"></param>
        /// <param name="userName"></param>
        /// <exception cref="DomainException"></exception>
        public void UpdatePaymentAutoDetail(PaymentAutoDetail paymentAutoDetail, string userName)
        {
            if (paymentAutoDetail == null)
            {
                throw new DomainException("批量付款明细不能为空!");
            }

            var res = PaymentAutoDetails.FirstOrDefault(x => x.Id == paymentAutoDetail.Id);

            if (res == null)
            {
                throw new DomainException("批量付款明细不存在!");
            }

            paymentAutoDetail.UpdateBy(userName);

            res = paymentAutoDetail.Adapt(res);
        }

        /// <summary>
        /// 删除批量付款明细
        /// </summary>
        /// <param name="paymentAutoDetail"></param>
        /// <exception cref="DomainException"></exception>
        public void RemovePaymentAutoDetail(PaymentAutoDetail paymentAutoDetail)
        {
            if (paymentAutoDetail == null)
            {
                throw new DomainException("批量付款明细不能为空!");
            }

            var res = PaymentAutoDetails.FirstOrDefault(x => x.Id == paymentAutoDetail.Id);

            if (res == null)
            {
                throw new DomainException("批量付款明细不存在!");
            }

            PaymentAutoDetails.Remove(res);
        }

    }
}
