﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Status_To_RecognizeReceiveTempDetailPo : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "RecognizeReceiveTempDetail",
                type: "int",
                nullable: true);
            // 根据条件更新状态
            migrationBuilder.Sql(@"
                -- 更新明细状态（正常）
                UPDATE RecognizeReceiveTempDetail SET Status = 1 WHERE Id IN (
                    SELECT rrtd.Id 
                    FROM RecognizeReceiveItem rri 
                    INNER JOIN RecognizeReceiveTempDetail rrtd ON rri.ID = rrtd.RecognizeReceiveItemId
                    WHERE rri.Status != -1
                ) and Status is null;
            
                -- 更新明细状态（撤销）
                UPDATE RecognizeReceiveTempDetail SET Status = 2 WHERE Id IN (
                    SELECT rrtd.Id 
                    FROM RecognizeReceiveItem rri 
                    INNER JOIN RecognizeReceiveTempDetail rrtd ON rri.ID = rrtd.RecognizeReceiveItemId
                    WHERE rri.Status = -1
                ) and Status is null;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Status",
                table: "RecognizeReceiveTempDetail");
        }
    }
}
