﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addInvoiceReceipt : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "InvoiceReceiptItem",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BillCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "单号"),
                    BillDate = table.Column<DateTime>(type: "date", nullable: false, comment: "单据日期"),
                    CompanyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "公司Id"),
                    CompanyName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "公司名称"),
                    NameCode = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "公司Code"),
                    ServiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "业务单元Id"),
                    ServiceName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "业务单元名称"),
                    CustomerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "客户Id"),
                    CustomerName = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "客户"),
                    BackAmountDays = table.Column<int>(type: "int", nullable: false, comment: "回款天数"),
                    SaleAccountPeriodDays = table.Column<int>(type: "int", nullable: false, comment: "销售账期天数"),
                    ActualBackAmountDays = table.Column<int>(type: "int", nullable: false, comment: "实际回款天数"),
                    Status = table.Column<int>(type: "int", nullable: false, comment: "状态"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "备注"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceReceiptItem", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "InvoiceReceiptDetail",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvoiceReceiptItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreditCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InvoiceNo = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InvoiceCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    InvoiceCheckCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    InvoiceTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InvoiceAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    IsCancel = table.Column<bool>(type: "bit", nullable: true, comment: "是否取消"),
                    Remark = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "备注"),
                    CreatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UpdatedTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceReceiptDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceReceiptDetail_InvoiceReceiptItem_InvoiceReceiptItemId",
                        column: x => x.InvoiceReceiptItemId,
                        principalTable: "InvoiceReceiptItem",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceReceiptDetail_InvoiceReceiptItemId",
                table: "InvoiceReceiptDetail",
                column: "InvoiceReceiptItemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InvoiceReceiptDetail");

            migrationBuilder.DropTable(
                name: "InvoiceReceiptItem");
        }
    }
}
