﻿using Inno.CorePlatform.Common.DDD;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills
{
    public class InputBillSubmitDetailQuantity : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 入库货号明细ID
        /// </summary>
        public Guid StoreInDetailId { get; set; }
        /// <summary>
        /// 入库货号追溯ID
        /// </summary>
        public Guid SysBakId { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }
        /// <summary>
        /// 类型 暂留
        /// </summary>
        public int Type { get; set; }


        /// <summary>
        /// 提交详情Id
        /// </summary>
        public Guid InputBillSubmitDetailId { get; set; }
    }
}
