using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 金蝶回调验证服务接口
    /// </summary>
    public interface IKingdeeCallbackValidationService
    {
        /// <summary>
        /// 检查批量付款冲销的盘点状态
        /// </summary>
        /// <param name="input">批量付款输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        Task CheckInventoryStatusForBatchPayment(List<GenerateAbtInput> input, string operationType);

        /// <summary>
        /// 检查应收冲销的盘点状态
        /// </summary>
        /// <param name="input">应收冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        Task CheckInventoryStatusForCredit(GenerateAbtForCreditInput input, string operationType);

        /// <summary>
        /// 检查收款(退款)与预付单冲销的盘点状态
        /// </summary>
        /// <param name="input">收款冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        Task CheckInventoryStatusForPayment(KingdeeAbatementPaymentInput input, string operationType);

        /// <summary>
        /// 检查退款与应付单冲销的盘点状态
        /// </summary>
        /// <param name="input">应付冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        Task CheckInventoryStatusForDebt(KingdeeAbatementDebtInput input, string operationType);
    }
}
