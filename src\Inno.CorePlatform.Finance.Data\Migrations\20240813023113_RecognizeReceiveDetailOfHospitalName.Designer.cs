﻿// <auto-generated />
using System;
using Inno.CorePlatform.Finance.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    [DbContext(typeof(FinanceDbContext))]
    [Migration("20240813023113_RecognizeReceiveDetailOfHospitalName")]
    partial class RecognizeReceiveDetailOfHospitalName
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AbatementPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Abtdate")
                        .HasColumnType("datetime2")
                        .HasComment("冲销日期");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditBillCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("冲销的单据号");

                    b.Property<string>("CreditType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("冲销的单据类型");

                    b.Property<string>("DebtBillCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("被冲销的单据号");

                    b.Property<string>("DebtType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("被冲销的单据类型");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("冲销金额");

                    b.HasKey("Id");

                    b.ToTable("Abatement", t =>
                        {
                            t.HasComment("冲销表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceBusinessApplyPO", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplyNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("date");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("垫资单号");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("EndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("EndHospitalId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EndHospitalName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ExpectAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ExpectInterestAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ExpectedAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("HospitalId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HospitalName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsInvoice")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTakeOver")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVerify")
                        .HasColumnType("bit");

                    b.Property<string>("NonVerifyRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldZXCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OperateNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("PreTimeOutReceivableAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PreUnrecycledReceivableAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ProvidePayDays")
                        .HasColumnType("int");

                    b.Property<decimal?>("RateOfYear")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Ratio")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("RealSupplyChainDiscounts")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("RealUseDays")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ReceivableAmountOfNon")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ReceivableAmountOfTimeout")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ReturnMoneyDays")
                        .HasColumnType("int");

                    b.Property<decimal?>("SalesVolume")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ServiceGroup")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StatusName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SupplyChainDiscounts")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TimeOutReceivableAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalAmountOfMonth")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalDiscounts")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnrecycledReceivableAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("AdvanceBusinessApply", t =>
                        {
                            t.HasComment("垫资申请单");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceBusinessDetailPO", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("ADFDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<int>("AccountPeriod")
                        .HasColumnType("int");

                    b.Property<Guid>("AdvanceBusinessApplyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("BaseDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreditDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreditId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("CreditValue")
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("DebtCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DebtDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DebtDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("DebtValue")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<DateTime?>("ExpectPaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpectReceiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PaymentCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReceiveCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ReceiveDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ReceivePeriod")
                        .HasColumnType("int");

                    b.Property<decimal>("SCFDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal>("SPDDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal?>("SalesTaxRate")
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("AdvanceBusinessApplyId");

                    b.ToTable("AdvanceBusinessDetail", t =>
                        {
                            t.HasComment("垫资评价明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceFundBusinessCheckDetailPO", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("ADFDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<int>("AccountPeriod")
                        .HasColumnType("int");

                    b.Property<string>("AdvanceBusinessApplyCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("AdvanceFundBusinessCheckItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AdvanceFundBusinessDays")
                        .HasColumnType("int");

                    b.Property<decimal>("BaseDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreditDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreditId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("CreditValue")
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("DebtCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DebtDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DebtDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("DebtValue")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<DateTime?>("ExpectPaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpectReceiveDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("HospitalId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HospitalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("IsCheckedHospital")
                        .HasColumnType("int");

                    b.Property<int?>("IsProcessAllMage")
                        .HasColumnType("int");

                    b.Property<string>("PaymentCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("RateOfYear")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceiveCode")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime?>("ReceiveDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ReceivePeriod")
                        .HasColumnType("int");

                    b.Property<decimal>("SCFDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal>("SPDDiscount")
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal?>("SalesTaxRate")
                        .HasColumnType("decimal(12,2)");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalDiscounts")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("AdvanceFundBusinessCheckItemId");

                    b.ToTable("AdvanceFundBusinessCheckDetail", t =>
                        {
                            t.HasComment("垫资盘点明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceFundBusinessCheckItemPO", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("AdvanceFundBusinessCheckItem", t =>
                        {
                            t.HasComment("垫资盘点单");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("AbatedStatus")
                        .HasColumnType("int")
                        .HasComment("冲销状态");

                    b.Property<int?>("Auto")
                        .HasColumnType("int");

                    b.Property<string>("AutoType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("AutoTypeName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("BillCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<DateTime?>("BillDate")
                        .HasColumnType("date")
                        .HasComment("单据日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int?>("CreditType")
                        .HasColumnType("int");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerOrderCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("客户订单号");

                    b.Property<string>("DeptName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FinishDate")
                        .HasColumnType("date");

                    b.Property<Guid?>("GroupId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("分组Id");

                    b.Property<Guid?>("HospitalId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("终端医院Id");

                    b.Property<string>("HospitalName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("终端医院");

                    b.Property<int?>("InvoiceStatus")
                        .HasColumnType("int");

                    b.Property<bool?>("IsInvoiced")
                        .HasColumnType("bit")
                        .HasComment("是否开票");

                    b.Property<int?>("IsLongTerm")
                        .HasColumnType("int");

                    b.Property<int?>("IsNoNeedInvoice")
                        .HasColumnType("int")
                        .HasComment("是否无需开票,1=无需开票，0 or null=需要开票");

                    b.Property<int?>("IsSureIncome")
                        .HasColumnType("int");

                    b.Property<DateTime?>("IsSureIncomeDate")
                        .HasColumnType("date");

                    b.Property<string>("LatestUniCode")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("客户统一社会信用代码");

                    b.Property<int?>("Mark")
                        .HasColumnType("int");

                    b.Property<string>("NameCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("订单号");

                    b.Property<string>("OriginOrderNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("原始订单号");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目名称");

                    b.Property<string>("RelateCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("SaleSource")
                        .HasColumnType("int")
                        .HasComment("销售订单来源");

                    b.Property<Guid?>("SaleSystemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SaleSystemName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("销售子系统名称");

                    b.Property<int?>("SaleType")
                        .HasColumnType("int");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShipmentCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SunPurchaseRelatecode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("阳采单号");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BillCode")
                        .IsUnique()
                        .HasFilter("[BillCode] IS NOT NULL");

                    b.ToTable("Credit");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditProjectDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("金额合计");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("CreditId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应付id");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目单号");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目id");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目名称");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.ToTable("CreditProjectDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditRecordDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("AbatedValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("CreditId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreditRecordItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("IsLongTerm")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.HasIndex("CreditRecordItemId");

                    b.ToTable("CreditRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditRecordItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单号日期");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("公司Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号中文名");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号");

                    b.HasKey("Id");

                    b.ToTable("CreditRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceClassifyPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AttachFileIds")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("附件Ids");

                    b.Property<string>("BillCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("编号");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("公司");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("客户邮箱");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("客户");

                    b.Property<string>("Invoiceofclassfiy")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasComment("开票主体");

                    b.Property<string>("OARequestId")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("OARequestId");

                    b.Property<string>("RelationCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("备注");

                    b.Property<string>("SaleSystemName")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("销售子系统名称");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasComment("状态");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BillCode")
                        .IsUnique();

                    b.ToTable("CustomizeInvoiceClassify", t =>
                        {
                            t.HasComment("运营制作开票单归类");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceCreditPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("应收单号");

                    b.Property<string>("CustomizeInvoiceItemCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("制作开票Code");

                    b.Property<Guid>("CustomizeInvoiceItemId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("制作开票Id");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("金额");

                    b.HasKey("Id");

                    b.HasIndex("CustomizeInvoiceItemId");

                    b.ToTable("CustomizeInvoiceCredit", t =>
                        {
                            t.HasComment("运营制作开票单与应收表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditBillCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("应收单号");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位ID");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位名称");

                    b.Property<string>("CustomizeInvoiceIndex")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid>("CustomizeInvoiceItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNo")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("订单号");

                    b.Property<string>("OriginDetailId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("原明细id");

                    b.Property<string>("OriginProductName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("原始品名（不变）");

                    b.Property<string>("OriginSpecification")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("原始规格型号");

                    b.Property<decimal?>("OriginalPrice")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("原始单价");

                    b.Property<string>("PackUnit")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("计量单位");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("单价");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("货号Id");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货品名称（开票名称）");

                    b.Property<string>("ProductNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货号");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("数量");

                    b.Property<string>("RelateCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("关联单号");

                    b.Property<string>("Specification")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("规格型号");

                    b.Property<string>("Tag")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("税额");

                    b.Property<decimal>("TaxRate")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("税率");

                    b.Property<string>("TaxTypeNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("税收分类编码");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("金额");

                    b.HasKey("Id");

                    b.HasIndex("CustomizeInvoiceItemId");

                    b.ToTable("CustomizeInvoiceDetail", t =>
                        {
                            t.HasComment("运营制作开票单合并明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApproveRemark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("审批备注");

                    b.Property<string>("AttachFileIds")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("附件Ids");

                    b.Property<DateTime?>("BillDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("BlueRedInvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ChangedStatus")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("编号");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位ID");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位名称");

                    b.Property<Guid?>("CustomizeInvoiceClassifyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("开票单分类Id");

                    b.Property<string>("InvoiceCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("InvoiceTotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("发票总金额");

                    b.Property<int>("InvoiceType")
                        .HasColumnType("int")
                        .HasComment("发票类型");

                    b.Property<bool>("IsInvoiced")
                        .HasColumnType("bit")
                        .HasComment("是否已开票");

                    b.Property<bool>("IsPush")
                        .HasColumnType("bit")
                        .HasComment("是否提交（推送金碟）");

                    b.Property<string>("NameCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司Code");

                    b.Property<string>("OARequestId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RedOffsetCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RedOffsetOpter")
                        .HasColumnType("int");

                    b.Property<int?>("RedOffsetReason")
                        .HasColumnType("int");

                    b.Property<string>("RelationCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("RelationType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasComment("状态");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("CustomizeInvoiceItem", t =>
                        {
                            t.HasComment("运营制作开票单");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceRedDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditBillCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("应收单号");

                    b.Property<Guid?>("CustomizeInvoiceItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IsInvoiced")
                        .HasColumnType("bit");

                    b.Property<string>("OriginDetailId")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("原明细id");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("单价");

                    b.Property<string>("ProductNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货号");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("数量");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("CustomizeInvoiceRedDetail", t =>
                        {
                            t.HasComment("运营制作开票单红冲明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceSubDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditBillCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("应收单号");

                    b.Property<Guid?>("CustomizeInvoiceItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OriginDetailId")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("原明细id");

                    b.Property<string>("OriginProductName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("原始品名（不变）");

                    b.Property<string>("OriginSpecification")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("原始规格型号");

                    b.Property<string>("PackUnit")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("计量单位");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("单价");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("货号Id");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货品名称（开票名称）");

                    b.Property<string>("ProductNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货号");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("数量");

                    b.Property<string>("Specification")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("规格型号");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("CustomizeInvoiceSubDetail", t =>
                        {
                            t.HasComment("运营制作开票单明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtDetailExcutePo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("DebtDetailId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应付执行计划Id");

                    b.Property<string>("ExcuteType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单号");

                    b.Property<string>("PaymentCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单号");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime2")
                        .HasComment("付款时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("付款金额");

                    b.HasKey("Id");

                    b.HasIndex("DebtDetailId");

                    b.ToTable("DebtDetailExcute", t =>
                        {
                            t.HasComment("应付付款计划执行明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccountPeriodType")
                        .HasColumnType("int")
                        .HasComment("账期类型");

                    b.Property<DateTime?>("BackPayTime")
                        .HasColumnType("datetime2")
                        .HasComment("回款日期");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<decimal?>("CostDiscount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("厂家折扣");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("CreditId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应收单Id");

                    b.Property<Guid?>("DebtId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应付单Id");

                    b.Property<decimal?>("Discount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("折扣");

                    b.Property<decimal?>("DistributionDiscount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("基础折扣");

                    b.Property<DateTime?>("DraftBillExpireDate")
                        .HasMaxLength(200)
                        .HasColumnType("datetime2")
                        .HasComment("到期日");

                    b.Property<decimal?>("FinanceDiscount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("供应链金融折扣");

                    b.Property<bool?>("IsInvoiceReceipt")
                        .HasColumnType("bit")
                        .HasComment("是否发票入账");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("订单号");

                    b.Property<decimal?>("OriginValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("折前金额(原始金额)");

                    b.Property<DateTime?>("ProbablyPayTime")
                        .HasColumnType("datetime2")
                        .HasComment("预计付款日期");

                    b.Property<string>("PurchaseCode")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("采购单号");

                    b.Property<string>("ReceiveCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("收款单号");

                    b.Property<string>("Settletype")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("结算方式");

                    b.Property<decimal?>("SpdDiscount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("SPD折扣");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("状态");

                    b.Property<decimal?>("TaxDiscount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("税率折扣");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("付款金额");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.HasIndex("DebtId");

                    b.ToTable("DebtDetail", t =>
                        {
                            t.HasComment("应付付款计划明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtPaymentUseDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DebtCode")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("应付单号");

                    b.Property<Guid?>("DebtId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应付单Id");

                    b.Property<Guid?>("RelateId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("付款申请Id");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("UseAmount")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("使用金额");

                    b.Property<string>("UseCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("使用单号");

                    b.HasKey("Id");

                    b.ToTable("DebtPaymentUseDetail", t =>
                        {
                            t.HasComment("应付当着付款单使用明细表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("AbatedStatus")
                        .HasColumnType("int")
                        .HasComment("冲销状态");

                    b.Property<string>("AccountPeriodScale")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Auto")
                        .HasColumnType("int");

                    b.Property<string>("AutoType")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("AutoTypeName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BillCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<DateTime?>("BillDate")
                        .HasColumnType("date");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CoinCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CoinName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int?>("DebtType")
                        .HasColumnType("int");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("InvoiceStatus")
                        .HasColumnType("int");

                    b.Property<string>("NameCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("订单号");

                    b.Property<Guid?>("PaymentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PerPaymentCodes")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ProducerOrderNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("厂家订单号");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目名称");

                    b.Property<string>("PurchaseCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("采购单号");

                    b.Property<string>("PurchaseContactNo")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("采购合同单号");

                    b.Property<decimal?>("RMBAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RelateCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("关联单号");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BillCode")
                        .IsUnique()
                        .HasFilter("[BillCode] IS NOT NULL");

                    b.ToTable("Debt");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtProjectDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("金额合计");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("DebtId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目单号");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目id");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目名称");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("DebtId");

                    b.ToTable("DebtProjectDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtRecordDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("AbatedValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("DebtId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DebtRecordItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("DebtId");

                    b.HasIndex("DebtRecordItemId");

                    b.ToTable("DebtRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtRecordItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单号日期");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("公司Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号中文名");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号");

                    b.HasKey("Id");

                    b.ToTable("DebtRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ExchangeRatePo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CurName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Effectdate")
                        .HasColumnType("date");

                    b.Property<decimal>("Excval")
                        .HasColumnType("decimal(18,10)");

                    b.Property<DateTime>("Expirydate")
                        .HasColumnType("date");

                    b.Property<decimal>("Indirectexrate")
                        .HasColumnType("decimal(18,10)");

                    b.Property<string>("OrgcurName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("ExchangeRate", t =>
                        {
                            t.HasComment("汇率表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillDebtPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("DebtAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DebtCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("InputBillId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("InputBillId");

                    b.ToTable("InputBillDebt");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("InputBillId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("NoTaxAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("NoTaxCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,10)");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TaxCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("InputBillId");

                    b.ToTable("InputBillDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("BillTime")
                        .HasColumnType("date");

                    b.Property<string>("CompanName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("InvoiceCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("NotaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PurchaseDutyNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SaleDutyNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("InputBill");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("BusinessType")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("InputBillId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("NoTaxAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("NoTaxCost")
                        .HasColumnType("decimal(18,10)");

                    b.Property<Guid?>("OriginalId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProducerOrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ProductNameId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProductNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,10)");

                    b.Property<int>("ReceivedNumber")
                        .HasColumnType("int");

                    b.Property<DateTime>("StoreInDate")
                        .HasColumnType("date");

                    b.Property<string>("StoreInItemCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TaxCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("InputBillId");

                    b.ToTable("InputBillSubmitDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailQuantityPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("InputBillSubmitDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,10)");

                    b.Property<Guid>("StoreInDetailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SysBakId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("InputBillSubmitDetailId");

                    b.ToTable("InputBillSubmitDetailQuantity");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InventoryItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdvanceRecordCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("垫资盘点单号");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("公司Id");

                    b.Property<string>("CompanyLongName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("公司完整名称");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditRecordCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("应收盘点单号");

                    b.Property<string>("DebtRecordCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("应付盘点单号");

                    b.Property<string>("Exchange")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("换货盘点单号");

                    b.Property<DateTimeOffset?>("FinishTime")
                        .HasColumnType("datetimeoffset")
                        .HasComment("完成时间");

                    b.Property<string>("GroupEntrustCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("集团委托盘点单号");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("跟台盘点单号");

                    b.Property<string>("PaymentRecordCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("付款盘点单号");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("状态：0-待启动库存盘点，1-库存盘点中，2-库存盘点完成，99-完成");

                    b.Property<string>("Store")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)")
                        .HasComment("库存盘点单号");

                    b.Property<string>("SureIncomeCode")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("待确认收入盘点单号");

                    b.Property<string>("SysMonth")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("盘点月度");

                    b.Property<string>("TempStore")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("暂存盘点单号");

                    b.Property<string>("ThirdStore")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("第三方库存盘点单号");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("InventoryItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceCreditPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal?>("CreditAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("应收单金额");

                    b.Property<Guid?>("CreditId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("相关联应收单Id");

                    b.Property<string>("CustomizeInvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("运营制作开票Code");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("InvoiceAmountNoTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InvoiceCheckCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("InvoiceTime")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsCancel")
                        .HasColumnType("bit")
                        .HasComment("是否取消");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<decimal?>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.ToTable("InvoiceCredit");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoicePo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomizeInvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("运营制作开票Code");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("InvoiceAmountNoTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InvoiceCheckCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("InvoiceTime")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsCancel")
                        .HasColumnType("bit")
                        .HasComment("是否取消");

                    b.Property<bool?>("IsInit")
                        .HasColumnType("bit")
                        .HasComment("是否初始化数据");

                    b.Property<bool?>("IsRedOff")
                        .HasColumnType("bit")
                        .HasComment("是否红冲");

                    b.Property<string>("NameCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司Code");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("项目名称");

                    b.Property<decimal?>("ReceiveAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("认款金额");

                    b.Property<decimal?>("RedAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("红冲金额");

                    b.Property<string>("RelateInvoiceNo")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("红/蓝票号");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<int?>("SPDStatus")
                        .HasColumnType("int");

                    b.Property<int?>("SunPurchaseStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("Invoice");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceReceiptDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InvoiceCheckCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceNo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("InvoiceReceiptItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("InvoiceTime")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsCancel")
                        .HasColumnType("bit")
                        .HasComment("是否取消");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceReceiptItemId");

                    b.ToTable("InvoiceReceiptDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceReceiptItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("ActualBackAmountDays")
                        .HasColumnType("int")
                        .HasComment("实际回款天数");

                    b.Property<string>("AttachFileIds")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("附件Ids");

                    b.Property<int>("BackAmountDays")
                        .HasColumnType("int")
                        .HasComment("回款天数");

                    b.Property<string>("BillCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("date")
                        .HasComment("单据日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("公司Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("客户Id");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("客户");

                    b.Property<string>("NameCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("公司Code");

                    b.Property<string>("OARequestId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<int>("SaleAccountPeriodDays")
                        .HasColumnType("int")
                        .HasComment("销售账期天数");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("业务单元Id");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("业务单元名称");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("状态");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("InvoiceReceiptItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.OutputInvoicePo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AmountOfNoTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreditNo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CustomizeInvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal?>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InvoiceCheckCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("InvoiceNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("InvoiceTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProductName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ProductNo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal?>("Quantity")
                        .HasColumnType("decimal(18,10)");

                    b.Property<decimal?>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Unit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("decimal(30,13)");

                    b.Property<decimal?>("UnitPriceOfNoTax")
                        .HasColumnType("decimal(30,13)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("OutputInvoice");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoAgentBankInfoPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Account")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AccountName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BankName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Contractno")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CostBearingParty")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("InvoiceNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayClassify")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("支付类型");

                    b.Property<Guid>("PaymentAutoItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Paymentabroad")
                        .HasColumnType("int");

                    b.Property<string>("Postscript")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Transactioncoding")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransferDiscourse")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("转账附言");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int?>("Ynpush")
                        .HasColumnType("int");

                    b.Property<string>("importGoods")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("PaymentAutoItemId");

                    b.ToTable("PaymentAutoAgent");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("DebtDetilId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("应付付款计划明细Id");

                    b.Property<Guid>("PaymentAutoItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PaymentCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("datetime2")
                        .HasComment("付款时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("付款金额");

                    b.HasKey("Id");

                    b.HasIndex("DebtDetilId");

                    b.HasIndex("PaymentAutoItemId");

                    b.ToTable("PaymentAutoDetail", t =>
                        {
                            t.HasComment("批量付款明细表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单据日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NameCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("OAAuditTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OAAuditor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OARemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OARequestId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasComment("状态");

                    b.Property<string>("TransferDiscourse")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("整个附言");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("PaymentAutoItem", t =>
                        {
                            t.HasComment("批量付款单表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("AbatedStatus")
                        .HasColumnType("int")
                        .HasComment("冲销状态");

                    b.Property<int?>("AdvancePayMode")
                        .HasColumnType("int");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单据日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单号");

                    b.Property<string>("CoinCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CoinName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal?>("CreditAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("额度金额");

                    b.Property<string>("NameCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("NearOrderUseValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("近期订单使用金额");

                    b.Property<string>("PayClassify")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("支付类型");

                    b.Property<string>("PaymentAutoItemCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("批量付款单号");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("datetime2")
                        .HasComment("付款时间");

                    b.Property<string>("ProducerOrderNo")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("厂家单号");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目名称");

                    b.Property<string>("PurchaseCode")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("采购单号");

                    b.Property<decimal?>("RMBAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("RelateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasComment("付款单类型");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasComment("付款金额");

                    b.HasKey("Id");

                    b.ToTable("Payment", t =>
                        {
                            t.HasComment("付款单表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentRecordDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("AbatedValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("PaymentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PaymentRecordItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("PaymentId");

                    b.HasIndex("PaymentRecordItemId");

                    b.ToTable("PaymentRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentRecordItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单号日期");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("公司Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("公司名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operator")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号中文名");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("操作人账号");

                    b.HasKey("Id");

                    b.ToTable("PaymentRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PreCustomizeInvoiceDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PackUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("PreCustomizeInvoiceItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProductName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RelateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Specification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TaxTypeNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("PreCustomizeInvoiceItemId");

                    b.ToTable("PreCustomizeInvoiceDetail", t =>
                        {
                            t.HasComment("预开票明细");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PreCustomizeInvoiceItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("单号");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NameCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目名称");

                    b.Property<int?>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("PreCustomizeInvoiceItem", t =>
                        {
                            t.HasComment("预开票表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PurchasePayPlanPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("AccountPeriodDays")
                        .HasColumnType("int")
                        .HasComment("账期天数");

                    b.Property<int>("AccountPeriodType")
                        .HasColumnType("int")
                        .HasComment("付款计划账期");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ForwardPurchaseCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("远期采购单号");

                    b.Property<string>("NameCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("备注");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("价格");

                    b.Property<DateTime?>("ProbablyPayTime")
                        .HasColumnType("datetime2")
                        .HasComment("预计付款时间");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("备注");

                    b.Property<string>("ProductNo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PurchaseCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("采购单号");

                    b.Property<Guid?>("PurchaseId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("采购Id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasComment("数量");

                    b.Property<decimal>("Ratio")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("比例");

                    b.Property<decimal>("RatioPrice")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("比例价格");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("PurchasePayPlan", t =>
                        {
                            t.HasComment("采购付款计划");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RebateProvisionDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActualarrivalDate")
                        .HasColumnType("Date")
                        .HasComment("实际收到日期");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("供应商Id");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("供应商");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ConfirmTaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("金额");

                    b.Property<DateTime?>("ConfirmationDate")
                        .HasColumnType("Date")
                        .HasComment("确认函日期");

                    b.Property<DateTime?>("CouponDate")
                        .HasColumnType("Date")
                        .HasComment("发票日期/优惠卷日期");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("客户Id");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("客户");

                    b.Property<decimal?>("NextAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("下家对应金额");

                    b.Property<string>("NextInvoiceOrCoupon")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("下家返利发票号/优惠券(针对已结算的)");

                    b.Property<int?>("NextRebateMethod")
                        .HasColumnType("int")
                        .HasComment("下家返利方式 A:发票, B:优惠劵");

                    b.Property<decimal?>("NextTaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("下家不含税金额");

                    b.Property<string>("PeriodSummary")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("返利期间(摘要)");

                    b.Property<string>("Policydeadline")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("政策期限");

                    b.Property<string>("ProjectCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目单号");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目id");

                    b.Property<string>("ProjectName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasComment("项目名称");

                    b.Property<decimal?>("RebateAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("返利金额");

                    b.Property<Guid?>("RebateProvisionItemId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("返利计提");

                    b.Property<decimal?>("RebateTaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("返利不含税金额");

                    b.Property<int?>("RebateType")
                        .HasColumnType("int")
                        .HasComment("返利类型 A:平移返利, B:指标返利, C:补偿返利	");

                    b.Property<string>("Redinvoice")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("厂家红票发票号");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("RebateProvisionItemId");

                    b.ToTable("RebateProvisionDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RebateProvisionItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BillCode")
                        .HasColumnType("nvarchar(450)")
                        .HasComment("单据号");

                    b.Property<DateTime?>("BillDate")
                        .HasColumnType("date")
                        .HasComment("单据日期");

                    b.Property<string>("BusinessDeptFullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessDeptShortName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NameCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProvisionType")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("BillCode")
                        .IsUnique()
                        .HasFilter("[BillCode] IS NOT NULL");

                    b.ToTable("RebateProvisionItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveConfigPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("CustomerId")
                        .HasMaxLength(50)
                        .HasColumnType("uniqueidentifier")
                        .HasComment("客户Id");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("客户名称");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("RecognizeReceiveConfig", t =>
                        {
                            t.HasComment("认款配置表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("发票号/订单号");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("付款单位Id");

                    b.Property<string>("CustomerNme")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位名称");

                    b.Property<Guid?>("HospitalId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("终端医院Id");

                    b.Property<string>("HospitalName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("终端医院");

                    b.Property<bool>("IsSkip")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("RecognizeDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("RecognizeReceiveItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ServiceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("RecognizeReceiveItemId");

                    b.ToTable("RecognizeReceiveDetail", t =>
                        {
                            t.HasComment("认款单详情");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AttachFileIds")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("批量附件Id");

                    b.Property<DateTime>("BillDate")
                        .HasColumnType("datetime2")
                        .HasComment("单号日期");

                    b.Property<int?>("BusinessDepId")
                        .HasColumnType("int");

                    b.Property<string>("BusinessDeptFullName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("核算部门名称路径");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("核算部门Id路径");

                    b.Property<int>("Classify")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("认款单号");

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("收款单位Id");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("收款单位名称");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("付款单位Id");

                    b.Property<string>("CustomerNme")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("付款单位名称");

                    b.Property<DateTime?>("DraftBillExpireDate")
                        .HasMaxLength(200)
                        .HasColumnType("datetime2")
                        .HasComment("到期日");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("项目单号");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasComment("项目名称");

                    b.Property<string>("ReceiveCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("收款单号");

                    b.Property<DateTime>("ReceiveDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("ReceiveValue")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("收款金额");

                    b.Property<string>("RelateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Settletype")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("结算方式");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasComment("本次认款金额");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("RecognizeReceiveItem", t =>
                        {
                            t.HasComment("认款单表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveTempDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BusinessDeptFullName")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("核算部门名称路径");

                    b.Property<string>("BusinessDeptFullPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasComment("核算部门Id路径");

                    b.Property<int?>("BusinessDeptId")
                        .HasColumnType("int");

                    b.Property<int?>("Classify")
                        .HasColumnType("int");

                    b.Property<int?>("CollectionType")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProjectCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目单号");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("项目Id");

                    b.Property<string>("ProjectName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("项目名称");

                    b.Property<DateTime>("RecognizeDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("RecognizeReceiveItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("RecognizeReceiveItemId");

                    b.ToTable("RecognizeReceiveTempDetail", t =>
                        {
                            t.HasComment("认款单详情");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ReconciliationIncomeDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BillType")
                        .HasColumnType("int");

                    b.Property<string>("BillTypeStr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(21,10)");

                    b.Property<decimal?>("CostOfNoTax")
                        .HasColumnType("decimal(21,10)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Income")
                        .HasColumnType("decimal(21,10)");

                    b.Property<decimal?>("IncomeOfNoTax")
                        .HasColumnType("decimal(21,10)");

                    b.Property<int?>("Mark")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ReconciliationItemId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("对账Id");

                    b.Property<string>("SaleOrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("StandardUnitCost")
                        .HasColumnType("decimal(18,10)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("ReconciliationItemId");

                    b.ToTable("ReconciliationIncomeDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ReconciliationItemPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("BillDate")
                        .HasColumnType("date")
                        .HasComment("单据日期");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SysMonth")
                        .HasColumnType("nvarchar(max)")
                        .HasComment("系统月度");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("ReconciliationItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ReconciliationStockDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BillType")
                        .HasColumnType("int");

                    b.Property<string>("BillTypeStr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ChangeAmount")
                        .HasColumnType("decimal(18,10)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(21,10)");

                    b.Property<decimal?>("CostOfNoTax")
                        .HasColumnType("decimal(21,10)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Income")
                        .HasColumnType("decimal(21,10)");

                    b.Property<decimal?>("IncomeOfNoTax")
                        .HasColumnType("decimal(21,10)");

                    b.Property<int?>("Mark")
                        .HasColumnType("int");

                    b.Property<string>("OrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ReconciliationItemId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("对账Id");

                    b.Property<string>("SaleOrderNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("StandardUnitCost")
                        .HasColumnType("decimal(18,10)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.HasIndex("ReconciliationItemId");

                    b.ToTable("ReconciliationStockDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.SubLogPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Operate")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Source")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("SubLog", t =>
                        {
                            t.HasComment("订阅日志表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.SunPurchaseInvoiceDetailPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("GGXHSM")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("规格型号说明(选填)");

                    b.Property<string>("GLMXBH")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("关联明细编号(选填)");

                    b.Property<string>("HCTBDM")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("耗材统编代码(必填)");

                    b.Property<string>("HCXFDM")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("耗材细分代码(选填)");

                    b.Property<decimal>("HSDJ")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("含税单价(必填)");

                    b.Property<decimal>("HSJE")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("含税金额(必填)");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("LSJ")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("零售价(必填)");

                    b.Property<decimal>("PFJ")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("批发价(必填)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("货号Id");

                    b.Property<string>("ProductNo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("货号");

                    b.Property<string>("PurchaseCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("顺序号(必填)");

                    b.Property<string>("QYBDDM")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("企业本地代码(选填)");

                    b.Property<string>("SCPH")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("生产批号(必填)");

                    b.Property<DateTime>("SCRQ")
                        .HasColumnType("datetime2")
                        .HasComment("生产日期(必填)");

                    b.Property<decimal>("SE")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("税额(必填)");

                    b.Property<string>("SFCH")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("是否冲红(必填)");

                    b.Property<string>("SFWPSFP")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("是否无配送发票(必填)");

                    b.Property<decimal>("SL")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("税率(必填)");

                    b.Property<decimal>("SPSL")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("商品数量(必填)");

                    b.Property<string>("SXH")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("顺序号(必填)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("WPSFPSM")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("无配送发票说明(选填)");

                    b.Property<decimal>("WSDJ")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("无税单价(必填)");

                    b.Property<string>("XSDDH")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("销售订单号(选填)");

                    b.Property<DateTime>("YXRQ")
                        .HasColumnType("datetime2")
                        .HasComment("有效日期(必填)");

                    b.Property<string>("ZCZH")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("注册证号(必填)");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.ToTable("SunPurchaseInvoiceDetail", t =>
                        {
                            t.HasComment("阳采发票明细表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.TempStoreToKingdeeLogPo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("AccountingDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Classify")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PreRequestBody")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PushDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("RequestBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResponseBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset?>("UpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.HasKey("Id");

                    b.ToTable("TempStoreTokKngdeeLog", t =>
                        {
                            t.HasComment("暂存出入库记录提交给金蝶日志表");
                        });
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceBusinessDetailPO", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.AdvanceBusinessApplyPO", "AdvanceBusinessApply")
                        .WithMany()
                        .HasForeignKey("AdvanceBusinessApplyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdvanceBusinessApply");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceFundBusinessCheckDetailPO", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.AdvanceFundBusinessCheckItemPO", "AdvanceFundBusinessCheckItem")
                        .WithMany("AdvanceRecordDetail")
                        .HasForeignKey("AdvanceFundBusinessCheckItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdvanceFundBusinessCheckItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditProjectDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CreditPo", "Credit")
                        .WithMany()
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditRecordDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CreditPo", "Credit")
                        .WithMany()
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CreditRecordItemPo", "CreditRecordItem")
                        .WithMany("CreditRecordDetail")
                        .HasForeignKey("CreditRecordItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");

                    b.Navigation("CreditRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceCreditPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceItemPo", "CustomizeInvoiceItem")
                        .WithMany()
                        .HasForeignKey("CustomizeInvoiceItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CustomizeInvoiceItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceItemPo", "CustomizeInvoiceItem")
                        .WithMany("CustomizeInvoiceDetail")
                        .HasForeignKey("CustomizeInvoiceItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CustomizeInvoiceItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtDetailExcutePo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtDetailPo", "DebtDetail")
                        .WithMany("DebtDetailExcutes")
                        .HasForeignKey("DebtDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DebtDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CreditPo", "Credit")
                        .WithMany()
                        .HasForeignKey("CreditId");

                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtPo", "Debt")
                        .WithMany("DebtDetails")
                        .HasForeignKey("DebtId");

                    b.Navigation("Credit");

                    b.Navigation("Debt");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtProjectDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtPo", "Debt")
                        .WithMany()
                        .HasForeignKey("DebtId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Debt");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtRecordDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtPo", "Debt")
                        .WithMany()
                        .HasForeignKey("DebtId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtRecordItemPo", "DebtRecordItem")
                        .WithMany("DebtRecordDetail")
                        .HasForeignKey("DebtRecordItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Debt");

                    b.Navigation("DebtRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillDebtPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InputBillPo", "InputBill")
                        .WithMany()
                        .HasForeignKey("InputBillId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InputBill");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InputBillPo", "InputBill")
                        .WithMany("InputBillDetail")
                        .HasForeignKey("InputBillId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InputBill");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InputBillPo", "InputBill")
                        .WithMany("InputBillSubmitDetail")
                        .HasForeignKey("InputBillId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InputBill");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailQuantityPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailPo", "InputBillSubmitDetail")
                        .WithMany("InputBillSubmitDetailQuantity")
                        .HasForeignKey("InputBillSubmitDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InputBillSubmitDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceCreditPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.CreditPo", "Credit")
                        .WithMany()
                        .HasForeignKey("CreditId");

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceReceiptDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InvoiceReceiptItemPo", "InvoiceReceiptItem")
                        .WithMany("InvoiceReceiptDetails")
                        .HasForeignKey("InvoiceReceiptItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InvoiceReceiptItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoAgentBankInfoPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.PaymentAutoItemPo", "PaymentAutoItem")
                        .WithMany()
                        .HasForeignKey("PaymentAutoItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentAutoItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.DebtDetailPo", "DebtDetail")
                        .WithMany()
                        .HasForeignKey("DebtDetilId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.PaymentAutoItemPo", "PaymentAutoItem")
                        .WithMany("PaymentAutoDetails")
                        .HasForeignKey("PaymentAutoItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DebtDetail");

                    b.Navigation("PaymentAutoItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentRecordDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.PaymentPo", "Payment")
                        .WithMany()
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.PaymentRecordItemPo", "PaymentRecordItem")
                        .WithMany("PaymentRecordDetail")
                        .HasForeignKey("PaymentRecordItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Payment");

                    b.Navigation("PaymentRecordItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PreCustomizeInvoiceDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.PreCustomizeInvoiceItemPo", "PreCustomizeInvoiceItem")
                        .WithMany()
                        .HasForeignKey("PreCustomizeInvoiceItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PreCustomizeInvoiceItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RebateProvisionDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.RebateProvisionItemPo", "RebateProvisionItem")
                        .WithMany()
                        .HasForeignKey("RebateProvisionItemId");

                    b.Navigation("RebateProvisionItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveItemPo", "RecognizeReceiveItem")
                        .WithMany("RecognizeReceiveDetails")
                        .HasForeignKey("RecognizeReceiveItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RecognizeReceiveItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveTempDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveItemPo", "RecognizeReceiveItem")
                        .WithMany("RecognizeReceiveTempDetails")
                        .HasForeignKey("RecognizeReceiveItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RecognizeReceiveItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ReconciliationIncomeDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.ReconciliationItemPo", "ReconciliationItem")
                        .WithMany()
                        .HasForeignKey("ReconciliationItemId");

                    b.Navigation("ReconciliationItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.ReconciliationStockDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.ReconciliationItemPo", "ReconciliationItem")
                        .WithMany()
                        .HasForeignKey("ReconciliationItemId");

                    b.Navigation("ReconciliationItem");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.SunPurchaseInvoiceDetailPo", b =>
                {
                    b.HasOne("Inno.CorePlatform.Finance.Data.Models.InvoicePo", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.AdvanceFundBusinessCheckItemPO", b =>
                {
                    b.Navigation("AdvanceRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CreditRecordItemPo", b =>
                {
                    b.Navigation("CreditRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.CustomizeInvoiceItemPo", b =>
                {
                    b.Navigation("CustomizeInvoiceDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtDetailPo", b =>
                {
                    b.Navigation("DebtDetailExcutes");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtPo", b =>
                {
                    b.Navigation("DebtDetails");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.DebtRecordItemPo", b =>
                {
                    b.Navigation("DebtRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillPo", b =>
                {
                    b.Navigation("InputBillDetail");

                    b.Navigation("InputBillSubmitDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InputBillSubmitDetailPo", b =>
                {
                    b.Navigation("InputBillSubmitDetailQuantity");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.InvoiceReceiptItemPo", b =>
                {
                    b.Navigation("InvoiceReceiptDetails");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentAutoItemPo", b =>
                {
                    b.Navigation("PaymentAutoDetails");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.PaymentRecordItemPo", b =>
                {
                    b.Navigation("PaymentRecordDetail");
                });

            modelBuilder.Entity("Inno.CorePlatform.Finance.Data.Models.RecognizeReceiveItemPo", b =>
                {
                    b.Navigation("RecognizeReceiveDetails");

                    b.Navigation("RecognizeReceiveTempDetails");
                });
#pragma warning restore 612, 618
        }
    }
}
