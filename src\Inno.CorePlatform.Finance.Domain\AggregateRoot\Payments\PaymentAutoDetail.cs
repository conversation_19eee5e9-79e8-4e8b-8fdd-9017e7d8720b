﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate
{
    public class PaymentAutoDetail: EntityWithBasicInfo<Guid>
    {
        /// <summary>
        /// 批量付款单Id
        /// </summary>
        public Guid PaymentAutoItemId { get; set; }


        /// <summary>
        /// 应付付款计划明细Id
        /// </summary>
        public Guid DebtDetilId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string PaymentCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        //限定折扣金额
        [Comment("限定折扣金额")]
        [Precision(18, 2)]
        public decimal? LimitedDiscount { get; set; }
    }
}
