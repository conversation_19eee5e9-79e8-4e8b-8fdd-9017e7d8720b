﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice
{
    public class InvoiceReceiptDetail
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid InvoiceReceiptItemId { get; set; }

        public InvoiceReceiptItem InvoiceReceiptItem { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>  
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary>  
        public string? InvoiceCheckCode { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>  
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 是否取消
        /// </summary> 
        public bool? IsCancel { get; set; }

        /// <summary>
        /// 备注
        /// </summary> 
        public string? Remark { get; set; }

        public Guid Id { get; set; }

        public DateTimeOffset CreatedTime { get; set; } = DateTimeOffset.UtcNow;

        public DateTimeOffset? UpdatedTime { get; set; } = DateTimeOffset.UtcNow;

        public string CreatedBy { get; set; } = "none";

        public string? UpdatedBy { get; set; }
    }
}
