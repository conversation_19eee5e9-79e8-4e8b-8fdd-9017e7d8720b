using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    /// <summary>
    /// 盘点记录查询服务实现
    /// </summary>
    public class InventoryRecordQueryService : IInventoryRecordQueryService
    {
        private readonly IInventoryRecordRepository _inventoryRecordRepository;
        private readonly ILogger<InventoryRecordQueryService> _logger;

        public InventoryRecordQueryService(
            IInventoryRecordRepository inventoryRecordRepository,
            ILogger<InventoryRecordQueryService> logger)
        {
            _inventoryRecordRepository = inventoryRecordRepository;
            _logger = logger;
        }

        /// <summary>
        /// 根据盘点单ID获取所有记录
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetByInventoryItemIdAsync(Guid inventoryItemId)
        {
            try
            {
                return await _inventoryRecordRepository.GetByInventoryItemIdAsync(inventoryItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取盘点记录失败 - 盘点单ID: {InventoryItemId}", inventoryItemId);
                throw;
            }
        }

        /// <summary>
        /// 根据公司ID和系统月度获取记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetByCompanyIdAndSysMonthAsync(Guid companyId, string sysMonth)
        {
            try
            {
                return await _inventoryRecordRepository.GetByCompanyIdAndSysMonthAsync(companyId, sysMonth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取盘点记录 失败 - 公司ID: {CompanyId}, 系统月度: {SysMonth}", companyId, sysMonth);
                throw;
            }
        }

        /// <summary>
        /// 获取待处理的记录
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<List<InventoryRecord>> GetPendingRecordsAsync(Guid? companyId = null, string? sysMonth = null)
        {
            try
            {
                return await _inventoryRecordRepository.GetPendingRecordsAsync(companyId, sysMonth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取待处理 盘点记录 失败 - 公司ID: {CompanyId}, 系统月度: {SysMonth}", companyId, sysMonth);
                throw;
            }
        }

        /// <summary>
        /// 检查指定盘点单的所有记录是否都已完成
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        public async Task<bool> CheckAllRecordsCompletedAsync(Guid inventoryItemId)
        {
            try
            {
                return await _inventoryRecordRepository.CheckAllRecordsCompletedAsync(inventoryItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查盘点记录 完成状态 失败 - 盘点单ID: {InventoryItemId}", inventoryItemId);
                throw;
            }
        }

        /// <summary>
        /// 获取盘点记录统计信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<InventoryRecordStatisticsDto> GetStatisticsAsync(Guid companyId, string sysMonth)
        {
            try
            {
                var records = await _inventoryRecordRepository.GetByCompanyIdAndSysMonthAsync(companyId, sysMonth);
                
                var statistics = new InventoryRecordStatisticsDto
                {
                    TotalCount = records.Count,
                    PendingCount = records.Count(r => r.Status == (int)InventoryRecordStatus.Pending),
                    ProcessingCount = records.Count(r => r.Status == (int)InventoryRecordStatus.Processing),
                    CompletedCount = records.Count(r => r.Status == (int)InventoryRecordStatus.Completed),
                    FailedCount = records.Count(r => r.Status == (int)InventoryRecordStatus.Failed)
                };

                _logger.LogInformation("盘点记录 统计 - 公司: {CompanyId}, 月度: {SysMonth}, 总数: {Total}, 完成: {Completed}, 完成率: {Rate}%",
                    companyId, sysMonth, statistics.TotalCount, statistics.CompletedCount, statistics.CompletionRate);

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取盘点记录统计信息失败 - 公司ID: {CompanyId}, 系统月度: {SysMonth}", companyId, sysMonth);
                throw;
            }
        }


    }
}
