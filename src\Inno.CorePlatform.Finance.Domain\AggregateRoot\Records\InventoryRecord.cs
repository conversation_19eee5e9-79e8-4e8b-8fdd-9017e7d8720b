using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Records
{
    /// <summary>
    /// 盘点记录实体 - 用于处理并发更新盘点单号
    /// </summary>
    public class InventoryRecord : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public Guid Id { get; set; }

        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 盘点动作类型
        /// </summary>
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 动作名称
        /// </summary>
        public string ActionName { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string? InventoryCode { get; set; }

        /// <summary>
        /// 状态：0-待处理，1-处理中，2-已完成，99-失败
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }


}
