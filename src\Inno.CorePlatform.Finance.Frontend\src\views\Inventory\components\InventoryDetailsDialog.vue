<template>
  <el-dialog v-model="dialogVisible" :title="title" width="83%" draggable>
    <el-table
      :data="tabData"
      stripe
      border
      
      style="height: 50vh"
    >
      <el-table-column
        property="billCode"
        label="应收单号"
        width="240"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.billCode }}
        </template>
      </el-table-column>
      <el-table-column
        property="billDate"
        width="180"
        label="单据日期"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}
        </template>
      </el-table-column>
      <el-table-column
        property="relateCode"
        label="关联单号"
        width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.relateCode }}
        </template>
      </el-table-column>
      <el-table-column
        property="companyName"
        label="公司"
        width="230"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.companyName }}
        </template>
      </el-table-column>
      <el-table-column
        property="customerName"
        label="客户"
        width="150"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.customerName }}
        </template>
      </el-table-column>
      <el-table-column
        property="serviceName"
        label="业务单元"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.serviceName }}
        </template>
      </el-table-column>
      <el-table-column
        property="creditType"
        label="应收类型"
        show-overflow-tooltip
      >
        <template #default="scope">
          <inno-button-copy :link="false">
            {{ scope.row.creditTypeStr }}
          </inno-button-copy>
        </template>
      </el-table-column>
      <el-table-column
        property="businessDeptFullName"
        label="核算部门"
        width="230"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.businessDeptFullName }}
        </template>
      </el-table-column>
      <el-table-column
        property="orderNo"
        label="订单号"
        width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.orderNo }}
        </template>
      </el-table-column>
      <el-table-column property="value" label="金额" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.value }}
        </template>
      </el-table-column>
      <el-table-column
        property="abatmentAmount"
        label="已冲销"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.abatmentAmount }}
        </template>
      </el-table-column>
      <el-table-column property="leftAmount" label="余额" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.leftAmount }}
        </template>
      </el-table-column>
      <el-table-column
        property="isSureIncome"
        label="确认收入"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.isSureIncome === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        property="createdByName"
        label="创建人"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.createdByName }}
        </template>
      </el-table-column>
    </el-table>
    <slot name="pagination"></slot>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';
import { CreditTypeEnum } from '@/api/metaInfo';
import { ElTable } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';

const props = defineProps({
  dataList: { type: Array, default: () => [] },
  title: { type: String, default: '' }
});
const emits = defineEmits(['submit']);
const tabData = ref([]);
watch(
  () => props.dataList,
  (newVal: any, oldVal) => {
    tabData.value = newVal;
  },
  { deep: true }
);

const dialogVisible = ref(false);

const openDialog = () => {
  dialogVisible.value = true;
};

const closeDialog = () => {
  dialogVisible.value = false;
};

const submit = () => {
  emits('submit');
  closeDialog();
};

onActivated(() => {});
onMounted(() => {});

defineExpose({
  openDialog,
  closeDialog
});
</script>
