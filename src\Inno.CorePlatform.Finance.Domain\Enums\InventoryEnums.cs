using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.Enums
{
    /// <summary>
    /// 盘点动作类型枚举
    /// </summary>
    public enum InventoryActionType
    {
        /// <summary>
        /// 创建暂存盘点
        /// </summary>
        CreateTinyInventory = 1,

        /// <summary>
        /// 创建跟台盘点
        /// </summary>
        CreateSginyInventory = 2,

        /// <summary>
        /// 创建换货盘点
        /// </summary>
        CreateExchangeInventory = 3,

        /// <summary>
        /// 创建待确认收入盘点
        /// </summary>
        CreateSureIncomeInventory = 4,

        /// <summary>
        /// 创建应收盘点
        /// </summary>
        CreateCreditRecordInventory = 5,

        /// <summary>
        /// 创建已签收待开票盘点
        /// </summary>
        CreateReceivedNoInvoiceInventory = 6,

        /// <summary>
        /// 创建应付盘点
        /// </summary>
        CreateDebtRecordInventory = 7,

        /// <summary>
        /// 创建付款盘点
        /// </summary>
        CreatePaymentRecordInventory = 8,

        /// <summary>
        /// 创建垫资盘点
        /// </summary>
        CreateAdvanceRecordInventory = 9,

        /// <summary>
        /// 创建库存盘点
        /// </summary>
        CreateStockInventory = 10,

        /// <summary>
        /// 更新是否完成实盘状态
        /// </summary>
        UpdateActualInventoryCompleted = 11
    }

    /// <summary>
    /// 盘点动作执行状态枚举
    /// </summary>
    public enum InventoryActionStatus
    {
        /// <summary>
        /// 待执行
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 执行中
        /// </summary>
        Running = 1,

        /// <summary>
        /// 执行成功
        /// </summary>
        Success = 2,

        /// <summary>
        /// 执行失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 跳过执行
        /// </summary>
        Skipped = 4
    }

    /// <summary>
    /// 盘点记录状态枚举
    /// </summary>
    public enum InventoryRecordStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 处理中
        /// </summary>
        Processing = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 99
    }
}
