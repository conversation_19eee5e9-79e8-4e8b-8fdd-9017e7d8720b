﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("InputBillDebt")]
    public class InputBillDebtPo : BasePo
    {

        /// <summary>
        /// 进项发票id
        /// </summary>
        public Guid InputBillId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [ForeignKey("InputBillId")]
        public virtual InputBillPo InputBill { get; set; }

        /// <summary>
        /// 关联应付金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DebtAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DebtCode { get; set; }
    }
}
