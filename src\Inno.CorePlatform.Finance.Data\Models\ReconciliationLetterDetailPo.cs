﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("ReconciliationLetterDetail")]
    [Comment("财务对账函明细表")]
    public class ReconciliationLetterDetailPo : BasePo
    {
        /// <summary>
        /// 财务对账函Id
        /// </summary>
        public Guid ReconciliationLetterItemId { get; set; }
        /// <summary>
        ///  财务对账函
        /// </summary>
        [ForeignKey("ReconciliationLetterItemId")]
        public virtual ReconciliationLetterItemPo ReconciliationLetterItem { get; set; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public ReconciliationLetterEnum Classify { get; set; }

        /// <summary>
        /// 明细单据日期（开票日期、应收日期）
        /// </summary> 
        [Comment("明细单据日期（开票日期、应收日期）")]
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 明细单据号（开票日期、应收日期）
        /// </summary>
        [Comment("明细单据号（发票号、应收单号）")]
        public string BillCode { get; set; }

        /// <summary>
        /// 明细金额（开票金额、应收金额）
        /// </summary>
        [Comment("明细金额（开票金额、应收金额）")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }

        /// <summary>
        /// 已收明细金额（开票金额、应收金额）
        /// </summary>
        [Comment("已收明细金额（开票金额、应收金额）")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReceivedValue { get; set; }

        /// <summary>
        /// 未收明细金额（开票金额、应收金额）
        /// </summary>
        [Comment("未收明细金额（开票金额、应收金额）")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal NonReceivedValue { get; set; }
    }
}
