<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>计提返利清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button type="primary" icon="Check" v-if="crud.rowData.status === 0" content="请至少选择一条数据" :disabled="crud.selections.length === 0" @click="submit(crud.rowData)">提交</el-button>
              <!-- <el-button type="primary" icon="Plus" @click="createRebateProvision">创建</el-button>
              <el-button type="danger" icon="Delete" :disabled="!crud.selections.length" v-if="crud.rowData.status === 0" @click="deleteRebateProvision">删除</el-button>-->
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                  <el-tab-pane :label="`已完成(${tabCount.complateCount})`" name="99" lazy />
                  <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            :data="crud.data"
            stripe
            fit1
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55" />

            <el-table-column label="公司" property="companyName" width="300" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单号" property="billCode" width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="billDate" label="单据日期" min-width="100">
              <template #default="scope">
                {{
                scope.row.billDate === null
                ? ''
                : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="类型" property="provisionTypeStr" show-overflow-tooltip min-width="100">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.provisionTypeStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="statusStr" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-tag type="primary">{{ scope.row.statusStr }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" property="createdTime" show-overflow-tooltip>
              <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
            </el-table-column>
            <!-- <el-table-column label="创建人" property="createdByName" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column> -->
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud2" border hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`计提返利信息`" />
              </el-tabs>
            </template>
            <template #default>
              <!-- <inno-button-tooltip type="primary" :disabled="crud.rowData.status === 99" @click="pullDetailsData">重新拉取明细</inno-button-tooltip> -->
              <inno-button-tooltip type="primary" :disabled="crud.rowData.status === 99" @click="ExcelImportHandler">按Excel导入明细</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef2"
            v-inno-loading="crud2.loading"
            class="auto-layout-table"
            highlight-current-row
            
            border
            :data="crud2.data"
            stripe
            @selection-change="crud2.selectionChangeHandler"
            @row-click="crud2.rowClick"
          >
            <el-table-column label="确认函日期" property="confirmationDate" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.confirmationDate === null
                ? ''
                : dateFormat(scope.row.confirmationDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="确认函不含税金" property="confirmTaxAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.confirmTaxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="返利类型" property="rebateTypeStr" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.rebateTypeStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="返利金额" property="rebateAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.rebateAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="不含税金额" property="rebateTaxAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.rebateTaxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="返利名称" property="periodSummary" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.periodSummary }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="下游返利金额" property="nextAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.nextAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="下游返利不含税金额" property="nextTaxAmount" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.nextTaxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="下游返利方式" property="nextRebateMethodStr" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.nextRebateMethodStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="businessDeptFullName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="供应商" property="agentName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="导入时间" property="confirmationDate" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.confirmationDate === null
                ? ''
                : dateFormat(scope.row.confirmationDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud2.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud2" />
          </div>
        </template>
      </inno-split-pane>
      <el-dialog v-model="createFromVisible" title="创建返利计提" width="400" draggable>
        <el-form ref="formDataRef" :model="formData" :rules="formDataRules" v-inno-loading="crud.loading">
          <el-form-item label="部门:" prop="department">
            <inno-department-select
              v-model="formData.newDepart.id"
              v-model:name="formData.newDepart.name"
              v-model:path="formData.newDepart.path"
              v-model:fullName="formData.newDepart.fullName"
              v-model:item="formData.newDepart.item"
              functionUri="metadata://fam"
              @change="businessDeptsChange"
              :style="{ width: inputWidth + 'px' }"
            ></inno-department-select>
          </el-form-item>
          <!-- <el-form-item label="公司:" prop="company.id">
            <inno-remote-select
              v-model="formData.company"
              isObject
              placeholder="请选择或填写公司"
              :queryData="{
                  functionUri: 'metadata://fam'
                }"
              :url="bdsHost + '/api/companies/queryItem'"
              :style="{ width: inputWidth + 'px' }"
            />
          </el-form-item>-->
          <el-form-item label="公司" prop="company.id">
            <el-select v-model="formData.company.id" filterable placeholder="请选择公司" :style="{ width: inputWidth + 'px' }">
              <el-option v-for="item in CompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="月度:" prop="sysMonth">
            <el-date-picker v-model="formData.sysMonth" type="month" format="YYYY-MM" value-format="YYYY-MM" placeholder="请选择月度" clearable :style="{ width: inputWidth + 'px' }" />
          </el-form-item>-->
          <el-form-item label="类型" prop="provisionType">
            <el-select v-model="formData.provisionType" filterable placeholder="请选择类型" clearable :style="{ width: inputWidth + 'px' }">
              <el-option v-for="item in ProvisionTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="submitData" :loading="crud.loading">确定</el-button>
            <el-button @click="createFromVisible = false" :loading="crud.loading">取消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- gatewayUrl + 'v1.0/finance-backend -->
      <!-- 'http://localhost:6211 -->
      <excel-import
        v-model:visibel="ExcelImportVisibel"
        title="按Excel导入"
        :action="
          gatewayUrl + 'v1.0/finance-backend/api/RebateProvision/ImportDetailsData?id='+crud.rowData.id
        "
        :tipStyle="{ color: 'red' }"
        tip="提示: 导入时会清除现有所有明细数据，重新写入明细，请谨慎操作！"
        @submitSuccess="handleSuccess"
      >
        <template v-slot:importTemplate>
          <a href="https://static.innostic.com/template/返利计提导入明细模板.xlsx" style="color: red">下载返利计提明细导入模板</a>
        </template>
      </excel-import>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable,  ElLoading, ElMessage, ElMessageBox } from 'element-plus'; 
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { parse } from 'qs';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ExportInvoices } from '@/api/financeapi';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import { getDepartTree, queryCheckedByDept, getTreeList } from '@/api/bdsData';
import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
const functionUris = {
  export: 'metadata://fam/finance-InvoiceCredit/functions/excute-export'
};
//获取路由
const router = useRouter();
const route = useRoute();
const tableItem = ref<InstanceType<typeof ElTable>>();
const props = defineProps({
  __refresh: Boolean
});
// input宽度统一管理
const inputWidth = 280;
const crud = CRUD(
  {
    title: '返利计提清单',
    url: '/api/RebateProvision/GetList',
    idField: 'id',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      searchKey: ''
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
        loadItemTableData();
      }
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableItem
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '返利计提明细',
    url: '/api/RebateProvision/GetDetailsByItemId',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef2',
    optShow: {
      exportCurrentPage: false // 为false则不会显示按钮
    }
  },
  {
    table: tableRef2
  }
);
onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();
  }
});
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  crud.toQuery();
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  return [
    {
      key: 'companyId',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      placeholder: '公司搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'billDateBeging',
      endDate: 'billDateEnd',
      label: '起始日期',
      type: 'daterange',
      show: true,
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ]
    }
  ];
});

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud2.query = {
        rebateProvisionItemId: crud.rowData.id
      };
      crud2.toQuery();
    }
  },
  { deep: true }
);

const tabhandleClick = () => {
  crud.toQuery();
};
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};

const exportLoading = ref(false);
const exportdata = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    exportLoading.value = true;
    var exportquery = JSON.parse(JSON.stringify(crud.query));
    exportquery['page'] = 0;
    exportquery['limit'] = 0;
    ExportInvoices(exportquery);
    
    exportLoading.value = false;
  });
};

const downloadFile = (invoiceNo, invoiceCode) => {
  request({
    url:
      '/api/CreditQuery/GetKDFilePath?invoiceNo=' +
      invoiceNo +
      '&invoiceCode=' +
      invoiceCode,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          FileViewer.show(
            res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
            0, // 默认打开的下标
            {} // FileViewer props
          );
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶附件，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};

//提交
const submit = (row) => {
  ElMessageBox.confirm(
      '此操作将该公司的返利计提数据提交至金蝶将无法撤回, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      var id = row.id;
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    
      request({
        url: '/api/RebateProvision/Submit',
        method: 'post',
        params: { id: id }
      }).then((res) => {
        if (res.data.code == '200') {
          ElMessage({
            showClose: true,
            message: '提交成功',
            type: 'success',
            duration: 3 * 1000
          }); 
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          }); 
        } 
        loading.close();
      }).catch(() => { 
        loading.close();
      });
    });
}

let tabCount = ref({
  waitAuditCount: 0,
  waitSubmitCount: 0,
  complateCount:0,
  allCount: 0
});

const loadItemTableData = () => {
  request({
    url: '/api/RebateProvision/GetTabCount',
    data: crud.query,
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};

// 添加组件控制显示隐藏
const createFromVisible = ref(false);
// 创建
const createRebateProvision = () => {
  createFromVisible.value = true;
};
// 删除
const deleteRebateProvision = () => {
  if (crud.selections && crud.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请选择要删除的返利计提',
      type: 'error',
      duration: 3 * 1000
    });
  } else { 
    ElMessageBox.confirm(
      '此操作将删除返利计提以及关联的单数据, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      request({
        url: `/api/RebateProvision/Remove`,
        method: 'POST',
        data: { rebateProvisionItemId: crud.rowData.id }
      })
      .then((res) => {
        if (res.data.code === 200) {
          crud.toQuery();
          ElMessage({
            showClose: true,
            message: '删除成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
    })
  }
}
const formDataRef = ref(); // 获取添加form表单实例
const submitData = () => {
  formDataRef.value.validate(async (valid: Object) => {
    if (valid) {
      let requestData = {
        companyId: formData.value.company.id,
        businessDeptId: formData.value.newDepart.id,
        businessDeptFullName: formData.value.newDepart.fullName,
        businessDeptFullPath: formData.value.newDepart.path,
        businessDeptShortName: formData.value.newDepart.name,
        sysMonth: formData.value.sysMonth,
        companyName: formData.value.company.name,
        provisionType: formData.value.provisionType,
      };
      request({
        url: '/api/RebateProvision/Create',
        data: requestData,
        method: 'post'
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '创建成功',
            type: 'success',
            duration: 3 * 1000
          });
          createFromVisible.value = false;
          formData.value = { company: { id: '', name: '' }, sysMonth: '', provisionType: '' };
          formData.value.company = { id: '', name: '' };
          formData.value.newDepart = {
                                      id: '',
                                      name: '',
                                      path: '',
                                      fullName: '',
                                      item: {}
                                    };
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: '创建失败',
            type: 'error',
            duration: 3 * 1000
          });
        }
      });
    }
  });
};
// 添加列表form
const formData = ref({
  company: {
    id:'',
    name:''
  },
  newDepart: {
    id: '',
    name: '',
    path: '',
    fullName: '',
    item: {}
  },
  sysMonth: '',
  provisionType:''
});
let CompanyList = ref([]);
const businessDeptsChange = (node, notclear) => {
  if (!notclear) {
    formData.value.company.id = '';
  }
  if (node !== undefined) {
    //  formData.id = node.data.id;
    queryCheckedByDept(node).then((res) => {
      CompanyList.value = res.data.data;
      //console.log(JSON.stringify(CompanyList))
    });
  }
};
const formDataRules = reactive<FormRules<RuleForm>>({
  // 设置表单校验
  'company.id': [{ required: true, message: '请选择公司', trigger: 'change' }],
  // sysMonth: [{ required: true, message: '请选择日期', trigger: 'blur' }],
  provisionType: [{required: true,message: '请选择类型',trigger: 'change'}]
});
//枚举
const ProvisionTypeList = [
  {
    id: '1',
    name: '月末计提'
  },
  {
    id: '2',
    name: '月初冲回'
  }
];

// 拉取明细
const pullDetailsData = async () => {  
  ElMessageBox.confirm(
      '此操作将重新来取明细数据替换现有数据, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => { 
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    
      request({
        url: '/api/RebateProvision/pullDetailsData',
        method: 'post',
        data: { companyId:crud.rowData.companyId,RebateProvisionItemId:crud.rowData.id  }//
      }).then((res) => {
        if (res.data.code == '200') {
          ElMessage({
            showClose: true,
            message: '拉取成功',
            type: 'success',
            duration: 3 * 1000
          }); 
          crud2.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          }); 
        } 
        loading.close();
      }).catch(() => { 
        loading.close();
      });
    });
};

// 按Excel导入
const ExcelImportVisibel = ref(false);
const ExcelImportHandler = () => {
  ExcelImportVisibel.value = true;
};
// 按Excel导入成功后的回调
const handleSuccess = (res) => {
  if (res.code === 200) {
    crud2.toQuery();
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'success',
      duration: 3 * 1000
    });
  } else {
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'error',
      duration: 3 * 1000
    });
  }
};
</script>
