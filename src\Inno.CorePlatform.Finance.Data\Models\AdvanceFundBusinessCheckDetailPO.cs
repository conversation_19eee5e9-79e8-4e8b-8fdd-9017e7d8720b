﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("AdvanceFundBusinessCheckDetail")]
    [Comment("垫资盘点明细")]
    public class AdvanceFundBusinessCheckDetailPO : BasePo
    {
        [MaxLength(50)]
        public string AdvanceBusinessApplyCode { get; set; }

        public Guid ServiceId { get; set; }
        public string ServiceName { get; set; }

        public Guid HospitalId { get; set; }
        public string HospitalName { get; set; }

        public int IsCheckedHospital { get; set; }

        public int AccountPeriod { get; set; }

        public int ReceivePeriod { get; set; }

        public int AdvanceFundBusinessDays { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal Discount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal BaseDiscount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal SCFDiscount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal SPDDiscount { get; set; }

        public Guid CreditId { get; set; }

        public Guid DebtDetailId { get; set; }

        [MaxLength(50)]
        public string CreditCode { get; set; }

        public DateTime CreditDate { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal CreditValue { get; set; }

        public DateTime? InvoiceDate { get; set; }

        [MaxLength(50)]
        public string DebtCode { get; set; }

        public DateTime? DebtDate { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal DebtValue { get; set; }

        [MaxLength(4000)]
        public string ReceiveCode { get; set; }

        public DateTime? ReceiveDate { get; set; }

        public DateTime? ExpectReceiveDate { get; set; }

        [MaxLength(50)]
        public string PaymentCode { get; set; }

        public DateTime? PaymentDate { get; set; }

        public DateTime? ExpectPaymentDate { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal? SalesTaxRate { get; set; }

        public int? IsProcessAllMage { get; set; }
        /// <summary>
        /// 垫资比例Ratio
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal? ADFDiscount { get; set; }
        /// <summary>
        /// 合计折扣（%）
        /// </summary>
        [Precision(18, 2)]
        public decimal? TotalDiscounts { get; set; }
        /// <summary>
        /// 年化垫资利率（%）
        /// </summary>
        [Precision(18, 2)]
        public decimal? RateOfYear { get; set; }
        /// <summary>
        /// 提前还款利息
        /// </summary> 
        [Precision(18, 6)]
        public decimal? EarlyReturnInterest { get; set; }

        /// <summary>
        /// 逾期利息
        /// </summary>
        [Precision(18, 6)]
        public decimal? OverdueInterest { get; set; }

        #region 新增字段
        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? AdvanceDays { get; set; }

        /// <summary>
        /// 实际供应链金额折扣
        /// </summary> 
        [Precision(18, 6)]
        public decimal? ActualFinanceDiscount { get; set; }
        /// <summary>
        /// 垫资应收到期时间
        /// </summary> 
        public DateTime? AdvanceExpireTime { get; set; }

        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? OverdueDays { get; set; }

        /// <summary>
        /// 逾期状态
        /// </summary>
        public string? OverdueStatus { get; set; }

        /// <summary>
        /// 资金占用金额
        /// </summary> 
        [Precision(18, 6)]
        public decimal? FundUsedValue { get; set; }

        /// <summary>
        /// 基础毛利
        /// </summary> 
        [Precision(18, 6)]
        public decimal? BasicGrossProfit { get; set; }

        /// <summary>
        /// 垫资利息收入
        /// </summary> 
        [Precision(18, 6)]
        public decimal? IntrestIncome { get; set; }

        /// <summary>
        /// 合计毛利
        /// </summary> 
        [Precision(18, 6)]
        public decimal? TotalGrossProfit { get; set; }

        /// <summary>
        /// 校验
        /// </summary> 
        [Precision(18, 6)]
        public decimal? Verify { get; set; }

        /// <summary>
        /// 开票后实际支付(天数)
        /// </summary> 
        [Precision(18, 6)]
        public decimal? ActualPaydays { get; set; }

        /// <summary>
        /// 提示(放款风险)
        /// </summary>
        public string? HintRisk { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [MaxLength(200)]
        public string? AgentName { get; set; }
        /// <summary>
        /// 批量付款单号
        /// </summary>
        [MaxLength(200)]
        public string? BatchpaymentCode { get; set; }
        #endregion

        public Guid AdvanceFundBusinessCheckItemId { get; set; }

        [ForeignKey("AdvanceFundBusinessCheckItemId")]
        public virtual AdvanceFundBusinessCheckItemPO AdvanceFundBusinessCheckItem { get; set; }
    }
}
