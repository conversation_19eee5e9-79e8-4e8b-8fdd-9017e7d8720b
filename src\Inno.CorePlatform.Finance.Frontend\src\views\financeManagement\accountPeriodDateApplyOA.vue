<template>
  <div class="app-page-container">
    <div v-if="idValue === undefined" class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>预计付款日期调整申请</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud"
                           :permission="crudPermission"
                           :hiddenColumns="[]"
                           hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey"
                             @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-if="idValue === undefined"
                            v-model:query-list="queryList"
                            :crud="crud" />
      <inno-split-pane split="horizontal" :default-percent="100">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation v-if="idValue === undefined"
                               :crud="crud"
                               :hiddenColumns="[]"
                               hidden-opts-right
                               style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.auditStatus"
                       class="demo-tabs"
                       @tab-change="tabhandleClick">

                <el-tab-pane :label="`待审核(${tabCount.waitAuditCount})`"
                             name="1"
                             lazy />
                <el-tab-pane :label="`已完成(${tabCount.complateCount})`"
                             name="99"
                             lazy />
                <el-tab-pane :label="`已拒绝(${tabCount.refuseCount})`"
                             name="66"
                             lazy />
                <el-tab-pane :label="`全部(${tabCount.allCount})`"
                             name="-1"
                             lazy />
                <el-tab-pane :label="`我的审核(${tabCount.myCount})`"
                             name="5000"
                             lazy />
              </el-tabs>
            </template>
            <template #default>



              <el-button v-if="crud.rowData.oaRequestId !== null"
                         icon="View"
                         content="请选择一条数据"
                         :disabled="!crud.selections.length"
                         type="primary"
                         @click="auditProcessClick(crud.rowData)">
                查看审批过程
              </el-button>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'"
                               class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table ref="tableRef0"
                    v-inno-loading="crud.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    border
                    :data="crud.data"
                    stripe
                    show-summary
                    :summary-method="getSummaries"
                    :row-class-name="crud.tableRowClassName"
                    @sort-change="crud.sortChange"
                    @selection-change="crud.selectionChangeHandler"
                    @row-click="crud.singleSelection">
            <el-table-column type="selection"
                             fixed="left"
                             width="55"></el-table-column>
            <!-- <el-table-column fixed="left" width="55">
    <template #default="scope">
      <inno-table-checkbox
        :checked="scope.row.id === crud.rowData.id"
      />
    </template>
  </el-table-column>-->

            <el-table-column property="code"
                             label="调整申请单号"
                             width="150"
                             fixed="left"
                             show-overflow-tooltip>
              <template #header="{ column }">
              </template>
              <template #default="scope">
                <inno-button-copy type="primary"
                                  link
                                  size="small"
                                  :text="scope.row.code">
                  {{ scope.row.code }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column property="billCode"
                             label="应付单号"
                             width="150"
                             fixed="left"
                             show-overflow-tooltip>
              <template #header="{ column }">
              </template>
              <template #default="scope">
                <inno-button-copy type="primary"
                                  link
                                  size="small"
                                  :text="scope.row.debtDetail.debt.billCode">
                  {{ scope.row.debtDetail.debt.billCode }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="账期类型"
                             property="accountPeriodTypeStr"
                             show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.debtDetail.accountPeriodTypeStr }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司"
                             property="companyName"
                             show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.debtDetail.debt.companyName }}
                </inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="供应商"
                             property="agentName"
                             show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.debtDetail.debt.agentName }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称"
                             property="projectName"
                             show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.debtDetail.debt.projectName }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="付款金额"
                             property="value"
                             show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.debtDetail.value || 0"
                              format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column label="原始预计付款日期"
                             :width="120"
                             property="probablyPayTime"
                             show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.originProbablyPayTime, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column label="修改后预计付款日期"
                             :width="180"
                             property="accountingPeriodDate"
                             show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.currentProbablyPayTime, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <!--<el-table-column property="purchaseCode"
                   label="采购单号"
                   width="150"
                   show-overflow-tooltip>
    <template #header="{ column }">
    </template>
    <template #default="scope">
      <inno-button-copy type="primary"
                        link
                        size="small"
                        :text="scope.row.debtDetail.purchaseCode">
        {{ scope.row.debtDetail.purchaseCode }}
      </inno-button-copy>
    </template>
  </el-table-column>
  <el-table-column property="relateCode"
                   label="入库单号"
                   width="150"
                   show-overflow-tooltip>
    <template #header="{ column }">
    </template>
    <template #default="scope">
      <inno-button-copy type="primary"
                        link
                        size="small"
                        :text="scope.row.debtDetail.debt.relateCode">
        {{ scope.row.debtDetail.debt.relateCode }}
      </inno-button-copy>
    </template>
  </el-table-column>-->
            <el-table-column label="备注"
                             :width="150"
                             property="remark"
                             show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.remark }}
              </template>
            </el-table-column>

            <el-table-column label="查看附件"
                             property="attachFileIds"
                             width="100"
                             show-overflow-tooltip>
              <template #default="scope">
                <el-link style="font-size: 13px"
                         type="primary"
                         @click="showAttachFile(scope.row.attachFileIds, scope.row.id)">
                  {{ scope.row.attachFileIds ? '查看附件' : '' }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="创建时间"
                             property="updatedTime"
                             width="150"
                             show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD HH:mm:ss') }}
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>

              <template #default="scope">
                {{ scope.row.createdByName }}
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条

            <div class="flex-1"></div>
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>

      </inno-split-pane>
    </div>

    <!-- 查看附件 -->
    <el-dialog v-model="comfile_show"
               title="查看附件"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">
              {{ format(scope.row.length) }}
            </inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">
              查看
            </span>

            <!-- <span style="cursor: pointer" @click="deleteFile_letter(scope.row.id)">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />

  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import request from '@/utils/request';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';
import { ReconciliationLetterTmpEnum } from '@/api/metaInfo';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import approveProcess from '@/component/ApproveProcess.vue';
import { queryCheckedByDept } from '@/api/bdsData';
import { FormRules } from 'element-plus';
import { useRoute } from 'vue-router';
const route = useRoute();
const idValue = route.query.id; //获取地址栏id参数
const tableRef0 = ref<InstanceType<typeof ElTable>>();
const addRef = ref<InstanceType<typeof ElForm>>();
const dialogVisible = ref(false);
const splitDlgShow = ref(false);
let dataListBusinessDept = reactive([]);
const selectDetailId = ref('');
const splitAmount = ref('');
const approveProcessRef = ref(null);
//上传回函件
let flieList_letter = ref('');
let comfile_upload_letter = ref(false);
const comfile_show_letter = ref(false);
const showfiles_letter = ref([]);
const openAttachment_letter = (row) => {
  flieList_letter = ref('');
  comfile_upload_letter.value = true;
};
//上传附件
const comfile_show = ref(false);
const showfiles = ref([]);
const handleClosefile = () => {
  comfile_upload_letter.value = false;
};
const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
};
// input宽度统一管理
const inputWidth = 280;




watch(
  () => splitAmount.value,
  () => {
    splitAmount.value = splitAmount.value
      .replace(/[^\d.]/g, '')
      .replace(/\.{2,}/g, '.')
      .replace('.', '$#$')
      .replace(/\./g, '')
      .replace('$#$', '.')
      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
      .replace(/^\./g, '');
  }
);

const crudPermission = ref({
  add: ['permission.add.uri']
  //download: ['permission.export.uri']
});
const crud = CRUD(
  {
    title: '账期起始日期调整申请',
    url: '/api/DebtQuery/GetDebtDetailListQueryAsync',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: {
      auditStatus: -1, auditId: idValue === undefined ? '' : idValue
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadTableData();
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
      }
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0
  }
);
const queryList = computed(() => [

  {
    key: 'code',
    label: '调整申请单号',
    show: true
  },
  {
    key: 'billCode',
    label: '应付单号',
    show: true
  },
  //{
  //  key: 'purchaseCode',
  //  label: '采购单号',
  //  show: true
  //},
  //{
  //  key: 'storeInItemCode',
  //  label: '入库单号',
  //  show: true
  //},
  {
    key: 'companyId',
    label: '公司',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    placeholder: '公司名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: false
  },
  {
    key: 'agentId',
    label: '供应商',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    placeholder: '供应商名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: false
  },
  {
    key: 'projectId',
    label: '项目',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/meta`,
    placeholder: '项目称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: false
  },
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
  //附件
  const showAttachFile = (showAttachFileids, id) => {
    if (showAttachFileids == '' || showAttachFileids.length <= 0) {
      ElMessage({
        showClose: true,
        message: '操作失败，原因：该数据没有附件！',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      request({
        url: `/api/DebtQuery/GetAttachFile`,
        method: 'POST',
        data: {
          debtDetailAuditItemId: id
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            comfile_show.value = true;
            showfiles.value = res.data.data;
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    }
  };


//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
let tabCount = ref({
  allCount: 0,
  waitSubmitCount: 0,
  waitAuditCount: 0,
  refuseCount: 0,
  complateCount: 0,
  myCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/DebtQuery/GetDebtDetailTabCount',
    data: {
      auditStatus: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
const searchCrud = () => {
  crud.query.auditStatus = '-1';
  crud.toQuery();
};

onMounted(() => {
  crud.toQuery();
});
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
//文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
};
const getStatus = (auditStatus) => {
  var statusText = '';
  switch (auditStatus) {
    case 0:
      statusText = '待提交';
      break;
    case 1:
      statusText = '待审核';
      break;
    case 66:
      statusText = '已拒绝';
      break;
    case 99:
      statusText = '已完成';
      break;
    case 5000:
      statusText = '我的审核';
      break;
    default:
  }
  return statusText;
};
//审批过程
const auditProcessClick = (row) => {
  //审批过程
  approveProcessRef.value.requestId = row.oaRequestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.activeName = 'first';
  approveProcessRef.value.GetRemark();
};
</script>

<style scoped lang="scss">
.mycard_css {
  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  :deep(.el-tabs__content) {
    padding: 0px;
  }
}
:deep(.el-table__expand-column) {
  position: absolute;
  display: none;
}
</style>
