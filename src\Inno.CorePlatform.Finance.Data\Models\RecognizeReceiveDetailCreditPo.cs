﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("RecognizeReceiveDetailCredit")]
    [Comment("认款明细列表对应应收信息")]
    public class RecognizeReceiveDetailCreditPo : BasePo
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 认款明细Id
        /// </summary>
        public Guid RecognizeReceiveDetailId { get; set; }


        [ForeignKey("RecognizeReceiveDetailId")]
        public RecognizeReceiveDetailPo RecognizeReceiveDetail { get; set; }

        /// <summary>
        /// 本次认款金额
        /// </summary> 
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CurrentValue { get; set; }

        public Guid? CreditId { get; set; }

        [ForeignKey("CreditId")]
        public CreditPo Credit { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        [MaxLength(200)]

        public string CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        [MaxLength(200)]

        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 发票号Id 
        /// </summary>
        public Guid? InvoiceId { get; set; }
        /// <summary>
        /// 订单号 
        /// </summary>
        [MaxLength(200)] 
        public string? OrderNo { get; set; }
        /// <summary>
        /// 订单Id 
        /// </summary>
        public Guid? OrderId { get; set; }
    }
}
