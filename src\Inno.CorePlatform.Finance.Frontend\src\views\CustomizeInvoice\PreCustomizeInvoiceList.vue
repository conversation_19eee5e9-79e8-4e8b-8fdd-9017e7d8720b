<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
</style>

<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>预开票列表</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-left>
        <template v-if="!crud.query?.id" #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL>
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button slot="reference" type="primary" @click="applyFor">预开票申请</el-button>
              <el-button
                slot="reference"
                type="success"
                :disabled="crud.selections.length === 0"
                v-if="crud.rowData.status === 0 && crud2.data.length > 0"
                :loading="submitLoading"
                @click="submit"
              >提交</el-button>
              <el-button slot="reference" type="danger" :disabled="crud.selections.length === 0" v-if="crud.rowData.status === 0" @click="remove">删除</el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                  <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                  <el-tab-pane :label="`已提交(${tabCount.waitAuditCount})`" name="1" lazy />
                  <el-tab-pane :label="`已开票(${tabCount.complateCount})`" name="99" lazy />
                  <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            
            :data="crud.data"
            stripe
            fit1
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column label="序号" fixed="left" show-overflow-tooltip width="55">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.$index + 1 }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单号" property="code" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="businessDeptFullName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="状态" property="statusStr" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.statusStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" class-name="isSum" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <!-- <el-table-column label="应收类型" property="creditTypeStr" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>
              </template>
            </el-table-column>-->
            <el-table-column label="创建人" property="createdByName" width="90" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
              </template>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建日期" min-width="100" sortable>
              <template #default="scope">
                {{
                scope.row.createdTime === null
                ? ''
                : dateFormat(scope.row.createdTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR>
          <inno-crud-operation :crud="crud2" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 40px">
            <template #default>
              <el-button slot="reference" :disabled="crud.rowData.status !== 0" type="primary" :loading="exportDetailsloading" @click="exportDetails">导入明细</el-button>
            </template>
            <template #opts-left>
              <div>
                <el-tabs class="demo-tabs">
                  <el-tab-pane :label="`预开票明细`" name="0" lazy />
                </el-tabs>
              </div>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef2"
            v-inno-loading="crud2.loading"
            class="auto-layout-table"
            highlight-current-row
            
            border
            :data="crud2.data"
            draggable
            stripe
            :row-class-name="crud2.tableRowClassName"
            @selection-change="crud2.selectionChangeHandler"
            @row-click="crud2.rowClick"
          >
            <!-- <el-table-column label="货号" property="productNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
              </template>
            </el-table-column>-->
            <el-table-column label="开票名称" property="productName" width="150" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObjectDetail.productName" :crud="crud2" :column="column" />
              </template>
              <template #default="scope">{{ scope.row.productName }}</template>
            </el-table-column>
            <el-table-column label="计量单位" property="packUnit" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.packUnit }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" property="specification" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="数量" property="quantity" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.quantity }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="单价" property="price" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.price }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="税收分类编码" property="taxTypeNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.taxTypeNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="税率" property="taxRate" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.taxRate }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" class-name="isSum" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="行性质" property="tag" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.tag }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud2.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud2" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <!-- 预开票申请组件 -->
    <el-dialog v-model="applyForDialog" title="预开票申请" style="width:20%">
      <el-form ref="applyForRef" :model="applyForData" :rules="applyForformRules">
        <el-form-item label="部门:" prop="newDepart.id">
          <inno-department-select
            ref="innoDepartmentSelectRef"
            v-model="applyForData.newDepart.id"
            v-model:name="applyForData.newDepart.name"
            v-model:path="applyForData.newDepart.path"
            v-model:fullName="applyForData.newDepart.fullName"
            v-model:item="applyForData.newDepart.item"
            functionUri="metadata://fam"
            @change="businessDeptsChange"
          ></inno-department-select>
        </el-form-item>
        <el-form-item label="公司" prop="company.id" required>
          <el-select v-model="applyForData.company" clearable filterable placeholder="请选择公司" value-key="id">
            <el-option v-for="item in CompayList" :key="item.id" :label="item.name" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="公司" prop="companyId" required>
          <inno-remote-select
            v-model="applyForData.companyId"
            v-model:item="applyForData.company"
            is-guid="2"
            default-first-option
            :queryData="{
              functionUri: 'metadata://fam'
            }"
            placeholder="请选择或填写公司"
            :url="gatewayUrl + 'v1.0/bdsapi/api/companies/meta'"
          />
        </el-form-item>-->
        <el-form-item label="项目" prop="projectId">
          <inno-remote-select
            v-model="applyForData.projectId"
            v-model:item="applyForData.project"
            :queryData="{status:2,type:'0000400008',companyId:applyForData.company?.id}"
            default-first-option
            placeholder="请选择项目"
            :url="gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta'"
          />
        </el-form-item>
        <el-form-item label="客户" prop="customers.id">
          <inno-remote-select
            v-model="applyForData.customers"
            :max-collapse-tags="1"
            isObject
            :is-guid="2"
            placeholder="请选择客户"
            :queryData="{
              functionUri: 'metadata://fam',
              projectId:applyForData.projectId
            }"
            @change="customerChange"
            labelK="name"
            valueK="id"
            :url="`${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/GetCustomersByProjectId?projectId=${applyForData.projectId??''}`"
          />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input v-model="applyForData.amount" @change="validateAmount" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="applyForDialog = false">取消</el-button>
          <el-button type="primary" :loading="applyForloading" @click="applyForSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 导入明细 -->
    <!-- gatewayUrl + 'v1.0/finance-backend -->
    <excel-import
      v-model:visibel="ExcelImportVisibel"
      title="按Excel导入明细"
      :action="gatewayUrl + 'v1.0/finance-backend/api/CustomizeInvoice/ExportPreCustomizeInvoiceDetails?id='+crud.rowData.id"
      :tipStyle="{ color: 'red' }"
      tip="提示: 导入时会清除现有所有明细数据，重新写入明细且列表不支持修改，请确认好信息再导入"
      @submitSuccess="handleSuccess"
    >
      <template v-slot:importTemplate>
        <a href="https://static.innostic.com/template/预开票明细导入模板.xlsx?v=1.0" style="color: red">下载预开票明细导入模板</a>
      </template>
    </excel-import>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';

import { ElTable,ElLoading } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import request from '@/utils/request';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { ExportInvoices } from '@/api/financeapi';
import { FormRules } from 'element-plus';
import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
import { getDepartTree, queryCheckedByDept, getTreeList } from '@/api/bdsData';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
const functionUris = {
  export: 'metadata://fam/finance-InvoiceCredit/functions/excute-export'
};
const tableItem = ref<InstanceType<typeof ElTable>>();
const props = defineProps({
  __refresh: Boolean
});
const crud = CRUD(
  {
    title: '预开票列表',
    url: '/api/CustomizeInvoice/GetPreCustomizeInvoiceList',
    idField: 'id',
    userNames: ['createdBy'],
    method: 'post',
    query: {
      searchKey: '',
      status: '0', //待提交
    },
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
        loadTableData();                                                                
      }
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableItem
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const queryListDetail = computed(() => [
  {
    key: 'productName',
    label: '产品名称',
    show: true
  }
]);
const queryObjectDetail = computed(() =>
  Object.fromEntries(queryListDetail.value.map((item) => [item.key, item]))
);
const crud2 = CRUD(
  {
    title: '订单信息',
    url: '/api/CustomizeInvoice/GetPreCustomizeInvoiceDetails',
    method: 'post',
    query: {},
    resultKey: {
      list: 'list', 
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud2.data.length && crud2.data.length > 0) {
          crud2.singleSelection(crud2.data[0]);
        }                                                            
      }
    },
    tablekey: 'tableRef2',
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableRef2
  }
);
onActivated(() => {
  if (props.__refresh) {
    crud.toQuery();
  }
});
onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  crud.toQuery();
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId'
    }
  );
return [
    {
      key: 'code',
      label: '单号',
      show: true
    },
    {
      key: 'projectName',
      label: '项目名称',
      show: true
    },
    ...items,
    {
      key: 'customerId',
      label: '客户',
      method: 'post',
      type: 'remoteSelect',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      placeholder: '客户搜索',
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'orderNo',
      label: '订单号',
      show: true
    },
    {
      key: 'createdBy',
      label: '创建人',
      multiple: true,
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
      placeholder: '用户名称搜索',
      valueK: 'name',
      labelK: 'displayName',
      props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
      slots: {
        option: ({ item }) => (
          <>
            <span>{item.displayName}</span>
            <span style="float:right">{item.name}</span>
          </>
        )
      }
    },
    {
      key: 'createdDateBeging',
      endDate: 'createdDateEnd',
      label: '创建日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    },
  ]
});
// const queryList = computed(() => {
//   return [
//     {
//       key: 'code',
//       label: '单号',
//       show: true
//     },
//     // {
//     //   key: 'invoiceNo',
//     //   label: '发票号',
//     //   show: true
//     // },
//     {
//       key: 'projectName',
//       label: '项目名称',
//       show: true
//     },
//     {
//       key: 'companyId',
//       label: '公司',
//       method: 'post',
//       type: 'remoteSelect',
//       url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
//       placeholder: '公司搜索',
//       labelK: 'name',
//       valueK: 'id',
//       props: { KeyWord: 'name', resultKey: 'data.data' },
//       show: true
//     },
//     {
//       key: 'customerId',
//       label: '客户',
//       method: 'post',
//       type: 'remoteSelect',
//       url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
//       placeholder: '客户搜索',
//       labelK: 'name',
//       valueK: 'id',
//       props: { KeyWord: 'name', resultKey: 'data.data' },
//       show: true
//     },
//     {
//       key: 'businessDeptId',
//       label: '核算部门',
//       type: 'departmentSelect',
//       props: {
//         queryData: { functionUri: 'metadata://fam' },
//         allSelectable: true
//       },
//       show: true
//     },
//     {
//       key: 'orderNo',
//       label: '订单号',
//       show: true
//     },
//     {
//       key: 'createdBy',
//       label: '创建人',
//       multiple: true,
//       method: 'post',
//       type: 'remoteSelect',
//       url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
//       placeholder: '用户名称搜索',
//       valueK: 'name',
//       labelK: 'displayName',
//       props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
//       slots: {
//         option: ({ item }) => (
//           <>
//             <span>{item.displayName}</span>
//             <span style="float:right">{item.name}</span>
//           </>
//         )
//       }
//     },
//     {
//       key: 'createdDateBeging',
//       endDate: 'createdDateEnd',
//       label: '创建日期',
//       type: 'daterange',
//       formart: 'YYYY-MM-DD',
//       defaultTime: [
//         new Date(2000, 1, 1, 0, 0, 0),
//         new Date(2000, 2, 1, 23, 59, 59)
//       ],
//       show: true
//     },
//   ];
// });

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud2.query = {
        preCustomizeInvoiceItemId: crud.rowData.id,
      };
      crud2.toQuery();
    }
  },
  { deep: true }
);

let tabCount = ref({
  waitSubmitCount: 0,
  waitAuditCount: 0,
  complateCount:0,
  allCount: 0
});
const tabhandleClick = () => {
  crud.toQuery();
  loadTableData();
};
const loadTableData = () => {
  request({
    url: '/api/CustomizeInvoice/GetPreCustomizeInvoiceTabCount',
    data: {
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  if (cellValue !== null) {
    cellValue = Number(cellValue).toFixed(4);
    cellValue += '';
    if (!cellValue.includes('.')) cellValue += '.';
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
        return $1 + ',';
      })
      .replace(/\.$/, '');
  }
};

const searchCrud = () => {
  crud.query.status = '0';
  crud.toQuery();
};
//表单规则
const applyForRef = ref();
const applyForformRules = reactive<FormRules>({
  'newDepart.id':[
    {
      required: true,
      message: '请选择部门',
      trigger: 'change'
    }
  ],
  projectId:[
    {
      required: true,
      message: '请选择项目',
      trigger: 'change'
    }
  ],
  'company.id':[
    {
      required: true,
      message: '请选择公司',
      trigger: 'change'
    }
  ],
  'customers.id':[
    {
      required: true,
      message: '请选择客户',
      trigger: 'change'
    }
  ],
  amount:[
    {
      required: true,
      message: '请填写金额',
      trigger: 'blur'
    }
  ],
});
const invoiceFillingConfirmformRef = ref();
const invoiceFillingConfirmformRules = reactive<FormRules>({
  purchaseCode: [
    {
      required: true,
      message: '请填写采购单编号',
      trigger: 'blur'
    }
  ]
});
//删除
const remove = () => {
   ElMessageBox.confirm('此操作将删除当前单据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/CustomizeInvoice/DeletePreCustomizeInvoice?id='+crud.rowData.id,
      method: 'POST'
    }).then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '删除成功',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      crud.toQuery(); //刷新页面
    });
  });
}
//预开票申请弹窗
const applyForDialog = ref(false);

//预开票申请 Vo
const applyForData = ref({
  project:{id:'',name:''},
  projectId:'',
  company:{},
  companyId:'',
  customerId:'',
  customers:{id:'',name:''},
  newDepart: {
    id: '',
    name: '',
    path: '',
    fullName: '',
    item:'',
    businessArea:''
  },
})
//部门改变事件
let CompayList = ref([]);
const businessDeptsChange = (node, notclear) => {
  if (!notclear) {
    applyForData.value.company = {};
  }
  if (node !== undefined) {
    //  formData.id = node.data.id;
    queryCheckedByDept(node).then((res) => {
      CompayList.value = res.data.data;
    });
  }
};
//客户更改事件
const customerChange = () => {
  applyForData.value.customerId = applyForData.value.customers.id
}
//预开票申请
const applyFor = () => {
  if (applyForRef.value) {
    applyForRef.value.resetFields();
    CompayList.value = [];
    applyForData.value.company.id = '';
    applyForData.value.company = {};
    applyForData.value.customers = {};
  }
  applyForDialog.value = true;
}
//预开票提交
const applyForloading = ref(false);
const applyForSubmit = () => {
  if (applyForDialog.value) {
    //填报
    if (!applyForRef.value) return;
    applyForRef.value.validate(async (valid, fields) => {
      if (valid) {
        submitOpt();
      }
    })
  }
}
const isAmountValid = ref(false);
//校验金额
const validateAmount = () => {
  const emailRegex = /^[0-9]+(\.[0-9]{1,2})?$/;
  isAmountValid.value = emailRegex.test(applyForData.value.amount);
}
// 递归方法检查树结构中是否存在指定的parentId
const innoDepartmentSelectRef = ref();
const checkIfParentExists = (tree, targetParentId) => {
  let resultData = {
    result: false,
    data: {}
  };
  // 遍历当前层级的每个节点
  for (const node of tree) {
    // 如果找到了匹配的parentId，返回true
    if (node.id === targetParentId) {
      return (resultData = {
        result: true,
        data: node
      });
    }
    // 如果当前节点有子节点，继续递归检查子节点
    if (node.children && node.children.length > 0) {
      resultData = checkIfParentExists(node.children, targetParentId);
      // 只要有一个分支返回true，整个结果就为true
      if (resultData.result) break;
    }
  }
  return resultData;
};
//调用同一接口
const submitOpt = () => {
  let parentData = {
    data: {
      extraInfo: {
        deptShortName: null
      }
    }
  };
  if (
    applyForData.value.newDepart.item.extraInfo.deptShortName === null &&
    applyForData.value.newDepart.fullName.indexOf('/') != -1
  ) {
    parentData = checkIfParentExists(
      innoDepartmentSelectRef.value.list,
      applyForData.value.newDepart.item.parentId
    );
    // 限制20次递归
    let i = 20;
    while (parentData.data.extraInfo.deptShortName == null && i > 0) {
      parentData = checkIfParentExists(
        innoDepartmentSelectRef.value.list,
        parentData.data.parentIdx
      );
      i--;
    }
  }
  applyForData.value.newDepart.businessArea = parentData?.data?.extraInfo.deptShortName ||
      applyForData.value.newDepart?.item?.extraInfo.deptShortName ||
      '';
  applyForloading.value = true;
  if (!isAmountValid.value){
    ElMessage({
      showClose: true,
      message: "请输入正确的金额",
      type: 'error',
      duration: 3 * 1000
    });
    applyForloading.value = false;
    return;
  }
  request({
    url: '/api/CustomizeInvoice/CreatePreCustomizeInvoice',
    data: applyForData.value,
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'success',
        duration: 3 * 1000
      });
      applyForDialog.value = false;
      applyForloading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      applyForloading.value = false;
      applyForDialog.value = false;
    }
  });
}
//导入明细
const ExcelImportVisibel = ref(false);
const exportDetails = () => {
  ExcelImportVisibel.value = true;
}
// 按Excel导入成功后的回调
const handleSuccess = (res) => {
  if (res.code === 200) {
    crud2.toQuery();
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'success',
      duration: 3 * 1000
    });
  } else {
    ElMessage({
      showClose: true,
      message: res.message,
      type: 'error',
      duration: 3 * 1000
    });
  }
};
// 提交
const submitLoading = ref(false);
const submit = () =>{
  submitLoading.value = true;
  request({
    url: '/api/CustomizeInvoice/SubmitPreCustomizeInvoice?id='+crud.rowData.id,
    method: 'post'
  }).then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'success',
        duration: 3 * 1000
      });
      submitLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      submitLoading.value = false;
    }
  });
}
</script>
