﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    /// <summary>
    /// 应收明细表
    /// </summary>
    [Table("CreditDetail")]
    public class CreditDetailPo : BasePo
    {
        /// <summary>
        /// 应收Id
        /// </summary>
        public Guid CreditId { get; set; }

        /// <summary>
        /// 应收
        /// </summary>
        [ForeignKey("CreditId")]
        public virtual CreditPo Credit { get; set; }

        /// <summary>
        /// 原明细Id
        /// </summary>
        public string? OriginDetailId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary> 
        [Comment("包装规格")]
        public string? PackSpec { get; set; }
        /// <summary>
        /// 品名
        /// </summary> 
        [Comment("原始品名")]
        public string? ProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Comment("原始单位")]
        public string? PackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary> 
        [Comment("原始规格")]
        public string? Specification { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }

        /// <summary>
        /// 原始单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高价值(0:否,1:是)
        /// </summary>
        public int? IFHighValue { get; set; }

        /// <summary>
        /// 金额
        /// </summary> 
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// 未开票金额
        /// </summary> 
        [Column(TypeName = "decimal(18,4)")]
        public decimal? NoInvoiceAmount { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary> 
        [Column(TypeName = "decimal(18,4)")]
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary> 
        public string? RelateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary> 
        public string? OrderNo { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary> 
        public string? CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary> 
        public string? CustomerName { get; set; }

        /// <summary>
        /// 销售的原始明细Id
        /// </summary> 
        [Comment("销售的原始明细Id")]
        public Guid? OriginalId { get; set; }

        /// <summary>
        /// 销售明细Id
        /// </summary> 
        [Comment("销售明细Id")]
        public Guid? SaleDetailId { get; set; }
        /// <summary>
        /// 批次Id
        /// </summary> 
        [Comment("批次Id")]
        public Guid? BatchId { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary> 
        [Comment("业务单元Id")]
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }


        /// <summary>
        ///价格来源
        /// </summary>  
        [Comment("价格来源")]
        public PriceSourceEnum? PriceSource { get; set; }
        /// <summary>
        /// 原始成本
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? OriginalCost { get; set; }
    }
}
