﻿using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot
{
    public class DebtRecordDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 应付盘点Id
        /// </summary>
        public Guid DebtRecordItemId { get; set; }
       

        /// <summary>
        /// 应付Id
        /// </summary>
        public Guid DebtId { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedValue { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal Value { get; set; }

    }
}
