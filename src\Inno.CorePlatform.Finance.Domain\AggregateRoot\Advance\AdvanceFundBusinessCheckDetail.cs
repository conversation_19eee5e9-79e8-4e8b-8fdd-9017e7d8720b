﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance
{

    public class AdvanceFundBusinessCheckDetail : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        public string AdvanceBusinessApplyCode { get; set; }

        public Guid ServiceId { get; set; }
        public string ServiceName { get; set; }

        public Guid HospitalId { get; set; }
        public string HospitalName { get; set; }

        public int IsCheckedHospital { get; set; }

        public int AccountPeriod { get; set; }

        public int ReceivePeriod { get; set; }

        public int AdvanceFundBusinessDays { get; set; }

        public decimal Discount { get; set; }

        public decimal BaseDiscount { get; set; }

        public decimal SCFDiscount { get; set; }

        public decimal SPDDiscount { get; set; }

        public Guid CreditId { get; set; }

        public Guid DebtDetailId { get; set; }

        public string CreditCode { get; set; }

        public DateTime CreditDate { get; set; }

        public decimal CreditValue { get; set; }

        public DateTime? InvoiceDate { get; set; }

        public string DebtCode { get; set; }

        public DateTime? DebtDate { get; set; }

        public decimal DebtValue { get; set; }

        public string ReceiveCode { get; set; }

        public DateTime? ReceiveDate { get; set; }

        public DateTime? ExpectReceiveDate { get; set; }

        public string PaymentCode { get; set; }

        public DateTime? PaymentDate { get; set; }

        public DateTime? ExpectPaymentDate { get; set; }

        public decimal? SalesTaxRate { get; set; }

        public int? IsProcessAllMage { get; set; }

        public decimal? ADFDiscount { get; set; }


        public Guid AdvanceFundBusinessCheckItemId { get; set; }
        /// <summary>
        /// 合计折扣（%）
        /// </summary>
        [Precision(18, 2)]
        public decimal? TotalDiscounts { get; set; }
        /// <summary>
        /// 年化垫资利率（%）
        /// </summary>
        [Precision(18, 2)]
        public decimal? RateOfYear { get; set; }
        /// <summary>
        /// 提前还款利息
        /// </summary> 
        [Precision(18, 6)]
        public decimal? EarlyReturnInterest { get; set; }
         
        /// <summary>
        /// 逾期利息
        /// </summary>
        [Precision(18, 6)]
        public decimal? OverdueInterest { get; set; }
        #region 新增字段
        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? AdvanceDays { get; set; }

        /// <summary>
        /// 实际供应链金额折扣
        /// </summary>
        public decimal? ActualFinanceDiscount { get; set; }
        /// <summary>
        /// 垫资应收到期时间
        /// </summary>
        public DateTime? AdvanceExpireTime { get; set; }

        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? OverdueDays { get; set; }

        /// <summary>
        /// 逾期状态
        /// </summary>
        public string? OverdueStatus { get; set; }

        /// <summary>
        /// 资金占用金额
        /// </summary>
        public decimal? FundUsedValue { get; set; }

        /// <summary>
        /// 基础毛利
        /// </summary>
        public decimal? BasicGrossProfit { get; set; }

        /// <summary>
        /// 垫资利息收入
        /// </summary>
        public decimal? IntrestIncome { get; set; }

        /// <summary>
        /// 合计毛利
        /// </summary>
        public decimal? TotalGrossProfit { get; set; }

        /// <summary>
        /// 校验
        /// </summary>
        public decimal? Verify { get; set; }

        /// <summary>
        /// 开票后实际支付(天数)
        /// </summary>
        public decimal? ActualPaydays { get; set; }

        /// <summary>
        /// 提示(放款风险)
        /// </summary>
        public string? HintRisk { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }
        /// <summary>
        /// 批量付款单号
        /// </summary>
        public string? BatchpaymentCode { get; set; }

        #endregion
    }
}
