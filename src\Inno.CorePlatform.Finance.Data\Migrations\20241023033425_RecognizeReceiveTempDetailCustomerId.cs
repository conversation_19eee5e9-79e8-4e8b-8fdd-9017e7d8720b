﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class RecognizeReceiveTempDetailCustomerId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CustomerId",
                table: "RecognizeReceiveTempDetail",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "付款单位Id");

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "RecognizeReceiveTempDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                comment: "付款单位名称");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "RecognizeReceiveTempDetail");

            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "RecognizeReceiveTempDetail");
        }
    }
}
