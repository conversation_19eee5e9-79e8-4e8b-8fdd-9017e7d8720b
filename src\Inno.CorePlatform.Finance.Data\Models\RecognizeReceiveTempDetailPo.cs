﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("RecognizeReceiveTempDetail")]
    [Comment("认款单详情")]
    public class RecognizeReceiveTempDetailPo : BasePo
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }

        [ForeignKey("RecognizeReceiveItemId")]
        public RecognizeReceiveItemPo RecognizeReceiveItem { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary> 
        [Comment("项目名称")]
        [MaxLength(200)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        [Comment("项目单号")]
        [MaxLength(200)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 认款时间
        /// </summary>
        public DateTime RecognizeDate { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>
        [Precision(18, 2)]
        public decimal Value { get; set; }
        
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        [Comment("核算部门Id路径")]
        [MaxLength(500)]
        public string? BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        [Comment("核算部门名称路径")]
        [MaxLength(500)]
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int? BusinessDeptId { get; set; }

        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }

        /// <summary>
        /// 收款类型
        /// </summary>
        public int? CollectionType { get; set; }

        /// <summary>
        /// 付款单位Id
        /// </summary>
        [Comment("付款单位Id")]
        [MaxLength(50)]
        public string? CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary>
        [Comment("付款单位名称")]
        [MaxLength(200)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RecognizeReceiveDetailEnum? Status { get; set; }

        /// <summary>
        /// 撤销金额
        /// </summary>
        public decimal? CancelValue { get; set; }
    }
}
