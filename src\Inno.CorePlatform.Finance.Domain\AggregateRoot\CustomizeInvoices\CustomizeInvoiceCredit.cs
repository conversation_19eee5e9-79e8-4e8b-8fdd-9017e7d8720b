﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices
{
    public class CustomizeInvoiceCredit : EntityWithBasicInfo<Guid>, IAggregateRoot
    {
        /// <summary>
        /// 制作开票Id
        /// </summary> 
        public Guid CustomizeInvoiceItemId { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>  
        public string CreditCode { get; set; }

        /// <summary>
        /// 制作开票Code
        /// </summary>

        public string CustomizeInvoiceItemCode { get; set; }

        /// <summary>
        ///  金额
        /// </summary> 
        public decimal Value { get; set; }
    }
}
