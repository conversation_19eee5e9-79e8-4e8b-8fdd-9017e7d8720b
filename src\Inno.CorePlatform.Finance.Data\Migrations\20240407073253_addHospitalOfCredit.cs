﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addHospitalOfCredit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "HospitalId",
                table: "Credit",
                type: "uniqueidentifier",
                nullable: true,
                comment: "终端医院Id");

            migrationBuilder.AddColumn<string>(
                name: "HospitalName",
                table: "Credit",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                comment: "终端医院");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "Credit");

            migrationBuilder.DropColumn(
                name: "HospitalName",
                table: "Credit");
        }
    }
}
