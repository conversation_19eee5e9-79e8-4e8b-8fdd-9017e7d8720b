﻿using Dapr;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 金蝶订阅
    /// </summary>
    [ApiController]
    [Route("api/KingdeeSub")]
    public class KingdeeSubController : ControllerBase
    {
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly ILogger<KingdeeSubController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public KingdeeSubController(IInvoiceQueryService invoiceQueryService, ILogger<KingdeeSubController> logger)
        {
            _invoiceQueryService = invoiceQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 订阅批量下载发票广播
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubBatchDownloadInvoice")]
        [Topic(DomainConstants.Default_PubSubName, DomainConstants.Batch_Download_Invoice)]
        public async Task SubBatchDownloadInvoice(BatchDownLoadInvoiceInput input)
        {
            try
            {
                var jsonStr = JsonConvert.SerializeObject(input);
                _logger.LogWarning($"接收到批量打包下载发票广播参数：" + jsonStr);
                await _invoiceQueryService.BatchSendEmailInvoices(input);
            }
            catch (Exception ex)
            {
                _logger.LogError($"订阅批量下载发票广播失败:{ex.Message}");
            }
        }
    }
}
